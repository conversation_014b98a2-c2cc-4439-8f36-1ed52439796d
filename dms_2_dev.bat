adb  root
adb  remount

set filepath=%cd%
set sdk=/data/tongxingDms


adb  push %filepath%\build_android_aarch64\libtx_dms.so %sdk%/libtx_dms.so
adb  push %filepath%\build_android_aarch64\test_tx_dms %sdk%/test_tx_dms
adb  push %filepath%\phone2.jpg %sdk%/phone2.jpg
adb  push %filepath%\whx4.png %sdk%/whx4.png


adb  shell chmod +x %sdk%/test_tx_dms


adb  shell "ps -A | grep evs | awk '{print $2}' | xargs kill"
adb  shell "ps -A | grep tvm | awk '{print $2}' | xargs kill"

timeout /t 10
exit