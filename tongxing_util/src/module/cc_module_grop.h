#include "cc_module.h"
#include <map>

namespace tongxing{
     class CcModuleGrop :public CcModule{
        
        class SubModule {
            public:
                std::string name;
                std::vector< std::vector< int> > inbound_nodes;
                std::shared_ptr<CcModule> module;
                bool execute_flag;
        };
        public:
         int init(const Json::Value& config);
         int setInput(const std::vector< std::shared_ptr<NumArray> >& in);
         size_t getOutputNum();
         std::shared_ptr<NumArray> getOutput(int index);
        private:
            void clear_execute_flags();
            int execute_dependency(std::shared_ptr<SubModule> module);
            std::vector<std::shared_ptr<SubModule> > moduleList;
            std::vector<std::string> moduleNameList;
            std::vector<std::vector<int>> output_layer_indexs;
            int input_layer_index;
            // std::vector<std::vector<int>> output_dependencys;
            // std::vector<std::shared_ptr<CcModuleNode> > nodeList;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

    };
}

