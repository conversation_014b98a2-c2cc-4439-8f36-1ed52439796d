#ifndef __CC_RESIZE2D_H__
#define __CC_RESIZE2D_H__
#include "cc_module.h"
#include "json.h"
namespace tongxing{
    class CcResize2D: public CcModule
    {
    public:
        int init(const Json::Value& config);
        int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
    private:
        std::vector<std::shared_ptr<NumArray> > input_;
        std::vector<std::shared_ptr<NumArray> > output_;
        int width_=0;
        int height_=0;
        bool keep_ratio_=false;

    };


}



#endif