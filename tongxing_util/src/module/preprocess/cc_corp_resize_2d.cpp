#include "cc_corp_resize_2d.h"
#include "cnpy.h"
#include "time_function.h"
#include "time_profile.h"

namespace tongxing {
int CcCropResize2D::init(const Json::Value& config) {
    norm_resize_param.output_w = config["output_width"].asInt();
    norm_resize_param.output_h = config["output_height"].asInt();
    norm_resize_param.keep_ratio = config["keep_ratio"].asBool();
    flag_crop_ = config["flag_crop"].asBool();
    if (flag_crop_) {
        crop_x_ = config["crop_x"].asInt();
        crop_y_ = config["crop_y"].asInt();
        crop_width_ = config["crop_width"].asInt();

        crop_height_ = config["crop_height"].asInt();
    }

    if (config.isMember("flag_input_nchw")) {
        flag_input_nchw = config["flag_input_nchw"].asBool();
    }
    if (config.isMember("flag_output_nchw")) {
        flag_output_nchw = config["flag_output_nchw"].asBool();
    }

    return 0;
}
int CcCropResize2D::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}
int CcCropResize2D::execute() {
    // TimeFunction time_f("cc_corp_resize_norm_2d.cpp execute");
    TX_LOG_DEBUG("CcCropResize2D ", "---- execute---- start shape[0]:%d", input_[0]->shape[0]);
    // printf("CcCropResize2D intput shape:%d,%d,%d,%d\n", input_[0]->shape[0], input_[0]->shape[1],
    //        input_[0]->shape[2], input_[0]->shape[3]);
    // std::vector<size_t> temp_vec1 = {
    //     (unsigned long)input_[0]->shape[0], (unsigned long)input_[0]->shape[1],
    //     (unsigned long)input_[0]->shape[2], (unsigned long)input_[0]->shape[3]};
    // cnpy::npy_save("output_face_resize_before.npy", (unsigned char*)input_[0]->data, temp_vec1,
    //                "w");
    if (flag_crop_) {
        cc_assert(input_.size() == 1);
    } else {
        cc_assert(input_.size() <= 2 && input_.size() > 0);
    }

    cc_assert(input_[0]->shape[0] == 1);

    NumArrayCorpParam corp_param;
    corp_param.flag_corp = flag_crop_;
    if (flag_crop_) {
        corp_param.x = crop_x_;
        corp_param.y = crop_y_;
        corp_param.w = crop_width_;
        corp_param.h = crop_height_;
    } else if (input_.size() == 2) {
        corp_param.flag_corp = true;
        corp_param.x = input_[1]->getTensor<float>()->operator[](0)[0][2].get();
        corp_param.y = input_[1]->getTensor<float>()->operator[](0)[0][3].get();
        corp_param.w = input_[1]->getTensor<float>()->operator[](0)[0][4].get();
        corp_param.h = input_[1]->getTensor<float>()->operator[](0)[0][5].get();
    }

    index_map.reset();
    norm_param_std_map.reset();
    norm_param_mean_map.reset();
    creat_corp_resize_map(index_map, norm_param_std_map, norm_param_mean_map,
                          flag_input_nchw ? input_[0]->shape[3] : input_[0]->shape[2],
                          flag_input_nchw ? input_[0]->shape[2] : input_[0]->shape[1],
                          flag_input_nchw ? input_[0]->shape[1] : input_[0]->shape[3],
                          norm_resize_param, corp_param, flag_input_nchw, flag_output_nchw);

    std::vector<int> ouput_dim;
    ouput_dim.resize(4);
    ouput_dim[0] = input_[0]->shape[0];

    if (flag_output_nchw) {
        ouput_dim[1] = flag_input_nchw ? input_[0]->shape[1] : input_[0]->shape[3];
        ouput_dim[2] = norm_resize_param.output_h;
        ouput_dim[3] = norm_resize_param.output_w;
    } else {
        ouput_dim[3] = flag_input_nchw ? input_[0]->shape[1] : input_[0]->shape[3];
        ouput_dim[1] = norm_resize_param.output_h;
        ouput_dim[2] = norm_resize_param.output_w;
    }

    norm_param_std_map = nullptr;
    norm_param_mean_map = nullptr;
    auto output = numarray_remap_normalization(input_[0], ouput_dim, index_map, norm_param_std_map,
                                               norm_param_mean_map);
    std::vector<std::shared_ptr<NumArray>> outputs;
    outputs.push_back(output);
    output_ = outputs;

    TX_LOG_DEBUG("CcCropResize2D ", "---- execute---- end");
    // printf("CcCropResize2D out shape:%d,%d,%d,%d\n", output_[0]->shape[0], output_[0]->shape[1],
    //        output_[0]->shape[2], output_[0]->shape[3]);
    // std::vector<size_t> temp_vec = {
    //     (unsigned long)output_[0]->shape[0], (unsigned long)output_[0]->shape[1],
    //     (unsigned long)output_[0]->shape[2], (unsigned long)output_[0]->shape[3]};
    // cnpy::npy_save("output_face_resize.npy", (float*)output_[0]->data, temp_vec, "w");
    return 0;
}
size_t CcCropResize2D::getOutputNum() {
    return output_.size();
}
std::shared_ptr<NumArray> CcCropResize2D::getOutput(int index) {
    return output_[index];
}
REGISTER_CC_MODULE(CropResize2D, CcCropResize2D)
}  // namespace tongxing