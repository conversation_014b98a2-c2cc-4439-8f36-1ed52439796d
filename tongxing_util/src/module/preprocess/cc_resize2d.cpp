#include "cc_resize2d.h"
#include <iostream>
#include "cc_assert.h"
#include "cc_numarray_tool.h"
#include "opencv2/opencv.hpp"
namespace tongxing
{
    int CcResize2D::init(const Json::Value &config)
    {
        if (config.isMember("width"))
        {
            width_ = config["width"].asInt();
        }
        if (config.isMember("height"))
        {
            height_ = config["height"].asInt();
        }
        if (config.isMember("keep_ratio"))
        {
            keep_ratio_ = config["keep_ratio"].asBool();
        }
        return 0;
    }
    int CcResize2D::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcResize2D::execute()
    {
        cc_assert(input_.size() == 1);
        std::vector<std::shared_ptr<NumArray>> outputs;
        auto &input_ptr = input_[0];
        int batch = input_ptr->shape[0];
        int ch = input_ptr->shape[1];
        int input_h = input_ptr->shape[2];
        int input_w = input_ptr->shape[3];
        //  std::cout << ch << " " << input_h << " " << input_w << std::endl;
        float scale_h = 1;
        float scale_w = 1;
        if (input_h > input_w)
        {
            scale_w = (float)input_w / (float)input_h;
        }
        else
        {
            scale_h = (float)input_h / (float)input_w;
        }
        // std::cout << ch << " " << scale_h << " " << scale_w << std::endl;
        int output_h = height_;
        int output_w = width_;
        if (keep_ratio_)
        {
            output_h = height_ * scale_h;
            output_w = width_ * scale_w;
        }
        // std::cout << ch << " " << output_h << " " << output_w << std::endl;
        std::shared_ptr<NumArray> output = creat_numarray({batch,ch, output_h, output_w}, NumArray::DataType::UINT8);
        auto output_tensor = output->getTensor<unsigned char>();
        auto input_tensor = input_ptr->getTensor<unsigned char>();
        for (int i = 0; i < batch; i++)
        {
            for (int j = 0; j < ch; j++)
            {
                ScalePlane(input_tensor->operator[](i)[j].data_, input_w,
                                   input_w, input_h,
                                   output_tensor->operator[](i)[j].data_, output_w,
                                   output_w, output_h,1,1);
            }
            // cv::Mat m(output_h,output_w,CV_8UC1,output_tensor->operator[](i).data_);
            // cv::imwrite("test_resize2d.jpg",m);
        }
        outputs.push_back(output);

        output_ = outputs;
        return 0;
    }
    size_t CcResize2D::getOutputNum()
    {

        return output_.size();
    }
    std::shared_ptr<NumArray> CcResize2D::getOutput(int index)
    {
        return output_[index];
    }
    REGISTER_CC_MODULE(resize2d, CcResize2D)
}