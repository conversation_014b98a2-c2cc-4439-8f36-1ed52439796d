#include "cc_pad2d.h"
#include <iostream>
#include "cc_assert.h"
#include <vector>
#include <memory>
#include "cc_numarray_tool.h"
#include "opencv2/opencv.hpp"
namespace tongxing
{
    int CcPad2D::init(const Json::Value &config)
    {
        if (config.isMember("width"))
        {
            pad_width = config["width"].asInt();
        }
        if (config.isMember("height"))
        {
            pad_height = config["height"].asInt();
        }
        return 0;
    }
    int CcPad2D::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcPad2D::execute()
    {
        cc_assert(input_.size() == 1);
        std::vector<std::shared_ptr<NumArray>> outputs;
        auto &input_ptr = input_[0];

        int batch = input_ptr->shape[0];
        int ch = input_ptr->shape[1];
        int p_w = input_ptr->shape[3] % pad_width;
        int p_h = input_ptr->shape[2] % pad_height;
        if (p_w != 0)
        {
            p_w = pad_width - p_w;
        }
        if (p_h != 0)
        {
            p_h = pad_height - p_h;
        }

        // std::cout << ch << " " << p_h << " " << p_w << std::endl;
        if (p_w != 0 || p_h != 0)
        {

            int w = input_ptr->shape[3] + p_w;
            int h = input_ptr->shape[2] + p_h;
            std::shared_ptr<NumArray> output = creat_numarray({batch, ch, h, w}, NumArray::DataType::UINT8);

            auto output_tensor = output->getTensor<unsigned char>();
            auto input_tensor = input_ptr->getTensor<unsigned char>();

            for (int i = 0; i < batch; i++)
            {
                for (int c = 0; c < ch; c++)
                {
                    auto output_tensor_ch = output_tensor->operator[](i)[c];
                    auto input_tensor_ch = input_tensor->operator[](i)[c];
                    //  std::cout<<input_tensor_ch.shape()[0]<<" "<<input_tensor_ch.shape()[1]<<std::endl;
                    //   std::cout<<output_tensor_ch.shape()[0]<<" "<<output_tensor_ch.shape()[1]<<std::endl;
                    for (int j = 0; j < input_tensor_ch.shape()[0] * input_tensor_ch.shape()[1]; j++)
                    {
                        // std::cout<<j%input_tensor_ch.shape()[1]<<" "<<j/input_tensor_ch.shape()[1]<<std::endl;
                        output_tensor_ch[j / input_tensor_ch.shape()[1]][j % input_tensor_ch.shape()[1]].get() = input_tensor_ch[j / input_tensor_ch.shape()[1]][j % input_tensor_ch.shape()[1]].get();
                    }
                }
                // cv::Mat m(h,w,CV_8UC1,output_tensor->operator[](i).data_);
                // cv::imwrite("test_pad2d.jpg",m);
            }
            outputs.push_back(output);
        }
        else
        {
            outputs.push_back(input_ptr);
        }

        output_ = outputs;
        return 0;
    }
    size_t CcPad2D::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcPad2D::getOutput(int index)
    {
        return output_[index];
    }
    REGISTER_CC_MODULE(pad2d, CcPad2D)
}