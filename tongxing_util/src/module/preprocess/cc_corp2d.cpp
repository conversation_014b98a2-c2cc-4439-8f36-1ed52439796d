#include "cc_corp2d.h"
#include <memory.h>
#include "cc_numarray_tool.h"
#include "opencv2/opencv.hpp"

namespace tongxing
{

    int CcCorp2D::init(const Json::Value &config)
    {

        return 0;
    }
    int CcCorp2D::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcCorp2D::execute()
    {
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        cc_assert(input_.size() == 2);
        cc_assert(input_[0]->shape[0] == 1);
        // input_[0]->getTensor<unsigned char>()->printShape();
        // input_[1]->getTensor<float>()->printShape();
        int x = input_[1]->getTensor<float>()->operator[](0)[0][2].get();
        int y = input_[1]->getTensor<float>()->operator[](0)[0][3].get();
        int w = input_[1]->getTensor<float>()->operator[](0)[0][4].get();
        int h = input_[1]->getTensor<float>()->operator[](0)[0][5].get();
        std::vector<int> shape = input_[0]->shape;
        //  std::cout<<x<<" "<<y<<" "<<w<<" "<<h<<std::endl;
        int max_h = shape[2];
        int max_w = shape[3];
        x = std::max(x, 0);
        y = std::max(y, 0);
        int xmax = std::min(max_w, x + w);
        int ymax = std::min(max_h, y + h);
        w = xmax - x;
        h = ymax - y;
       
        int input_w = shape[3];
        int input_h = shape[2];
        int batch = shape[0];
        int ch = shape[1];
        std::shared_ptr<NumArray> output = creat_numarray({batch, ch, h, w}, NumArray::DataType::UINT8);
        auto output_tensor = output->getTensor<unsigned char>();
        auto input_tensor = input_[0]->getTensor<unsigned char>();
        std::vector<std::shared_ptr<NumArray>> outputs;

            for (int i = 0; i < ch; i++)
            {
                unsigned char *src = input_tensor->operator[](0)[i].data_;
                unsigned char *dst = output_tensor->operator[](0)[i].data_;
                for (int y_ = 0; y_ < h; y_++)
                {
                    // std::cout<<y_*w<<" "<<(y+y_)*input_w+x<<std::endl;
                    memcpy(dst + y_ * w, src + (y + y_) * input_w + x, w);
                }

                // cv::Mat m(h, w, CV_8UC1, output_tensor->operator[](0)[i].data_);
                // cv::imwrite("test_crop2d.jpg", m);
                // exit(1);
            }
        
        outputs.push_back(output);
        output_ = outputs;
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        return 0;
    }
    size_t CcCorp2D::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcCorp2D::getOutput(int index)
    {
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        // output_[index]->getTensor<unsigned char>()->printShape();
        return output_[index];
    }
    REGISTER_CC_MODULE(Corp2D, CcCorp2D)
}