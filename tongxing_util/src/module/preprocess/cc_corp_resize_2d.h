#ifndef __CC_CROP_RESIZE_2D_H__
#define __CC_CROP_RESIZE_2D_H__
#include "cc_module.h"
#include "cc_numarray_tool.h"
#include "json.h"
namespace tongxing {
class CcCropResize2D : public CcModule {
  public:
    int init(const Json::Value& config);
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:
    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;
    NumArrayResizeNormParam norm_resize_param;
    bool flag_crop_ = false;
    int crop_x_;
    int crop_y_;
    int crop_width_;
    int crop_height_;

    bool flag_input_nchw = true;
    bool flag_output_nchw = true;

    std::shared_ptr<std::vector<int>> index_map;
    std::shared_ptr<std::vector<float>> norm_param_std_map;
    std::shared_ptr<std::vector<float>> norm_param_mean_map;
};

}  // namespace tongxing

#endif