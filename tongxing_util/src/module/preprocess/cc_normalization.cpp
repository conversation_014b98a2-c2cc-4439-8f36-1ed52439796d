#include "cc_normalization.h"
#include "cc_assert.h"
#include <iostream>
#include "cc_numarray_tool.h"
namespace tongxing
{
    int CcNormalization::init(const Json::Value &config)
    {
        cc_assert(config["std"].isArray());
        cc_assert(config["mean"].isArray());
        cc_assert(config["std"].size() == config["mean"].size());

        if (config.isMember("flag_output_nchw"))
        {
            flag_output_nchw_ = config["flag_output_nchw"].asBool();
        }
        
        for (int i = 0; i < config["std"].size(); i++)
        {
            std.push_back(config["std"][i].asFloat());
            mean.push_back(config["mean"][i].asFloat());
        }
        return 0;
    }
    int CcNormalization::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        input_ = in;
        return 0;
    }
    int CcNormalization::execute()
    {
        std::vector<std::shared_ptr<NumArray>> outputs;
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;

        auto &input_ptr = input_[0];
        int batch = input_ptr->shape[0];
        int ch = input_ptr->shape[1];
        cc_assert(ch == std.size());
        int h = input_ptr->shape[2];
        int w = input_ptr->shape[3];

        auto input_tensor = input_ptr->getTensor<unsigned char>();
        if (flag_output_nchw_)
        {
            std::shared_ptr<NumArray> output = creat_numarray({batch,ch, h, w}, NumArray::DataType::FLOAT32);
            auto output_tensor = output->getTensor<float>();
            for (int b = 0; b < batch; b++)
            {
                for (int i = 0; i < ch; i++)
                {
                    unsigned char *input_tensor_ch = input_tensor->operator[](b)[i].data_;
                    float* output_tensor_ch = output_tensor->operator[](b)[i].data_;
                    for (int j = 0; j < w * h; j++)
                    {
                        // std::cout<<j/input_ptr->shape[2]<<":"<<j%input_ptr->shape[2]<<std::endl;
                        output_tensor_ch[j]= ((float)input_tensor_ch[j] - mean[i]) / std[i];
                    }
                }
            }
            outputs.push_back(output);
        }else{
            std::shared_ptr<NumArray> output = creat_numarray({batch,h, w, ch}, NumArray::DataType::FLOAT32);
            auto output_tensor = output->getTensor<float>();
            for (int b = 0; b < batch; b++)
            {
                for (int i = 0; i < h; i++) 
                {
                    for (int j = 0; j < w; j++) {
                        for (int k = 0; k < ch; k++) {
                            unsigned char *input_tensor_ch = input_tensor->operator[](b)[k].data_; // 获取 NCHW 布局中的数据
                            float *output_tensor_ch = output_tensor->operator[](b)[i][j][k].data_; // 获取 NHWC 布局中的数据
                            
                            // 将数据从 NCHW 转换到 NHWC 并进行归一化
                            *output_tensor_ch = static_cast<float>(input_tensor_ch[i * w + j]) - mean[k];
                            *output_tensor_ch /= std[k];
                        }
                    }
                }
            }
            outputs.push_back(output);
        }
        
        
       
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
       
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        

        output_ = outputs;
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        return 0;
    }
    size_t CcNormalization::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcNormalization::getOutput(int index)
    {
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        // output_[index]->getTensor<float>()->printShape();
        return output_[index];
    }
    REGISTER_CC_MODULE(normalization, CcNormalization)
}