#include "cc_corp_resize_norm_2d.h"
#include "time_profile.h"
#include "time_function.h"

namespace tongxing
{
    int CcCropResizeNorm2D::init(const Json::Value &config)
    {

        norm_resize_param.output_w = config["output_width"].asInt();

        norm_resize_param.output_h = config["output_height"].asInt();

        norm_resize_param.keep_ratio = config["keep_ratio"].asBool();
        flag_crop_ = config["flag_crop"].asBool();
        if (flag_crop_)
        {
            crop_x_ = config["crop_x"].asInt();
            crop_y_ = config["crop_y"].asInt();
            crop_width_ = config["crop_width"].asInt();

            crop_height_ = config["crop_height"].asInt();
        }
        norm_resize_param.flag_norm = config["flag_norm"].asBool();
        if (norm_resize_param.flag_norm)
        {
            cc_assert(config["std"].isArray());
            cc_assert(config["mean"].isArray());
            cc_assert(config["std"].size() <= 3);
            cc_assert(config["std"].size() == config["mean"].size());
            for (int i = 0; i < config["std"].size(); i++)
            {
                norm_resize_param.norm_std[i] = (config["std"][i].asFloat());
                norm_resize_param.norm_mean[i] = (config["mean"][i].asFloat());
            }
        }
        if (config.isMember("flag_input_nchw"))
        {
            flag_input_nchw = config["flag_input_nchw"].asBool();
        }
        if (config.isMember("flag_output_nchw"))
        {
            flag_output_nchw = config["flag_output_nchw"].asBool();
        }
        if (config.isMember("flag_static_mode"))
        {
            flag_static_mode = config["flag_static_mode"].asBool();
        }
        if (config.isMember("flag_calc_histogram")) {
            flag_calc_histogram = config["flag_calc_histogram"].asBool();
        }

        return 0;
    }
    int CcCropResizeNorm2D::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcCropResizeNorm2D::execute()
    {
        // TimeFunction time_f("cc_corp_resize_norm_2d.cpp execute");
        TX_LOG_DEBUG("CropResizeNorm2D ", "---- execute---- start shape[0]:%d", input_[0]->shape[0]);
        if (flag_crop_)
        {
            cc_assert(input_.size() == 1);
        }
        else
        {
            cc_assert(input_.size() <= 2 && input_.size() > 0);
        }

        cc_assert(input_[0]->shape[0] == 1);

        NumArrayCorpParam corp_param;
        corp_param.flag_corp = flag_crop_;
        if (flag_crop_)
        {

            corp_param.x = crop_x_;
            corp_param.y = crop_y_;
            corp_param.w = crop_width_;
            corp_param.h = crop_height_;
        }
        else if (input_.size() == 2)
        {
            corp_param.flag_corp = true;
            corp_param.x = input_[1]->getTensor<float>()->operator[](0)[0][2].get();
            corp_param.y = input_[1]->getTensor<float>()->operator[](0)[0][3].get();
            corp_param.w = input_[1]->getTensor<float>()->operator[](0)[0][4].get();
            corp_param.h = input_[1]->getTensor<float>()->operator[](0)[0][5].get();
        }
        // std::cout << " -------- input_:"<< input_.size() << std::endl;
        // std::cout << " x :"<<corp_param.x <<" y:"<<corp_param.y << " w:"<<corp_param.w << " h:" <<corp_param.h << std::endl;
        // TX_LOG_DEBUG("cc_corp_resize_norm_2d.cpp","debug");
        if (!flag_static_mode)
        {
            // std::vector<NumArrayCorpParam> corp_params;
            // corp_params.push_back(corp_param);
            std::shared_ptr<NumArray> pixels_histogram = creat_numarray({256}, NumArray::DataType::UINT64);
            auto output = numarray_corp_resize_pad_normalization(input_[0], norm_resize_param, {corp_param}, flag_calc_histogram, pixels_histogram, flag_input_nchw, flag_output_nchw);
            std::vector<std::shared_ptr<NumArray>> outputs;
            outputs.push_back(output);
            outputs.push_back(pixels_histogram);
            output_ = outputs;
        }
        else
        {
            bool flag_creat_map = false;
            if (last_image_dim.size() != input_[0]->shape.size())
            {
                flag_creat_map = true;
            }
            else
            {
                for (int i = 0; i < last_image_dim.size(); i++)
                {
                    if (last_image_dim[i] != input_[0]->shape[i])
                    {
                        flag_creat_map = true;
                        break;
                    }
                }
            }
            if (corp_param.flag_corp != last_corp_param_.flag_corp)
            {
                flag_creat_map = true;
            }
            else if (corp_param.flag_corp)
            {
                if (corp_param.h != last_corp_param_.h || corp_param.w != last_corp_param_.w || corp_param.x != last_corp_param_.x || corp_param.y != last_corp_param_.y)
                {
                    flag_creat_map = true;
                }
            }

            if (!index_map)
            {
                flag_creat_map = true;
            }

            if (norm_resize_param.flag_norm)
            {
                if (!norm_param_std_map || !norm_param_mean_map)
                {
                    flag_creat_map = true;
                }
            }
            // std::cout<<"flag_creat_map:"<<flag_creat_map<<std::endl;
            //  TX_LOG_DEBUG("cc_corp_resize_norm_2d.cpp","debug out w :%d  h :%d" ,norm_resize_param.output_w,norm_resize_param.output_h);
            if (flag_creat_map)
            {
                index_map.reset();
                norm_param_std_map.reset();
                norm_param_mean_map.reset();
                creat_corp_resize_map(index_map, norm_param_std_map, norm_param_mean_map,
                                      flag_input_nchw ? input_[0]->shape[3] : input_[0]->shape[2],
                                      flag_input_nchw ? input_[0]->shape[2] : input_[0]->shape[1],
                                      flag_input_nchw ? input_[0]->shape[1] : input_[0]->shape[3],
                                      norm_resize_param, corp_param, flag_input_nchw, flag_output_nchw);
            }
            std::vector<int> ouput_dim;
            ouput_dim.resize(4);
            ouput_dim[0] = input_[0]->shape[0];

            if (flag_output_nchw)
            {
                ouput_dim[1] = flag_input_nchw ? input_[0]->shape[1] : input_[0]->shape[3];
                ouput_dim[2] = norm_resize_param.output_h;
                ouput_dim[3] = norm_resize_param.output_w;
            }
            else
            {
                ouput_dim[3] = flag_input_nchw ? input_[0]->shape[1] : input_[0]->shape[3];
                ouput_dim[1] = norm_resize_param.output_h;
                ouput_dim[2] = norm_resize_param.output_w;
            }
            // TX_LOG_DEBUG("cc_corp_resize_norm_2d.cpp","debug out w :%d  h :%d" ,norm_resize_param.output_w,norm_resize_param.output_h);
            bool isEqual = std::equal(input_[0]->shape.begin(), input_[0]->shape.end(), ouput_dim.begin());
            if (isEqual)
            {
                std::vector<std::shared_ptr<NumArray>> outputs;
                outputs.push_back(input_[0]);
                output_ = outputs;
                last_image_dim = input_[0]->shape;
                last_corp_param_ = corp_param;
            }
            else
            {
                auto output = numarray_remap_normalization(input_[0], ouput_dim, index_map, norm_param_std_map, norm_param_mean_map);
                std::vector<std::shared_ptr<NumArray>> outputs;
                outputs.push_back(output);
                output_ = outputs;
                last_image_dim = input_[0]->shape;
                last_corp_param_ = corp_param;
            }
        }
        TX_LOG_DEBUG("CropResizeNorm2D ", "---- execute---- end");
        return 0;
    }
    size_t CcCropResizeNorm2D::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcCropResizeNorm2D::getOutput(int index)
    {
        return output_[index];
    }
    REGISTER_CC_MODULE(CropResizeNorm2D, CcCropResizeNorm2D)
}