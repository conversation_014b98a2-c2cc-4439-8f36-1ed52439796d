#include "roi_generate.h"
#include <cmath> 
#include "CalmCarLog.h"
#include "cc_numarray_tool.h"

namespace tongxing {
int ROIGenerator::init(const Json::Value& config) {
    TX_LOG_DEBUG("roi_generator", "Initializing ROI generator");
    
    cc_assert(config["indexs"].isArray());
    for (int i = 0; i < config["indexs"].size(); i++)
    {
        indexs.push_back(config["indexs"][i].asInt());
    }
    if (config.isMember("upper_offset")) {
        upper_offset = config["upper_offset"].asInt();
    }
    if (config.isMember("lower_offset")) {
        lower_offset = config["lower_offset"].asInt();
    }

    cc_assert(upper_offset > 0 && lower_offset > 0);

    return 0;
}

int ROIGenerator::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    TX_LOG_DEBUG("roi_generator", "Setting %zu input arrays", in.size());
    return 0;
}

RotatedROI ROIGenerator::generateROI(int point1_x, int point1_y, int point2_x, int point2_y, int upper_offset, int lower_offset) {
    RotatedROI roi;
    
    // 计算两点间向量
    float dx = point2_x - point1_x;
    float dy = point2_y - point1_y;
    
    // 计算旋转角度(弧度转角度)
    roi.angle = atan2f(dy, dx) * 180.0f / M_PI;
    
    // 计算矩形中心点(两点中点)
    roi.x = static_cast<int>((point1_x + point2_x) / 2.0f);
    roi.y = static_cast<int>((point1_y + point2_y) / 2.0f);
    
    // 计算矩形宽度(两点间距离)
    roi.width = static_cast<int>(sqrtf(dx*dx + dy*dy));
    
    // 设置矩形高度(上下偏移量之和)
    roi.height = upper_offset + lower_offset;
    
    TX_LOG_DEBUG("roi_generator",
               "Generated ROI: center(%d,%d), size(%dx%d), angle(%.1f°) from "
               "points (%d,%d)-(%d,%d) with offsets %d/%d",
               roi.x, roi.y, roi.width, roi.height, roi.angle,
               point1_x, point1_y, point2_x, point2_y,
               upper_offset, lower_offset);
    
    return roi;
}

int ROIGenerator::execute() {
    cc_assert(!input_.empty());
    cc_assert(indexs.size() == 2);
    
    cc_assert(input_.size() == 1);
    std::vector<int> shape = input_[0]->shape;
    int batch = input_[0]->shape[0];

    std::vector<int> output_shape;
    output_shape.push_back(batch);
    output_shape.push_back(5); //x,y,w,h,angle

    std::shared_ptr<NumArray> output = creat_numarray(output_shape, NumArray::DataType::FLOAT32);

    for (int b = 0; b < batch; b++)
    {
        float *output_ptr = output->getTensor<float>()->operator[](b).data_;
        auto input_tensor = input_[0]->getTensor<float>()->operator[](b);
        int point1_x = (int)input_tensor[indexs[0]][1].get();
        int point1_y = (int)input_tensor[indexs[0]][2].get();
        int point2_x = (int)input_tensor[indexs[1]][1].get();
        int point2_y = (int)input_tensor[indexs[1]][2].get();
        // std::cout << "point1_x:" << point1_x << " point1_y:" << point1_y << " point2_x:" << point2_x << " point2_y:" << point2_y << std::endl;

        // int point1_x = (unsigned char *)input_tensor[indexs[0]].data_;
        RotatedROI roi = generateROI(point1_x, point1_y, point2_x, point2_y, upper_offset, lower_offset);
        output_ptr[0] = roi.x;
        output_ptr[1] = roi.y;
        output_ptr[2] = roi.width;
        output_ptr[3] = roi.height;
        output_ptr[4] = roi.angle;
    
    }
    std::vector<std::shared_ptr<NumArray>> outputs;
    outputs.push_back(output);
    output_ = outputs;

    return 0;
}

size_t ROIGenerator::getOutputNum() {
    return output_.size();
}

std::shared_ptr<NumArray> ROIGenerator::getOutput(int index) {
    if (index < 0 || index >= static_cast<int>(output_.size())) {
        TX_LOG_ERROR("roi_generator", "Invalid output index: %d", index);
        return nullptr;
    }
    return output_[index];
}

REGISTER_CC_MODULE(CcROIGenerator, ROIGenerator)

}  // namespace tongxing