#ifndef _BLOCK_DETECTOR_
#define _BLOCK_DETECTOR_

#include "opencv2/core/core.hpp"
#include "cc_module.h"
namespace tongxing
{
    class OcclusionDetector : public CcModule
    {
    public:
        int init(const Json::Value &config);
        int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
        OcclusionDetector();
        ~OcclusionDetector();

    private:
        std::vector<std::shared_ptr<NumArray>> input_;
        std::vector<std::shared_ptr<NumArray>> output_;
        bool flag_input_nchw=true;
        int sample_w;
        int table_r[256];
        int table_g[256];
        int table_b[256];
        void InitTable();
        void Edgextract(cv::Mat srcImg, cv::Mat &dstImg, int threshold = 40,
                        int Filter_size = 5);
        bool Detect(cv::Mat img);
    };

} // namespace cc_dms
#endif
