#include "lane_detector.h"
#include "opencv2/imgproc/imgproc.hpp"
#include <opencv2/imgcodecs.hpp>
#include "cc_numarray_tool.h"
#include <unistd.h>
#include "time_profile.h"
#ifdef __ANDROID__
#include <log_android.h>
#endif
namespace tongxing
{
    int LaneDetector::init(const Json::Value &config)
    {
        if (config.isMember("keep_ratio"))
        {
            keep_ratio = config["keep_ratio"].asBool();
        }
        if (config.isMember("flag_input_nchw"))
        {
            flag_input_nchw = config["flag_input_nchw"].asBool();
        }
        if (config.isMember("flag_image_shape_nchw"))
        {
            flag_image_shape_nchw = config["flag_image_shape_nchw"].asBool();
        }
        if (config.isMember("class_num"))
        {
            class_num_ = config["class_num"].asInt();
        }
        return 0;
    }
    int LaneDetector::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int LaneDetector::execute()
    {
#ifdef __ANDROID_DEBUG__
        TimeProfile LaneExecuteTime;
        LaneExecuteTime.Reset();
#elif __DEBUG__
        TimeProfile LaneExecuteTime;
        LaneExecuteTime.Reset();
#endif
        TX_LOG_DEBUG("module_grop", "LaneDetector::execute start");
        // cc_assert(input_.size() == 1);
        // input_[0]->getTensor<float>()->printShape();
        float scale_x = 1;
        float scale_y = 1;
        cc_assert(input_.size() == 2);
        int batch = input_[0]->shape[0];
        int ch = input_[0]->shape[1];
        int height = input_[0]->shape[2];
        int width = input_[0]->shape[3];
        if (!flag_input_nchw)
        {
            ch = input_[0]->shape[3];
            height = input_[0]->shape[1];
            width = input_[0]->shape[2];
        }
        // std::cout << "ch:" << ch << " height:" << height << " width:" << width << std::endl;
        int output_max_height;
        int output_max_width;
        if (flag_image_shape_nchw == false)
        {
            output_max_height = input_[1]->getTensor<int>()->operator[](1).get();
            output_max_width = input_[1]->getTensor<int>()->operator[](2).get();
        }
        else
        {
            output_max_height = input_[1]->getTensor<int>()->operator[](2).get();
            output_max_width = input_[1]->getTensor<int>()->operator[](3).get();
        }
        // std::cout << output_max_height << " " << output_max_width << std::endl;
        // std::cout << height << " " << width << std::endl;
        scale_y = (float)(output_max_height) / (float)height;
        scale_x = (float)(output_max_width) / (float)width;
        if (keep_ratio)
        {
            if (output_max_height > output_max_width)
            {
                scale_y = scale_x;
            }
            else
            {
                scale_x = scale_y;
            }
        }

        std::shared_ptr<NumArray> output = creat_numarray({batch, 2, max_output_num, 2}, NumArray::DataType::FLOAT32);
        std::shared_ptr<NumArray> output_num = creat_numarray({batch, 2}, NumArray::DataType::INT32);
        memset(output_num->data_blob_ptr->pu8VirAddr, 0, output_num->data_blob_ptr->u32Size);
        int *output_num_ptr_ = (int *)output_num->data_blob_ptr->pu8VirAddr;
        float *output_data_ptr_ = (float *)output->data_blob_ptr->pu8VirAddr;
        std::vector<std::vector<cv::Point>> lanes;
        lanes.clear();
        if (ch == 2 && class_num_ == 2)
        {
            cv::Mat img_ch2(height, width, CV_32FC1, input_[0]->data_blob_ptr->pu8VirAddr + (width * height * 4));
            cv::Mat img_ch1(height, width, CV_32FC1, input_[0]->data_blob_ptr->pu8VirAddr);
            cv::Mat img_tmp = img_ch2 > img_ch1;

            // cv::imwrite("./out/img_dst.jpg", img_tmp);
#ifdef __ANDROID_DEBUG__
            TimeProfile LaneDetectTime;
            LaneDetectTime.Reset();
#elif __DEBUG__
            TimeProfile LaneDetectTime;
            LaneDetectTime.Reset();
#endif
            lanes = Detect(img_tmp);
#ifdef __ANDROID_DEBUG__
            LaneDetectTime.Update("lane_detect");
            LOGD("lane_detect Time: %s", LaneDetectTime.GetTimeProfileString());
            LaneDetectTime.Reset();
#elif __DEBUG__
            LaneDetectTime.Update("lane_detect");
            std::cout << LaneDetectTime.GetTimeProfileString() << std::endl;
            LaneDetectTime.Reset();
#endif
        }
        else if (ch == 3)
        {
            cv::Mat img_ch1(height, width, CV_32FC1, input_[0]->data_blob_ptr->pu8VirAddr);
            cv::Mat img_ch2(height, width, CV_32FC1, input_[0]->data_blob_ptr->pu8VirAddr + (width * height * 4));
            cv::Mat img_ch3(height, width, CV_32FC1, input_[0]->data_blob_ptr->pu8VirAddr + (width * height * 4) * 2);

            cv::Mat binary_img = cv::Mat::zeros(img_ch2.size(), CV_8UC1);
             
            // Create temporary matrices to store the comparison results
            cv::Mat img_tmp1 = img_ch2 > img_ch1;
            cv::Mat img_tmp2 = img_ch2 > img_ch3;

            // Use bitwise AND to combine the results
            cv::Mat mask = img_tmp1 & img_tmp2;

            // Set the pixels in the binary image to 255 where the mask is true
            binary_img.setTo(255, mask);

            // save the binary image
            // cv::imwrite("./out/img_dst.jpg", binary_img);
#ifdef __ANDROID_DEBUG__
            TimeProfile LaneDetectTime;
            LaneDetectTime.Reset();
#elif __DEBUG__
            TimeProfile LaneDetectTime;
            LaneDetectTime.Reset();
#endif
            lanes = Detect(binary_img);
#ifdef __ANDROID_DEBUG__
            LaneDetectTime.Update("lane_detect");
            LOGD("lane_detect Time: %s", LaneDetectTime.GetTimeProfileString());
            LaneDetectTime.Reset();
#elif __DEBUG__
            LaneDetectTime.Update("lane_detect");
            std::cout << LaneDetectTime.GetTimeProfileString() << std::endl;
            LaneDetectTime.Reset();
#endif
        }
        if (lanes.empty())
        {
            // printf("\033[33m --ll-- file:lane_detector.cpp line:%d info:no lanes detected \033[0m \n ", __LINE__);
        }
        else
        {
            for (int i = 0; i < lanes.size(); i++)
            {
                int limit = (lanes[i].size() > max_output_num) ? max_output_num : lanes[i].size();
                for (int j = 0; j < limit; j++)
                {
                    // std::cout << " point:" << lanes[i][j].x << " " << lanes[i][j].y << std::endl;
                    output_data_ptr_[(max_output_num * i + output_num_ptr_[i]) * 2] = static_cast<float>(lanes[i][j].x * scale_x);
                    output_data_ptr_[(max_output_num * i + output_num_ptr_[i]) * 2 + 1] = static_cast<float>(lanes[i][j].y * scale_y);
                    output_num_ptr_[i]++;
                }
            }
        }
        output_ = {output, output_num};
#ifdef __ANDROID_DEBUG__
        LaneExecuteTime.Update("lane_execute");
        LOGD("lane_execute Time: %s", LaneExecuteTime.GetTimeProfileString());
        LaneExecuteTime.Reset();
#elif __DEBUG__
        LaneExecuteTime.Update("lane_execute");
        std::cout << LaneExecuteTime.GetTimeProfileString() << std::endl;
        LaneExecuteTime.Reset();
#endif
        return 0;
    }
    size_t LaneDetector::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> LaneDetector::getOutput(int index)
    {
        return output_[index];
    }

    LaneDetector::LaneDetector()
    {
    }

    LaneDetector::~LaneDetector() {}
    std::vector<std::vector<cv::Point>> LaneDetector::Detect(cv::Mat gray)
    {
        if (gray.empty())
        {
            std::cerr << "Error loading the image!" << std::endl;
        }
        return process_detect_lines(gray);
    }

    /**
     * @brief 获取直线的斜率和截距
     *
     * 根据给定的两个点 (x1, y1) 和 (x2, y2)，计算并返回直线的斜率和截距。
     *
     * @param x1 第一个点的 x 坐标
     * @param y1 第一个点的 y 坐标
     * @param x2 第二个点的 x 坐标
     * @param y2 第二个点的 y 坐标
     *
     * @return 返回一个包含斜率和截距的 std::pair 对象
     */
    std::pair<float, float> LaneDetector::get_slope_intercept(int x1, int y1, int x2, int y2)
    {

        float k = 0.0;
        float b = 0.0;

        if (x2 - x1 == 0)
            return std::make_pair(std::numeric_limits<double>::infinity(), x1); // 斜率无穷大表示垂直线
        else
        {
            k = static_cast<double>(y2 - y1) / static_cast<double>(x2 - x1);
            b = y1 - k * x1;
        }
        return std::make_pair(k, b);
    }

    std::vector<cv::Point> LaneDetector::unify_output_format(cv::Vec4i avg_line)
    {
        std::vector<cv::Point> lines(5);

        float k = static_cast<double>(avg_line[3] - avg_line[1]) / static_cast<double>(avg_line[2] - avg_line[0]);
        float b = avg_line[3] - k * avg_line[2];

        int x_min = (avg_line[0] < avg_line[2]) ? avg_line[0] : avg_line[2];
        int x_len = std::abs(avg_line[0] - avg_line[2]);

        for (int i = 0; i < 5; ++i)
        {
            lines[i].x = x_min + (x_len / 5) * i;
            lines[i].y = k * lines[i].x + b;
        }

        return lines;
    }
    // 计算中心点
    cv::Point2f computeCenter(const cv::Vec4i &line)
    {
        return cv::Point2f((line[0] + line[2]) / 2.0f, (line[1] + line[3]) / 2.0f);
    }

    // 选择离中心点最近的线
    cv::Vec4i selectClosestLine(const std::vector<cv::Vec4i> &lines, const cv::Point2f &targetCenter, float max_x_diff)
    {
        cv::Vec4i closestLine;
        float minDistance = std::numeric_limits<float>::max();
        for (const auto &line : lines)
        {
            cv::Point2f center = computeCenter(line);
            if (center.y < targetCenter.y)
                continue;
            float distance = abs(center.x - targetCenter.x); // 仅计算 x 方向的距离
            // if (distance > max_x_diff)
            //     continue;
            if (distance < minDistance)
            {
                minDistance = distance;
                closestLine = line;
            }
        }
        return closestLine;
    }


    // 计算轮廓的最小外接矩形角度并调整
    float calculateAdjustedAngle(const cv::RotatedRect& rect) {
        float angle = rect.angle;
        if (rect.size.width < rect.size.height) {
            angle += 90;
        }
        // std::cout << "angle: " << angle << std::endl;
        return angle;
    }

    // 将y坐标小于min_y的区域置为黑色
    void setAboveYToBlack(cv::Mat& img_binary, float min_y) {
        img_binary(cv::Rect(0, 0, img_binary.cols, static_cast<int>(min_y))) = cv::Scalar(0);
    }
    // 处理轮廓并计算车道线点
    void processContour(const std::vector<cv::Point>& contour, std::vector<cv::Point>& lanePoints) {
        cv::RotatedRect rect = cv::minAreaRect(contour);
        float angle = calculateAdjustedAngle(rect);

        if (angle == 180 || angle == 0 || angle == 90) {
            return; // 跳过特定角度
        }

        cv::Rect boundingRect = cv::boundingRect(contour);
        for (int y = boundingRect.y; y < boundingRect.y + boundingRect.height; ++y) {
            for (int x = boundingRect.x; x < boundingRect.x + boundingRect.width; ++x) {
                if (cv::pointPolygonTest(contour, cv::Point(x, y), false) >= 0) {
                    lanePoints.push_back(cv::Point(x, y));
                }
            }
        }
    }
    /**
     * @brief 处理检测到的车道线
     *
     * 对输入的车道线图像进行一系列处理，提取车道线的位置，并返回车道线的坐标点集合。
     *
     * @param gray 车道线图像
     * @param roi 感兴趣区域
     *
     * @return 车道线的坐标点集合
     */

    std::vector<std::vector<cv::Point>> LaneDetector::process_detect_lines(cv::Mat& gray) 
    {
        std::vector<std::vector<cv::Point>> vec_points;
        cv::Mat img_binary = gray.clone(); 
        // cv::imwrite("./out/img_binary.jpg", img_binary);

        //形态学开运算
        // cv::Mat element = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(5, 5));
        // cv::morphologyEx(img_binary, img_binary, cv::MORPH_OPEN, element);

        // std::string img_path = "./ldw0815_out/2";
        // std::cout << "file path :" << img_path << std::endl;
        // std::vector<std::string> src_name;
        // cv::glob(img_path, src_name, false);
        // size_t count = src_name.size(); // 文件夹中图片的数量

        // std::cout << "file path :" << src_name[index] << std::endl;
        // cv::Mat img = cv::imread(src_name[index]);
        // cv::resize(img, img, cv::Size(448, 256));
        // // 将二值化图像中非零像素渲染到原始图像上
        // for (int y = 0; y < img_binary.rows; y++) {
        //     for (int x = 0; x < img_binary.cols; x++) {
        //         if (img_binary.at<uchar>(y, x) > 0) {
        //             // BGR 顺序
        //             img.at<cv::Vec3b>(y, x) = cv::Vec3b(0, 0, 255);
        //         }
        //     }
        // }
         
#ifndef __ANDROID_JTD__
        // 将y坐标小于min_y的区域置为黑色
        // std::cout << "first min_y: " << min_y << std::endl;
        setAboveYToBlack(img_binary, img_binary.rows * 0.625);

        // 将二值化图像中非零像素渲染到原始图像上
        // for (int y = 0; y < img_binary.rows; y++) {
        //     for (int x = 0; x < img_binary.cols; x++) {
        //         if (img_binary.at<uchar>(y, x) > 0) {
        //             // BGR 顺序
        //             img.at<cv::Vec3b>(y, x) = cv::Vec3b(0, 255, 0);
        //         }
        //     }
        // }

        // 提取轮廓
        std::vector<std::vector<cv::Point>> contours;
        std::vector<cv::Vec4i> hierarchy;
        cv::findContours(img_binary, contours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
        if (contours.empty()) {
            return vec_points; // 如果没有找到轮廓，返回空的结果
        }
        // 定义存储左右车道线点的向量
        std::vector<cv::Point> leftLanePoints, rightLanePoints;

        // 处理每一个轮廓
        for (const auto& contour : contours) {
            cv::RotatedRect rect = cv::minAreaRect(contour);
            float area = rect.size.area();
            // std::cout << "area: " << area << std::endl;
            
            // 如果面积太小，跳过
            if (area < 80) {
                continue;
            }
            if (area > 5000) 
            {
                cv::Rect boundingRect = cv::boundingRect(contour);
                float min_y = boundingRect.y + img_binary.rows * 0.08;
                // std::cout << "min_y: " << min_y <<" limit_y: " << (min_y + img_binary.rows * 0.08) << std::endl;
                // 更新黑色区域，将y坐标小于新的min_y的区域置为黑色
                setAboveYToBlack(img_binary, min_y);

                // 再次寻找轮廓
                std::vector<std::vector<cv::Point>> newContours;
                cv::findContours(img_binary, newContours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

                // 处理新找到的轮廓
                for (const auto& newContour : newContours) {
                    processContour(newContour, (calculateAdjustedAngle(cv::minAreaRect(newContour)) > 90) ? leftLanePoints : rightLanePoints);
                }
            }
            else
            {
                processContour(contour, (calculateAdjustedAngle(rect) > 90) ? leftLanePoints : rightLanePoints);
            } 
        
        }

        // cv::imwrite("./out/img_contour.jpg", img_binary);
        // std::string save_path = "./out/model_out/" + std::to_string(index) + ".jpg";
        // std::cout<<"save_path: "<<save_path<<std::endl;
        // cv::imwrite(save_path, img);
        // index++;

        // 将左右车道线点集添加到结果中
        vec_points.push_back(leftLanePoints);  // 存储左车道线点集
        vec_points.push_back(rightLanePoints); // 存储右车道线点集

        return vec_points;
#else 
        std::vector<cv::Vec4i> lines;
        // 倒数第一个参数表示两段直线在同一个方向，要不要连成一条所需设置的阈值;
        // 倒数第二个参数表示检测直线的最小长度，如果检测的直线的长度小于这个设定的值就丢弃;
        // 倒数第三个参数表示检测的直线中点的最少个数，这个值应该可以设置大于minlength的；
        cv::HoughLinesP(img_binary, lines, 3, CV_PI / 180, 60, 20, 50);
        // std::cout<<lines.size()<<" lines detected"<<std::endl;
        // Convert gray image to BGR if necessary
        // cv::Mat output_image;
        // if (img_binary.channels() == 1) {
        //     cv::cvtColor(img_binary, output_image, cv::COLOR_GRAY2BGR);
        // } else {
        //     output_image = img_binary.clone();
        // }
        // for (const auto &line : lines)
        // {
        //     cv::line(output_image, cv::Point(line[0], line[1]), cv::Point(line[2], line[3]), cv::Scalar(0, 0, 255), 1);
        // }
        // // save detected lines from hough transform
        // cv::imwrite("./out/img_hough.jpg", output_image);
        if (lines.size() == 0)
        {
            return vec_points;
        }
        int width = img_binary.cols;
        int height = img_binary.rows;
        std::vector<cv::Vec4i> left_lines, right_lines, mid_lines;

        for (const auto &line : lines)
        {
            std::pair<float, float> kinfo = get_slope_intercept(line[0], line[1], line[2], line[3]);
            // std::cout<<"line k:"<<kinfo.first<<std::endl;
            if (0.0 < kinfo.first && kinfo.first < 2.0)
            {
                right_lines.push_back(line);
                // cv::line(output_image, cv::Point(line[0], line[1]), cv::Point(line[2], line[3]), cv::Scalar(0, 0, 255), 1);
            }


            if (-2.0 < kinfo.first && kinfo.first < -0.0)
            {
                left_lines.push_back(line);
                // cv::line(output_image, cv::Point(line[0], line[1]), cv::Point(line[2], line[3]), cv::Scalar(0, 255, 0), 1);
            }
        }
        // cv::imwrite("./out/img_hough.jpg", output_image);
        // 选择每组中离中心点最近的线
        cv::Point2f targetCenter(width / 2.0f, height / 2.0f); // 中心点位于图像 x 轴的中点
        float max_x_diff = width / 4;
        cv::Vec4i avg_left_line = selectClosestLine(left_lines, targetCenter, max_x_diff);
        cv::Vec4i avg_right_line = selectClosestLine(right_lines, targetCenter, max_x_diff);
        std::vector<cv::Point> points;
        points.clear();
        points = unify_output_format(avg_left_line);
        vec_points.push_back(points);

        points.clear();
        points = unify_output_format(avg_right_line);
        vec_points.push_back(points);
        return vec_points;
#endif
    }

    REGISTER_CC_MODULE(lane_detector, LaneDetector)
} // namespace tongxing
