#ifndef _CC_MODULE_REGISTER_H_
#define _CC_MODULE_REGISTER_H_
#include <string>
#include <map>
#include "cc_assert.h"
#include <iostream>
namespace tongxing{
    template <class T>
    class ModuleRegister{
        public:
            static ModuleRegister<T>& instance(){
                static ModuleRegister<T> register_;
                return register_;
            }
            ModuleRegister<T>& register_function(const std::string& key,T fun){
            
                fun_map[key]=fun;
                return instance();
            
            }
            T get_function(const std::string& key){
                auto iter=fun_map.find(key);
                if(iter==fun_map.end()){
                    std::cout<<key<<" module load fail"<<std::endl;
                    cc_assert(iter!=fun_map.end());
                }
                return iter->second;
            }
            bool find_function(const std::string& key,T& value){
                auto iter=fun_map.find(key);
                if(iter==fun_map.end()){
                    return false;
                }
                value=iter->second;
                return true;
            }

            void del_function(const std::string& key){
                auto iter=fun_map.find(key);
                if(iter==fun_map.end()) 
                {
                    return ;
                }
                fun_map.erase(iter);
            }
        private:
            std::map<std::string,T> fun_map;
    };




}

#endif //_CC_MODULE_REGISTER_H_