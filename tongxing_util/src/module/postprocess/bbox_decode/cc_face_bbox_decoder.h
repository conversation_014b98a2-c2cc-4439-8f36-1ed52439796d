#ifndef _FACE_BBOX_DECODER_H_
#define _FACE_BBOX_DECODER_H_
#include <opencv2/imgproc.hpp>
#include <vector>
#include "cc_math_tool.h"
#include "cc_module.h"
#include "json.h"

namespace tongxing
{
    class CcFaceBboxDecoder : public CcModule
    {
    public:
        int init(const Json::Value &config);
        int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);

    private:
        int feat_w_ = 16;
        int feat_h_ = 16;
        int stride_=8;
        float offset_;
        float scale_;
        float confThreshold_ = 0.5; // Confidence threshold
        float nmsThreshold_ = 0.1; // Non-maximum suppression threshold

        std::vector<std::shared_ptr<NumArray>> input_;
        std::vector<std::shared_ptr<NumArray>> output_;

        Json::Value root_;
    };
}

#endif