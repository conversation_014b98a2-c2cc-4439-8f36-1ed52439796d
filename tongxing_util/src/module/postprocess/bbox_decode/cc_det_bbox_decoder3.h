/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2023-11-28 17:44:59
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-06-03 14:52:13
 * @FilePath: /adas_sdk/calmcar_util/src/module/postprocess/bbox_decode/cc_det_bbox_decoder.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef _DET_BBOX_DECODER_H_
#define _DET_BBOX_DECODER_H_
#include <opencv2/imgproc.hpp>
#include <vector>
#include "cc_math_tool.h"
#include "cc_module.h"
#include "json.h"

namespace tongxing {
class CcDetBboxDecoder3 : public CcModule {
  public:
    int init(const Json::Value& config);
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:
    std::vector<std::shared_ptr<NumArray>> decode(const std::vector<std::shared_ptr<NumArray>>& bbox_tensor);
    int max_output_num = 20;

  private:
    // Initialize the parameters
    int scalesNum_ = 3;
    // Confidence threshold
    float confThreshold_ = 0.45;
    // Non-maximum suppression threshold
    float nmsThreshold_ = 0.6;
    int imageWidth_ = 288;
    int imageHeight_ = 288;
    int classNum_ = 2;
    bool flag_nchw_ = false;

    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;

    Json::Value root_;
};
}  // namespace tongxing

#endif