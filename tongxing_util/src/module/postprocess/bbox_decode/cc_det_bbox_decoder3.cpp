
#include "cc_det_bbox_decoder3.h"
#include "cc_numarray_tool.h"
#include "time_profile.h"
#ifdef __ANDROID__
#include <log_android.h>
#endif
namespace tongxing {
int CcDetBboxDecoder3::init(const Json::Value& config) {
    root_ = config;
    cc_assert(root_["score_th"].isDouble());
    cc_assert(root_["iou_th"].isDouble());
    cc_assert(root_["image_width"].isInt());
    cc_assert(root_["image_height"].isInt());
    confThreshold_ = root_["score_th"].asDouble();
    nmsThreshold_ = root_["iou_th"].asDouble();
    imageWidth_ = root_["image_width"].asInt();
    imageHeight_ = root_["image_height"].asInt();
    classNum_ = root_["class_num"].asInt();
    scalesNum_ = root_["scales_num"].asInt();
    flag_nchw_ = root_["flag_nchw"].asBool();
    return 0;
}
std::vector<std::shared_ptr<NumArray>> CcDetBboxDecoder3::decode(
    const std::vector<std::shared_ptr<NumArray>>& bbox_tensor) {
    // printf("bbox_tensor.size():%d\n", bbox_tensor.size());
    // for (int a = 0; a < bbox_tensor.size(); a++) {
    //     printf("shape:%d,%d,%d,%d\n", bbox_tensor[a]->shape[0], bbox_tensor[a]->shape[1],
    //            bbox_tensor[a]->shape[2], bbox_tensor[a]->shape[3]);
    // }
    // cc_assert(bbox_tensor.size() == 2);
    cc_assert(scalesNum_ > 0);
    int max_ch = 0;
    int batch = bbox_tensor[0]->shape[0];
    int value_num = 6;
    std::shared_ptr<NumArray> res =
        creat_numarray({batch, max_output_num, value_num}, NumArray::DataType::FLOAT32);
    std::shared_ptr<NumArray> res_num = creat_numarray({batch}, NumArray::DataType::INT32);
    std::shared_ptr<CcTensor<float>> tensor_ptr = res->getTensor<float>();
    int* tensor_num_ptr = (int*)res_num->data;
    std::vector<std::vector<CcObjBBox>> outBBoxs_vec;
    float data;
    if (flag_nchw_) {
        for (int b = 0; b < batch; b++) {
            std::vector<CcObjBBox> objBBoxs;
            std::vector<CcObjBBox> nmsObjBBoxs;
            for (int a = 0; a < scalesNum_; a++) {
                int w = bbox_tensor[a]->shape[3];
                int h = bbox_tensor[a]->shape[2];
                const int ch = bbox_tensor[a]->shape[1];

                int feature_strip = w * h;  // 每个特征跨度
                // std::cout << __FILE__ << ":" << __LINE__ << " w:" << w << " h:" << h << " ch:" << ch << " a:" << a << std::endl;

                // float ratio_w = imageWidth_ / w;
                // float ratio_h = imageHeight_ / h;
                float sampling_rate = imageWidth_ / w;

                auto bboxs_tensor_data_ptr =
                    (float*)bbox_tensor[a]->getTensor<float>()->operator[](b).data_;

                std::vector<float> max_vec;
                std::vector<int> x_vec;
                std::vector<int> y_vec;
                std::vector<std::vector<float>> bbox_vec;
                for (int j = 0; j < feature_strip; j++) {
                    int c_index = -1;
                    float max_c = -99999;
                    for (int a = 0; a < classNum_; a++) {
                        float data = bboxs_tensor_data_ptr[a * feature_strip + j];
                        if (data >= max_c) {
                            c_index = a;
                            max_c = data;
                        }
                    }
                    // printf("max:%f\n", max_c);
                    // max_vec.push_back(max_c);
                    int x = j / w;
                    int y = j % w;
                    if (max_c <= 0.45)
                        continue;
                    x_vec.push_back(x);
                    y_vec.push_back(y);
                    std::vector<float> bbox;
                    bbox.push_back(
                        bboxs_tensor_data_ptr[(x * w + y) + (classNum_ + 0) * feature_strip]);
                    bbox.push_back(
                        bboxs_tensor_data_ptr[(x * w + y) + (classNum_ + 1) * feature_strip]);
                    bbox.push_back(
                        bboxs_tensor_data_ptr[(x * w + y) + (classNum_ + 2) * feature_strip]);
                    bbox.push_back(
                        bboxs_tensor_data_ptr[(x * w + y) + (classNum_ + 3) * feature_strip]);
                    bbox.push_back(max_c);
                    bbox.push_back(c_index);
                    bbox_vec.push_back(bbox);
                }
                // printf("max size:%d\n", max_vec.size());
                // printf("x_vec size:%d,y_vec:%d\n", x_vec.size(), y_vec.size());

                // 取bbox
                for (int k = 0; k < bbox_vec.size(); k++) {
                    float x0 = bbox_vec[k][0];
                    float y0 = bbox_vec[k][1];
                    float x1 = bbox_vec[k][2];
                    float y1 = bbox_vec[k][3];
                    float score = bbox_vec[k][4];
                    int label = bbox_vec[k][5];
                    // printf("x0:%d,y0:%d,x1:%d,y1:%d\n", x0, y0, x1, y1);
                    // 384*192的模型解码
                    // x0 = y_vec[k] * sampling_rate - x0;
                    // y0 = x_vec[k] * sampling_rate - y0;
                    // x1 = y_vec[k] * sampling_rate + x1;
                    // y1 = x_vec[k] * sampling_rate + y1;
                    // 320*192的模型解码
                    x0 = (y_vec[k] - x0) * sampling_rate;
                    y0 = (x_vec[k] - y0) * sampling_rate;
                    x1 = (y_vec[k] + x1) * sampling_rate;
                    y1 = (x_vec[k] + y1) * sampling_rate;

                    float ww = x1 - x0;
                    float hh = y1 - y0;

                    CcObjBBox objBBox;

                    objBBox.bbox.x = x0;
                    objBBox.bbox.y = y0;
                    objBBox.bbox.width = ww;
                    objBBox.bbox.height = hh;
                    objBBox.score = score;
                    objBBox.label = label;

                    nmsObjBBoxs.push_back(objBBox);

                    // printf("x0:%d,y0:%d,x1:%d,y1:%d,w:%d,h:%d\n", x0, y0, x1, y1, ww, hh);
                }
            }
            std::vector<int> out_nms = nms(nmsObjBBoxs, nmsThreshold_, classNum_);
            // std::cout << "objBBoxs size:" << out_nms.size() << std::endl;
            int j = 0;
            for (int i : out_nms) {
                float* output_data = tensor_ptr->operator[](b)[j].data_;
                output_data[0] = nmsObjBBoxs[i].score;
                output_data[1] = (float)nmsObjBBoxs[i].label;
                output_data[2] = (float)nmsObjBBoxs[i].bbox.x;
                output_data[3] = (float)nmsObjBBoxs[i].bbox.y;
                output_data[4] = (float)nmsObjBBoxs[i].bbox.width;
                output_data[5] = (float)nmsObjBBoxs[i].bbox.height;
                j++;
                if (j >= max_output_num) {
                    break;
                }
            }

            tensor_num_ptr[b] = j;
            if (j > max_ch) {
                max_ch = j;
            }
        }
    } else {
        for (int b = 0; b < batch; b++) {
            std::vector<CcObjBBox> objBBoxs;
            std::vector<CcObjBBox> nmsObjBBoxs;
            for (int a = 0; a < scalesNum_; a++) {
                int w = bbox_tensor[a]->shape[2];
                int h = bbox_tensor[a]->shape[1];
                const int ch = bbox_tensor[a]->shape[3];

                int feature_strip = w * h;  // 每个特征跨度
                // std::cout << __FILE__ << ":" << __LINE__ << " w:" << w << " h:" << h << " ch:" << ch << " a:" << a << std::endl;

                // float ratio_w = imageWidth_ / w;
                // float ratio_h = imageHeight_ / h;
                float sampling_rate = imageWidth_ / w;

                auto bboxs_tensor_data_ptr =
                    (float*)bbox_tensor[a]->getTensor<float>()->operator[](b).data_;

                std::vector<float> max_vec;
                std::vector<int> x_vec;
                std::vector<int> y_vec;
                std::vector<std::vector<float>> bbox_vec;
                for (int j = 0; j < feature_strip; j++) {
                    int x = j / w;
                    int y = j % w;
                    int c_index = -1;
                    float max_c = -99999;
                    for (int a = 0; a < classNum_; a++) {
                        float data = bboxs_tensor_data_ptr[a + x * w * ch + y * ch];
                        if (data >= max_c) {
                            c_index = a;
                            max_c = data;
                        }
                    }
                    max_c = sigmoid(max_c);
                    // printf("max:%f\n", max_c);
                    // max_vec.push_back(max_c);
                    if (max_c <= 0.45)
                        continue;
                    // printf("max:%f\n", max_c);
                    x_vec.push_back(x);
                    y_vec.push_back(y);
                    std::vector<float> bbox;
                    bbox.push_back(bboxs_tensor_data_ptr[(classNum_ + 0) + x * w * ch + y * ch]);
                    bbox.push_back(bboxs_tensor_data_ptr[(classNum_ + 1) + x * w * ch + y * ch]);
                    bbox.push_back(bboxs_tensor_data_ptr[(classNum_ + 2) + x * w * ch + y * ch]);
                    bbox.push_back(bboxs_tensor_data_ptr[(classNum_ + 3) + x * w * ch + y * ch]);
                    bbox.push_back(max_c);
                    bbox.push_back(c_index);
                    bbox_vec.push_back(bbox);
                }
                // printf("max size:%d\n", max_vec.size());
                // printf("x_vec size:%d,y_vec:%d\n", x_vec.size(), y_vec.size());

                // 取bbox
                for (int k = 0; k < bbox_vec.size(); k++) {
                    float x0 = bbox_vec[k][0];
                    float y0 = bbox_vec[k][1];
                    float x1 = bbox_vec[k][2];
                    float y1 = bbox_vec[k][3];
                    float score = bbox_vec[k][4];
                    int label = bbox_vec[k][5];
                    // printf("x0:%d,y0:%d,x1:%d,y1:%d\n", x0, y0, x1, y1);

                    x0 = (y_vec[k] - x0) * sampling_rate;
                    y0 = (x_vec[k] - y0) * sampling_rate;
                    x1 = (y_vec[k] + x1) * sampling_rate;
                    y1 = (x_vec[k] + y1) * sampling_rate;

                    float ww = x1 - x0;
                    float hh = y1 - y0;

                    CcObjBBox objBBox;

                    objBBox.bbox.x = x0;
                    objBBox.bbox.y = y0;
                    objBBox.bbox.width = ww;
                    objBBox.bbox.height = hh;
                    objBBox.score = score;
                    objBBox.label = label;

                    nmsObjBBoxs.push_back(objBBox);

                    // printf("x0:%d,y0:%d,x1:%d,y1:%d,w:%d,h:%d\n", x0, y0, x1, y1, ww, hh);
                }
            }
            std::vector<int> out_nms = nms(nmsObjBBoxs, nmsThreshold_, classNum_);
            // std::cout << "objBBoxs size:" << out_nms.size() << std::endl;
            int j = 0;
            for (int i : out_nms) {
                float* output_data = tensor_ptr->operator[](b)[j].data_;
                output_data[0] = nmsObjBBoxs[i].score;
                output_data[1] = (float)nmsObjBBoxs[i].label;
                output_data[2] = (float)nmsObjBBoxs[i].bbox.x;
                output_data[3] = (float)nmsObjBBoxs[i].bbox.y;
                output_data[4] = (float)nmsObjBBoxs[i].bbox.width;
                output_data[5] = (float)nmsObjBBoxs[i].bbox.height;
                j++;
                if (j >= max_output_num) {
                    break;
                }
            }

            tensor_num_ptr[b] = j;
            if (j > max_ch) {
                max_ch = j;
            }
        }
    }
    std::vector<std::shared_ptr<NumArray>> res_vec;
    res->shape[1] = max_ch;
    res_vec.push_back(res);
    res_vec.push_back(res_num);
    // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
    return res_vec;
}
int CcDetBboxDecoder3::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}
int CcDetBboxDecoder3::execute() {
#ifdef __ANDROID_DEBUG__
    TimeProfile odPostProcessTime;
    odPostProcessTime.Reset();
#elif __DEBUG__
    TimeProfile odPostProcessTime;
    odPostProcessTime.Reset();
#endif
    output_ = decode(input_);
#ifdef __ANDROID_DEBUG__
    odPostProcessTime.Update("od_postprocess");
    LOGD("od_postprocess Time: %s", odPostProcessTime.GetTimeProfileString());
    odPostProcessTime.Reset();
#elif __DEBUG__
    odPostProcessTime.Update("od_postprocess");
    std::cout << odPostProcessTime.GetTimeProfileString() << std::endl;
    odPostProcessTime.Reset();
#endif
    return 0;
}
size_t CcDetBboxDecoder3::getOutputNum() {
    return output_.size();
}
std::shared_ptr<NumArray> CcDetBboxDecoder3::getOutput(int index) {
    return output_[index];
}

REGISTER_CC_MODULE(det_bbox_decode3, CcDetBboxDecoder3)
}  // namespace tongxing