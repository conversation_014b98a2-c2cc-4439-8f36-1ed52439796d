
#include "cc_yolo_bbox_decoder.h"
#include <fstream>
#include <iostream>
#include <opencv2/imgproc.hpp>
#include <sstream>
#include "cc_tensor.h"
#include "cc_numarray_tool.h"
#include "CalmCarLog.h"
#include "time_profile.h"
#ifdef __ANDROID__
#include <log_android.h>
#endif
// #include <pair>
using namespace cv;
using namespace std;

namespace tongxing
{

    void CcYoloFastDecode::init(int feat_w,
                                int feat_h,
                                int classNum,
                                int featmapStride,
                                const std::vector<int> &anchor,
                                int minBboxSize,
                                float confThreshold,
                                const std::vector<int> &chw_order,
                                int align,
                                bool has_cube, float vedgeThreshold)
    {
        score_indexs.clear();
        cx_indexs.clear();
        cy_indexs.clear();
        w_indexs.clear();
        h_indexs.clear();
        classify_indexs.clear();
        feat_w_ = feat_w;
        feat_h_ = feat_h;
        classNum_ = classNum;
        minBboxSize_ = minBboxSize;
        anchor_ = anchor;
        featmapStride_ = featmapStride;
        confThreshold_ = inverse_sigmoid(confThreshold);
        vedgeThreshold_ = inverse_sigmoid(vedgeThreshold);
        int step = 5 + classNum_;
        int cube_step = 12;
        step_ = feat_w * feat_h;
        // std::cout<<step_<<std::endl;
        std::vector<int> tensor_dim;
        std::vector<int> tensor_align;
        tensor_dim.resize(3);
        tensor_align.resize(3);
        tensor_align[0] = 1;
        tensor_align[1] = 1;
        tensor_align[2] = align;
        tensor_dim[chw_order[0]] = (step * 3) % tensor_align[2] > 0 ? ((step * 3) + (tensor_align[2] - (step * 3) % tensor_align[2])) : (step * 3);
        tensor_dim[chw_order[1]] = feat_h;
        tensor_dim[chw_order[2]] = feat_w;
        has_cube_ = has_cube;
        tensorIndex.init(tensor_dim, tensor_align, false);

        for (int a = 0; a < 3; a++)
        {
            int bbox_x_index = a * step;
            int bbox_y_index = a * step + 1;
            int bbox_w_index = a * step + 2;
            int bbox_h_index = a * step + 3;
            int score_index = a * step + 4;
            int score_class_index = a * step + 5;
            for (int w = 0; w < feat_w; w++)
            {
                for (int h = 0; h < feat_h; h++)
                {
                    tensor_dim[chw_order[2]] = w;
                    tensor_dim[chw_order[1]] = h;
                    tensor_dim[chw_order[0]] = score_index;
                    score_indexs.push_back(tensorIndex.get_index(tensor_dim));
                    tensor_dim[chw_order[0]] = bbox_x_index;
                    cx_indexs.push_back(tensorIndex.get_index(tensor_dim));
                    tensor_dim[chw_order[0]] = bbox_y_index;
                    cy_indexs.push_back(tensorIndex.get_index(tensor_dim));
                    tensor_dim[chw_order[0]] = bbox_w_index;
                    w_indexs.push_back(tensorIndex.get_index(tensor_dim));
                    tensor_dim[chw_order[0]] = bbox_h_index;
                    h_indexs.push_back(tensorIndex.get_index(tensor_dim));
                    tensor_dim[chw_order[0]] = score_class_index;
                    classify_indexs.push_back(tensorIndex.get_index(tensor_dim));
                }
            }
        }
        if (has_cube_)
        {
            std::vector<int> cube_tensor_dim;
            cube_tensor_dim.resize(3);
            cube_tensor_dim[chw_order[0]] = (cube_step * 3) % tensor_align[2] > 0 ? ((cube_step * 3) + (tensor_align[2] - (cube_step * 3) % tensor_align[2])) : (cube_step * 3);
            cube_tensor_dim[chw_order[1]] = feat_h;
            cube_tensor_dim[chw_order[2]] = feat_w;
            cube_tensorIndex.init(cube_tensor_dim, tensor_align, false);
            std::vector<int> cube_index;
            cube_index.resize(3);
            for (int a = 0; a < 3; a++)
            {
                int cube_lf_s_index = a * cube_step;
                int cube_lb_s_index = a * cube_step + 1;
                int cube_rf_s_index = a * cube_step + 2;
                int cube_rb_s_index = a * cube_step + 3;

                int cube_lf_x_index = a * cube_step + 4;
                int cube_lf_y_index = a * cube_step + 5;

                int cube_lb_x_index = a * cube_step + 6;
                int cube_lb_y_index = a * cube_step + 7;

                int cube_rf_x_index = a * cube_step + 8;
                int cube_rf_y_index = a * cube_step + 9;

                int cube_rb_x_index = a * cube_step + 10;
                int cube_rb_y_index = a * cube_step + 11;
                for (int w = 0; w < feat_w; w++)
                {
                    for (int h = 0; h < feat_h; h++)
                    {
                        cube_index[chw_order[2]] = w;
                        cube_index[chw_order[1]] = h;
                        cube_index[chw_order[0]] = cube_lb_s_index;
                        cube_lb_s_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                        cube_index[chw_order[0]] = cube_lb_x_index;
                        cube_lb_x_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                        cube_index[chw_order[0]] = cube_lb_y_index;
                        cube_lb_y_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                        cube_index[chw_order[0]] = cube_rb_s_index;
                        cube_rb_s_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                        cube_index[chw_order[0]] = cube_rb_x_index;
                        cube_rb_x_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                        cube_index[chw_order[0]] = cube_rb_y_index;
                        cube_rb_y_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                        cube_index[chw_order[0]] = cube_lf_s_index;
                        cube_lf_s_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                        cube_index[chw_order[0]] = cube_lf_x_index;
                        cube_lf_x_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                        cube_index[chw_order[0]] = cube_lf_y_index;
                        cube_lf_y_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                        cube_index[chw_order[0]] = cube_rf_s_index;
                        cube_rf_s_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                        cube_index[chw_order[0]] = cube_rf_x_index;
                        cube_rf_x_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                        cube_index[chw_order[0]] = cube_rf_y_index;
                        cube_rf_y_indexs.push_back(cube_tensorIndex.get_index(cube_index));
                    }
                }
            }
        }
    }

    void CcYoloFastDecode::decode(float *data, std::vector<CcObjBBox> &output)
    {
        int featSize = score_indexs.size();
        for (int i = 0; i < featSize; i++)
        {
            float score = data[score_indexs[i]];
            if (score < confThreshold_)
            {
                continue;
            }
            // std::cout<<score<<std::endl;
            int a2 = i / step_;

            CcObjBBox objBBox;
            objBBox.bbox.width = exp(data[w_indexs[i]]) * anchor_[a2 * 2];
            // std::cout<<objBBox.bbox.width<<std::endl;
            if (objBBox.bbox.width < minBboxSize_)
            {
                continue;
            }
            objBBox.bbox.height = exp(data[h_indexs[i]]) * anchor_[a2 * 2 + 1];
            if (objBBox.bbox.height < minBboxSize_)
            {
                continue;
            }

            objBBox.score = sigmoid(score);

            int a1 = i % step_;
            int w = a1 % feat_w_;
            int h = a1 / feat_w_;
            // printf("%d %d %d\n",a1,w,h);
            objBBox.bbox.x =
                (sigmoid(data[cx_indexs[i]]) + w) * featmapStride_ - objBBox.bbox.width * 0.5;
            objBBox.bbox.y =
                (sigmoid(data[cy_indexs[i]]) + h) * featmapStride_ - objBBox.bbox.height * 0.5;

            float *classify_ptr = &data[classify_indexs[i]];
            float max_class_score = -9999;
            int max_class_score_i = -1;
            for (int c = 0; c < classNum_; c++)
            {
                float score_class_f = classify_ptr[c];
                if (score_class_f > max_class_score)
                {
                    max_class_score = score_class_f;
                    max_class_score_i = c;
                }
            }
            objBBox.label = max_class_score_i;

            output.push_back(objBBox);
        }
    }
    void CcYoloFastDecode::decode_cube(float *data, float *cube, std::vector<ObjCubeBBox> &output)
    {
        int featSize = score_indexs.size();
        int inpWidth = feat_w_ * featmapStride_;
        int inpHeight = feat_h_ * featmapStride_;
        for (int i = 0; i < featSize; i++)
        {
            float score = data[score_indexs[i]];
            if (score < confThreshold_)
            {
                continue;
            }
            // std::cout<<score<<std::endl;
            int a2 = i / step_;

            ObjCubeBBox objBBox;
            memset(&objBBox, 0, sizeof(ObjCubeBBox));
            objBBox.bbox.width = exp(data[w_indexs[i]]) * anchor_[a2 * 2];
            // std::cout<<objBBox.bbox.width<<std::endl;
            if (objBBox.bbox.width < minBboxSize_)
            {
                continue;
            }
            objBBox.bbox.height = exp(data[h_indexs[i]]) * anchor_[a2 * 2 + 1];
            if (objBBox.bbox.height < minBboxSize_)
            {
                continue;
            }

            objBBox.score = sigmoid(score);

            int a1 = i % step_;
            int w = a1 % feat_w_;
            int h = a1 / feat_w_;
            // printf("%d %d %d\n",a1,w,h);
            objBBox.bbox.x =
                (sigmoid(data[cx_indexs[i]]) + w) * featmapStride_ - objBBox.bbox.width * 0.5;
            objBBox.bbox.y =
                (sigmoid(data[cy_indexs[i]]) + h) * featmapStride_ - objBBox.bbox.height * 0.5;

            float *classify_ptr = &data[classify_indexs[i]];
            float max_class_score = -9999;
            int max_class_score_i = -1;
            for (int c = 0; c < classNum_; c++)
            {
                float score_class_f = classify_ptr[c];
                if (score_class_f > max_class_score)
                {
                    max_class_score = score_class_f;
                    max_class_score_i = c;
                }
            }
            objBBox.label = max_class_score_i;
            float ctr_x = (w + 0.5) * featmapStride_;
            float ctr_y = (h + 0.5) * featmapStride_;

            if (cube[cube_lf_s_indexs[i]] >= vedgeThreshold_)
            {
                objBBox.orientation += 1 << (1);
                float pred_x = cube[cube_lf_x_indexs[i]] * 0.1 * anchor_[a2 * 2] + ctr_x;
                float pred_y = cube[cube_lf_y_indexs[i]] * 0.1 * anchor_[a2 * 2 + 1] + ctr_y;
                objBBox.left_front_edge.x = std::max(std::min(pred_x, inpWidth - 1.f), 0.f);
                objBBox.left_front_edge.y = std::max(std::min(pred_y, inpHeight - 1.f), 0.f);
            }
            if (cube[cube_lb_s_indexs[i]] >= vedgeThreshold_)
            {
                objBBox.orientation += 1 << (2);
                float pred_x = cube[cube_lb_x_indexs[i]] * 0.1 * anchor_[a2 * 2] + ctr_x;
                float pred_y = cube[cube_lb_y_indexs[i]] * 0.1 * anchor_[a2 * 2 + 1] + ctr_y;
                objBBox.left_behind_edge.x = std::max(std::min(pred_x, inpWidth - 1.f), 0.f);
                objBBox.left_behind_edge.y = std::max(std::min(pred_y, inpHeight - 1.f), 0.f);
            }
            if (cube[cube_rf_s_indexs[i]] >= vedgeThreshold_)
            {
                objBBox.orientation += 1 << (3);
                float pred_x = cube[cube_rf_x_indexs[i]] * 0.1 * anchor_[a2 * 2] + ctr_x;
                float pred_y = cube[cube_rf_y_indexs[i]] * 0.1 * anchor_[a2 * 2 + 1] + ctr_y;
                objBBox.right_front_edge.x = std::max(std::min(pred_x, inpWidth - 1.f), 0.f);
                objBBox.right_front_edge.y = std::max(std::min(pred_y, inpHeight - 1.f), 0.f);
            }
            if (cube[cube_rb_s_indexs[i]] >= vedgeThreshold_)
            {
                objBBox.orientation += 1 << (4);
                float pred_x = cube[cube_rb_x_indexs[i]] * 0.1 * anchor_[a2 * 2] + ctr_x;
                float pred_y = cube[cube_rb_y_indexs[i]] * 0.1 * anchor_[a2 * 2 + 1] + ctr_y;
                objBBox.right_behind_edge.x = std::max(std::min(pred_x, inpWidth - 1.f), 0.f);
                objBBox.right_behind_edge.y = std::max(std::min(pred_y, inpHeight - 1.f), 0.f);
            }
            output.push_back(objBBox);
        }
    }
    CcYoloBboxDecoder::~CcYoloBboxDecoder() {}

    CcYoloBboxDecoder::CcYoloBboxDecoder(int imageWidth,
                                         int imageHeight,
                                         int classNum,
                                         const std::vector<int> &featmapStrides,
                                         const std::vector<std::vector<int>> &anchor,
                                         int minBboxSize,
                                         float confThreshold,
                                         float nmsThreshold,
                                         const std::vector<int> &chw_order,
                                         int align,
                                         int scalesNum, bool has_cube, float vedgeThreshold)
    {
        init(imageWidth, imageHeight, classNum, featmapStrides, anchor, minBboxSize, confThreshold,
             nmsThreshold, chw_order, align, scalesNum, has_cube, vedgeThreshold);
    }
    int CcYoloBboxDecoder::init(int imageWidth,
                                int imageHeight,
                                int classNum,
                                const std::vector<int> &featmapStrides,
                                const std::vector<std::vector<int>> &anchor,
                                int minBboxSize,
                                float confThreshold,
                                float nmsThreshold,
                                const std::vector<int> &chw_order,
                                int align,
                                int scalesNum,
                                bool has_cube, float vedgeThreshold)
    {
        confThreshold_ = inverse_sigmoid(confThreshold); // Confidence threshold
        vedgeThreshold_ = inverse_sigmoid(vedgeThreshold_);
        has_cube_ = has_cube;
        nmsThreshold_ = nmsThreshold; // Non-maximum suppression threshold
        classNum_ = classNum;
        scalesNum_ = scalesNum;
        imageWidth_ = imageWidth;
        imageHeight_ = imageHeight;
        align_ = align;
        minBboxSize_ = minBboxSize;
        featmapStrides_ = featmapStrides;
        chw_order_ = chw_order;
        anchor_.resize(scalesNum_);
        for (int i = 0; i < scalesNum_; i++)
        {
            cc_assert(anchor[i].size() == 6);
            anchor_[i].resize(6);
            for (int j = 0; j < 6; j++)
            {
                anchor_[i][j] = anchor[i][j];
            }
        }

        bbox_dim_.resize(scalesNum_);
        for (int i = 0; i < scalesNum_; i++)
        {
            bbox_dim_[i].resize(3);
            bbox_dim_[i][chw_order_[2]] = imageWidth_ / featmapStrides_[i];
            bbox_dim_[i][chw_order_[1]] = imageHeight_ / featmapStrides_[i];
            bbox_dim_[i][chw_order_[0]] = (5 + classNum_) * 3;
        }
        decoder.resize(scalesNum_);
        for (int i = 0; i < scalesNum_; i++)
        {
            decoder[i].init(bbox_dim_[i][chw_order_[2]], bbox_dim_[i][chw_order_[1]], classNum_,
                            featmapStrides[i], anchor[i], minBboxSize, confThreshold, chw_order, align, has_cube, vedgeThreshold);
        }
        return 0;
        // std::cout<<bbox_dim_[2][0]<<" "<<bbox_dim_[2][1]<<" "<<bbox_dim_[2][2]<<std::endl;
    }
    CcYoloBboxDecoder::CcYoloBboxDecoder() {}
    int CcYoloBboxDecoder::init(const Json::Value &config)
    {
        root_ = config;

        cc_assert(root_["score_th"].isDouble());
        cc_assert(root_["class_num"].isInt());
        cc_assert(root_["scales_num"].isInt());
        cc_assert(root_["image_width"].isInt());
        cc_assert(root_["image_height"].isInt());
        cc_assert(root_["iou_th"].isDouble());
        cc_assert(root_["featmap_strides"].isArray());
        cc_assert(root_["anchor"].isArray());
        cc_assert(root_["chw_dim"].isArray());
        cc_assert(root_["align"].isInt());
        cc_assert(root_["min_bbox_size"].isInt());
        cc_assert(root_["chw_dim"].size() == 3);

        float confThreshold = root_["score_th"].asDouble();
        float nmsThreshold = root_["iou_th"].asDouble();
        ;
        int classNum = root_["class_num"].asInt();
        int scalesNum = root_["scales_num"].asInt();
        int imageWidth = root_["image_width"].asInt();
        int imageHeight = root_["image_height"].asInt();
        int align = root_["align"].asInt();
        int minBboxSize = root_["min_bbox_size"].asInt();
        std::vector<int> featmapStrides;
        std::vector<std::vector<int>> anchors;
        std::vector<int> chw_order;

        for (int i = 0; i < root_["featmap_strides"].size(); i++)
        {
            cc_assert(root_["featmap_strides"][i].isInt());
            featmapStrides.push_back(root_["featmap_strides"][i].asInt());
        }

        for (int i = 0; i < root_["anchor"].size(); i++)
        {
            cc_assert(root_["anchor"][i].isArray());
            cc_assert(root_["anchor"][i].size() == 6);
            std::vector<int> anchor;
            for (int j = 0; j < 6; j++)
            {
                cc_assert(root_["anchor"][i][j].isInt());
                anchor.push_back(root_["anchor"][i][j].asInt());
            }

            anchors.push_back(anchor);
        }

        for (int i = 0; i < root_["chw_dim"].size(); i++)
        {
            cc_assert(root_["chw_dim"][i].isInt());
            chw_order.push_back(root_["chw_dim"][i].asInt());
        }
        if (root_.isMember("has_cube"))
        {
            has_cube_ = root_["has_cube"].asBool();
        }
        if (root_.isMember("vedgeThreshold"))
        {
            vedgeThreshold_ = root_["vedgeThreshold"].asBool();
        }
        return init(imageWidth, imageHeight, classNum, featmapStrides, anchors, minBboxSize,
                    confThreshold, nmsThreshold, chw_order, align, scalesNum, has_cube_, vedgeThreshold_);
    }

    std::vector<std::vector<CcObjBBox>> CcYoloBboxDecoder::decode(
        std::vector<CcTensor<float>> &bbox_tensers)
    {
        cc_assert(bbox_tensers.size() == scalesNum_);
        cc_assert(scalesNum_ > 0);
        int batch_num = bbox_tensers[0].shape()[0];
        for (int i = 1; i < scalesNum_; i++)
        {
            cc_assert(bbox_tensers[i].shape()[0] == batch_num);
        }
        std::vector<std::vector<CcObjBBox>> outBBoxs_vec;

        for (int b = 0; b < batch_num; b++)
        {
            std::vector<CcObjBBox> objBBoxs;
            for (int i = 0; i < scalesNum_; i++)
            {

                decoder[i].decode(bbox_tensers[i][b].data_, objBBoxs);
                TX_LOG_DEBUG("CcYoloBboxDecoder", "decode end");
            }
            std::vector<int> out_nms = nms(objBBoxs, nmsThreshold_, classNum_);
            std::vector<CcObjBBox> outBBoxs;
            for (int i : out_nms)
            {
                outBBoxs.push_back(objBBoxs[i]);
            }
            // std::cout << outBBoxs.size() << std::endl;
            outBBoxs_vec.push_back(outBBoxs);
        }
        return outBBoxs_vec;
    }

    std::vector<std::shared_ptr<NumArray>> CcYoloBboxDecoder::decode(const std::vector<std::shared_ptr<NumArray>> &bbox_tensor)
    {
        if (has_cube_)
        {
            cc_assert(bbox_tensor.size() == scalesNum_ * 2);
        }
        else
        {
            cc_assert(bbox_tensor.size() == scalesNum_);
        }
        cc_assert(scalesNum_ > 0);
        int max_ch = 0;
        int batch = bbox_tensor[0]->shape[0];
        int value_num = 6;
        if (has_cube_)
        {
            value_num = 15;
        }
        std::shared_ptr<NumArray> res = creat_numarray({batch, max_output_num, value_num}, NumArray::DataType::FLOAT32);
        std::shared_ptr<NumArray> res_num = creat_numarray({batch}, NumArray::DataType::INT32);
        std::shared_ptr<CcTensor<float>> tensor_ptr = res->getTensor<float>();
        int *tensor_num_ptr = (int *)res_num->data;

        std::vector<std::vector<CcObjBBox>> outBBoxs_vec;
        for (int b = 0; b < batch; b++)
        {
            std::vector<CcObjBBox> objBBoxs;
            std::vector<ObjCubeBBox> objCubeBBoxs;
            for (int i = 0; i < scalesNum_; i++)
            {
                if (has_cube_)
                {
                    // bbox_tensor[i]->getTensor<float>()->printShape();
                    // bbox_tensor[i + 3]->getTensor<float>()->printShape();
                    // TX_LOG_DEBUG("CcYoloBboxDecoder", "decode start");
                    decoder[i].decode_cube((float *)bbox_tensor[i]->getTensor<float>()->operator[](b).data_, (float *)bbox_tensor[i + 3]->getTensor<float>()->operator[](b).data_, objCubeBBoxs);
                    // TX_LOG_DEBUG("CcYoloBboxDecoder", "decode end");
                }
                else
                {
                    decoder[i].decode((float *)bbox_tensor[i]->getTensor<float>()->operator[](b).data_, objBBoxs);
                }
            }
            if (has_cube_)
            {
                std::vector<int> out_nms = nms(objCubeBBoxs, nmsThreshold_, classNum_);
                // std::vector<CcObjBBox> outBBoxs;
                int j = 0;

                for (int i : out_nms)
                {
                    float *output_data = tensor_ptr->operator[](b)[j].data_;
                    // outBBoxs.push_back(objBBoxs[i]);
                    output_data[0] = objCubeBBoxs[i].score;
                    output_data[1] = (float)objCubeBBoxs[i].label;
                    output_data[2] = (float)objCubeBBoxs[i].bbox.x;
                    output_data[3] = (float)objCubeBBoxs[i].bbox.y;
                    output_data[4] = (float)objCubeBBoxs[i].bbox.width;
                    output_data[5] = (float)objCubeBBoxs[i].bbox.height;
                    output_data[6] = objCubeBBoxs[i].orientation;
                    output_data[7] = (float)objCubeBBoxs[i].left_front_edge.x;
                    output_data[8] = (float)objCubeBBoxs[i].left_front_edge.y;
                    output_data[9] = (float)objCubeBBoxs[i].left_behind_edge.x;
                    output_data[10] = (float)objCubeBBoxs[i].left_behind_edge.y;
                    output_data[11] = (float)objCubeBBoxs[i].right_front_edge.x;
                    output_data[12] = (float)objCubeBBoxs[i].right_front_edge.y;
                    output_data[13] = (float)objCubeBBoxs[i].right_behind_edge.x;
                    output_data[14] = (float)objCubeBBoxs[i].right_behind_edge.y;
                    j++;
                    if (j >= max_output_num)
                    {
                        break;
                    }
                }
                // std::cout<<outBBoxs.size()<<std::endl;
                tensor_num_ptr[b] = j;
                if (j > max_ch)
                {
                    max_ch = j;
                }
            }
            else
            {
                std::vector<int> out_nms = nms(objBBoxs, nmsThreshold_, classNum_);
                // std::vector<CcObjBBox> outBBoxs;
                int j = 0;

                for (int i : out_nms)
                {
                    float *output_data = tensor_ptr->operator[](b)[j].data_;
                    // outBBoxs.push_back(objBBoxs[i]);
                    output_data[0] = objBBoxs[i].score;
                    output_data[1] = (float)objBBoxs[i].label;
                    output_data[2] = (float)objBBoxs[i].bbox.x;
                    output_data[3] = (float)objBBoxs[i].bbox.y;
                    output_data[4] = (float)objBBoxs[i].bbox.width;
                    output_data[5] = (float)objBBoxs[i].bbox.height;
                    j++;
                    if (j >= max_output_num)
                    {
                        break;
                    }
                }
                // std::cout<<outBBoxs.size()<<std::endl;
                tensor_num_ptr[b] = j;
                if (j > max_ch)
                {
                    max_ch = j;
                }
            }
            // outBBoxs_vec.push_back(outBBoxs);
        }

        std::vector<std::shared_ptr<NumArray>> res_vec;

        // for (int b = 0; b < outBBoxs_vec.size(); b++)
        // {
        //     auto &outBBoxs=outBBoxs_vec[b];
        //     for (int c = 0; c < outBBoxs.size(); c++)
        //     {
        //         //  std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        //         tensor_ptr->operator[](b)[c][0].get() = outBBoxs[c].score;
        //         tensor_ptr->operator[](b)[c][1].get() = (float)outBBoxs[c].label;
        //         tensor_ptr->operator[](b)[c][2].get() = (float)outBBoxs[c].bbox.x;
        //         tensor_ptr->operator[](b)[c][3].get() = (float)outBBoxs[c].bbox.y;
        //         tensor_ptr->operator[](b)[c][4].get() = (float)outBBoxs[c].bbox.width;
        //         tensor_ptr->operator[](b)[c][5].get() = (float)outBBoxs[c].bbox.height;
        //         //  std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        //     }
        // }
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        // res->getTensor<float>()->printShape();
        res->shape[1] = max_ch;
        res_vec.push_back(res);
        res_vec.push_back(res_num);
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        return res_vec;
    }

    int CcYoloBboxDecoder::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        module_in_ = in;
        return 0;
    }
    int CcYoloBboxDecoder::execute()
    {
#ifdef __ANDROID_DEBUG__
        TimeProfile odPostProcessTime;
        odPostProcessTime.Reset();
#elif __DEBUG__
        TimeProfile odPostProcessTime;
        odPostProcessTime.Reset();
#endif
        module_out_ = decode(module_in_);
#ifdef __ANDROID_DEBUG__
        odPostProcessTime.Update("od_postprocess");
        LOGD("od_postprocess Time: %s", odPostProcessTime.GetTimeProfileString());
        odPostProcessTime.Reset();
#elif __DEBUG__
        odPostProcessTime.Update("od_postprocess");
        std::cout << odPostProcessTime.GetTimeProfileString() << std::endl;
        odPostProcessTime.Reset();
#endif
        return 0;
    }
    size_t CcYoloBboxDecoder::getOutputNum()
    {
        return module_out_.size();
    }
    std::shared_ptr<NumArray> CcYoloBboxDecoder::getOutput(int index)
    {
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        return module_out_[index];
    }

    REGISTER_CC_MODULE(yolo_bbox_decode, CcYoloBboxDecoder)
}
