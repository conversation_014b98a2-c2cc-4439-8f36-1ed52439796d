#ifndef _PICODET_BBOX_DECODER_H_
#define _PICODET_BBOX_DECODER_H_
#include <opencv2/imgproc.hpp>
#include <vector>
#include "cc_math_tool.h"
#include "cc_module.h"
#include "json.h"

namespace tongxing
{
    class CcPicoDetBboxDecoder : public CcModule
    {
    public:
        int init(const Json::Value &config);
        int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);

    private:
        std::vector<int> featmapStrides_;
        std::vector<int> chw_order_;
        std::vector<std::vector<std::vector<int>>> bbox_dim_;
        std::vector<std::vector<int>> cls_index_;
        int classNum_ = 1;
        int scalesNum_ = 3;
        int imageWidth_ = 256;
        int imageHeight_ = 256;
        int minBboxSize_ = 4;
        float confThreshold_ = 0.5; // Confidence threshold
        float nmsThreshold_ = 0.4; // Non-maximum suppression threshold

        std::vector<std::shared_ptr<NumArray>> input_;
        std::vector<std::shared_ptr<NumArray>> output_;

        Json::Value root_;
    };
}

#endif