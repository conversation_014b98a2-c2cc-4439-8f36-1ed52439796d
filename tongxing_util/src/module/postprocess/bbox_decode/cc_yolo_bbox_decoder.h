#ifndef _YOLO_BBOX_DECODER_H_
#define _YOLO_BBOX_DECODER_H_
#include <opencv2/imgproc.hpp>
#include <vector>
#include "cc_math_tool.h"
#include "cc_module.h"
#include "json.h"

namespace tongxing
{
  class CcYoloFastDecode
  {
  public:
    // CcYoloFastDecoder();
    void init(int feat_w,
              int feat_h,
              int classNum,
              int featmapStride,
              const std::vector<int> &anchor,
              int minBboxSize,
              float confThreshold,
              const std::vector<int> &chw_order,
              int align,
              bool has_cube = false, float vedgeThreshold = 0.7);
    void decode(float *data, std::vector<CcObjBBox> &output);
    void decode_cube(float *data, float *cube, std::vector<ObjCubeBBox> &output);

  private:
    std::vector<int> score_indexs;
    std::vector<int> cx_indexs;
    std::vector<int> cy_indexs;
    std::vector<int> w_indexs;
    std::vector<int> h_indexs;
    std::vector<int> classify_indexs;

    std::vector<int> cube_lb_s_indexs;
    std::vector<int> cube_lb_x_indexs;
    std::vector<int> cube_lb_y_indexs;

    std::vector<int> cube_rb_s_indexs;
    std::vector<int> cube_rb_x_indexs;
    std::vector<int> cube_rb_y_indexs;

    std::vector<int> cube_lf_s_indexs;
    std::vector<int> cube_lf_x_indexs;
    std::vector<int> cube_lf_y_indexs;

    std::vector<int> cube_rf_s_indexs;
    std::vector<int> cube_rf_x_indexs;
    std::vector<int> cube_rf_y_indexs;
    bool has_cube_;

    int feat_w_;
    int feat_h_;
    int classNum_;
    float confThreshold_;
    float vedgeThreshold_;
    int minBboxSize_;
    int step_;
    int featmapStride_;
    std::vector<int> anchor_;
    CcTensorIndex tensorIndex;
    CcTensorIndex cube_tensorIndex;
  };
  class CcYoloBboxDecoder : public CcModule
  {
  public:
    CcYoloBboxDecoder(int imageWidth,
                      int imageHeight,
                      int classNum,
                      const std::vector<int> &featmapStrides,
                      const std::vector<std::vector<int>> &anchor,
                      int minBboxSize,
                      float confThreshold,
                      float nmsThreshold,
                      const std::vector<int> &chw_order,
                      int align,
                      int scalesNum,
                      bool has_cube = false, float vedgeThreshold = 0.7);
    CcYoloBboxDecoder();
    ~CcYoloBboxDecoder();
    int init(const Json::Value &config);
    std::vector<std::vector<CcObjBBox>> decode(std::vector<CcTensor<float>> &bbox_tensor);

  private:
    int init(int imageWidth,
             int imageHeight,
             int classNum,
             const std::vector<int> &featmapStrides,
             const std::vector<std::vector<int>> &anchor,
             int minBboxSize,
             float confThreshold,
             float nmsThreshold,
             const std::vector<int> &chw_order,
             int align,
             int scalesNum,
             bool has_cube = false, float vedgeThreshold = 0.7);
    // Initialize the parameters
    float confThreshold_ = 0.5; // Confidence threshold
    float vedgeThreshold_ = 0.7;
    float nmsThreshold_ = 0.4; // Non-maximum suppression threshold
    int scalesNum_ = 3;
    int imageWidth_ = 256;
    int imageHeight_ = 256;
    int minBboxSize_ = 4;
    bool has_cube_ = false;
    std::vector<std::vector<int>> bbox_dim_;
    std::vector<int> chw_order_;
    int align_ = 1;
    std::vector<int> featmapStrides_;
    std::vector<std::vector<int>> anchor_;
    int classNum_ = 1;
    Json::Value root_;
    std::vector<CcYoloFastDecode> decoder;

  private:
    void creatIndexMap();

  public:
    int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:
    std::vector<std::shared_ptr<NumArray>> decode(const std::vector<std::shared_ptr<NumArray>> &bbox_tensor);
    std::vector<std::shared_ptr<NumArray>> module_in_;
    std::vector<std::shared_ptr<NumArray>> module_out_;
    int max_output_num = 20;
  };
} // namespace tongxing

#endif
