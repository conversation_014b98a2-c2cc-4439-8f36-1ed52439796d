
#include "cc_od_bbox_decoder.h"
#include "cc_numarray_tool.h"
#include "time_function.h"
#include "time_profile.h"
#ifdef __ANDROID__
#include <log_android.h>
#endif
namespace tongxing {
static float proj_conv_softmax(const float *src, float *dst, int length) {
  const float alpha = *std::max_element(src, src + length);
  float denominator = 0;
  float dis_sum = 0;
  for (int i = 0; i < length; ++i) {
    dst[i] = exp(src[i] - alpha);
    denominator += dst[i];
  }
  for (int i = 0; i < length; ++i) {
    dst[i] /= denominator;
    dis_sum += i * dst[i];
  }
  return dis_sum;
}
// static inline bool cmp_score(const StObject& a, const StObject& b) {
//     return a.score > b.score;
// }

// static float cal_iou(const StObject& a, const StObject& b) {
//     cv::Rect r1 = cv::Rect(cv::Point(a.xmin, a.ymin), cv::Point(a.xmax, a.ymax));
//     cv::Rect r2 = cv::Rect(cv::Point(b.xmin, b.ymin), cv::Point(b.xmax, b.ymax));
//     cv::Rect inter = r1 & r2;
//     if (inter.area() <= 0)
//         return 0.;
//     float iou_value = 1. * inter.area() / (r1.area() + r2.area() - inter.area());
//     return iou_value;
// }
// static int nms_sort_bboxes(std::vector<StObject>& objects,
//                            std::vector<int>& keptIndices,
//                            float nms_thresh) {
//     keptIndices.clear();
//     std::sort(objects.begin(), objects.end(), cmp_score);
//     for (int i = 0; i < objects.size(); i++) {
//         int keep = 1;
//         for (int j = 0; j < keptIndices.size(); j++) {
//             float iou_value = cal_iou(objects[i], objects[keptIndices[j]]);
//             if (iou_value >= nms_thresh) {
//                 keep = 0;
//             }
//         }
//         if (keep) {
//             keptIndices.push_back(i);
//         }
//     }
//     return 0;
// }

int CcOdBboxDecoder::init(const Json::Value &config) {
  root_ = config;
  cc_assert(root_["score_th"].isDouble());
  cc_assert(root_["iou_th"].isDouble());
  cc_assert(root_["image_width"].isInt());
  cc_assert(root_["image_height"].isInt());
  confThreshold_ = root_["score_th"].asDouble();
  nmsThreshold_ = root_["iou_th"].asDouble();
  imageWidth_ = root_["image_width"].asInt();
  imageHeight_ = root_["image_height"].asInt();
  classNum_ = root_["class_num"].asInt();
  scalesNum_ = root_["scales_num"].asInt();
  flag_nchw_ = root_["flag_nchw"].asBool();
  std::vector<int> featmapStrides;
  for (int i = 0; i < root_["featmap_strides"].size(); i++) {
    cc_assert(root_["featmap_strides"][i].isInt());
    featmapStrides.push_back(root_["featmap_strides"][i].asInt());
  }
  return 0;
}
std::vector<std::shared_ptr<NumArray>> CcOdBboxDecoder::decode(
    const std::vector<std::shared_ptr<NumArray>> &bbox_tensor) {
  cc_assert(bbox_tensor.size() == scalesNum_);
  cc_assert(scalesNum_ > 0);
  int max_ch = 0;
  int batch = bbox_tensor[0]->shape[0];
  int value_num = 6;
  std::shared_ptr<NumArray> res = creat_numarray(
      {batch, max_output_num, value_num}, NumArray::DataType::FLOAT32);
  std::shared_ptr<NumArray> res_num =
      creat_numarray({batch}, NumArray::DataType::INT32);
  std::shared_ptr<CcTensor<float>> tensor_ptr = res->getTensor<float>();
  int *tensor_num_ptr = (int *)res_num->data;
  std::vector<std::vector<CcObjBBox>> outBBoxs_vec;
  float data;
  if (flag_nchw_) {
    for (int b = 0; b < batch; b++) {
      std::vector<std::vector<float>> final_kps;

      std::vector<int> sampling_rate = {8, 16, 32};

      std::vector<CcObjBBox> objBBoxs;
      std::vector<CcObjBBox> nmsObjBBoxs;
      for (int i = 0; i < scalesNum_; i++) // 3个输出tensor
      {
        int w = bbox_tensor[i]->shape[3];
        int h = bbox_tensor[i]->shape[2];
        const int ch = bbox_tensor[i]->shape[1];

        int feature_strip = w * h; // 每个特征跨度
        std::cout << __FILE__ << ":" << __LINE__ << " w:" << w << " h:" << h
        << " ch:" << ch <<std::endl;

        auto bboxs_tensor_data_ptr =
            (float *)bbox_tensor[i]->getTensor<float>()->operator[](b).data_;
        auto score_feat = bboxs_tensor_data_ptr + feature_strip * 64;
        int feat_w = w;
        int feat_h = h;

        int reg_max = 16;
        int reg_groups = 4;
        int reg_proj_conv_weight_num = reg_max * reg_groups;
        float dis_after_sm[16];

        std::vector<float> proj_conv_weights;

        std::vector<float> max_vec;
        std::vector<int> x_vec;
        std::vector<int> y_vec;
        std::vector<std::vector<float>> bbox_vec;

        for (int h = 0; h < feat_h; h++) {
          for (int w = 0; w < feat_w; w++) {
            int max_index = -1;
            float max_score = 0.0f;
            for (int c = 0; c < 5; c++) {
              float pred_score = sigmoid(
                  bboxs_tensor_data_ptr[feat_h * feat_w *
                                            (reg_proj_conv_weight_num + c) +
                                        h * feat_w + w]);
              if (pred_score > max_score) {
                max_score = pred_score;
                max_index = c;
              }
            }
            if (max_score > 0.3) {
              proj_conv_weights.clear();
              for (int g = 0; g < reg_proj_conv_weight_num; g++) {
                proj_conv_weights.emplace_back(
                    bboxs_tensor_data_ptr[feat_h * feat_w * g + h * feat_w +
                                          w]);
              }
              float *proj_conv_ptr = proj_conv_weights.data();
              float x1 =
                  static_cast<float>(w) + 0.5f -
                  proj_conv_softmax(proj_conv_ptr, dis_after_sm, reg_max);
              float y1 = static_cast<float>(h) + 0.5f -
                         proj_conv_softmax(proj_conv_ptr + reg_max,
                                           dis_after_sm, reg_max);
              float x2 = static_cast<float>(w) + 0.5f +
                         proj_conv_softmax(proj_conv_ptr + 2 * reg_max,
                                           dis_after_sm, reg_max);
              float y2 = static_cast<float>(h) + 0.5f +
                         proj_conv_softmax(proj_conv_ptr + 3 * reg_max,
                                           dis_after_sm, reg_max);
              x1 *= static_cast<float>(sampling_rate[i]);
              y1 *= static_cast<float>(sampling_rate[i]);
              x2 *= static_cast<float>(sampling_rate[i]);
              y2 *= static_cast<float>(sampling_rate[i]);
              CcObjBBox objBBox;
              objBBox.bbox.x = x1;
              objBBox.bbox.y = y1;
              objBBox.bbox.width = (x2 - x1);
              objBBox.bbox.height = (y2 - y1);
              objBBox.score = max_score;
              objBBox.label = max_index;

              nmsObjBBoxs.push_back(objBBox);
            }
          }
        }
      }
    //   std::vector<int> keptIndices;
    // nms_sort_bboxes(objectInfos, keptIndices, 0.6);

      std::vector<int> out_nms = nms(nmsObjBBoxs, nmsThreshold_, classNum_);
      std::cout << "objBBoxs size:" << out_nms.size() << std::endl;
      int j = 0;
      for (int i : out_nms) {
        float *output_data = tensor_ptr->operator[](b)[j].data_;
        output_data[0] = nmsObjBBoxs[i].score;
        output_data[1] = (float)nmsObjBBoxs[i].label;
        output_data[2] = (float)nmsObjBBoxs[i].bbox.x;
        output_data[3] = (float)nmsObjBBoxs[i].bbox.y;
        output_data[4] = (float)nmsObjBBoxs[i].bbox.width;
        output_data[5] = (float)nmsObjBBoxs[i].bbox.height;
        j++;
        if (j >= max_output_num) {
          break;
        }
      }

      tensor_num_ptr[b] = j;
      if (j > max_ch) {
        max_ch = j;
      }
    }
  }
  std::vector<std::shared_ptr<NumArray>> res_vec;
  res->shape[1] = max_ch;
  res_vec.push_back(res);
  res_vec.push_back(res_num);
  // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
  return res_vec;
}
int CcOdBboxDecoder::setInput(
    const std::vector<std::shared_ptr<NumArray>> &in) {
  input_ = in;
  return 0;
}
int CcOdBboxDecoder::execute() {
  TimeFunction time_f("ccOdBboxDecoder");
#ifdef __ANDROID_DEBUG__
  TimeProfile OdPostProcess;
  OdPostProcess.Reset();
#elif __DEBUG__
  TimeProfile OdPostProcess;
  OdPostProcess.Reset();
#endif

  output_ = decode(input_);

#ifdef __ANDROID_DEBUG__
  OdPostProcess.Update("od_postprocess");
  LOGD("od_postprocess Time: %s", OdPostProcess.GetTimeProfileString());
  OdPostProcess.Reset();
#elif __DEBUG__
  OdPostProcess.Update("od_postprocess");
  std::cout << OdPostProcess.GetTimeProfileString() << std::endl;
  OdPostProcess.Reset();
#endif
  return 0;
}
size_t CcOdBboxDecoder::getOutputNum() { return output_.size(); }
std::shared_ptr<NumArray> CcOdBboxDecoder::getOutput(int index) {
  return output_[index];
}

REGISTER_CC_MODULE(od_bbox_decode, CcOdBboxDecoder)
} // namespace tongxing