#include "cc_picodet_bbox_decoder.h"
namespace tongxing
{
    int CcPicoDetBboxDecoder::init(const Json::Value &config)
    {
        root_ = config;
        cc_assert(root_["score_th"].isDouble());
        cc_assert(root_["class_num"].isInt());
        cc_assert(root_["scales_num"].isInt());
        cc_assert(root_["image_width"].isInt());
        cc_assert(root_["image_height"].isInt());
        cc_assert(root_["iou_th"].isDouble());
        cc_assert(root_["featmap_strides"].isArray());
        cc_assert(root_["chw_dim"].isArray());
        cc_assert(root_["min_bbox_size"].isInt());
        cc_assert(root_["chw_dim"].size() == 3);
        classNum_ = root_["class_num"].asInt();
        scalesNum_ = root_["scales_num"].asInt();
        imageWidth_ = root_["image_width"].asInt();
        imageHeight_ = root_["image_height"].asInt();
        confThreshold_ = root_["score_th"].asDouble();
        nmsThreshold_ = root_["iou_th"].asDouble();
        minBboxSize_ = root_["min_bbox_size"].asInt();
        for (int i = 0; i < root_["featmap_strides"].size(); i++)
        {
            cc_assert(root_["featmap_strides"][i].isInt());
            featmapStrides_.push_back(root_["featmap_strides"][i].asInt());
        }
        
        for (int i = 0; i < root_["chw_dim"].size(); i++)
        {
            cc_assert(root_["chw_dim"][i].isInt());
            chw_order_.push_back(root_["chw_dim"][i].asInt());
        }
        return 0;
    }
    int CcPicoDetBboxDecoder::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_=in;
        return 0;
    }
    int CcPicoDetBboxDecoder::execute()
    {
        return 0;
    }
    size_t CcPicoDetBboxDecoder::getOutputNum()
    {
        return 0;
    }
    std::shared_ptr<NumArray> CcPicoDetBboxDecoder::getOutput(int index)
    {
        return 0;
    }

    REGISTER_CC_MODULE(PicoDetBboxDecoder, CcPicoDetBboxDecoder)
}