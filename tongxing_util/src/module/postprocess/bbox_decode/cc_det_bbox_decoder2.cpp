
#include "cc_det_bbox_decoder2.h"
#include "cc_numarray_tool.h"
#include "time_profile.h"

#ifdef __ANDROID__
#include <log_android.h>
#endif
namespace tongxing
{
    int CcDetBboxDecoder2::init(const Json::Value &config)
    {
        root_ = config;
        cc_assert(root_["score_th"].isDouble());
        cc_assert(root_["iou_th"].isDouble());
        cc_assert(root_["image_width"].isInt());
        cc_assert(root_["image_height"].isInt());
        cc_assert(root_["featmap_strides"].isArray());
        confThreshold_ = root_["score_th"].asDouble();
        nmsThreshold_ = root_["iou_th"].asDouble();
        imageWidth_ = root_["image_width"].asInt();
        imageHeight_ = root_["image_height"].asInt();
        classNum_ = root_["class_num"].asInt();
        scalesNum_ = root_["scales_num"].asInt();
        for (int i = 0; i < root_["featmap_strides"].size(); i++)
        {
            cc_assert(root_["featmap_strides"][i].isInt());
            featmapStrides_.push_back(root_["featmap_strides"][i].asInt());
        }
        return 0;
    }

    int CcDetBboxDecoder2::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcDetBboxDecoder2::execute()
    {
        // cc_assert(input_.size() == 1);
        // std::cout<<input_[0]->shape.size()<<std::endl;
        // cc_assert(input_[0]->shape.size() == 3 || input_[0]->shape.size() == 4);
        // std::cout<<"-------------"<<input_[0]->shape[3]<<std::endl;
        // size_t last_shape = 0;
        // if (input_[0]->shape.size() == 3)
        // {
        //     cc_assert(input_[0]->shape[1] == feat_w_ * feat_h_);
        //     last_shape = input_[0]->shape[2];
        // }
        // else if (input_[0]->shape.size() == 4)
        // {
        //     // cc_assert(input_[0]->shape[1] == feat_h_&&input_[0]->shape[2] == feat_w_);
        //     last_shape = input_[0]->shape[3];
        // }

        int batch = input_[0]->shape[0];
        int max_ch = 0;
        std::shared_ptr<NumArray> res = creat_numarray({batch, max_output_num, 6}, NumArray::DataType::FLOAT32);
        memset(res->data, 0, batch * max_output_num * 6 * sizeof(float));
        std::shared_ptr<NumArray> res_num = creat_numarray({batch}, NumArray::DataType::INT32);
        std::shared_ptr<CcTensor<float>> tensor_ptr = res->getTensor<float>();
        int *tensor_num_ptr = (int *)res_num->data;

        for (int b = 0; b < batch; b++)
        {
            std::vector<CcObjBBox> objBBoxs;

            for (int j = 0; j < scalesNum_; j++)
            {
                float *bboxs_tensor_data_ptr = (float *)input_[j]->data;
                int feat_w_ = input_[j]->shape[2];
                int feat_h_ = input_[j]->shape[1];
                int f_size = feat_w_ * feat_h_;
                int ch = input_[j]->shape[3];
                bboxs_tensor_data_ptr += b * f_size * ch;
                // std::cout<<featmapStrides_[j]<<" "<<feat_w_<<" "<<feat_h_<<" "<<ch<<std::endl;
                for (int i = 0; i < f_size; i++)
                {
                    float *bbox_data_ptr = bboxs_tensor_data_ptr + i * ch;

                    float max_scorc = -9;
                    int max_index = -1;
                    for (int k = 0; k < classNum_; k++)
                    {
                        if (bbox_data_ptr[k] > max_scorc)
                        {
                            max_scorc = bbox_data_ptr[k];
                            max_index = k;
                        }
                    }
                    // std::cout<<bbox_data_ptr[0]<<" "<<bbox_data_ptr[1]<<std::endl;
                    if (max_scorc >= confThreshold_)
                    {
                        // std::cout<<max_scorc<<" "<<ch<<std::endl;
                        int w = i % feat_w_;
                        int h = i / feat_w_;
                        float xmin = ((w + 0.5) - (bbox_data_ptr[classNum_ + 0])) * featmapStrides_[j];
                        float ymin = ((h + 0.5) - (bbox_data_ptr[classNum_ + 1])) * featmapStrides_[j];
                        float xmax = ((w + 0.5) + (bbox_data_ptr[classNum_ + 2])) * featmapStrides_[j];
                        float ymax = ((h + 0.5) + (bbox_data_ptr[classNum_ + 3])) * featmapStrides_[j];
                        CcObjBBox bbox;
                        bbox.label = max_index;
                        bbox.score = sigmoid(max_scorc);
                        bbox.bbox.x = xmin;
                        bbox.bbox.y = ymin;
                        bbox.bbox.width = xmax - xmin;
                        bbox.bbox.height = ymax - ymin;
                        // std::cout<<"bbox:"<<bbox.score<<" "<<bbox.bbox.x<<" "<<bbox.bbox.y<<" "<<bbox.bbox.width<<" "<<bbox.bbox.height<<std::endl;
                        if (bbox.bbox.width >= 3 && bbox.bbox.height >= 3)
                        {
                            objBBoxs.push_back(bbox);
                        }
                    }
                }
            }
            // std::cout << objBBoxs.size() << std::endl;
            std::vector<int> out_nms = nms(objBBoxs, nmsThreshold_, classNum_);
            int j = 0;
            for (int i : out_nms)
            {
                if (j >= max_output_num)
                {
                    break;
                }
                float *output_data = tensor_ptr->operator[](b)[j].data_;
                output_data[0] = objBBoxs[i].score;
                output_data[1] = (float)objBBoxs[i].label;
                output_data[2] = (float)objBBoxs[i].bbox.x;
                output_data[3] = (float)objBBoxs[i].bbox.y;
                output_data[4] = (float)objBBoxs[i].bbox.width;
                output_data[5] = (float)objBBoxs[i].bbox.height;
                j++;
            }
            tensor_num_ptr[b] = j;
            if (j > max_ch)
            {
                max_ch = j;
            }
        }
        std::vector<std::shared_ptr<NumArray>> res_vec;
        res->shape[1] = max_ch;
        res_vec.push_back(res);
        res_vec.push_back(res_num);
        output_ = res_vec;
        return 0;
    }
    size_t CcDetBboxDecoder2::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcDetBboxDecoder2::getOutput(int index)
    {
        return output_[index];
    }

    REGISTER_CC_MODULE(det_bbox_decode2, CcDetBboxDecoder2)
}