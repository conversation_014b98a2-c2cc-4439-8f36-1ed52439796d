
#include "cc_face_bbox_decoder.h"
#include "cc_numarray_tool.h"
#include <time_profile.h>
namespace tongxing
{
    int CcFaceBboxDecoder::init(const Json::Value &config)
    {
        root_ = config;
        cc_assert(root_["score_th"].isDouble());
        cc_assert(root_["iou_th"].isDouble());
        cc_assert(root_["feat_width"].isInt());
        cc_assert(root_["feat_height"].isInt());
        cc_assert(root_["stride"].isInt());
        feat_w_ = root_["feat_width"].asInt();
        feat_h_ = root_["feat_height"].asInt();
        stride_ = root_["stride"].asInt();
        confThreshold_ = inverse_sigmoid(root_["score_th"].asDouble());
        nmsThreshold_ = root_["iou_th"].asDouble();
        if(config.isMember("offset")){
            offset_=config["offset"].asFloat();
        }
        else{
            offset_=0;
        }

        if(config.isMember("scale")){
            scale_=config["scale"].asFloat();
        }
        else{
            scale_=stride_;
        }
        return 0;
    }
    int CcFaceBboxDecoder::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcFaceBboxDecoder::execute()
    {
        TX_LOG_DEBUG("cc_face_bbox_decoder.cpp","debug execute start");
        cc_assert(input_.size() == 1);
        // std::cout<<input_[0]->shape.size()<<std::endl;
        cc_assert(input_[0]->shape.size() == 3 || input_[0]->shape.size() == 4);
        // std::cout<<"-------------"<<input_[0]->shape[3]<<std::endl;
        size_t last_shape=0;
        if(input_[0]->shape.size()==3)
        {
            cc_assert(input_[0]->shape[1] == feat_w_ * feat_h_);
            last_shape=input_[0]->shape[2];
        }
        else if(input_[0]->shape.size()==4){
            // cc_assert(input_[0]->shape[1] == feat_h_&&input_[0]->shape[2] == feat_w_);
            last_shape=input_[0]->shape[3];
        }
        
        int batch = input_[0]->shape[0];
        int max_ch = 0;
        std::shared_ptr<NumArray> res = creat_numarray({batch, feat_w_ * feat_h_, 6}, NumArray::DataType::FLOAT32);
        memset(res->data,0,batch*feat_w_ * feat_h_*6*sizeof(float));
        std::shared_ptr<NumArray> res_num = creat_numarray({batch}, NumArray::DataType::INT32);
        std::shared_ptr<CcTensor<float>> tensor_ptr = res->getTensor<float>();
        int *tensor_num_ptr = (int *)res_num->data;
        int f_size= feat_w_ * feat_h_;
        float* bboxs_tensor_data_ptr = (float*)input_[0]->data;
        for (int b = 0; b < batch; b++)
        {
            std::vector<CcObjBBox> objBBoxs;
            bboxs_tensor_data_ptr+=b*1024*last_shape;
            for (int i = 0; i < f_size; i++)
            {
                float *bbox_data_ptr = bboxs_tensor_data_ptr + i * last_shape;
                // std::cout<<(void*)bbox_data_ptr<<" "<<last_shape<<std::endl;
                if (bbox_data_ptr[0] >= confThreshold_)
                {
                    int w = i % feat_w_;
                    int h = i / feat_w_;
                    float xmin = ((w + 0.5)* stride_ - (bbox_data_ptr[1]+offset_)*scale_) ;
                    float ymin = ((h + 0.5)* stride_ - (bbox_data_ptr[2]+offset_)*scale_) ;
                    float xmax = ((w + 0.5)* stride_ + (bbox_data_ptr[3]+offset_)*scale_) ;
                    float ymax = ((h + 0.5)* stride_ + (bbox_data_ptr[4]+offset_)*scale_) ;
                    // std::cout<<bbox_data_ptr[0]<<" "<<(bbox_data_ptr[1]+offset_)*scale_<<" "<<(bbox_data_ptr[2]+offset_)*scale_<<" "<<(bbox_data_ptr[3]+offset_)*scale_<<" "<<(bbox_data_ptr[4]+offset_)*scale_<<std::endl;
                    CcObjBBox bbox;
                    bbox.label = 0;
                    bbox.score = sigmoid(bbox_data_ptr[0]);
                    bbox.bbox.x = xmin;
                    bbox.bbox.y = ymin;
                    bbox.bbox.width = xmax - xmin;
                    bbox.bbox.height = ymax - ymin;
                    // std::cout<<"bbox:"<<bbox.score<<" "<<bbox.bbox.x<<" "<<bbox.bbox.y<<" "<<bbox.bbox.width<<" "<<bbox.bbox.height<<std::endl;
                    if (bbox.bbox.width >= 8 && bbox.bbox.height >= 8)
                    {
                        objBBoxs.push_back(bbox);
                    }
                }
            }
            std::vector<int> out_nms = nms(objBBoxs, nmsThreshold_, 1);
            int j = 0;
            float *output_data = tensor_ptr->data_+b*feat_w_ * feat_h_;
            for (int i : out_nms)
            {
                
                // float *output_data = tensor_ptr->operator[](b)[j].data_;
                output_data[0] = objBBoxs[i].score;
                output_data[1] = (float)objBBoxs[i].label;
                output_data[2] = (float)objBBoxs[i].bbox.x;
                output_data[3] = (float)objBBoxs[i].bbox.y;
                output_data[4] = (float)objBBoxs[i].bbox.width;
                output_data[5] = (float)objBBoxs[i].bbox.height;
                output_data +=6;
                j++;
            
            }
            tensor_num_ptr[b] = j;
            if (j > max_ch)
            {
                max_ch = j;
            }
        }
        std::vector<std::shared_ptr<NumArray>> res_vec;
        res->shape[1] = max_ch;
        res_vec.push_back(res);
        res_vec.push_back(res_num);
        output_ = res_vec;
        TX_LOG_DEBUG("cc_face_bbox_decoder.cpp","debug execute end");
        return 0;
    }
    size_t CcFaceBboxDecoder::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcFaceBboxDecoder::getOutput(int index)
    {
        return output_[index];
    }

    REGISTER_CC_MODULE(FaceBboxDecoder, CcFaceBboxDecoder)
}