#include "cc_heatmap_keypoints_decoder.h"
#include "cc_assert.h"
#include <cmath>
#include "cc_math_tool.h"
#include <iostream>
#include "cc_numarray_tool.h"
#include <time_profile.h>
namespace tongxing
{
    static void argmax(float *data, size_t data_size, float &maxvalue, size_t &max_index)
    {
        maxvalue = data[0];
        max_index = 0;
        for (size_t i = 1; i < data_size; i++)
        {
            if (maxvalue < data[i])
            {
                maxvalue = data[i];
                max_index = i;
            }
        }
    }

    static void argmax(float *data, size_t data_size, int ch, float &maxvalue, size_t &max_index)
    {
        maxvalue = data[0];
        max_index = 0;
        for (size_t i = 1; i < data_size; i++)
        {
            if (maxvalue < data[i*ch])
            {
                maxvalue = data[i*ch];
                max_index = i;
            }
        }
    }

    static float sigmoid_(float x)
    {
        return (1.0 / (1.0 + exp(-x)));
    }
    int CcHeatmapKeypointsDecoder::init(const Json::Value &config)
    {
        stride_ = config["stride"].asInt();
        if ( config["flag_input_nchw"].isBool() )
        {
            flag_input_nchw_ = config["flag_input_nchw"].asBool();
        }else{
            flag_input_nchw_ = true;
        }
        if(config.isMember("offset")){
            offset_=config["offset"].asFloat();
        }
        else{
            offset_=0;
        }

        if(config.isMember("scale")){
            scale_=config["scale"].asFloat();
        }
        else{
            scale_=6;
        }
        
       
        return 0;
    }
    int CcHeatmapKeypointsDecoder::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcHeatmapKeypointsDecoder::execute()
    {
        TX_LOG_DEBUG("CcHeatmapKeypointsDecoder", "execute START");
        
        cc_assert(input_.size() == 1);
        cc_assert(input_[0]->shape.size() == 4);
        int batch = input_[0]->shape[0];
        int ch = input_[0]->shape[1];
        int h = input_[0]->shape[2];
        int w = input_[0]->shape[3];

        if (!flag_input_nchw_)
        {
             ch = input_[0]->shape[3];
             h = input_[0]->shape[1];
             w = input_[0]->shape[2];
        }
        
        TX_LOG_DEBUG("cc_heatmap_keypoints_decoder.cpp","debug ch:%d,h:%d,w:%d",ch,h,w);
        int pointNum = ch / 3;
        std::shared_ptr<NumArray> output = creat_numarray({batch, pointNum, 3}, NumArray::DataType::FLOAT32);

        for (int b = 0; b < batch; b++)
        {
            auto output_tensor = output->getTensor<float>()->operator[](b).data_;
            auto input_tensor = input_[0]->getTensor<float>()->operator[](b).data_;
            if (flag_input_nchw_)
            {
                for (int i = 0; i < pointNum; i++)
                {
                    auto heatmap =  &input_tensor[i*w*h];
                    auto offset_x = &input_tensor[(pointNum+i*2)*w*h];
                    auto offset_y = &input_tensor[(pointNum+i*2+1)*w*h];
                    size_t heatmap_max_index;
                    float heatmap_max_value;
                    argmax(heatmap, w * h, heatmap_max_value, heatmap_max_index);
                  
                    int heatmap_max_x = (heatmap_max_index % w);
                    int heatmap_max_y = (heatmap_max_index / w);
                    float max_offset_x = offset_x[heatmap_max_index]/scale_;
                    float max_offset_y = offset_y[heatmap_max_index]/scale_;
                    float x = ((float)heatmap_max_x + max_offset_x) * stride_+offset_;
                    float y = ((float)heatmap_max_y + max_offset_y) * stride_+offset_;
                    // std::cout<<max_offset_x<<" "<<max_offset_y<<" "<<heatmap_max_value<<std::endl;
                    output_tensor[i*3+0] = sigmoid_(heatmap_max_value);
                    output_tensor[i*3+1] = x;
                    output_tensor[i*3+2] = y;
                    // std::cout<<sigmoid_(heatmap_max_value)<<" "<<x<<" "<<y<<" "<<heatmap_max_x<<" "<<heatmap_max_y<<" "<<max_offset_x<<" "<<max_offset_y<<" "<<stride_<<std::endl;
                }
            }else{
                for (int i = 0; i < pointNum; i++)
                {
                    auto heatmap =  &input_tensor[i];
                    auto offset_x = &input_tensor[pointNum+(2*i)];
                    auto offset_y = &input_tensor[pointNum+(2*i)+1];
                    size_t heatmap_max_index;
                    float heatmap_max_value;
                    
                    argmax(heatmap, w * h, ch, heatmap_max_value, heatmap_max_index);
                   
                    int heatmap_max_x = heatmap_max_index % w;
                    int heatmap_max_y = heatmap_max_index / w;
                    float max_offset_x = offset_x[heatmap_max_index*ch]/scale_;
                    float max_offset_y = offset_y[heatmap_max_index*ch]/scale_;
                    float x = ((float)heatmap_max_x + max_offset_x) * stride_+offset_;
                    float y = ((float)heatmap_max_y + max_offset_y) * stride_+offset_;
                    output_tensor[i*3+0] = sigmoid_(heatmap_max_value);
                    output_tensor[i*3+1] = x;
                    output_tensor[i*3+2] = y;
                    // std::cout<<sigmoid_(heatmap_max_value)<<" "<<x<<" "<<y<<" "<<heatmap_max_x<<" "<<heatmap_max_y<<" "<<max_offset_x<<" "<<max_offset_y<<" "<<stride_<<std::endl;
                }
            }
        }
        // std::cout<<scale_<<" "<<offset_<<std::endl;
        std::vector<std::shared_ptr<NumArray>> outputs;
        outputs.push_back(output);
        output_ = outputs;
        TX_LOG_DEBUG("CcHeatmapKeypointsDecoder", "execute END");
        return 0;
    }
    size_t CcHeatmapKeypointsDecoder::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcHeatmapKeypointsDecoder::getOutput(int index)
    {
        return output_[index];
    }
    REGISTER_CC_MODULE(HeatmapKeypointsDecoder, CcHeatmapKeypointsDecoder)
}