#ifndef __CC_KEYPOINTS_QUALITY_EVALUATOR_H__
#define __CC_KEYPOINTS_QUALITY_EVALUATOR_H__

#include "cc_module.h"
#include "json.h"
#include <memory>
#include <vector>
#include <deque>
#include <fstream>
#include "opencv2/core/types.hpp"
#include "../../../../src/util/eye_topology_validator.h"

namespace tongxing {

// 关键点质量评估器 - 用于检测遮挡导致的关键点不稳定
class CcKeypointsQualityEvaluator : public CcModule {
public:
    CcKeypointsQualityEvaluator();
    ~CcKeypointsQualityEvaluator();

    int init(const Json::Value& config) override;
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in) override;
    int execute() override;
    size_t getOutputNum() override;
    std::shared_ptr<NumArray> getOutput(int index) override;

    // 时序稳定性指标
    struct TemporalStabilityMetrics {
        float std_deviation_x;      // X坐标标准差
        float std_deviation_y;      // Y坐标标准差
        float std_deviation_xy;     // 综合坐标标准差
        float mean_velocity;        // 平均移动速度
        float velocity_variance;    // 速度方差
        float mean_acceleration;    // 平均加速度
        float acceleration_variance; // 加速度方差
        float confidence_stability; // 置信度稳定性
        float low_confidence_ratio; // 低置信度帧占比
        float instability_score;    // 不稳定性评分 [0,1]
        bool is_unstable;          // 是否不稳定
    };

    // 单个关键点的时序稳定性指标
    struct PerKeypointTemporalMetrics {
        int id;                     // 关键点ID
        TemporalStabilityMetrics metrics; // 稳定性指标
    };

    std::vector<PerKeypointTemporalMetrics> getPerKeypointMetrics() const;
    std::vector<float> getInstabilityScores() const;
    std::vector<int> getBinaryMask() const;
    void resetMetrics();

    // 眼睛拓扑验证相关方法
    EyeTopologyValidator::TopologyValidationResult getEyeTopologyValidation() const;
    bool isEyeTopologyValid() const;

private:
    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;
    
    // 配置参数
    int num_keypoints_ = 0;
    int window_size_ = 50;                      // 滑动窗口大小
    float std_threshold_ = 2.0f;                // 标准差阈值
    float velocity_threshold_ = 5.0f;           // 速度阈值
    float acceleration_threshold_ = 10.0f;      // 加速度阈值
    float confidence_threshold_ = 0.7f;         // 置信度阈值
    float instability_ratio_threshold_ = 0.3f;  // 不稳定帧占比阈值
    float std_velocity_disable_threshold_ = 50.0f; // std过大时禁用velocity的阈值
    bool enable_logging_ = false;               // 是否启用日志记录
    std::string log_file_path_;                 // 日志文件路径
    bool enable_eye_topology_validation_ = true; // 是否启用眼睛拓扑验证
    
    // 历史数据存储 - 单个关键点
    std::vector<std::deque<cv::Point2f>> per_keypoint_positions_;
    std::vector<std::deque<float>> per_keypoint_scores_;
    std::vector<std::deque<float>> per_keypoint_velocities_;
    std::vector<std::deque<float>> per_keypoint_accelerations_;
    
    // 计算结果
    mutable std::vector<PerKeypointTemporalMetrics> per_keypoint_metrics_;
    mutable std::vector<float> instability_scores_;
    mutable std::vector<int> binary_mask_;

    // 眼睛拓扑验证器
    std::unique_ptr<EyeTopologyValidator> eye_topology_validator_;
    mutable EyeTopologyValidator::TopologyValidationResult last_topology_result_;

    // 日志文件
    mutable std::ofstream log_file_;
    int frame_count_ = 0;
    
    // 内部计算方法
    void updateHistory(const std::vector<cv::Point2f>& keypoints, 
                      const std::vector<float>& scores);
    void calculateTemporalStabilityMetrics();
    float calculateStandardDeviation(const std::deque<cv::Point2f>& positions, bool x_only = false, bool y_only = false) const;
    float calculateMeanVelocity(const std::deque<float>& velocities) const;
    float calculateVariance(const std::deque<float>& values) const;
    float calculateInstabilityScore(const TemporalStabilityMetrics& metrics, bool use_velocity = true) const;
    bool isKeypointUnstable(const TemporalStabilityMetrics& metrics) const;
    void logFrameData(const std::vector<cv::Point2f>& keypoints,
                     const std::vector<float>& scores) const;
    void createOutputTensors();
};

} // namespace tongxing

#endif // __CC_KEYPOINTS_QUALITY_EVALUATOR_H__