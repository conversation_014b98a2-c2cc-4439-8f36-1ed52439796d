#include "keypoints_quality_evaluator.h"
#include "cc_assert.h"
#include "cc_numarray_tool.h"
#include "cc_module_register.h"
#include "opencv2/core/mat.hpp"
#include "opencv2/core/types.hpp"
#include <cmath>
#include <numeric>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sstream>

namespace tongxing {

CcKeypointsQualityEvaluator::CcKeypointsQualityEvaluator() {
    frame_count_ = 0;
    eye_topology_validator_ = std::make_unique<EyeTopologyValidator>();
}

CcKeypointsQualityEvaluator::~CcKeypointsQualityEvaluator() {
    if (log_file_.is_open()) {
        log_file_.close();
    }
}

int CcKeypointsQualityEvaluator::init(const Json::Value& config) {
    
    if (config["num_keypoints"].isNull() || !config["num_keypoints"].isNumeric()) {
        return -1;
    }
    num_keypoints_ = config["num_keypoints"].asInt();

    // 初始化单个关键点的历史数据容器
    per_keypoint_positions_.resize(num_keypoints_);
    per_keypoint_scores_.resize(num_keypoints_);
    per_keypoint_velocities_.resize(num_keypoints_);
    per_keypoint_accelerations_.resize(num_keypoints_);
    per_keypoint_metrics_.resize(num_keypoints_);
    instability_scores_.resize(num_keypoints_, 0.0f);
    binary_mask_.resize(num_keypoints_, 0);
    
    for (int i = 0; i < num_keypoints_; ++i) {
        per_keypoint_metrics_[i].id = i;
    }

    // 读取配置参数
    if (config["window_size"].isNumeric()) {
        window_size_ = config["window_size"].asInt();
    }

    if (config["std_threshold"].isNumeric()) {
        std_threshold_ = config["std_threshold"].asFloat();
    }

    if (config["velocity_threshold"].isNumeric()) {
        velocity_threshold_ = config["velocity_threshold"].asFloat();
    }

    if (config["acceleration_threshold"].isNumeric()) {
        acceleration_threshold_ = config["acceleration_threshold"].asFloat();
    }

    if (config["confidence_threshold"].isNumeric()) {
        confidence_threshold_ = config["confidence_threshold"].asFloat();
    }

    if (config["instability_ratio_threshold"].isNumeric()) {
        instability_ratio_threshold_ = config["instability_ratio_threshold"].asFloat();
    }

    if (config["std_velocity_disable_threshold"].isNumeric()) {
        std_velocity_disable_threshold_ = config["std_velocity_disable_threshold"].asFloat();
    }

    if (config["enable_logging"].isBool()) {
        enable_logging_ = config["enable_logging"].asBool();
    }

    if (config["log_file_path"].isString()) {
        log_file_path_ = config["log_file_path"].asString();
        if (enable_logging_ && !log_file_path_.empty()) {
            log_file_.open(log_file_path_, std::ios::out | std::ios::app);
            if (log_file_.is_open()) {
                printf("Keypoints_quality_evaluator log file: %s\n", log_file_path_.c_str());
                log_file_ << "frame_id,keypoint_id,x,y,confidence,std_x,std_y,velocity,acceleration,instability_score,is_unstable\n";
            }
        }
    }

    if (config["enable_eye_topology_validation"].isBool()) {
        enable_eye_topology_validation_ = config["enable_eye_topology_validation"].asBool();
    }

    // printf("CcKeypointsQualityEvaluator initialized with %d keypoints\n", num_keypoints_);
    return 0;
}

int CcKeypointsQualityEvaluator::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}

int CcKeypointsQualityEvaluator::execute() {
    if (input_.empty() || !input_[0]) {
        printf("DEBUG: Input is empty or null\n");
        return -1;
    }
    auto input_tensor = input_[0]->getTensor<float>();
    printf("DEBUG: Input tensor shape: [%d, %d], num_keypoints_=%d\n",
           input_tensor->shape()[0], input_tensor->shape()[1], num_keypoints_);
    if (input_tensor->shape().size() < 2 || input_tensor->shape()[0] != num_keypoints_) {
        printf("DEBUG: Shape validation failed\n");
        return -1;
    }

    // 解析关键点和置信度
    std::vector<cv::Point2f> keypoints;
    std::vector<float> scores;
    keypoints.reserve(num_keypoints_);
    scores.reserve(num_keypoints_);
    
    // printf("Processing %d keypoints\n", num_keypoints_);
    for (int i = 0; i < num_keypoints_; ++i) {
        float score = input_tensor->data_[3 * i];
        float x = input_tensor->data_[3 * i + 1];
        float y = input_tensor->data_[3 * i + 2];
        
        keypoints.emplace_back(x, y);
        scores.push_back(score);
    }

    // 更新历史数据
    updateHistory(keypoints, scores);
    
    // 计算时序稳定性指标
    calculateTemporalStabilityMetrics();

    printf("DEBUG: Before eye topology validation check\n");
    printf("DEBUG: enable_eye_topology_validation_=%d, num_keypoints_=%d\n",
           enable_eye_topology_validation_, num_keypoints_);
    printf("DEBUG: input_tensor=%p\n", input_tensor.get());

    // 执行眼睛拓扑验证（如果启用且关键点数量为17）
    if (enable_eye_topology_validation_ && num_keypoints_ == 17 && input_tensor) {
        printf("DEBUG: Executing eye topology validation, num_keypoints=%d\n", num_keypoints_);
        auto eye_keypoints = eye_topology_validator_->ParseEyeKeypoints(input_tensor->data_);
        last_topology_result_ = eye_topology_validator_->ValidateTopology(eye_keypoints);
        printf("DEBUG: Eye topology validation result: is_valid=%d\n", last_topology_result_.is_valid);
    } else {
        printf("DEBUG: Eye topology validation skipped, enable=%d, num_keypoints=%d, input_tensor=%p\n",
               enable_eye_topology_validation_, num_keypoints_, input_tensor.get());
    }

    // 记录日志
    if (enable_logging_) {
        logFrameData(keypoints, scores);
    }

    // 创建输出张量
    createOutputTensors();
    
    frame_count_++;
    // printf("Frame %d processed, output tensors created\n", frame_count_);
    
    return 0;
}

size_t CcKeypointsQualityEvaluator::getOutputNum() {
    return output_.size();
}

std::shared_ptr<NumArray> CcKeypointsQualityEvaluator::getOutput(int index) {
    if (index >= 0 && index < static_cast<int>(output_.size())) {
        return output_[index];
    }
    return nullptr;
}

void CcKeypointsQualityEvaluator::updateHistory(const std::vector<cv::Point2f>& keypoints,
                                                         const std::vector<float>& scores) {
    for (int i = 0; i < num_keypoints_; ++i) {
        // 添加当前位置和置信度
        per_keypoint_positions_[i].push_back(keypoints[i]);
        per_keypoint_scores_[i].push_back(scores[i]);
        
        // 计算速度（相对于前一帧）
        if (per_keypoint_positions_[i].size() >= 2) {
            auto& pos_queue = per_keypoint_positions_[i];
            cv::Point2f prev_pos = pos_queue[pos_queue.size() - 2];
            cv::Point2f curr_pos = pos_queue[pos_queue.size() - 1];
            float velocity = cv::norm(curr_pos - prev_pos);
            per_keypoint_velocities_[i].push_back(velocity);
        }
        
        // 计算加速度（相对于前一帧的速度变化）
        if (per_keypoint_velocities_[i].size() >= 2) {
            auto& vel_queue = per_keypoint_velocities_[i];
            float prev_vel = vel_queue[vel_queue.size() - 2];
            float curr_vel = vel_queue[vel_queue.size() - 1];
            float acceleration = std::abs(curr_vel - prev_vel);
            per_keypoint_accelerations_[i].push_back(acceleration);
        }
        
        // 维护滑动窗口大小
        while (static_cast<int>(per_keypoint_positions_[i].size()) > window_size_) {
            per_keypoint_positions_[i].pop_front();
            per_keypoint_scores_[i].pop_front();
        }
        
        while (static_cast<int>(per_keypoint_velocities_[i].size()) > window_size_ - 1) {
            per_keypoint_velocities_[i].pop_front();
        }
        
        while (static_cast<int>(per_keypoint_accelerations_[i].size()) > window_size_ - 2) {
            per_keypoint_accelerations_[i].pop_front();
        }
    }
}

void CcKeypointsQualityEvaluator::calculateTemporalStabilityMetrics() {
    for (int i = 0; i < num_keypoints_; ++i) {
        auto& metrics = per_keypoint_metrics_[i].metrics;
        auto& positions = per_keypoint_positions_[i];
        auto& scores = per_keypoint_scores_[i];
        auto& velocities = per_keypoint_velocities_[i];
        auto& accelerations = per_keypoint_accelerations_[i];
        
        if (positions.empty()) {
            metrics = {};
            instability_scores_[i] = 0.0f;
            binary_mask_[i] = 0;
            continue;
        }
        
        // 1. 计算位置标准差
        metrics.std_deviation_x = calculateStandardDeviation(positions, true, false);
        metrics.std_deviation_y = calculateStandardDeviation(positions, false, true);
        metrics.std_deviation_xy = calculateStandardDeviation(positions, false, false);
        
        // 2. 计算速度统计
        metrics.mean_velocity = calculateMeanVelocity(velocities);
        metrics.velocity_variance = calculateVariance(velocities);
        
        // 3. 计算加速度统计
        if (!accelerations.empty()) {
            metrics.mean_acceleration = std::accumulate(accelerations.begin(), accelerations.end(), 0.0f) / accelerations.size();
            metrics.acceleration_variance = calculateVariance(accelerations);
        } else {
            metrics.mean_acceleration = 0.0f;
            metrics.acceleration_variance = 0.0f;
        }
        
        // 4. 计算置信度相关指标
        if (!scores.empty()) {
            // 计算1-score的平均值作为置信度不稳定性指标
            float sum_confidence_instability = 0.0f;
            for (float score : scores) {
                sum_confidence_instability += (1.0f - score);
            }
            metrics.low_confidence_ratio = sum_confidence_instability / scores.size();
            // std::cout << i << " v:"<< metrics.mean_velocity << " ratio:" << metrics.low_confidence_ratio <<
            // " sum_confidence_instability:" << sum_confidence_instability << " scores.size():" << scores.size() << std::endl;
            metrics.confidence_stability = calculateVariance(scores);
        } else {
            metrics.low_confidence_ratio = 0.0f;
            metrics.confidence_stability = 0.0f;
        }
        
        // 5. 判断是否使用velocity进行评分
        // 当std过大时，速度计算不可靠，不应该用于稳定性判定
        bool use_velocity = (metrics.std_deviation_x < std_velocity_disable_threshold_) &&
                           (metrics.std_deviation_y < std_velocity_disable_threshold_);

        // 6. 计算综合不稳定性评分
        metrics.instability_score = calculateInstabilityScore(metrics, use_velocity);
        metrics.is_unstable = isKeypointUnstable(metrics);
        
        // 更新输出数组
        instability_scores_[i] = metrics.instability_score;
        binary_mask_[i] = metrics.is_unstable ? 1 : 0;
        
        // // 实时打印速度和分数信息（仅每隔几帧打印一次，避免刷屏）
        // if (frame_count_ % 30 == 0 && i < 5) {  // 每30帧打印一次，只显示前5个关键点
        //     printf("KeypointQualityEvaluator [Frame:%d Keypoint:%d] "
        //            "Velocity:%.2f(thr:%.1f) ConfInstability:%.3f(thr:%.1f) "
        //            "FinalScore:%.3f %s\n",
        //            frame_count_, i,
        //            metrics.mean_velocity, velocity_threshold_,
        //            metrics.low_confidence_ratio, instability_ratio_threshold_,
        //            metrics.instability_score,
        //            metrics.is_unstable ? "UNSTABLE" : "STABLE");
        // }
    }
}

float CcKeypointsQualityEvaluator::calculateStandardDeviation(const std::deque<cv::Point2f>& positions,
                                                                        bool x_only, bool y_only) const {
    if (positions.size() < 2) return 0.0f;
    
    if (x_only) {
        // 计算X坐标标准差
        float mean_x = 0.0f;
        for (const auto& pos : positions) {
            mean_x += pos.x;
        }
        mean_x /= positions.size();
        
        float variance = 0.0f;
        for (const auto& pos : positions) {
            float diff = pos.x - mean_x;
            variance += diff * diff;
        }
        variance /= positions.size();
        return std::sqrt(variance);
    } else if (y_only) {
        // 计算Y坐标标准差
        float mean_y = 0.0f;
        for (const auto& pos : positions) {
            mean_y += pos.y;
        }
        mean_y /= positions.size();
        
        float variance = 0.0f;
        for (const auto& pos : positions) {
            float diff = pos.y - mean_y;
            variance += diff * diff;
        }
        variance /= positions.size();
        return std::sqrt(variance);
    } else {
        // 计算综合坐标标准差（到中心点的距离标准差）
        cv::Point2f center(0, 0);
        for (const auto& pos : positions) {
            center.x += pos.x;
            center.y += pos.y;
        }
        center.x /= positions.size();
        center.y /= positions.size();
        
        float variance = 0.0f;
        for (const auto& pos : positions) {
            float dx = pos.x - center.x;
            float dy = pos.y - center.y;
            variance += dx * dx + dy * dy;
        }
        variance /= positions.size();
        return std::sqrt(variance);
    }
}

float CcKeypointsQualityEvaluator::calculateMeanVelocity(const std::deque<float>& velocities) const {
    if (velocities.empty()) return 0.0f;
    return std::accumulate(velocities.begin(), velocities.end(), 0.0f) / velocities.size();
}

float CcKeypointsQualityEvaluator::calculateVariance(const std::deque<float>& values) const {
    if (values.size() < 2) return 0.0f;
    
    float mean = std::accumulate(values.begin(), values.end(), 0.0f) / values.size();
    float variance = 0.0f;
    for (float value : values) {
        float diff = value - mean;
        variance += diff * diff;
    }
    variance /= values.size();
    return variance;
}

float CcKeypointsQualityEvaluator::calculateInstabilityScore(const TemporalStabilityMetrics& metrics, bool use_velocity) const {
    // 计算归一化的不稳定性评分 [0, 1]
    float score = 0.0f;
    if (!use_velocity) 
        return -1.0f;
    // // 位置不稳定性 (权重: 0.3)
    float position_instability = std::min(1.0f, metrics.std_deviation_xy / std_threshold_);
    // score += 0.3f * position_instability;

    // 速度不稳定性 (权重: 0.6) - 只有当std不过大时才使用
    float velocity_instability = std::min(1.0f, metrics.mean_velocity / velocity_threshold_);
    score += 0.6f * velocity_instability;
    

    // // 加速度不稳定性 (权重: 0.2)
    float acceleration_instability = std::min(1.0f, metrics.mean_acceleration / acceleration_threshold_);
    // score += 0.2f * acceleration_instability;

    // 置信度不稳定性 (权重: 0.4)
    float confidence_instability = metrics.low_confidence_ratio;
    score += 0.4f * confidence_instability;
    
    // 详细分数分解打印（每120帧打印一次详细信息）
    // static int detailed_print_counter = 0;
    // if (detailed_print_counter++ % 120 == 0) {
    //     printf("KeypointQualityEvaluator Score Breakdown:\n");
    //     printf("  Position: %.3f (disabled), Velocity: %s, Acceleration: %.3f (disabled), Confidence: %.3f*0.4=%.3f\n",
    //            position_instability,
    //            use_velocity ? "enabled" : "disabled (std too large)",
    //            acceleration_instability, confidence_instability, confidence_instability * 0.4f);
    //     printf("  Final Score: %.3f\n", score);
    // }
    
    return std::max(0.0f, std::min(1.0f, score));
}

bool CcKeypointsQualityEvaluator::isKeypointUnstable(const TemporalStabilityMetrics& metrics) const {
    // 任一条件满足即认为不稳定
    return (metrics.std_deviation_xy > std_threshold_) ||
           (metrics.mean_velocity > velocity_threshold_) ||
           (metrics.mean_acceleration > acceleration_threshold_) ||
           (metrics.low_confidence_ratio > instability_ratio_threshold_);
}

void CcKeypointsQualityEvaluator::logFrameData(const std::vector<cv::Point2f>& keypoints,
                                                        const std::vector<float>& scores) const {
    if (!log_file_.is_open()) return;
    
    for (int i = 0; i < num_keypoints_; ++i) {
        const auto& metrics = per_keypoint_metrics_[i].metrics;
        log_file_ << frame_count_ << ","
                  << i << ","
                  << keypoints[i].x << ","
                  << keypoints[i].y << ","
                  << scores[i] << ","
                  << metrics.std_deviation_x << ","
                  << metrics.std_deviation_y << ","
                  << metrics.mean_velocity << ","
                  << metrics.mean_acceleration << ","
                  << metrics.instability_score << ","
                  << (metrics.is_unstable ? 1 : 0) << std::endl;
    }
}

void CcKeypointsQualityEvaluator::createOutputTensors() {
    output_.clear();

    // 输出1: 不稳定性评分数组 [num_keypoints]
    auto instability_scores_tensor = creat_numarray({num_keypoints_}, NumArray::DataType::FLOAT32);
    float* scores_data = (float*)instability_scores_tensor->data;
    std::copy(instability_scores_.begin(), instability_scores_.end(), scores_data);
    output_.push_back(instability_scores_tensor);

    // 输出2: 二进制掩码数组 [num_keypoints]
    auto binary_mask_tensor = creat_numarray({num_keypoints_}, NumArray::DataType::INT32);
    int* mask_data = (int*)binary_mask_tensor->data;
    std::copy(binary_mask_.begin(), binary_mask_.end(), mask_data);
    output_.push_back(binary_mask_tensor);
}

std::vector<CcKeypointsQualityEvaluator::PerKeypointTemporalMetrics>
CcKeypointsQualityEvaluator::getPerKeypointMetrics() const {
    return per_keypoint_metrics_;
}

std::vector<float> CcKeypointsQualityEvaluator::getInstabilityScores() const {
    return instability_scores_;
}

std::vector<int> CcKeypointsQualityEvaluator::getBinaryMask() const {
    return binary_mask_;
}

void CcKeypointsQualityEvaluator::resetMetrics() {
    frame_count_ = 0;
    
    for (auto& queue : per_keypoint_positions_) {
        queue.clear();
    }
    for (auto& queue : per_keypoint_scores_) {
        queue.clear();
    }
    for (auto& queue : per_keypoint_velocities_) {
        queue.clear();
    }
    for (auto& queue : per_keypoint_accelerations_) {
        queue.clear();
    }
    
    per_keypoint_metrics_.assign(num_keypoints_, {});
    for (int i = 0; i < num_keypoints_; ++i) {
        per_keypoint_metrics_[i].id = i;
    }
    
    std::fill(instability_scores_.begin(), instability_scores_.end(), 0.0f);
    std::fill(binary_mask_.begin(), binary_mask_.end(), 0);

    // 重置眼睛拓扑验证结果
    last_topology_result_ = EyeTopologyValidator::TopologyValidationResult();
}

EyeTopologyValidator::TopologyValidationResult CcKeypointsQualityEvaluator::getEyeTopologyValidation() const {
    return last_topology_result_;
}

bool CcKeypointsQualityEvaluator::isEyeTopologyValid() const {
    return last_topology_result_.is_valid;
}

// 注册模块
REGISTER_CC_MODULE(KeypointsQualityEvaluator, CcKeypointsQualityEvaluator)

} // namespace tongxing