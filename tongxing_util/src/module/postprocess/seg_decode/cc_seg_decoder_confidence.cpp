#include "cc_seg_decoder_confidence.h"
#include "cc_assert.h"
#include "cc_math_tool.h"
#include "cc_numarray_tool.h"
#include <cmath>
#include <iostream>
namespace tongxing
{
    int CcSegDecoderConfidence::init(const Json::Value &config)
    {
        if (config.isMember("flag_nchw"))
        {
            flag_nchw = config["flag_nchw"].asBool();
        }
        if (config.isMember("flag_argmax"))
        {
            flag_argmax = config["flag_argmax"].asBool();
        }
        if (config.isMember("flag_argmax_int16"))
        {
            flag_argmax_int16 = config["flag_argmax_int16"].asBool();
        }
        if (config.isMember("class_num"))
        {
            class_num = config["class_num"].asInt();
        }
        if (config.isMember("keep_ratio"))
        {
            keep_ratio = config["keep_ratio"].asBool();
        }
        if (config.isMember("flag_image_shape_nchw"))
        {
            flag_image_shape_nchw = config["flag_image_shape_nchw"].asBool();
        }
        if (config.isMember("flag_use_bbox_scale"))
        {
            flag_use_bbox_scale = config["flag_use_bbox_scale"].asBool();
        
        }
        if (config.isMember("phone_threshold"))
        {
            phone_threshold = config["phone_threshold"].asFloat();
        }
        if (config.isMember("cigarette_threshold"))
        {
            cigarette_threshold = config["cigarette_threshold"].asFloat();
        }

        return 0;
    }
    int CcSegDecoderConfidence::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {

        input_ = in;
        return 0;
    }
    int CcSegDecoderConfidence::execute()
    {
        TX_LOG_DEBUG("module_grop", "CcSegDecoderConfidence::execute start");
        // cc_assert(input_.size() == 1);
        // input_[0]->getTensor<float>()->printShape();
        float scale_x = 1;
        float scale_y = 1;
        int output_x_offset = 0;
        int output_y_offset = 0;
        int input_h = input_[0]->shape[1];
        int input_w = input_[0]->shape[2];
        if (flag_nchw == true)
        {
            input_h = input_[0]->shape[2];
            input_w = input_[0]->shape[3];
        }
        if (max_output_num <= input_h * input_w)
        {
            max_output_num = input_h * input_w + 1;
        }

        if (!flag_argmax)
        {

            cc_assert(input_[0]->shape.size() == 4);

            int batch = input_[0]->shape[0];
            int ch = input_[0]->shape[1];
            int h = input_[0]->shape[2];
            int w = input_[0]->shape[3];
            if (flag_nchw == false)
            {
                h = input_[0]->shape[1];
                w = input_[0]->shape[2];
                ch = input_[0]->shape[3];
            }

            if (input_.size() == 2)
            {
                int output_max_height;
                int output_max_width;

                if (flag_use_bbox_scale)
                {
                    output_x_offset = input_[1]->getTensor<float>()->operator[](0)[0][2].get();
                    output_y_offset = input_[1]->getTensor<float>()->operator[](0)[0][3].get();
                    output_max_width = input_[1]->getTensor<float>()->operator[](0)[0][4].get();
                    output_max_height = input_[1]->getTensor<float>()->operator[](0)[0][5].get();
                }
                else
                {
                    output_max_height = input_[1]->getTensor<int>()->operator[](2).get();
                    output_max_width = input_[1]->getTensor<int>()->operator[](3).get();
                }
                int input_max_height = input_[0]->shape[2];
                int input_max_width = input_[0]->shape[3];
                if (flag_nchw == false)
                {

                    input_max_height = input_[0]->shape[1];
                    input_max_width = input_[0]->shape[2];
                }
                if (flag_image_shape_nchw == false)
                {
                    output_max_height = input_[1]->getTensor<int>()->operator[](1).get();
                    output_max_width = input_[1]->getTensor<int>()->operator[](2).get();
                }
                scale_y = (float)(output_max_height) / (float)input_max_height;
                scale_x = (float)(output_max_width) / (float)input_max_width;
                if (keep_ratio)
                {
                    if (output_max_height > output_max_width)
                    {
                        scale_y = scale_x;
                    }
                    else
                    {
                        scale_x = scale_y;
                    }
                }
            }
            // TX_LOG_DEBUG("module_grop", "CcSegDecoderConfidence::execute start");
            int loop_num = h * w;
            // std::vector<std::vector<std::vector<std::pair<int, int>>>> points;
            // points.resize(batch);
            // for (int b = 0; b < batch; b++)
            // {
            //     points[b].resize(ch - 1);
            // }
            // TX_LOG_DEBUG("module_grop", "CcSegDecoderConfidence::execute start");
            int n_dim_size = ch * w * h;
            std::shared_ptr<NumArray> output = creat_numarray({batch, ch - 1, max_output_num, 2}, NumArray::DataType::FLOAT32);
            std::shared_ptr<NumArray> output_num = creat_numarray({batch, ch - 1}, NumArray::DataType::INT32);
            memset(output_num->data_blob_ptr->pu8VirAddr, 0, output_num->data_blob_ptr->u32Size);
            float *src_data_ptr_ = ((float *)input_[0]->data);
            int *output_num_ptr_ = (int *)output_num->data_blob_ptr->pu8VirAddr;
            float *output_data_ptr_ = (float *)output->data_blob_ptr->pu8VirAddr;
            int value;
            float data;
            if (flag_nchw == true)
            {
                int c_dim_size = w * h;
                int h_dim_size = w;
                int w_dim_size = 1;
                for (int b = 0; b < batch; b++)
                {
                    float *src_data_ptr = src_data_ptr_ + b * n_dim_size;
                    for (int i = 0; i < loop_num; i++)
                    {
                        int x = i % w;
                        int y = i / w;
                        int max_index = 0;
                        if (x >= 36)
                        {
                            continue;
                        }
                        for (int c = 0; c < ch; c++)
                        {
                            data = src_data_ptr[c * c_dim_size + y * h_dim_size + x * w_dim_size];
                            
                            // std::cout << " data : "<<data << "  cigarette_threshold:"<<cigarette_threshold << std::endl;
                            if (data >= cigarette_threshold)
                            {
                                // max_value = data;
                            //    std::cout << "  c :"<<c << std::endl;
                                max_index = c;
                                break;
                            }
                        }
                        value = max_index - 1;

                        if (value >= 0 && value < ch && output_num_ptr_[value] < max_output_num)
                        {
                            // std::cout << " ############## value : "<< value << std::endl;
                            output_data_ptr_[(value * max_output_num + output_num_ptr_[value]) * 2] = x * scale_x+output_x_offset;
                            output_data_ptr_[(value * max_output_num + output_num_ptr_[value]) * 2 + 1] = y * scale_y+output_y_offset;
                            output_num_ptr_[value]++;
                            // points[b][max_index - 1].push_back(std::pair<int, int>(x, y));
                        }
                    }
                    output_data_ptr_ = output_data_ptr_ + 2 * max_output_num * (ch);
                    output_num_ptr_ = output_num_ptr_ + (ch);
                }
            }
            else
            {
                int h_dim_size = w * ch;
                int w_dim_size = ch;
                int c_dim_size = 1;
                for (int b = 0; b < batch; b++)
                {
                    float *src_data_ptr = src_data_ptr_ + b * n_dim_size;
                    for (int i = 0; i < loop_num; i++)
                    {
                        int x = i % w;
                        int y = i / w;
                        // float max_value = -999999999;
                        int max_index = 0;
                        for (int c = 0; c < ch; c++)
                        {
                            data = src_data_ptr[c * c_dim_size + y * h_dim_size + x * w_dim_size];
                            if (data >= cigarette_threshold)
                            {
                                // max_value = data;
                                max_index = c;
                                break;
                            }
                        }
                        value = max_index - 1;
                        if (value >= 0 && value < ch && output_num_ptr_[value] < max_output_num)
                        {
                            // std::cout << scale_x << " " << scale_y << std::endl;
                            output_data_ptr_[(value * max_output_num + output_num_ptr_[value]) * 2] = x * scale_x;
                            output_data_ptr_[(value * max_output_num + output_num_ptr_[value]) * 2 + 1] = y * scale_y;
                            output_num_ptr_[value]++;
                            // points[b][max_index - 1].push_back(std::pair<int, int>(x, y));
                        }
                    }
                    output_data_ptr_ = output_data_ptr_ + 2 * max_output_num * (ch);
                    output_num_ptr_ = output_num_ptr_ + (ch);
                }
            }
            // auto output_tensor = output->getTensor<float>();
            // // TX_LOG_DEBUG("module_grop", "CcSegDecoderConfidence::execute start");
            // auto output_num_tensor = output_num->getTensor<int>();
            // // LOG_DEBUG("module_grop", "CcSegDecoderConfidence::execute start");
            // for (int i = 0; i < points.size(); i++)
            // {
            //     for (int j = 0; j < points[i].size(); j++)
            //     {
            //         output_num_tensor->operator[](i)[j].get() = points[i][j].size();
            //         float *ptr = output_tensor->operator[](i)[j].data_;
            //         for (int k = 0; k < points[i][j].size(); k++)
            //         {

            //             ptr[k * 2] = (float)points[i][j][k].first;
            //             ptr[k * 2 + 1] = (float)points[i][j][k].second;
            //         }
            //     }
            // }
            // TX_LOG_DEBUG("module_grop", "CcSegDecoderConfidence::execute start");
            output_ = {output, output_num};
        }
        else
        {

            // cc_assert(input_[0]->shape.size() == 3);
            // input_[0]->getTensor<int>()->printShape();
            int batch = input_[0]->shape[0];
            int h = input_[0]->shape[1];
            int w = input_[0]->shape[2];
            if (flag_nchw == true)
            {
                h = input_[0]->shape[2];
                w = input_[0]->shape[3];
            }
            int loop_num = h * w;
            int ch = class_num - 1;
            if (input_.size() == 2)
            {
                int output_max_height = input_[1]->getTensor<int>()->operator[](2).get();
                int output_max_width = input_[1]->getTensor<int>()->operator[](3).get();
                int input_max_height = input_[0]->shape[1];
                int input_max_width = input_[0]->shape[2];
                if (flag_image_shape_nchw == false)
                {
                    output_max_height = input_[1]->getTensor<int>()->operator[](1).get();
                    output_max_width = input_[1]->getTensor<int>()->operator[](2).get();
                }
                if (flag_nchw == true)
                {
                    input_max_height = input_[0]->shape[2];
                    input_max_width = input_[0]->shape[3];
                }
                // std::cout << output_max_height << " " << output_max_width << " " << flag_nchw << std::endl;
                scale_y = (float)(output_max_height) / (float)input_max_height;
                scale_x = (float)(output_max_width) / (float)input_max_width;
                if (keep_ratio)
                {
                    if (scale_y < scale_x)
                    {
                        scale_y = scale_x;
                    }
                    else
                    {
                        scale_x = scale_y;
                    }
                }
            }
            std::shared_ptr<NumArray> output = creat_numarray({batch, ch, max_output_num, 2}, NumArray::DataType::FLOAT32);
            std::shared_ptr<NumArray> output_num = creat_numarray({batch, ch}, NumArray::DataType::FLOAT32);
            memset(output_num->data_blob_ptr->pu8VirAddr, 0, output_num->data_blob_ptr->u32Size);
            int x;
            int y;
            // std::cout<<"data_blob_ptr->size="<<input_[0]->data_blob_ptr->u32Size<<std::endl;
            if (flag_argmax_int16)
            {
                uint16_t *src_data_ptr_ = ((uint16_t *)input_[0]->data);
                int *output_num_ptr_ = (int *)output_num->data_blob_ptr->pu8VirAddr;
                float *output_data_ptr_ = (float *)output->data_blob_ptr->pu8VirAddr;
                int value = 0;
                int output_num_i;
                for (int b = 0; b < batch; b++)
                    for (int i = 0; i < loop_num; i += 4)
                    {
                        x = i % w;
                        if (x == 0)
                        {
                            i += w * 4;
                            if (i >= loop_num)
                            {
                                break;
                            }
                        }
                        value = (uint16_t)src_data_ptr_[i] - 1;
                        if (value >= 0)
                            if (value < ch)
                            {
                                output_num_i = output_num_ptr_[value];
                                if (output_num_i < max_output_num)
                                {
                                    y = i / w;
                                    // std::cout << scale_x << " " << scale_y << std::endl;
                                    output_data_ptr_[(value * max_output_num + output_num_i) * 2] = x * scale_x;
                                    output_data_ptr_[(value * max_output_num + output_num_i) * 2 + 1] = y * scale_y;
                                    output_num_ptr_[value]++;
                                }
                            }
                    }
                src_data_ptr_ = src_data_ptr_ + loop_num;
                output_data_ptr_ = output_data_ptr_ + 2 * max_output_num * (ch);
                output_num_ptr_ = output_num_ptr_ + (ch);
            }
            else
            {
                uint32_t *src_data_ptr_ = ((uint32_t *)input_[0]->data);
                int *output_num_ptr_ = (int *)output_num->data_blob_ptr->pu8VirAddr;
                float *output_data_ptr_ = (float *)output->data_blob_ptr->pu8VirAddr;
                int value = 0;
                int output_num_i;
                for (int b = 0; b < batch; b++)
                    for (int i = 0; i < loop_num; i += 4)
                    {
                        x = i % w;
                        if (x == 0)
                        {
                            i += w * 4;
                            if (i >= loop_num)
                            {
                                break;
                            }
                        }
                        value = (uint32_t)src_data_ptr_[i] - 1;
                        if (value >= 0)
                            if (value < ch)
                            {
                                output_num_i = output_num_ptr_[value];
                                if (output_num_i < max_output_num)
                                {
                                    y = i / w;
                                    // std::cout << scale_x << " " << scale_y << std::endl;
                                    output_data_ptr_[(value * max_output_num + output_num_i) * 2] = x * scale_x;
                                    output_data_ptr_[(value * max_output_num + output_num_i) * 2 + 1] = y * scale_y;
                                    output_num_ptr_[value]++;
                                }
                            }
                    }
                src_data_ptr_ = src_data_ptr_ + loop_num;
                output_data_ptr_ = output_data_ptr_ + 2 * max_output_num * (ch);
                output_num_ptr_ = output_num_ptr_ + (ch);
            }
            {
            }
            output_ = {output, output_num};
        }
         TX_LOG_DEBUG("module_grop", "CcSegDecoderConfidence::execute start");
        return 0;
    }
    size_t CcSegDecoderConfidence::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcSegDecoderConfidence::getOutput(int index)
    {
        return output_[index];
    }
    REGISTER_CC_MODULE(SegDecoderConfidence, CcSegDecoderConfidence)
}