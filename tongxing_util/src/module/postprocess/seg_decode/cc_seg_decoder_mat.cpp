#include "cc_seg_decoder_mat.h"
#include "cc_assert.h"
#include "cc_math_tool.h"
#include "cc_numarray_tool.h"
#include <cmath>
#include <iostream>
#include "opencv2/opencv.hpp"
namespace tongxing
{
    int CcSegDecoderMat::init(const Json::Value &config)
    {
        if (config.isMember("flag_nchw"))
        {
            flag_nchw = config["flag_nchw"].asBool();
        }

        if (config.isMember("class_num"))
        {
            class_num = config["class_num"].asInt();
        }
        if (config.isMember("keep_ratio"))
        {
            keep_ratio = config["keep_ratio"].asBool();
        }
        if (config.isMember("flag_image_shape_nchw"))
        {
            flag_image_shape_nchw = config["flag_image_shape_nchw"].asBool();
        }
        if (config.isMember("flag_use_bbox_scale"))
        {
            flag_use_bbox_scale = config["flag_use_bbox_scale"].asBool();
        }
        return 0;
    }

    int CcSegDecoderMat::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {

        input_ = in;
        return 0;
    }

    int CcSegDecoderMat::execute()
    {
        TX_LOG_DEBUG("module_grop", "CcSegDecoderMat::execute start");
        // cc_assert(input_.size() == 1);
        // input_[0]->getTensor<float>()->printShape();
        float scale_x = 1;
        float scale_y = 1;
        int output_x_offset = 0;
        int output_y_offset = 0;
        int input_h = input_[0]->shape[2];
        int input_w = input_[0]->shape[3];



        

            cc_assert(input_[0]->shape.size() == 4);

            int batch = input_[0]->shape[0];
            int ch = input_[0]->shape[1];
            int h = input_[0]->shape[2];
            int w = input_[0]->shape[3];
        

            if (input_.size() == 2)
            {
                int output_max_height;
                int output_max_width;

                if (flag_use_bbox_scale)
                {
                    output_x_offset = input_[1]->getTensor<float>()->operator[](0)[0][2].get();
                    output_y_offset = input_[1]->getTensor<float>()->operator[](0)[0][3].get();
                    output_max_width = input_[1]->getTensor<float>()->operator[](0)[0][4].get();
                    output_max_height = input_[1]->getTensor<float>()->operator[](0)[0][5].get();
                }
                else
                {
                    output_max_height = input_[1]->getTensor<int>()->operator[](2).get();
                    output_max_width = input_[1]->getTensor<int>()->operator[](3).get();
                }
                int input_max_height = input_[0]->shape[2];
                int input_max_width = input_[0]->shape[3];
                if (flag_nchw == false)
                {

                    input_max_height = input_[0]->shape[1];
                    input_max_width = input_[0]->shape[2];
                }
                if (flag_image_shape_nchw == false)
                {
                    output_max_height = input_[1]->getTensor<int>()->operator[](1).get();
                    output_max_width = input_[1]->getTensor<int>()->operator[](2).get();
                }
                scale_y = (float)(output_max_height) / (float)input_max_height;
                scale_x = (float)(output_max_width) / (float)input_max_width;
                if (keep_ratio)
                {
                    if (output_max_height > output_max_width)
                    {
                        scale_y = scale_x;
                    }
                    else
                    {
                        scale_x = scale_y;
                    }
                }
            }
            int loop_num = h * w;
            int n_dim_size = ch * w * h;
            std::shared_ptr<NumArray> output = creat_numarray({batch, ch, h, w}, NumArray::DataType::UINT8);
            std::shared_ptr<NumArray> scale_offset = creat_numarray({batch, 4}, NumArray::DataType::FLOAT32);
            memset(output->data_blob_ptr->pu8VirAddr, 0, output->data_blob_ptr->u32Size);
            float *src_data_ptr_ = ((float *)input_[0]->data);
            uint8_t *output_data_ptr_ = (uint8_t *)output->data_blob_ptr->pu8VirAddr;
            float *scale_offset_data_ptr_ = (float *)scale_offset->data_blob_ptr->pu8VirAddr;
            scale_offset_data_ptr_[0]=output_x_offset;
            scale_offset_data_ptr_[1]=output_y_offset;
            scale_offset_data_ptr_[2]=scale_x;
            scale_offset_data_ptr_[3]=scale_y;
            int value;
            float data;
            
            
                int c_dim_size = w * h;
                int h_dim_size = w;
          
                // std::cout<<h<<" "<<w<<" "<<ch<<std::endl;
                for (int b = 0; b < batch; b++)
                {
                    
                    float *src_data_ptr = src_data_ptr_ + b * n_dim_size;
                        for (int c = 0; c < ch; c++)
                        {
                            cv::Mat input_ch(h,w,CV_32FC1,&src_data_ptr[(c * loop_num )]);
                            cv::Mat output_ch(h,w,CV_8UC1,&output_data_ptr_[(c * loop_num )]);
                            // std::cout<<output_ch<<std::endl;
                            output_ch=input_ch>0.5;

                        }
                    // cv::imwrite("seg_0.jpg",cv::Mat(h,w,CV_8UC1,&output_data_ptr_[(0 * loop_num )]));
                    // cv::imwrite("seg_1.jpg",cv::Mat(h,w,CV_8UC1,&output_data_ptr_[(1 * loop_num )]));
                    // cv::imwrite("seg_3.jpg",cv::Mat(h,w,CV_8UC1,&output_data_ptr_[(2 * loop_num )]));
                    // std::cout<<scale_offset_data_ptr_[0]<<" "<<scale_offset_data_ptr_[1]<<" "<<scale_offset_data_ptr_[2]<<" "<<scale_offset_data_ptr_[3]<<std::endl;
                    output_data_ptr_ = output_data_ptr_ + loop_num* (ch);

                    }
                
                output_={output,scale_offset};
         TX_LOG_DEBUG("module_grop", "CcSegDecoderMat::execute start");
        return 0;
    }

    size_t CcSegDecoderMat::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcSegDecoderMat::getOutput(int index)
    {
        return output_[index];
    }
    REGISTER_CC_MODULE(SegDecoderMat, CcSegDecoderMat)


}