#ifndef __CC_SEG_DECODER_NEW_H__
#define __CC_SEG_DECODER_NEW_H__
#include "cc_module.h"
#include "json.h"
namespace tongxing{
    class CcSegDecoderNew :public CcModule{
    public:
        int init(const Json::Value& config);
        int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
    private:
        std::vector<std::shared_ptr<NumArray> > input_;
        std::vector<std::shared_ptr<NumArray> > output_;
        bool flag_nchw=true;
        bool flag_argmax=false;
        bool flag_argmax_int16=false;
        int max_output_num=1024;
        int class_num=9;
        bool keep_ratio=true;
        bool flag_image_shape_nchw=false;
        bool flag_use_bbox_scale=false;
    };
}


#endif