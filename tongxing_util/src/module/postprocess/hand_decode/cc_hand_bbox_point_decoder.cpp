
#include "cc_hand_bbox_point_decoder.h"
#include "cc_numarray_tool.h"
#include "time_profile.h"
#include "CalmCarLog.h"
namespace tongxing
{
    int CcHandBboxPointDecoder::init(const Json::Value &config)
    {
        root_ = config;
        cc_assert(root_["hand_width"].isInt());
        cc_assert(root_["hand_height"].isInt());
        cc_assert(root_["stride"].isArray());
        cc_assert(root_["score_th"].isDouble());
        cc_assert(root_["hand_cls"].isInt());
        confThreshold_ = root_["score_th"].asFloat();
        nmsThreshold_ = root_["iou_th"].asFloat();
        handWidth_ = root_["hand_width"].asInt();
        handHeight_ = root_["hand_height"].asInt();
        handCls_ = root_["hand_cls"].asInt();
        Json::Value strideArray = root_["stride"];

        stride_.resize(strideArray.size());
    
        stride_[0] = strideArray[0].asInt();
        stride_[1] = strideArray[1].asInt();
        stride_[2] = strideArray[2].asInt();
        return 0;
    }

    int CcHandBboxPointDecoder::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }

    float CcHandBboxPointDecoder::proj_conv_softmax(const float *src, float *dst, int length)
    {
        const float alpha = *std::max_element(src, src + length);
        float denominator = 0;
        float dis_sum = 0;
        for (int i = 0; i < length; ++i)
        {
        dst[i] = exp(src[i] - alpha);
        denominator += dst[i];
        }
        for (int i = 0; i < length; ++i)
        {
        dst[i] /= denominator;
        dis_sum += i * dst[i];
        }
        return dis_sum;
    }

    int CcHandBboxPointDecoder::execute()
    {
        cc_assert(input_.size() == 6);
        std::vector<Hand_Info> hand_info_tmp;
        for (size_t i = 0; i < 3; i++)
        {
            auto box_prt = (float*)input_[i]->data;
            auto point_ptr = (float*)input_[3+i]->data;
            int stride = stride_[i];
            int hand_w = handWidth_/stride;
            int hand_h = handHeight_/stride;
            int reg_max = 16;
            int reg_groups = 4;
            int reg_proj_conv_weight_num = reg_max * reg_groups;
            float dis_after_sm[16];
            std::vector<float> proj_conv_weights;

            for (size_t h = 0; h < hand_h; h++)
            {
                for (size_t w = 0; w < hand_w; w++)
                {
                    int max_index = -1;
                    float max_score = 0.5f;
                    for (int c = 0; c < handCls_; c++)
                    {
                        float pred_score = sigmoid(box_prt[hand_h * hand_w * (reg_proj_conv_weight_num + c) + h * hand_w + w ]);
                        
                        if (pred_score > max_score)
                        {
                            max_score = pred_score;
                            max_index = c;
                        }
                    }
                    if (max_score > confThreshold_)
                    {
                        // std::cout << " ----------max_score------- : "<< max_score << std::endl;
                        proj_conv_weights.clear();
                        for (int g = 0; g < reg_proj_conv_weight_num; g++)
                        {
                            proj_conv_weights.emplace_back(box_prt[hand_h * hand_w * g  + h * hand_w + w]);
                        }
                        float *proj_conv_ptr = proj_conv_weights.data();
                        float x1 = static_cast<float>(w) + 0.5f -
                                        proj_conv_softmax(proj_conv_ptr, dis_after_sm, reg_max);
                        float y1 = static_cast<float>(h) + 0.5f -
                                        proj_conv_softmax(proj_conv_ptr + reg_max, dis_after_sm, reg_max);
                        float x2 = static_cast<float>(w) + 0.5f +
                                        proj_conv_softmax(proj_conv_ptr + 2 * reg_max, dis_after_sm, reg_max);
                        float y2 = static_cast<float>(h) + 0.5f +
                                        proj_conv_softmax(proj_conv_ptr + 3 * reg_max, dis_after_sm, reg_max);

                        x1 *= static_cast<float>(stride);
                        y1 *= static_cast<float>(stride);
                        x2 *= static_cast<float>(stride);
                        y2 *= static_cast<float>(stride);

                        Hand_Info hand_obj;
                        hand_obj.label = max_index;
                        hand_obj.score = max_score;
                        hand_obj.bbox.x = x1;
                        hand_obj.bbox.y = y1;
                        hand_obj.bbox.width = x2 - x1;
                        hand_obj.bbox.height = y2 - y1;
                        // obj.kps_feat.clear();
                        // std::vector<cv::Point> kps_hand_points;
                        for (int k = 0; k < 21; k++)
                        {
                            float kps_x = point_ptr[hand_h * hand_w * (2 * k + 0) + h * hand_w + w];
                            float kps_y = point_ptr[hand_h * hand_w * (2 * k + 1) + h * hand_w + w];
                            hand_obj.hand_point[k].x = (kps_x * 2.f + w) * stride;
                            hand_obj.hand_point[k].y = (kps_y * 2.f + h) * stride;
                        }
                        hand_info_tmp.emplace_back(hand_obj);
                    }
                }
            }
        }
        std::vector<Hand_Info> hand_info_;
        hand_info_.clear();
        if(hand_info_tmp.size()>0)
        {
            std::vector<int> keptIndices;
            nms(hand_info_tmp, keptIndices, nmsThreshold_);
            for (int i = 0; i < keptIndices.size(); i++)
            {
                int index = keptIndices[i];
                Hand_Info obj = hand_info_tmp[index];
                hand_info_.push_back(obj);
            }
        }

        int hand_num = hand_info_.size();
        std::shared_ptr<NumArray> hand_box = creat_numarray({1,1,6}, NumArray::DataType::FLOAT32);
        std::shared_ptr<NumArray> hand_point = creat_numarray({1,21, 3}, NumArray::DataType::FLOAT32);
        std::shared_ptr<NumArray> res_num = creat_numarray({1}, NumArray::DataType::INT32);
        if (hand_num>0)
        {
            hand_num = hand_num>1?1:hand_num;
            for (size_t i = 0; i < hand_num; i++)
            {
                    //  float *output_data = tensor_ptr->operator[](b)[j].data_;
                float * box_data = hand_box->getTensor<float>()->operator[](0)[i].data_;
                // float *output_data = hand_box->operator[]();
                box_data[0] = hand_info_[i].score;
                box_data[1] = (float)hand_info_[i].label;
                box_data[2] = (float)hand_info_[i].bbox.x;
                box_data[3] = (float)hand_info_[i].bbox.y;
                box_data[4] = (float)hand_info_[i].bbox.width;
                box_data[5] = (float)hand_info_[i].bbox.height;

        //  printf("45555555544444444score:%0.2f , label:%0.2f, [%0.2f,%0.2f,%0.2f,%0.2f]\n",box_data[0],box_data[1],box_data[2],box_data[3],box_data[4],box_data[5]);               
                // auto output_tensor = output->getTensor<float>()->operator[](b);
                auto point_data = hand_point->getTensor<float>()->operator[](0);
                for (size_t p = 0; p < 21; p++)
                {
                    point_data[p][0].get() = 0;
                    point_data[p][1].get() = hand_info_[i].hand_point[p].x;
                    point_data[p][2].get() = hand_info_[i].hand_point[p].y;
                }
            }  
        }else{
            hand_box->getTensor<float>()->operator[](0)[0].data_[0] = 0;
        } 

        std::vector<std::shared_ptr<NumArray>> res_vec;
        int *tensor_num_ptr = (int *)res_num->data;
        tensor_num_ptr[0] = hand_num;
        res_vec.push_back(hand_box);
        res_vec.push_back(hand_point);
        res_vec.push_back(res_num);
        output_ = res_vec;
        return 0;
    }
    size_t CcHandBboxPointDecoder::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcHandBboxPointDecoder::getOutput(int index)
    {
        return output_[index];
    }

    void CcHandBboxPointDecoder::nms(std::vector<Hand_Info> &input_boxes, std::vector<int> &keptIndices, float threshold)
    {
        // 现在只是针对一种class的非极大值抑制，如果需要对多类别进行则加个class的for循环就好了
        // 按照框的置信度从大到小进行排序
        std::sort(input_boxes.begin(), input_boxes.end(), [](Hand_Info a, Hand_Info b)
                { return a.score > b.score; });

        keptIndices.clear();
        for (int i = 0; i < input_boxes.size(); i++)
        {
            int keep = 1; // 假设当前边界框应被保留
            for (int j = 0; j < keptIndices.size(); j++)
            {
                // 计算当前边界框与已保留边界框的IOU
                float iou_value = cal_iou(input_boxes[i], input_boxes[keptIndices[j]]);
                // 如果IOU超过阈值，则不保留当前边界框
                if (iou_value >= threshold)
                {
                    keep = 0;
                }
            }
            // 如果当前边界框未与已保留的任何边界框重叠过多，则将其索引加入保留列表
            if (keep)
            {
                keptIndices.push_back(i);
            }
        }
    }

    float CcHandBboxPointDecoder::cal_iou(const Hand_Info &a, const Hand_Info &b)
    {
        cv::Rect r1 = a.bbox;
        cv::Rect r2 = b.bbox;
        cv::Rect inter = r1 & r2;
        if (inter.area() <= 0)
            return 0.;
        float iou_value = 1. * inter.area() / (r1.area() + r2.area() - inter.area());
        return iou_value;
    }

    REGISTER_CC_MODULE(HandBboxPointDecode, CcHandBboxPointDecoder)
}