
#ifndef _HAND_BBOX_DECODER_H_
#define _HAND_BBOX_DECODER_H_
#include <opencv2/imgproc.hpp>
#include <vector>
#include "cc_math_tool.h"
#include "cc_module.h"
#include "json.h"

namespace tongxing
{

    struct Hand_Info{
    float label;
    float score;
    cv::Rect2f bbox;
    cv::Point2f hand_point[21];
    };

    class CcHandBboxPointDecoder : public CcModule
    {
    public:
        int init(const Json::Value &config);
        int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);

    private:
        float proj_conv_softmax(const float *src, float *dst, int length);
        void nms(std::vector<Hand_Info> &input_boxes, std::vector<int> &keptIndices, float threshold);
        float cal_iou(const Hand_Info &a, const Hand_Info &b);
        // int max_output_num = 20;

    private:
        // Confidence threshold
        float confThreshold_ = 0.5;
        // Non-maximum suppression threshold
        float nmsThreshold_ = 0.1;
        int handWidth_ = 640;
        int handHeight_ = 640;
        int handCls_ = 2;
        bool flag_nchw_ = true;
        std::vector<int> stride_= {8, 16, 32};

        std::vector<std::shared_ptr<NumArray>> input_;
        std::vector<std::shared_ptr<NumArray>> output_;

        Json::Value root_;
    };
}

#endif