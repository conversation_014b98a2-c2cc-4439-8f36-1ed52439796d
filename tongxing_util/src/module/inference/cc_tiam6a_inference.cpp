#include "cc_tiam6a_inference.h"
#include "CalmCarLog.h"
#include "cc_assert.h"
#include "cc_resource_register.h"
#include <iostream>
#include "time_profile.h"
#include "time_function.h"
#include <unistd.h>
#include <numeric>  // 添加这一行
#include <opencv2/opencv.hpp>  // 添加这一行
#include "cc_numarray_tool.h"
// #include "cnpy.h"
namespace tongxing
{

    int CcTiam62aInference::init(const Json::Value &root)
    {
        root_ = root;
        TX_LOG_DEBUG("cc_tiam6a_inference.cpp","debug");
        // TX_LOG_DEBUG("cc_mnn_model_inference.cpp","debug mnn ver %s",MNN_VERSION);
        BlobData model_data;
        BlobData io_data;
        if (root_["type"].asString() == "file")
        {
            net_name = root_["filename"].asString();
            io_name = root_["ioname"].asString();
        }
        else if (root_["type"].asString() == "inside")
        {
            net_name = root_["filename"].asString();
            auto model_data_ = CcResourcDataRegister::instance().get_function(net_name);
            model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            model_data.u32Size = model_data_.first;
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug model_data size %d", model_data.u32Size );

            io_name = root_["ioname"].asString();
            auto io_data_ = CcResourcDataRegister::instance().get_function(io_name);
            io_data.pu8VirAddr = (uint8_t *)io_data_.second;
            io_data.u32Size = io_data_.first;
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug io_data size %d", io_data.u32Size );
        }
        else
        {
            cc_assert(false);
        }
         
        // 新增参数解析
        if (root_.isMember("input_w_h")) {
            Json::Value input_wh = root_["input_w_h"];
            if (input_wh.size() == 2) {
                input_w = input_wh[0].asInt();   // 160
                input_h = input_wh[1].asInt();  // 96
            } else {
                TX_LOG_ERROR("cc_tiam6a_inference.cpp", "Invalid input_w_h array size");
            }
        } 

        if (root_.isMember("out_shape")) {
             Json::Value out_shape_ = root_["out_shape"];
    
            for (Json::ArrayIndex i = 0; i < out_shape_.size(); ++i) {
                std::vector<int> tmp;
                int size_tmp = 1;
                const Json::Value& dims = out_shape_[i];
                for (Json::ArrayIndex j = 0; j < dims.size(); ++j) {
                    size_tmp *= dims[j].asInt();
                    tmp.push_back(dims[j].asInt());
                }
                out_size.push_back(size_tmp); // 保存每个输出的总元素数
                out_shape.push_back(tmp);     // 保存每个输出的维度信息
            }
        }

      
        //    // 查找子字符串
        // if (net_name.find("FaceDetection") == std::string::npos) {
        //     std::cout << "Substring '" << net_name << "' found in the string." << std::endl;
        //     return 0;
        // }

        int32_t status;

        TX_LOG_DEBUG("cc_tiam6a_inference.cpp","debug net_name: %s",net_name.c_str());
        infConfig.getConfig(net_name, true, 1);
        TX_LOG_DEBUG("cc_tiam6a_inference.cpp","debug");
        inferer = ti::dl_inferer::DLInferer::makeInferer(infConfig);
        TX_LOG_DEBUG("cc_tiam6a_inference.cpp","debug");
      
        const ti::dl_inferer::VecDlTensor  *dlInfInputs = inferer->getInputInfo();
        const ti::dl_inferer::VecDlTensor  *dlInfOutputs = inferer->getOutputInfo();   

        // float ccc = (*dlInfOutputs)[0].;
        // TX_LOG_DEBUG("cc_tiam6a_inference.cpp","debug");

        // for (size_t i = 0; i < (*dlInfOutputs).size(); i++)
        {
            std::cout << " sssss :" << (*dlInfOutputs).size() << std::endl;
        }
        
        // input_scale = (*dlInfOutputs)[0].scale;

        TX_LOG_DEBUG("cc_tiam6a_inference.cpp","debug");
        status = inferer->createBuffers(dlInfInputs, m_inferInputBuff, true);
        if (status < 0)
        {
            TX_LOG_FATAL("cc_tiam6a_inference.cpp","createBuffers(m_inferInputBuff) failed");
        }

        status = inferer->createBuffers(dlInfOutputs, m_inferOutputBuff, true);
        if (status < 0)
        {
            TX_LOG_FATAL("cc_tiam6a_inference.cpp","createBuffers(m_inferOutputBuff) failed");
        }

        for (size_t i = 0; i < out_shape.size(); i++)
        {
            std::shared_ptr<NumArray> ouput_numarray = 
                    creat_numarray(out_shape[i], NumArray::DataType::FLOAT32);
            output_.push_back(ouput_numarray);
        }
      
    
        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug init end ");
        return 0;
    }


    CcTiam62aInference::~CcTiam62aInference()
    {
        
        m_inferInputBuff.clear();
        m_inferOutputBuff.clear();
        delete inferer;
    }

    int CcTiam62aInference::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }

    int CcTiam62aInference::execute()
    {
        TimeFunction time_f("ti run model ------- ","cc_tiam6a_inference.cpp");
        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug execute start");
        auto    *buff = m_inferInputBuff[0];
        float* input_data_ = (float*)buff->data;
        char *input_data_char = (char*)input_[0]->data;
        for (size_t i = 0; i <input_w*input_h; i++){
            input_data_[i] = input_data_char[i]*0.00392;
        }
        
        inferer->run(m_inferInputBuff, m_inferOutputBuff);

        for (size_t i = 0; i < output_.size(); i++)
        {
            auto *temp_buff = m_inferOutputBuff[i];
            memcpy(output_[0]->data,temp_buff->data, out_size[i]*sizeof(float));
        }
    
        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug execute end");
        return 0;
    }

    size_t CcTiam62aInference::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcTiam62aInference::getOutput(int index)
    {
        return output_[index];
    }



    REGISTER_CC_MODULE(tiam62a_inference, CcTiam62aInference)

} // namespace tongxing
