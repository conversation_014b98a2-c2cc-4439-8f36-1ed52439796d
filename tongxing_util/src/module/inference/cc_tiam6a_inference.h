#ifndef _CC_TIAM62A_INFERENCE_H_
#define _CC_TIAM62A_INFERENCE_H_
#include <memory>
#include <ti_dl_inferer.h>
#include "ti_dl_inferer_config.h"
#include "cc_module.h"
namespace tongxing {
  
static const uint32_t TC_MAX_TENSORS_D = 18;
static const uint32_t APP_MAX_TENSOR_DIMS = 18u;

class CcTiam62aInference : public CcModule {
  public:
    int init(const Json::Value& root);
    ~CcTiam62aInference();
    // int init(const Json::Value& config);
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:
  

    std::vector<std::shared_ptr<CcTensor<float>>> input_tensor_ptr;
    Json::Value root_;

    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;
   
  

    ti::dl_inferer::InfererConfig   infConfig;
    ti::dl_inferer::DLInferer  *inferer;

    ti::dl_inferer::VecDlTensorPtr          m_inferInputBuff;
    ti::dl_inferer::VecDlTensorPtr          m_inferOutputBuff;
    float input_scale = 1.0f;

    std::string net_name;
    std::string io_name;
    int input_w = 0;
    int input_h = 0;

    std::vector<std::vector<int>> out_shape;
    std::vector<size_t> out_size;

};

}  // namespace tongxing

#endif
