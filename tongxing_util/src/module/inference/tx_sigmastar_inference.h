#ifndef __TX_SIGMASTAR_INFERENCE_H__
#define __TX_SIGMASTAR_INFERENCE_H__
#include "cc_module.h"
#include "json.h"
#include "cc_blob_data.h"
#include "mi_sys.h"
#include "mi_common_datatype.h"
#include "mi_ipu_datatype.h"

namespace tongxing
{
    class TxSigmaStarInference : public CcModule
    {
    public:
        int init(const Json::Value &root);
        int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
        ~TxSigmaStarInference();
    private:
        std::vector<std::shared_ptr<NumArray>> input_;
        std::vector<std::shared_ptr<NumArray>> output_;
        bool flag_init = false;
        Json::Value root_;

    private:
        MI_U32 u32BufSize = (2473344);
        MI_IPU_OfflineModelStaticInfo_t OfflineModelInfo;
        MI_U32 u32ChannelID = 0;
        MI_IPU_SubNet_InputOutputDesc_t stIpuDesc;
        MI_IPU_TensorVector_t stInputTensorVector;
        MI_IPU_TensorVector_t stOutputTensorVector;
        MI_S32 iResizeN = 0;
        MI_S32 iResizeC = 0;
        MI_S32 iResizeH = 0;
        MI_S32 iResizeW = 0;
    };
} // namespace tongxing
#endif