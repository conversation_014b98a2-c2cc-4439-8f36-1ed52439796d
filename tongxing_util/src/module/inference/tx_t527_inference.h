#ifndef __TX_T527_INFERENCE_H__
#define __TX_T527_INFERENCE_H__
#include <awnn_internal.h>
#include <awnn_lib.h>
#include "cc_blob_data.h"
#include "cc_module.h"
#include "json.h"

namespace tongxing {
class TxT527Inference : public CcModule {
  public:
    int init(const Json::Value& root);
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);
    ~TxT527Inference();

  private:
    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;
    bool flag_init = false;
    Json::Value root_;

  private:
    Awnn_Context_t* context = nullptr;           // t527 npu推理句柄
    std::vector<std::vector<int>> output_shape;  //output shape
};
}  // namespace tongxing
#endif