#ifndef _CC_MNN_MODEL_INFERENCE_H_
#define _CC_MNN_MODEL_INFERENCE_H_
#include <MNN/MNNDefine.h>
#include <MNN/ImageProcess.hpp>
#include <MNN/Interpreter.hpp>
#include <MNN/Tensor.hpp>
#include <memory>
#include "cc_module.h"
namespace tongxing {
class CcMnnModelInference : public CcModule {
  public:
    int init(const Json::Value& root);
    const std::vector<std::shared_ptr<CcTensor<float>>>& getInputTensor();
    int setInputTensor(std::vector<std::shared_ptr<CcTensor<float>>>& input_tensor);
    int doInference(std::vector<std::shared_ptr<CcTensor<float>>>& output_tensor);
    int deinit();
    ~CcMnnModelInference();

    // int init(const Json::Value& config);
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:
    std::vector<std::shared_ptr<CcTensor<float>>> input_tensor_ptr;
    //    std::vector<BlobData> vec_output_blob_;
    // CC_MODEL_INFERENCE_PARAM_S param_;
    std::shared_ptr<MNN::Interpreter> net_ptr;
    MNN::Session* session = NULL;
    std::map<std::string, MNN::Tensor*> inputs;
    //   std::vector<std::shared_ptr<MNN::Tensor>> inputs_cache;
    std::map<std::string, MNN::Tensor*> outputs;

    Json::Value root_;

    std::vector<std::string> vecStrOutputNames;
    std::vector<std::string> vecStrInputNames;

    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;
    std::string net_name;
    MNNForwardType mnn_type = MNN_FORWARD_CPU;
};

}  // namespace tongxing

#endif
