#ifndef __CC_AX_INFERENCE_H__
#define __CC_AX_INFERENCE_H__
#include "cc_module.h"
#include "json.h"
#include "ax_interpreter_external_api.h"
#include "joint.h"
namespace tongxing{
    class CcAxInference :public CcModule{
    public:
        int init(const Json::Value& config);
        int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
        ~CcAxInference();
    private:
        std::vector<std::shared_ptr<NumArray> > input_;
        std::vector<std::shared_ptr<NumArray> > output_;
        bool flag_init=false;
        AX_JOINT_HANDLE handle=NULL;
        AX_JOINT_EXECUTION_CONTEXT context=NULL;
    public:
        std::vector<AX_JOINT_IO_BUFFER_T> in_buffers;
        std::vector<std::vector<int> > in_strides;
        std::vector<AX_JOINT_IO_BUFFER_T> out_buffers;
        AX_JOINT_IO_INFO_T model_io_info;
    };
}


#endif