#ifndef _CC_OYE_NPU_INFERENCE_H_
#define _CC_OYE_NPU_INFERENCE_H_

#include <memory>
#include "ot_avp_npu_rts.h"
#include "cc_module.h"
namespace tongxing {

#define CC_CACHE_32_ALIGN(x)               (((x) + 0x1F) & ~0x1F)         
#define CC_CACHE_8_ALIGN(x)               (((x) + 0x07) & ~0x07)

typedef struct {
    void* buf;
    uint32_t buf_len;
} data_buf;


bool initialized_ = false;
class CcOyeNpuInference : public CcModule {
  public:
    int init(const Json::Value& root);
    ~CcOyeNpuInference();

    // int init(const Json::Value& config);
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:


    Json::Value root_;
    std::string net_name;

    std::vector<std::string> vecStrOutputNames;
    std::vector<std::string> vecStrInputNames;

    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;

    ot_avp_handle handle_ ;
    uint32_t input_num = 0;
    uint32_t output_num = 0;
    ot_avp_npu_shape input_shape;
    uint32_t input_stride ;
    uint32_t input_len;
    ot_avp_npu_dataset* input_dataset ;
    data_buf in_buf;
    ot_avp_npu_dataset* output_dataset ;
    std::vector<data_buf> out_buf;
    std::vector<std::vector<int>> out_shape;

};

}  // namespace tongxing

#endif
