/*
 * @file tda4vm_inferer.cpp
 * @brief
 *
 * <AUTHOR> CHENG
 * @version
 * @date Oct 24, 2020
 */
#include "tda4vm_inferer.h"
#include "diagnostic/perception_diagnostic.h"
#include "../tda4vm_a2/image_utils.hpp"
extern "C" {
#include "VX/vxu.h"
}
namespace perception {
namespace model {

static const uint32_t TIVX_TIDL_TRACE_DATA_SIZE = (256 * 1024 * 1024);

Tda4vmInfererv1::Tda4vmInfererv1() {
  memset(&model_desc_, 0, sizeof(sTIDL_IOBufDesc_t));
}

Tda4vmInfererv1::~Tda4vmInfererv1() {}

int Tda4vmInfererv1::Init(const std::string& model_path, size_t batch_size,
                          bool profiling, const std::string& name) {
  name_ = name;
  enable_datatrace_ = false;
  scale_initialized_ = true;
  max_batch_size_ = batch_size;  // A2中，从xml配置文件读取
  std::string prefix = model_path.substr(0, model_path.rfind("."));
  std::string param_name = prefix + ".param";
  NNInference::Init(model_path, batch_size, profiling, name);
  INFO_VX(
      "Initialize with model_path={:s}, param_path={:s}, max_batch_size={:d}, "
      "profiling={:b}",
      model_path, param_name, batch_size, profiling);


  //创建上下文
  context_ = vxCreateContext();
  CHECK_VX_RET_B(vxGetStatus((vx_reference)context_));

  tivxTIDLLoadKernels(context_); //加载tidl内核
  CHECK_VX_RET_B(LoadVxModelToKernel(context_, model_path, param_name)); //放到和内核有关，并设置输入输出

  int length = std::accumulate(output_result_length_.begin(),
                               output_result_length_.end(), 0);
  output_result_ = new float[length];

  //创建 graph
  graph_ = vxCreateGraph(context_);
  CHECK_VX_RET_B(vxGetStatus((vx_reference)graph_));
  CHECK_VX_RET_B(vxSetReferenceName((vx_reference)graph_,
                                    ReferName("model_graph").c_str())); //绑定一个名字

  //创建 node
  node_ = CreateVxModelNode(graph_, vx_tensor_in_, vx_tensor_out_);
  if (!node_) {
    ERROR_VX("Cannot create vx_node");
    return false;
  }

  //绑定一个名字
  CHECK_VX_RET_B(vxSetReferenceName((vx_reference)node_,
                                    ReferName("model_nodes").c_str()));
  //验证 graph
  CHECK_VX_RET_B(vxVerifyGraph(graph_));

  outputs_host_.clear();
  outputs_host_.resize(output_result_length_.size());
  outputs_host_[0] = output_result_;
  if (output_result_length_.size() > 1) {
    for (size_t i = 1; i < output_result_length_.size(); ++i) {
      outputs_host_[i] =
          (float*)outputs_host_[i - 1] + output_result_length_[i - 1];
    }
  }

  GetTensorProperties();

  return diag::INIT_NORMAL;
}

vx_image Tda4vmInfererv1::GetCroppedCameraImage(const vx_image& input_image,
                                                int roi_x, int roi_y, int roi_w,
                                                int roi_h) {
  void* data_ptr;
  int width = 0;
  int height = 0;
  int stride_y = 0;
  int stride_uv = 0;
  vx_map_id map_id;
  vx_status status = VX_SUCCESS;
  vx_image_attribute_e format;
  vx_rectangle_t rect, camera_rect;
  vx_imagepatch_addressing_t image_addr;

  vxQueryImage(input_image, VX_IMAGE_WIDTH, &width, sizeof(vx_uint32));
  vxQueryImage(input_image, VX_IMAGE_HEIGHT, &height, sizeof(vx_uint32));
  vxQueryImage(input_image, VX_IMAGE_FORMAT, &format, sizeof(vx_df_image));

  vx_image image = vxCreateImage(context_, roi_w, roi_h, format);

  rect.start_x = roi_x;
  rect.start_y = roi_y;
  rect.end_x = roi_x + roi_w;
  rect.end_y = roi_y + roi_h;

  camera_rect.start_x = 0;
  camera_rect.start_y = 0;
  camera_rect.end_x = roi_w;
  camera_rect.end_y = roi_h;

  status |= vxMapImagePatch(input_image, &rect, 0, &map_id, &image_addr, &data_ptr,
                      VX_READ_ONLY, VX_MEMORY_TYPE_NONE, VX_NOGAP_X);
  stride_y = image_addr.stride_y;
  status |= vxCopyImagePatch(image, &camera_rect, 0, &image_addr, data_ptr,
                             VX_WRITE_ONLY, VX_MEMORY_TYPE_NONE);
  status |= vxUnmapImagePatch(input_image, map_id);

  camera_rect.start_x = 0;
  camera_rect.start_y = 0;
  camera_rect.end_x = roi_w;
  camera_rect.end_y = (roi_h / 2);

  rect.start_x = roi_x;
  rect.start_y = (roi_y / 2);
  rect.end_x = roi_x + roi_w;
  rect.end_y = ((roi_y + roi_h) / 2);

  status |=
      vxMapImagePatch(input_image, &rect, 1, &map_id, &image_addr, &data_ptr,
                      VX_READ_ONLY, VX_MEMORY_TYPE_NONE, VX_NOGAP_X);
  stride_uv = image_addr.stride_y;
  status |= vxCopyImagePatch(image, &camera_rect, 1, &image_addr, data_ptr,
                             VX_WRITE_ONLY, VX_MEMORY_TYPE_NONE);
  status |= vxUnmapImagePatch(input_image, map_id);

  return image;
}

std::shared_ptr<void> Tda4vmInfererv1::GetImagePtr(const vx_image& image,
                                                   int plane_index) {
  void* data_ptr;
  vx_map_id map_id;
  vx_df_image format;
  vx_rectangle_t rect;
  vx_status status = VX_SUCCESS;
  vx_imagepatch_addressing_t image_addr;

  int w, h;
  status = vxQueryImage(image, VX_IMAGE_WIDTH, &w, sizeof(vx_uint32));
  status |= vxQueryImage(image, VX_IMAGE_HEIGHT, &h, sizeof(vx_uint32));
  status |= vxQueryImage(image, (vx_enum)VX_IMAGE_FORMAT, &format,
                         sizeof(vx_df_image));
  rect.start_x = 0;
  rect.start_y = 0;
  rect.end_x = w;
  if (format == VX_DF_IMAGE_NV12) {
    if (plane_index == 1) {
      rect.end_y = h / 2;
    }
  }
  vxMapImagePatch(image, &rect, plane_index, &map_id, &image_addr, &data_ptr,
                  VX_READ_AND_WRITE, VX_MEMORY_TYPE_NONE, VX_NOGAP_X);

  return std::shared_ptr<void>(data_ptr, [=](void* p) {
    (void)p;
    vxUnmapImagePatch(image, map_id);
  });
}

// Tda4vmInfererv1类的异步处理函数
// 该函数负责接收输入数据，进行预处理，并将数据送入模型进行推理
bool Tda4vmInfererv1::AsyncProcess(const std::vector<Input>& inputs,
                                   size_t batch_size, bool preprocess) {
  
  // 检查输入数据的有效性
  if (inputs.empty() || !inputs[0].ptr[0]) {
    CALMCAR_ERROR_C(PERCEPTION, "inputs is NULL!");
    return false;
  }
  // 重置性能分析器
  profiler_.Reset();

  // 获取模型的输入高度和宽度
  int model_input_height = this->input_height(0);
  int model_input_width = this->input_width(0);
  // 获取输入的ROI区域
  cv::Rect roi = inputs[0].roi;
  // 创建输入大小的cv::Size对象
  cv::Size input_size(model_input_width, model_input_height);
  // 定义用于存储图像的cv::Mat对象
  cv::Mat image;
  cv::Mat image_resized;
  // 获取预处理后的图像数据指针
  uint8_t* data_ptr =
      const_cast<uint8_t*>(inputs[0].FindResizedPtr(roi, input_size));

  // 初始化推理输入对象
  InferenceInput infer_input;

  // 如果预处理后的图像数据指针不为空，则直接使用；否则需要进行预处理
  if (data_ptr != nullptr) {
  } else {
    // 如果无法获取预处理后的图像数据，则发出警告
    CALMCAR_WARN("Tda4vm_inferer {} could not get preprocessed image data!!",
                 inferer_name_);
    // 根据输入的通道数选择不同的预处理方式
    if (inputs[0].channel == 1) {
      // 对于单通道输入，进行裁剪、颜色转换和缩放
      vx_image roi_img_rgb, roi_img;
      vx_image origin_img = static_cast<vx_image>(inputs[0].ptr[0]);
      int w = roi.width;
      int h = roi.height;
      roi_img = GetCroppedCameraImage(origin_img, roi.x, roi.y, w, h);
      profiler_.Update("Crop");

      roi_img_rgb = vxCreateImage(context_, w, h, VX_DF_IMAGE_RGB);
      vxuColorConvert(context_, roi_img, roi_img_rgb);
      profiler_.Update("Convert");

      auto rgb = GetImagePtr(roi_img_rgb, 0);
      image = cv::Mat(inputs[0].height, inputs[0].width, CV_8UC3,
                      static_cast<uint8_t*>(rgb.get()));

      // tda4平台直接拉伸resize
      cv::resize(image, image_resized, input_size, 0, 0, cv::INTER_NEAREST);
      profiler_.Update("Resize");

      rgb.reset();
      vxReleaseImage(&roi_img);
      vxReleaseImage(&roi_img_rgb);
    } else {
      // 对于多通道输入，直接进行裁剪和缩放
      image =
          cv::Mat(inputs[0].height, inputs[0].width, CV_8UC3, (uint8_t*)inputs[0].ptr);
      cv::Mat image_roi = image(roi);
      cv::Mat image_resized;
      cv::resize(image_roi, image_resized, input_size, 0, 0, cv::INTER_NEAREST);
      profiler_.Update("Resize");

      data_ptr = image_resized.data;
    }

    profiler_.Update("CropResizeImage");

    // 更新数据指针
    data_ptr = image_resized.data;
    // 记录日志信息
    CALMCAR_WARN(
        "width:{}, height:{}, ROI: x:{}, y:{}, w:{}, h:{}, input w:{}, y:{}",
        inputs[0].width, inputs[0].height, roi.x, roi.y, roi.width, roi.height,
        model_input_width, model_input_height);
  }
  // 填充推理输入对象
  infer_input.channel = 3;
  infer_input.ptr[0] = data_ptr;
  infer_input.width = input_size.width;
  infer_input.height = input_size.height;
  infer_input.stride[0] = input_size.width;
  infer_input.format = (InferenceInput::Format)inputs[0].format;
  // 检查并填充输入张量
  CHECK_VX_RET_V(FillInputTensors(infer_input), false);
  profiler_.Update("TensorRead");

  // 处理图形
  vxProcessGraph(graph_);
  profiler_.Update("ProcessGraph");

  // 如果比例数据已初始化，则进行比例数据处理
  if (scale_initialized_) {
    TIDLScaleData(dl_args_out_);
    scale_initialized_ = false;
    profiler_.Update("Scale");
  }

  // 如果启用了数据追踪，则进行数据追踪
  if (enable_datatrace_) {
    TIDLTraceData(dl_debugger_);
    profiler_.Update("Trace");
  }
  // 记录性能分析信息
  CALMCAR_INFO("{}, {}", name_, profiler_.str());

  return true;
}

namespace {
template <typename T>
void FillInputTensor(T* dst, const sTIDL_IOBufDesc_t& model_desc,
                     vx_size input_sizes[],
                     const Tda4vmInfererv1::InferenceInput& input, int id) {
  switch (input.format) {
    case Tda4vmInfererv1::InferenceInput::RGB:
      CALMCAR_WARN(
          "inPadL:{}, inPadT:{}, input Width:{}, inHeight:{}, model w:{}, h:{}",
          model_desc.inPadL[id], model_desc.inPadT[id],
          input_sizes[0] * sizeof(uint8_t), input_sizes[1],
          model_desc.inWidth[id], model_desc.inHeight[id]);
      calmcar::autopilot::tda4vm::FillPlannarPadding(
          dst, model_desc.inPadL[id], model_desc.inPadT[id],
          model_desc.inWidth[id], model_desc.inHeight[id], input_sizes[0],
          input_sizes[1], 3);
      calmcar::autopilot::tda4vm::RgbPackedToPlannar(
          dst, model_desc.inPadL[id], model_desc.inPadT[id],
          input_sizes[0] * sizeof(T), input_sizes[1],
          static_cast<const uint8_t*>(input.ptr[id]), 0, 0, input.stride[id],
          model_desc.inWidth[id], model_desc.inHeight[id]);
      break;
    case Tda4vmInfererv1::InferenceInput::BGR: {
      calmcar::autopilot::tda4vm::FillPlannarPadding(
          dst, model_desc.inPadL[id], model_desc.inPadT[id],
          model_desc.inWidth[id], model_desc.inHeight[id], input_sizes[0],
          input_sizes[1], 3);
      calmcar::autopilot::tda4vm::RgbPackedToPlannar(
          dst, model_desc.inPadL[id], model_desc.inPadT[id],
          input_sizes[0] * sizeof(T), input_sizes[1],
          static_cast<const uint8_t*>(input.ptr[id]), 0, 0, input.stride[id],
          model_desc.inWidth[id], model_desc.inHeight[id], true);
    } break;
    case Tda4vmInfererv1::InferenceInput::NV12:
      calmcar::autopilot::tda4vm::FillPlannarPadding(
          dst, model_desc.inPadL[id], model_desc.inPadT[id],
          model_desc.inWidth[id], model_desc.inHeight[id], input_sizes[0],
          input_sizes[1], 1);
      calmcar::autopilot::tda4vm::FillPlane(
          dst, input_sizes[0] * sizeof(T), model_desc.inPadL[id],
          model_desc.inPadT[id], static_cast<const uint8_t*>(input.ptr[id]),
          input.stride[id], 0, 0, model_desc.inWidth[id],
          model_desc.inHeight[id]);
      break;
    case Tda4vmInfererv1::InferenceInput::NV21:
      calmcar::autopilot::tda4vm::FillPlannarPadding(
          dst, model_desc.inPadL[id], model_desc.inPadT[id],
          model_desc.inWidth[id], model_desc.inHeight[id], input_sizes[0],
          input_sizes[1], 1);
      if (id == 0) {
        // Y
        calmcar::autopilot::tda4vm::FillPlane(
            dst, input_sizes[0] * sizeof(T), model_desc.inPadL[id],
            model_desc.inPadT[id], static_cast<const uint8_t*>(input.ptr[id]),
            input.stride[id], 0, 0, model_desc.inWidth[id],
            model_desc.inHeight[id]);
      } else {
        // VU
        calmcar::autopilot::tda4vm::ReshuffleNv12(
            dst, input_sizes[0] * sizeof(T), model_desc.inPadL[id],
            model_desc.inPadT[id], static_cast<const uint8_t*>(input.ptr[id]),
            input.stride[id], 0, 0, model_desc.inWidth[id],
            model_desc.inHeight[id]);
      }
      break;
    default:
      CALMCAR_ERROR("Input image format is unknow !");
      break;
  }
}
}  // namespace

// 填充输入张量函数，根据输入数据准备模型所需的输入缓冲区
vx_status Tda4vmInfererv1::FillInputTensors(const InferenceInput& input) {
  // 遍历模型的所有输入缓冲区
  for (int32_t id = 0; id < model_desc_.numInputBuf; ++id) {
    void* input_buffer = nullptr;
    vx_size start[4] = {0};
    vx_size input_sizes[TC_MAX_TENSORS_D];
    vx_map_id map_id_input;
    vx_size input_strides[TC_MAX_TENSORS_D];

    // 获取当前输入缓冲区的大小
    GetInputSize(input_sizes, &model_desc_, id);
    // 映射张量补丁到输入缓冲区，以便写入数据
    CHECK_VX_RET(tivxMapTensorPatch(vx_tensor_in_[id], 3, start, input_sizes,
                                    &map_id_input, input_strides, &input_buffer,
                                    VX_WRITE_ONLY, VX_MEMORY_TYPE_NONE));

    // 获取当前输入张量的数据类型
    vx_size data_type = GetTensorDataType(model_desc_.inElementType[id]);

    // 根据数据类型填充输入张量
    if ((data_type == VX_TYPE_INT8) || (data_type == VX_TYPE_UINT8)) {
      FillInputTensor<uint8_t>(static_cast<uint8_t*>(input_buffer), model_desc_,
                               input_sizes, input, id);
    } else if ((data_type == VX_TYPE_INT16) || (data_type == VX_TYPE_UINT16)) {
      FillInputTensor<uint16_t>(static_cast<uint16_t*>(input_buffer),
                                model_desc_, input_sizes, input, id);
    } else {
      // 当数据类型为不支持的类型时，返回错误
      ERROR_VX("float is unsupported");
      CHECK_VX_RET(tivxUnmapTensorPatch(vx_tensor_in_[id], map_id_input));
      return VX_FAILURE;
    }

    // 解映射张量补丁，释放资源
    CHECK_VX_RET(tivxUnmapTensorPatch(vx_tensor_in_[id], map_id_input));
  }

  // 所有输入张量填充成功，返回成功状态
  return VX_SUCCESS;
}

void Tda4vmInfererv1::CopyOutputToHost() {
  if (inferer_name_ == "Lane") {
    TensorOutputWithoutPad(&model_desc_);
  } else {
    TensorOutputWithPad(&model_desc_);
  }
  return;
}

void Tda4vmInfererv1::Synchronize() {}

size_t Tda4vmInfererv1::input_width(size_t input_index) const {
  return model_desc_.inWidth[input_index];
}

size_t Tda4vmInfererv1::input_height(size_t input_index) const {
  return model_desc_.inHeight[input_index];
}

size_t Tda4vmInfererv1::output_dimension(size_t output_index,
                                         size_t dim_index) const {
  size_t output_dims = 0;
  if (dim_index == 0) {
    output_dims = 1;
  } else if (dim_index == 1) {
    output_dims = std::max(0, model_desc_.outHeight[output_index]);
  } else if (dim_index == 2) {
    output_dims = std::max(0, model_desc_.outWidth[output_index]);
  } else if (dim_index == 3) {
    output_dims = std::max(0, model_desc_.outNumChannels[output_index]);
  } else {
    CALMCAR_WARN_C(PERCEPTION, "dim_index of output is out of index: {}",
                   dim_index);
  }

  return output_dims;
}

std::string Tda4vmInfererv1::ReferName(const std::string& name) {
  return reference_prefix_ + name;
}

/**
 * @brief 创建Vx模型节点
 * 
 * 本函数负责在给定的图形中创建一个Vx节点，该节点用于执行模型推理。
 * 它接受输入和输出张量数组，并根据是否启用数据跟踪来配置节点参数。
 * 
 * @param graph 图形对象，用于将节点添加到计算图中
 * @param _tensor_in 输入张量数组，包含模型推理所需的输入数据
 * @param _tensor_out 输出张量数组，用于存储模型推理结果
 * @return vx_node 返回创建的Vx节点，如果创建失败则返回nullptr
 */

vx_node Tda4vmInfererv1::CreateVxModelNode(vx_graph graph,
                                           vx_tensor _tensor_in[],
                                           vx_tensor _tensor_out[]) {
  vx_node node;
  vx_reference params[TIVX_KERNEL_TIDL_NUM_BASE_PARAMETERS];

  // 设置节点参数，包括配置、网络、创建参数、输入参数和输出参数
  params[TIVX_KERNEL_TIDL_IN_CONFIG_IDX] = (vx_reference)dl_config_;
  params[TIVX_KERNEL_TIDL_IN_NETWORK_IDX] = (vx_reference)dl_network_;
  params[TIVX_KERNEL_TIDL_IN_CREATE_PARAMS_IDX] = (vx_reference)dl_params_;
  params[TIVX_KERNEL_TIDL_IN_IN_ARGS_IDX] = (vx_reference)dl_args_in_;
  params[TIVX_KERNEL_TIDL_IN_OUT_ARGS_IDX] = (vx_reference)dl_args_out_;

  

  // 根据是否启用数据跟踪，设置调试参数
  if (enable_datatrace_) {
    params[TIVX_KERNEL_TIDL_IN_TRACE_DATA_IDX] = (vx_reference)dl_debugger_;
  } else {
    params[TIVX_KERNEL_TIDL_IN_TRACE_DATA_IDX] = (vx_reference)NULL;
  }

  // 创建TIDL节点，如果创建失败则返回nullptr
  node = tivxTIDLNode(graph_, kernel_, params, _tensor_in, _tensor_out);
  if (!node) {
    ERROR_VX("Cannot create vx_node!");
    return nullptr;
  }

  // 为相关引用设置名称，便于调试和日志记录
  CHECK_VX_RET_V(
      vxSetReferenceName((vx_reference)dl_config_, ReferName("config").c_str()),
      nullptr);
  CHECK_VX_RET_V(vxSetReferenceName((vx_reference)dl_network_,
                                    ReferName("network").c_str()),
                 nullptr);
  CHECK_VX_RET_V(vxSetReferenceName((vx_reference)dl_params_,
                                    ReferName("createparams").c_str()),
                 nullptr);
  CHECK_VX_RET_V(vxSetReferenceName((vx_reference)dl_args_in_,
                                    ReferName("inargs").c_str()),
                 nullptr);
  CHECK_VX_RET_V(vxSetReferenceName((vx_reference)dl_args_out_,
                                    ReferName("outargs").c_str()),
                 nullptr);

  // 为输入张量设置名称
  for (uint32_t id = 0; id < model_desc_.numInputBuf; ++id) {
    CHECK_VX_RET_V(
        vxSetReferenceName(
            (vx_reference)_tensor_in[id],
            ReferName(fmt::format(FMT_STRING("intensor_{:d}"), id)).c_str()),
        nullptr);
  }

  // 为输出张量设置名称
  for (uint32_t id = 0; id < model_desc_.numOutputBuf; ++id) {
    CHECK_VX_RET_V(
        vxSetReferenceName(
            (vx_reference)_tensor_out[id],
            ReferName(fmt::format(FMT_STRING("outtensor_{:d}"), id)).c_str()),
        nullptr);
  }

  // 为内核引用设置名称
  CHECK_VX_RET_V(
      vxSetReferenceName((vx_reference)kernel_, ReferName("kernel").c_str()),
      nullptr);

  return node;
}

// TO-DO
vx_user_data_object Tda4vmInfererv1::ReadModelStruct(
    vx_context context, const std::string& struct_file) {

  vx_status status = VX_SUCCESS;
  vx_user_data_object config = NULL;
  vx_uint32 capacity;
  vx_map_id map_id;
  FILE* fp_config;
  vx_size read_count;
  tivxTIDLJ7Params* tidl_params = nullptr;

//模型结构路径
  fp_config = std::fopen(struct_file.c_str(), "rb");
  if (fp_config == NULL) {
    CALMCAR_ERROR_C(PERCEPTION, "Unable to open IO config file {}",
                    struct_file);
    return NULL;
  }
  CALMCAR_DEFER[fp_config] { std::fclose(fp_config); };
  fseek(fp_config, 0, SEEK_END);
  capacity = ftell(fp_config);
  fseek(fp_config, 0, SEEK_SET);
  if (capacity != sizeof(sTIDL_IOBufDesc_t)) {
    CALMCAR_ERROR_C(PERCEPTION,
                    "Config file size({} bytes) does not match size"
                    "of sTIDL_IOBufDesc_t({} bytes)",
                    capacity, sizeof(sTIDL_IOBufDesc_t));
    return NULL;
  }

  //创建用户数据对象
  config = vxCreateUserDataObject(context, "tivxTIDLJ7Params",
                                  sizeof(tivxTIDLJ7Params), NULL);
  status = vxGetStatus((vx_reference)config);
  if (VX_SUCCESS == status) {
    // 映射用户数据对象
    status = vxMapUserDataObject(config, 0, sizeof(tivxTIDLJ7Params), &map_id,
                                 (void**)&tidl_params, VX_WRITE_ONLY,
                                 VX_MEMORY_TYPE_HOST, 0);

    if (VX_SUCCESS == status) {
      if (tidl_params == NULL) {
        CALMCAR_ERROR_C(PERCEPTION, "Map of config object failed!");
        return NULL;
      }

      //调用j7 初始化结构体
      tivx_tidl_j7_params_init(tidl_params);
      read_count = fread(&(tidl_params->ioBufDesc), capacity, 1, fp_config);
      if (read_count != 1) {
        CALMCAR_ERROR_C(PERCEPTION, "Unable to read config file!");
      }
      model_desc_ = tidl_params->ioBufDesc;
      //取消映射关系
      vxUnmapUserDataObject(config, map_id);
    }
  }

  return config;
}

vx_user_data_object Tda4vmInfererv1::ReadModelParams(
    vx_context context, const std::string& params_file) {
      
  vx_map_id map_id;
  vx_uint32 capacity;
  void* network_buffer = nullptr;
  vx_size read_count;
  FILE* fp_network;

  fp_network = std::fopen(params_file.c_str(), "rb");
  if (fp_network == NULL) {
    CALMCAR_ERROR_C(PERCEPTION, "Unable to open network file {}", params_file);
    return NULL;
  }

  CALMCAR_DEFER[fp_network] { std::fclose(fp_network); };
  fseek(fp_network, 0, SEEK_END);
  capacity = ftell(fp_network);
  fseek(fp_network, 0, SEEK_SET);

 // 创建用户数据对象
  vx_user_data_object network =
      vxCreateUserDataObject(context, "TIDL_network", capacity, NULL);
  vx_status status = vxGetStatus((vx_reference)network);
  if (VX_SUCCESS == status) {
    //映射
    status = vxMapUserDataObject(network, 0, capacity, &map_id,
                                 (void**)&network_buffer, VX_WRITE_ONLY,
                                 VX_MEMORY_TYPE_HOST, 0);
    if (VX_SUCCESS == status) {
      if (network_buffer) {
        //写到内存
        read_count = fread(network_buffer, capacity, 1, fp_network);
        if (read_count != 1) {
          CALMCAR_ERROR_C(PERCEPTION, "Unable to read network file!");
        }
      } else {
        CALMCAR_ERROR_C(
            PERCEPTION,
            "Unable to allocate memory for reading network file of {} bytes",
            capacity);
      }
      //反映射
      vxUnmapUserDataObject(network, map_id);
    }
  }

  return network;
}

// 设置创建参数函数，用于初始化推理器
// 参数: vx_context context - OpenVX上下文
// 返回值: vx_user_data_object - 创建的用户数据对象
vx_user_data_object Tda4vmInfererv1::SetCreateParams(vx_context context) {
  
  // 定义映射ID
  vx_map_id map_id;
  // 定义创建参数的缓冲区指针
  void* createParams_buffer = nullptr;
  // 定义容量为TIDL_CreateParams结构体大小
  vx_uint32 capacity = sizeof(TIDL_CreateParams);
  // 创建用户数据对象
  vx_user_data_object create_params =
      vxCreateUserDataObject(context, "TIDL_CreateParams", capacity, NULL);
  // 获取创建状态
  vx_status status = vxGetStatus((vx_reference)create_params);
  // 如果创建成功
  if (VX_SUCCESS == status) {
    // 映射用户数据对象以进行写操作
    status = vxMapUserDataObject(create_params, 0, capacity, &map_id,
                                 (void**)&createParams_buffer, VX_WRITE_ONLY,
                                 VX_MEMORY_TYPE_HOST, 0);

    // 如果映射成功
    if (VX_SUCCESS == status) {
      // 如果缓冲区分配成功
      if (createParams_buffer) {
        // 将缓冲区转换为TIDL_CreateParams结构体指针
        TIDL_CreateParams* prms = (TIDL_CreateParams*)createParams_buffer;
        // 初始化创建参数
        TIDL_createParamsInit(prms);
        // 设置参数属性
        prms->isInbufsPaded = 1;
        prms->quantRangeExpansionFactor = 1.0;
        prms->quantRangeUpdateFactor = 0.0;
        // 根据是否启用数据追踪设置日志级别
        if (enable_datatrace_) {
          prms->traceLogLevel = 1;
          prms->traceWriteLevel = 1;
        } else {
          prms->traceLogLevel = 0;
          prms->traceWriteLevel = 0;
        }

      } else {
        // 如果内存分配失败，输出错误日志
        CALMCAR_ERROR_C(
            PERCEPTION,
            "Unable to allocate memory for create time params! {} bytes",
            capacity);
      }
      // 解映射用户数据对象
      vxUnmapUserDataObject(create_params, map_id);
    }
  }

  // 返回创建的用户数据对象
  return create_params;
}

/**
 * 设置输入参数对象
 * 
 * 此函数负责在给定的上下文中创建并初始化一个用户数据对象，该对象包含了模型推理所需的输入参数
 * 它首先计算输入参数的大小，然后创建一个用户数据对象并尝试映射它以填充必要的数据
 * 如果映射成功，它将初始化输入参数的结构，并在完成后再将对象解除映射
 * 
 * @param context VX框架的上下文，用于创建用户数据对象
 * @return 返回创建的用户数据对象，如果创建过程中出现错误，则该对象可能不是有效的
 */
vx_user_data_object Tda4vmInfererv1::SetInArgs(vx_context context) {
  // 初始化状态变量和输入参数对象
  vx_status status;
  vx_user_data_object inArgs;
  vx_map_id map_id;
  vx_uint32 capacity;
  void* inArgs_buffer = NULL;

  // 计算输入参数的容量
  capacity = sizeof(TIDL_InArgs);
  // 创建用户数据对象
  inArgs = vxCreateUserDataObject(context, "TIDL_InArgs", capacity, NULL);
  // 获取创建状态
  status = vxGetStatus((vx_reference)inArgs);
  // 如果创建成功，尝试映射对象以填充数据
  if (VX_SUCCESS == status) {
    status = vxMapUserDataObject(inArgs, 0, capacity, &map_id,
                                 (void**)&inArgs_buffer, VX_WRITE_ONLY,
                                 VX_MEMORY_TYPE_HOST, 0);
    // 如果映射成功，初始化输入参数
    if (VX_SUCCESS == status) {
      if (inArgs_buffer) {
        TIDL_InArgs* prms = (TIDL_InArgs*)inArgs_buffer;
        prms->iVisionInArgs.size = sizeof(TIDL_InArgs);
        prms->iVisionInArgs.subFrameInfo = 0; // 默认为0
      } else {
        // 如果无法分配内存，记录错误
        CALMCAR_ERROR_C(PERCEPTION,
                        "Unable to allocate memory for inArgs! {} bytes",
                        capacity);
      }
      // 不再需要映射，解除映射
      vxUnmapUserDataObject(inArgs, map_id);
    }
  }

  // 返回创建的用户数据对象
  return inArgs;
}

vx_user_data_object Tda4vmInfererv1::SetOutArgs(vx_context context) {
  vx_status status;
  vx_user_data_object outArgs;
  vx_map_id map_id;
  vx_uint32 capacity;
  void* outArgs_buffer = NULL;

  capacity = sizeof(TIDL_outArgs);
  outArgs = vxCreateUserDataObject(context, "TIDL_outArgs", capacity, NULL);
  status = vxGetStatus((vx_reference)outArgs);
  if (VX_SUCCESS == status) {
    status = vxMapUserDataObject(outArgs, 0, capacity, &map_id,
                                 (void**)&outArgs_buffer, VX_WRITE_ONLY,
                                 VX_MEMORY_TYPE_HOST, 0);

    if (VX_SUCCESS == status) {
      if (outArgs_buffer) {
        TIDL_outArgs* prms = (TIDL_outArgs*)outArgs_buffer;
        prms->iVisionOutArgs.size = sizeof(TIDL_outArgs);
      } else {
        CALMCAR_ERROR_C(PERCEPTION,
                        "Unable to allocate memory for outArgs! {} bytes",
                        capacity);
      }
      vxUnmapUserDataObject(outArgs, map_id);
    }
  }

  return outArgs;
}

/**
 * @brief 生成输出参数对象
 * 
 * 本函数负责创建并填充输出参数对象outArgs，该对象用于存储推理输出数据
 * 它首先创建一个用户数据对象，然后映射该对象到主机内存中进行数据填充，
 * 填充完成后解除映射并返回该对象
 * 
 * @return vx_user_data_object 返回创建的输出参数对象，如果创建失败则返回nullptr
 */
vx_user_data_object Tda4vmInfererv1::GenerateOutArgs() const {
  // 创建输出参数对象
  vx_user_data_object outArgs;
  // 映射ID，用于标识映射的用户数据对象
  vx_map_id map_id;
  // 定义输出参数对象的容量
  vx_uint32 capacity;
  // 指向输出参数对象的缓冲区
  void* outArgs_buffer = NULL;

  // 计算输出参数对象的容量
  capacity = sizeof(TIDL_outArgs);
  // 创建用户数据对象，用于存储输出参数
  outArgs = vxCreateUserDataObject(context_, "TIDL_outArgs", capacity, NULL);

  // 检查创建用户数据对象的状态
  CHECK_VX_RET_V(vxGetStatus((vx_reference)outArgs), nullptr);

  // 映射用户数据对象到主机内存，以便填充数据
  CHECK_VX_RET_V(vxMapUserDataObject(outArgs, 0, capacity, &map_id,
                                     (void**)&outArgs_buffer, VX_WRITE_ONLY,
                                     VX_MEMORY_TYPE_HOST, 0),
                 nullptr);

  // 检查是否成功分配了输出参数缓冲区
  if (outArgs_buffer) {
    // 将缓冲区强制转换为输出参数类型，并设置其大小
    TIDL_outArgs* prms = (TIDL_outArgs*)outArgs_buffer;
    prms->iVisionOutArgs.size = sizeof(TIDL_outArgs);
    // 解除用户数据对象的映射
    CHECK_VX_RET_V(vxUnmapUserDataObject(outArgs, map_id), nullptr);
    // 返回创建的输出参数对象
    return outArgs;
  } else {
    // 如果未能分配内存，输出错误信息
    ERROR_VX("Unable to allocate memory for outArgs! {:d} bytes", capacity);
    // 解除用户数据对象的映射
    CHECK_VX_RET_V(vxUnmapUserDataObject(outArgs, map_id), nullptr);
    // 返回nullptr表示失败
    return nullptr;
  }
}

vx_status Tda4vmInfererv1::LoadVxModelToKernel(
    vx_context context, const std::string& tidl_network_file_path,
    const std::string& tidl_config_file_path) {
  vx_status status = (vx_status)VX_SUCCESS;
  INFO_VX("Reading model struct from {:s}", tidl_network_file_path);

  //读取模型结构文件
  dl_config_ = ReadModelStruct(context, tidl_network_file_path);
  if (!dl_config_) {
    ERROR_VX("Cannot read model struct from {:s}", tidl_network_file_path);
    return VX_FAILURE;
  }

  INFO_VX("Reading network params from {:s}", tidl_config_file_path);
  // 读取模型参数文件 
  dl_network_ = ReadModelParams(context, tidl_config_file_path);
  if (!dl_network_) {
    ERROR_VX("Cannot read network params from {:s}", tidl_config_file_path);
    return VX_FAILURE;
  }
  //设置参数
  dl_params_ = SetCreateParams(context);
  if (!dl_params_) {
    ERROR_VX("Cannot create dl_params_");
    return VX_FAILURE;
  }
  //设置输入args
  dl_args_in_ = SetInArgs(context);
  if (!dl_args_in_) {
    ERROR_VX("Cannot create dl_args_in_");
    return VX_FAILURE;
  }

  //设置输出args
  dl_args_out_ = SetOutArgs(context);
  if (!dl_args_in_) {
    ERROR_VX("Cannot create dl_args_out_");
    return VX_FAILURE;
  }

  dl_args_out_batch_.resize(max_batch_size_);
  for (size_t b = 0; b < max_batch_size_; ++b) {
     //设置输出args 
    dl_args_out_batch_[b] = GenerateOutArgs();
    if (!dl_args_out_batch_[b]) {
      ERROR_VX("Cannot create dl_args_out_batch_{:d}", b);
      return VX_FAILURE;
    }
  }

  //没有使用
  if (enable_datatrace_) {
    dl_debugger_batch_.resize(max_batch_size_);
    for (size_t b = 0; b < max_batch_size_; ++b) {
      static constexpr const uint32_t TIVX_TIDL_TRACE_DATA_SIZE =
          (256 * 1024 * 1024);
      dl_debugger_batch_[b] = vxCreateUserDataObject(
          context_, "TIDL_traceData", TIVX_TIDL_TRACE_DATA_SIZE, NULL);
      CHECK_VX_RET(vxGetStatus((vx_reference)dl_debugger_batch_[b]));
    }
    dl_debugger_ = vxCreateUserDataObject(context, "TIDL_traceData",
                                          TIVX_TIDL_TRACE_DATA_SIZE, NULL);
  }
  //添加kernel
  kernel_ = tivxAddKernelTIDL(context, model_desc_.numInputBuf,
                              model_desc_.numOutputBuf);
  if (!kernel_) {
    ERROR_VX("Cannot create kernel_");
    return VX_FAILURE;
  }

  //创建输入输出tensor
  CHECK_VX_RET(CreateInputTensors(&model_desc_, context, vx_tensor_in_));
  CHECK_VX_RET(CreateOutputTensors(&model_desc_, context, vx_tensor_out_));

  return status;
}

/**
 * @brief 创建输入张量
 * 
 * 根据模型描述和IO缓冲区描述，为模型推理创建所需的输入张量
 * 
 * @param io_desc IO缓冲区描述符，包含输入数据的布局和格式信息
 * @param context VX框架上下文，用于创建VX对象
 * @param input_tensors 输出参数，用于存储创建的输入张量数组
 * @return vx_status 返回状态码，表示创建是否成功
 */
vx_status Tda4vmInfererv1::CreateInputTensors(sTIDL_IOBufDesc_t* io_desc,
                                              vx_context context,
                                              vx_tensor* input_tensors) {
  // 日志输出：记录输入缓冲区数量
  INFO_VX("sTIDL_IOBufDesc_t numInputBuf={:d}", model_desc_.numInputBuf);
  CALMCAR_INFO_C(PERCEPTION, "sTIDL_IOBufDesc_t numInputBuf={}",
                 model_desc_.numInputBuf);
  
  // 遍历每个输入缓冲区，创建相应的输入张量
  for (uint32_t id = 0; id < (uint32_t)model_desc_.numInputBuf; ++id) {
    // 日志输出：记录每个输入缓冲区的尺寸和通道数
    INFO_VX("sTIDL_IOBufDesc_t inWidth={:d}, inHeight={:d}, inChannels={:d}",
            model_desc_.inWidth[id], model_desc_.inHeight[id],
            model_desc_.inNumChannels[id]);
    // 日志输出：记录每个输入缓冲区的数据名称和ID
    INFO_VX("sTIDL_IOBufDesc_t inDataName={:s}, inDataId={:d}",
            model_desc_.inDataName[id], model_desc_.inDataId[id]);
    // 日志输出：记录每个输入缓冲区的填充参数
    INFO_VX("sTIDL_IOBufDesc_t inPad: T={:d} L={:d} B={:d} R={:d} Ch={:d}",
            model_desc_.inPadT[id], model_desc_.inPadL[id],
            model_desc_.inPadB[id], model_desc_.inPadR[id],
            model_desc_.inPadCh[id]);

    // 根据元素类型获取张量数据类型
    vx_size data_type = GetTensorDataType(model_desc_.inElementType[id]);
    // 日志输出：记录输入数据类型
    INFO_VX("input data_type={:d}", data_type);
    // 如果数据类型无效，则返回错误
    if (data_type == VX_TYPE_INVALID) {
      ERROR_VX("Input data type is invalid!");
      return VX_FAILURE;
    }

    // 获取输入张量的尺寸
    vx_size input_sizes[APP_MAX_TENSOR_DIMS];
    GetInputSize(input_sizes, io_desc, id);
    // 日志输出：记录输入张量尺寸
    INFO_VX("input_sizes={:d}x{:d}x{:d}", input_sizes[0], input_sizes[1],
            input_sizes[2]);
    
    // 创建输入张量
    input_tensors[id] = CreateInputTensor(input_sizes, data_type);
    // 如果张量创建失败，则返回错误
    if (!vx_tensor_in_[id]) {
      ERROR_VX("Cannot create input tensor {:d}", id);
      return VX_FAILURE;
    }
    
    // 为批处理创建输入张量数组
    vx_tensor_in_batch_[id].resize(max_batch_size_);
    for (auto&& input_tensor : vx_tensor_in_batch_[id]) {
      input_tensor = CreateInputTensor(input_sizes, data_type);
      // 如果张量创建失败，则返回错误
      if (!input_tensor) {
        ERROR_VX("Cannot create input tensor batch {:d}", id);
        return VX_FAILURE;
      }
    }
  }

  // 返回成功
  return VX_SUCCESS;
}

vx_tensor Tda4vmInfererv1::CreateInputTensor(vx_size input_sizes[],
                                             vx_size data_type) const {
  vx_tensor input_tensor =
      vxCreateTensor(context_, 3, input_sizes, data_type, 0);

  CHECK_VX_RET_V(vxGetStatus((vx_reference)input_tensor), nullptr);

  vx_size start[4] = {0, 0, 0, 0};
  vx_map_id map_id_input;
  vx_size input_strides[TC_MAX_TENSORS_D];
  void* input_buffer = nullptr;
  CHECK_VX_RET_V(tivxMapTensorPatch(input_tensor, 3, start, input_sizes,
                                    &map_id_input, input_strides, &input_buffer,
                                    VX_WRITE_ONLY, VX_MEMORY_TYPE_NONE),
                 nullptr);
  memset(input_buffer, 0, input_sizes[0] * input_sizes[1] * 3);
  CHECK_VX_RET_V(tivxUnmapTensorPatch(input_tensor, map_id_input), nullptr);
  return input_tensor;
}

void Tda4vmInfererv1::Release() {
  if (node_ != nullptr) {
    vxReleaseNode(&node_);
    node_ = nullptr;
  }

  if (graph_ != nullptr) {
    vxReleaseGraph(&graph_);
    graph_ = nullptr;
  }

  VxModelRemoveKernel();
  tivxTIDLUnLoadKernels(context_);

  if (context_ != nullptr) {
    vxReleaseContext(&context_);
    context_ = nullptr;
  }

  if (output_result_) {
    outputs_host_.clear();
    delete std::exchange(output_result_, nullptr);
  }
}

// 创建输出张量
//
// 该函数根据模型描述和上下文信息，为模型的每个输出缓冲区创建OpenVX张量
// 它遍历模型的所有输出缓冲区，并为每个缓冲区创建一个张量对象
// 这些张量对象随后可用于处理和访问模型的输出数据
//
// 参数:
// - io_buffer_descriptor: 指向I/O缓冲区描述符的指针，用于获取输出缓冲区的信息
// - context: OpenVX上下文，用于创建张量对象
// - output_tensors: 指向一个数组，用于存储创建的输出张量
//
// 返回值:
// - vx_status: 表示函数执行状态的OpenVX状态码如果函数成功执行，返回VX_SUCCESS；如果出现错误，返回相应的错误码
vx_status Tda4vmInfererv1::CreateOutputTensors(
    sTIDL_IOBufDesc_t* io_buffer_descriptor, vx_context context,
    vx_tensor* output_tensors) {
  // 日志输出：记录输出缓冲区的数量
  INFO_VX("sTIDL_IOBufDesc_t numOutputBuf={:d}", model_desc_.numOutputBuf);

  // 遍历每个输出缓冲区
  for (uint32_t id = 0; id < (uint32_t)model_desc_.numOutputBuf; ++id) {
    // 日志输出：记录当前输出缓冲区的维度信息
    INFO_VX("sTIDL_IOBufDesc_t outWidth={:d}, outHeight={:d}, outChannels={:d}",
            model_desc_.outWidth[id], model_desc_.outHeight[id],
            model_desc_.outNumChannels[id]);
    // 日志输出：记录当前输出缓冲区的数据名称和ID
    INFO_VX("sTIDL_IOBufDesc_t outDataName={:s}, outDataId={:d}",
            model_desc_.outDataName[id], model_desc_.outDataId[id]);
    // 日志输出：记录当前输出缓冲区的填充信息
    INFO_VX(
        "sTIDL_IOBufDesc_t outGetOutputSizePad: T={:d} L={:d} B={:d} R={:d} "
        "Ch={:d}",
        model_desc_.outPadT[id], model_desc_.outPadL[id],
        model_desc_.outPadB[id], model_desc_.outPadR[id],
        model_desc_.outPadCh[id]);

    // 获取当前输出缓冲区的数据类型
    vx_size data_type = GetTensorDataType(model_desc_.outElementType[id]);
    // 日志输出：记录当前输出缓冲区的数据类型
    INFO_VX("output data_type={:d}", data_type);
    // 如果数据类型无效，则记录错误并返回失败
    if (data_type == VX_TYPE_INVALID) {
      ERROR_VX("output data type is invalid!");
      return VX_FAILURE;
    }

    // 计算当前输出缓冲区的数据长度
    int data_length = model_desc_.outHeight[id] * model_desc_.outWidth[id] *
                      model_desc_.outNumChannels[id];
    // 日志输出：记录当前输出缓冲区的数据长度
    INFO_VX("output data_length={:d}", data_length);
    // 将数据长度添加到输出结果长度列表中
    output_result_length_.push_back(data_length);

    // 准备存储输出张量尺寸的数组
    vx_size output_sizes[APP_MAX_TENSOR_DIMS];
    // 获取当前输出缓冲区的尺寸信息
    GetOutputSize(output_sizes, &model_desc_, id);
    // 日志输出：记录输出张量的尺寸
    INFO_VX("output_sizes={:d}x{:d}x{:d}", output_sizes[0], output_sizes[1],
            output_sizes[2]);

    // 创建当前输出缓冲区对应的OpenVX张量
    output_tensors[id] = vxCreateTensor(context, 3, output_sizes, data_type, 0);
    // 检查创建的张量状态
    CHECK_VX_RET(vxGetStatus((vx_reference)vx_tensor_out_[id]));

    // 为当前输出缓冲区创建一批张量，以支持批量处理
    vx_tensor_out_batch_[id].resize(max_batch_size_);
    for (auto&& output_tensor : vx_tensor_out_batch_[id]) {
      // 创建每个批量处理张量
      output_tensor = vxCreateTensor(context_, 3, output_sizes, data_type, 0);
      // 检查创建的批量处理张量状态
      CHECK_VX_RET(vxGetStatus((vx_reference)output_tensor));
    }
  }

  // 如果所有输出缓冲区的张量创建成功，返回成功状态
  return VX_SUCCESS;
}

void Tda4vmInfererv1::VxModelRemoveKernel() {
  if (kernel_ != nullptr) {
    vx_status status = vxRemoveKernel(kernel_);
    if (status != (vx_status)VX_SUCCESS) {
      CALMCAR_ERROR_C(PERCEPTION, "Unable to remove kernel: {}", status);
    }
    kernel_ = nullptr;
  }

  if (dl_config_ != nullptr) {
    vxReleaseUserDataObject(&dl_config_);
    dl_config_ = nullptr;
  }

  if (dl_network_ != nullptr) {
    vxReleaseUserDataObject(&dl_network_);
    dl_network_ = nullptr;
  }

  if (dl_params_ != nullptr) {
    vxReleaseUserDataObject(&dl_params_);
    dl_params_ = nullptr;
  }

  if (dl_args_in_ != nullptr) {
    vxReleaseUserDataObject(&dl_args_in_);
    dl_args_in_ = nullptr;
  }

  if (dl_args_out_ != nullptr) {
    vxReleaseUserDataObject(&dl_args_out_);
    dl_args_out_ = nullptr;
  }

  if (enable_datatrace_) {
    if (dl_debugger_ != nullptr) {
      vxReleaseUserDataObject(&dl_debugger_);
      dl_debugger_ = nullptr;
    }
  }

  uint32_t num_input_tensors = model_desc_.numInputBuf;
  for (uint32_t id = 0; id < num_input_tensors; ++id) {
    if (vx_tensor_in_[id] != nullptr) {
      vxReleaseTensor(&vx_tensor_in_[id]);
      vx_tensor_in_[id] = nullptr;
    }
  }

  uint32_t num_output_tensors = model_desc_.numOutputBuf;
  for (uint32_t id = 0; id < num_output_tensors; ++id) {
    if (vx_tensor_out_[id] != nullptr) {
      vxReleaseTensor(&vx_tensor_out_[id]);
      vx_tensor_out_[id] = nullptr;
    }
  }

  return;
}

void Tda4vmInfererv1::FillRgbU8(void* input_buffer, sTIDL_IOBufDesc_t* io_desc,
                                unsigned char* image_data, bool is_bgr,
                                int channel, vx_size* input_sizes, int32_t id) {
  int index[3] = {0, 1, 2};  // RGB
  if (is_bgr) {
    index[0] = 2;
    index[2] = 0;
  }

  int offset_tmp = io_desc->inPadT[id] * input_sizes[0] + io_desc->inPadL[id];
  int hw_length = input_sizes[0] * input_sizes[1];

  unsigned char* pR = static_cast<unsigned char*>(input_buffer) +
                      index[0] * hw_length + offset_tmp;
  unsigned char* pG = static_cast<unsigned char*>(input_buffer) +
                      index[1] * hw_length + offset_tmp;
  unsigned char* pB = static_cast<unsigned char*>(input_buffer) +
                      index[2] * hw_length + offset_tmp;

  int pitch = io_desc->inWidth[id] >> 4;

  for (vx_int32 i = 0; i < io_desc->inHeight[id]; ++i) {
    for (vx_int32 j = 0; j < pitch; ++j) {
      int step = j << 4;
      uint8x16x3_t pixel_rgb = vld3q_u8(image_data);
      vst1q_u8(pR + step, pixel_rgb.val[0]);
      vst1q_u8(pG + step, pixel_rgb.val[1]);
      vst1q_u8(pB + step, pixel_rgb.val[2]);
      image_data += 48;
    }
    pR += input_sizes[0];
    pG += input_sizes[0];
    pB += input_sizes[0];
  }
}

void Tda4vmInfererv1::FillRgbU16(void* input_buffer, sTIDL_IOBufDesc_t* io_desc,
                                 unsigned char* image_data, bool is_bgr,
                                 int channel, vx_size* input_sizes,
                                 int32_t id) {
  int index[3] = {0, 1, 2};  // RGB
  if (is_bgr) {
    index[0] = 2;
    index[2] = 0;
  }

  int offset_tmp = io_desc->inPadT[id] * input_sizes[0] + io_desc->inPadL[id];
  int hw_length = input_sizes[0] * input_sizes[1];
  unsigned short* pR = static_cast<unsigned short*>(input_buffer) +
                       index[0] * hw_length + offset_tmp;
  unsigned short* pG = static_cast<unsigned short*>(input_buffer) +
                       index[1] * hw_length + offset_tmp;
  unsigned short* pB = static_cast<unsigned short*>(input_buffer) +
                       index[2] * hw_length + offset_tmp;

#if 1
  int pitch = io_desc->inWidth[id] >> 3;
  for (vx_int32 i = 0; i < io_desc->inHeight[id]; ++i) {
    for (vx_int32 j = 0; j < pitch; ++j) {
      int step = j << 3;
      uint8x8x3_t pixel_rgb = vld3_u8(image_data);
      vst1q_u16(pR + step, vmovl_u8(pixel_rgb.val[0]));
      vst1q_u16(pG + step, vmovl_u8(pixel_rgb.val[1]));
      vst1q_u16(pB + step, vmovl_u8(pixel_rgb.val[2]));
      image_data += 3 << 3;
    }
    pR += input_sizes[0];
    pG += input_sizes[0];
    pB += input_sizes[0];
  }
#else
  int step_0 = io_desc->inWidth[id] * channel;
  for (vx_int32 i = 0; i < io_desc->inHeight[id]; ++i) {
    int i_index = i * step_0;
    for (vx_int32 j = 0; j < io_desc->inWidth[id]; ++j) {
      int j_index = i_index + j * channel;
      pR[j] = image_data[j_index + 0];
      pG[j] = image_data[j_index + 1];
      pB[j] = image_data[j_index + 2];
    }
    pR += input_sizes[0];
    pG += input_sizes[0];
    pB += input_sizes[0];
  }
#endif
}

vx_status Tda4vmInfererv1::TensorReadLimited(sTIDL_IOBufDesc_t* io_desc,
                                             unsigned char* image_data,
                                             bool is_bgr, int channel) {
  vx_status status = VX_SUCCESS;
  void* input_buffer = nullptr;
  void* input_buffer_1 = nullptr;
  vx_map_id map_id_input, map_id_input_1;
  vx_size input_strides[TC_MAX_TENSORS_D];
  vx_size input_sizes[TC_MAX_TENSORS_D];
  vx_size start[4] = {0};

  GetInputSize(input_sizes, io_desc, 0);
  status = tivxMapTensorPatch(vx_tensor_in_[0], 3, start, input_sizes,
                              &map_id_input, input_strides, &input_buffer,
                              VX_READ_AND_WRITE, VX_MEMORY_TYPE_HOST);
  if (VX_SUCCESS != status) {
    return false;
  }
  status = tivxMapTensorPatch(vx_tensor_in_[1], 3, start, input_sizes,
                              &map_id_input_1, input_strides, &input_buffer_1,
                              VX_WRITE_ONLY, VX_MEMORY_TYPE_HOST);
  if (VX_SUCCESS != status) {
    tivxUnmapTensorPatch(vx_tensor_in_[0], map_id_input);
    return false;
  }
  size_t sz =
      input_sizes[0] * input_sizes[1] * input_sizes[2] * input_strides[0];
  memcpy(input_buffer_1, input_buffer, sz);

  vx_size data_type = GetTensorDataType(io_desc->inElementType[0]);
  if ((data_type == VX_TYPE_INT8) || (data_type == VX_TYPE_UINT8)) {
    FillRgbU8(input_buffer, io_desc, image_data, is_bgr, channel, input_sizes,
              0);
  } else if ((data_type == VX_TYPE_INT16) || (data_type == VX_TYPE_UINT16)) {
    FillRgbU16(input_buffer, io_desc, image_data, is_bgr, channel, input_sizes,
               0);
  } else {
    CALMCAR_WARN_C(PERCEPTION, "float is unsupported");
  }
  tivxUnmapTensorPatch(vx_tensor_in_[0], map_id_input);
  tivxUnmapTensorPatch(vx_tensor_in_[1], map_id_input_1);
  return true;
}

vx_status Tda4vmInfererv1::TensorRead(sTIDL_IOBufDesc_t* io_desc,
                                      unsigned char* image_data, bool is_bgr,
                                      int channel) {
  vx_status status = VX_SUCCESS;

  for (int32_t id = 0; id < model_desc_.numInputBuf; ++id) {
    void* input_buffer = nullptr;
    vx_size start[4] = {0};
    vx_size input_strides[TC_MAX_TENSORS_D];
    vx_map_id map_id_input;
    vx_size input_sizes[TC_MAX_TENSORS_D];

    GetInputSize(input_sizes, &model_desc_, id);
    CHECK_VX_RET(tivxMapTensorPatch(vx_tensor_in_[id], 3, start, input_sizes,
                                    &map_id_input, input_strides, &input_buffer,
                                    VX_WRITE_ONLY, VX_MEMORY_TYPE_HOST));

    vx_size data_type = GetTensorDataType(model_desc_.inElementType[id]);

    if ((data_type == VX_TYPE_INT8) || (data_type == VX_TYPE_UINT8)) {
      // FillRgbU8(input_buffer, &model_desc_, image_data, is_bgr, channel,
      //           input_sizes, id);

      calmcar::autopilot::tda4vm::FillPlannarPadding<uint8_t>(
          (uint8_t*)input_buffer, model_desc_.inPadL[id],
          model_desc_.inPadT[id], model_desc_.inWidth[id],
          model_desc_.inHeight[id], input_sizes[0], input_sizes[1], 3);
      CALMCAR_WARN(
          "inPadL:{}, inPadT:{}, input Width:{}, inHeight:{}, model w:{}, h:{}",
          model_desc_.inPadL[id], model_desc_.inPadT[id],
          input_sizes[0] * sizeof(uint8_t), input_sizes[1],
          model_desc_.inWidth[id], model_desc_.inHeight[id]);
      calmcar::autopilot::tda4vm::RgbPackedToPlannar(
          (uint8_t*)input_buffer, model_desc_.inPadL[id],
          model_desc_.inPadT[id], input_sizes[0] * sizeof(uint8_t),
          input_sizes[1], image_data, 0, 0, model_desc_.inWidth[id],
          model_desc_.inWidth[id], model_desc_.inHeight[id]);
    } else if ((data_type == VX_TYPE_INT16) || (data_type == VX_TYPE_UINT16)) {
      FillRgbU16(input_buffer, &model_desc_, image_data, is_bgr, channel,
                 input_sizes, id);
    } else {
      CALMCAR_WARN_C(PERCEPTION, "float is unsupported");
      status = VX_FAILURE;
    }

    CHECK_VX_RET(tivxUnmapTensorPatch(vx_tensor_in_[id], map_id_input));
  }

  return status;
}

void Tda4vmInfererv1::TensorOutputWithPad(sTIDL_IOBufDesc_t* buf_desc) {
  vx_size output_sizes[4] = {0};
  float* dest = output_result_;
  output_tensors_.clear();
  outputs_host_.clear();
  for (int id = 0; id < buf_desc->numOutputBuf; ++id) {
    std::vector<size_t> paddings{};
    std::vector<size_t> dims{};
    vx_size data_type = GetTensorDataType(buf_desc->outElementType[id]);
    GetOutputSize(output_sizes, buf_desc, id);
    vx_size start[4] = {0};
    vx_size output_strides[4] = {0};
    void* output_buffer = nullptr;
    vx_map_id map_id = 0;
    tivxMapTensorPatch(vx_tensor_out_[id], 3, start, output_sizes, &map_id,
                       output_strides, &output_buffer, VX_READ_ONLY,
                       VX_MEMORY_TYPE_HOST);

    dims.push_back(1);                             // n
    dims.push_back(buf_desc->outNumChannels[id]);  // c
    dims.push_back(buf_desc->outHeight[id]);       // h
    dims.push_back(buf_desc->outWidth[id]);        // w
    
    paddings.push_back(1);                         // n
    paddings.push_back(buf_desc->outPadCh[id]);    // c
    paddings.push_back(buf_desc->outPadT[id]);     // t
    paddings.push_back(buf_desc->outPadB[id]);     // b
    paddings.push_back(buf_desc->outPadL[id]);     // l
    paddings.push_back(buf_desc->outPadR[id]);     // r

    bool dequantization = false;
    float* scale{nullptr};
    scale = &output_scales_[id];
    std::string output_name = (char*)buf_desc->outDataName[id];
    output_tensors_.push_back(Tensor(output_buffer, output_name, dims, paddings,
                                     dequantization, scale, 0, data_type));
    outputs_host_.push_back(output_buffer);
    tivxUnmapTensorPatch(vx_tensor_out_[id], map_id);
  }
  profiler_.Update("TensorOutputWithPad");
}

// 处理Tensor输出，不包含填充
void Tda4vmInfererv1::TensorOutputWithoutPad(sTIDL_IOBufDesc_t* buf_desc) {
  // 初始化输出尺寸
  vx_size output_sizes[4] = {0};
  // 清空之前的输出缓存
  outputs_host_.clear();
  // 设置目标浮点数指针以存储输出结果
  float* dest = output_result_;
  
  // 遍历每个输出缓冲区
  for (int id = 0; id < buf_desc->numOutputBuf; ++id) {
    // 获取当前输出缓冲区的数据类型
    vx_size data_type = GetTensorDataType(buf_desc->outElementType[id]);
    // 获取当前输出的尺寸
    GetOutputSize(output_sizes, buf_desc, id);
    
    // 初始化起始位置和输出步长
    vx_size start[4] = {0};
    vx_size output_strides[4] = {0};
    void* output_buffer = nullptr;
    vx_map_id map_id = 0;
    
    // 映射Tensor补丁到内存中
    tivxMapTensorPatch(vx_tensor_out_[id], 3, start, output_sizes, &map_id,
                       output_strides, &output_buffer, VX_READ_ONLY,
                       VX_MEMORY_TYPE_HOST);

    // 定义一个自动推导类型的lambda函数来填充输出
    auto FillOutput = [this, &dest, buf_desc, output_buffer, output_sizes,
                       output_strides](auto* type, int id, float scale) {
      // 使用type traits来获取类型
      using Type = typename std::decay<decltype(*type)>::type;
      // 输出调试信息
      CALMCAR_DEBUG("FillOutput with type {}\n", typeid(Type).name());
      
      // 遍历Tensor的每个元素并填充到输出中
      for (vx_size k = 0; k < output_sizes[2]; ++k) {
        Type* p = static_cast<Type*>(output_buffer) +
                  (output_sizes[0] * output_sizes[1] * k) +
                  (buf_desc->outPadT[id] * output_sizes[0]) +
                  buf_desc->outPadL[id];
        for (int32_t i = 0; i < buf_desc->outHeight[id]; ++i) {
          for (int32_t j = 0; j < buf_desc->outWidth[id]; ++j) {
            dest[j] = p[j] * scale;
          }
          p += output_sizes[0];
          dest += buf_desc->outWidth[id];
        }
      }
    };
    

    // 根据数据类型调用填充输出的lambda函数
    if ((data_type == VX_TYPE_INT8) || (data_type == VX_TYPE_UINT8)) {
      FillOutput((int8_t*)nullptr, id, output_scales_[id]);
    } else if ((data_type == VX_TYPE_INT16) || (data_type == VX_TYPE_UINT16)) {
      FillOutput((int16_t*)nullptr, id, output_scales_[id]);
    } else if (data_type == VX_TYPE_FLOAT32) {
      FillOutput((float32_t*)nullptr, id, output_scales_[id]);
    } else {
      // 输出警告信息，当数据类型不支持时
      CALMCAR_WARN("float is unsupported");
    }

    // 解映射Tensor补丁
    tivxUnmapTensorPatch(vx_tensor_out_[id], map_id);
  }

  // 将推理结果添加到输出缓存中
  for (uint32_t i = 0; i < buf_desc->numOutputBuf; ++i) {
    float* chw_result = GetInferResult(i);
    outputs_host_.push_back(static_cast<void*>(chw_result));
  }
  
  // 更新性能分析器
  profiler_.Update("TensorOutputWithoutPad");
}

void Tda4vmInfererv1::GetInputSize(vx_size* input_sizes,
                                   sTIDL_IOBufDesc_t* io_desc, int id) {
  input_sizes[0] =
      io_desc->inWidth[id] + io_desc->inPadL[id] + io_desc->inPadR[id];
  input_sizes[1] =
      io_desc->inHeight[id] + io_desc->inPadT[id] + io_desc->inPadB[id];
  input_sizes[2] = io_desc->inNumChannels[id];
}

void Tda4vmInfererv1::GetOutputSize(vx_size* output_sizes,
                                    sTIDL_IOBufDesc_t* io_desc, int id) {
  output_sizes[0] =
      io_desc->outWidth[id] + io_desc->outPadL[id] + io_desc->outPadR[id];
  output_sizes[1] =
      io_desc->outHeight[id] + io_desc->outPadT[id] + io_desc->outPadB[id];
  output_sizes[2] = io_desc->outNumChannels[id];
}

vx_status Tda4vmInfererv1::TIDLTraceData(vx_user_data_object traceData) {
  void* trace_buffer = nullptr;
  vx_map_id map_id = 0;
  vx_size capacity = 0;

  vx_status status = vxGetStatus((vx_reference)traceData);
  if ((vx_status)VX_SUCCESS == status) {
    status = vxQueryUserDataObject(traceData, (vx_enum)VX_USER_DATA_OBJECT_SIZE,
                                   &capacity, sizeof(capacity));

    if ((vx_status)VX_SUCCESS == status) {
      status = vxMapUserDataObject(traceData, 0, capacity, &map_id,
                                   (void**)&trace_buffer, (vx_enum)VX_READ_ONLY,
                                   (vx_enum)VX_MEMORY_TYPE_HOST, 0);
    }

    if ((vx_status)VX_SUCCESS == status) {
      tivxTIDLTraceDataManager mgr;
      tivxTIDLTraceHeader* header;
      uint64_t offset;

      tivxTIDLTraceDataInit(&mgr, (uint8_t*)trace_buffer, (uint64_t)capacity);

      offset = 0;
      header = (tivxTIDLTraceHeader*)tivxTIDLTraceGetData(
          &mgr, offset, (uint64_t)sizeof(tivxTIDLTraceHeader));
      offset += sizeof(tivxTIDLTraceHeader);
      while (strncmp(header->fileName, "EOB", 3) != 0) {
        FILE* fp;
        uint8_t* data_ptr;
        char file_name[TIVX_TIDL_TRACE_FILE_NAME_SIZE + 1];

        data_ptr = tivxTIDLTraceGetData(&mgr, header->offset, header->size);
        offset += header->size;

        snprintf(file_name, TIVX_TIDL_TRACE_FILE_NAME_SIZE, "%s",
                 header->fileName);
        fp = std::fopen(file_name, "wb");
        if (fp == NULL) {
          CALMCAR_ERROR_C(PERCEPTION, "Unable to open file {}", file_name);
          status = (vx_status)VX_FAILURE;
          break;
        }
        CALMCAR_DEFER[fp] { std::fclose(fp); };
        CALMCAR_INFO_C(PERCEPTION, "Writing {} of size {} bytes...", file_name,
                       header->size);

        fwrite(data_ptr, header->size, sizeof(uint8_t), fp);
        fflush(fp);

        CALMCAR_INFO_C(PERCEPTION, "Done!");

        header = (tivxTIDLTraceHeader*)tivxTIDLTraceGetData(
            &mgr, offset, sizeof(tivxTIDLTraceHeader));
        offset += sizeof(tivxTIDLTraceHeader);
      }

      vxUnmapUserDataObject(traceData, map_id);
    } else {
      CALMCAR_ERROR_C(PERCEPTION, "Unable to map trace_buffer!");
      status = (vx_status)VX_FAILURE;
    }
  }

  return status;
}

void Tda4vmInfererv1::GetTensorProperties() {
  //  input tensors
  input_tensors_.clear();
  //  output tensors
  output_tensors_.clear();

  // to-do
}

vx_size Tda4vmInfererv1::GetTensorDataType(vx_int32 tidl_type) {
  vx_size openvx_type = VX_TYPE_INVALID;
  if (tidl_type == TIDL_UnsignedChar) {
    openvx_type = VX_TYPE_UINT8;
  } else if (tidl_type == TIDL_SignedChar) {
    openvx_type = VX_TYPE_INT8;
  } else if (tidl_type == TIDL_UnsignedShort) {
    openvx_type = VX_TYPE_UINT16;
  } else if (tidl_type == TIDL_SignedShort) {
    openvx_type = VX_TYPE_INT16;
  } else if (tidl_type == TIDL_UnsignedWord) {
    openvx_type = VX_TYPE_UINT32;
  } else if (tidl_type == TIDL_SignedWord) {
    openvx_type = VX_TYPE_INT32;
  } else if (tidl_type == TIDL_SinglePrecFloat) {
    openvx_type = VX_TYPE_FLOAT32;
  }

  return openvx_type;
}

vx_status Tda4vmInfererv1::TIDLScaleData(vx_user_data_object outArgs) {
  vx_status status = vxGetStatus((vx_reference)outArgs);
  if ((vx_status)VX_SUCCESS != status) {
    CALMCAR_ERROR_C(PERCEPTION, "Unable to map dl_outArgs!");
    status = (vx_status)VX_FAILURE;
  }
  void* outargs_buffer = nullptr;
  vx_map_id map_id = 0;
  status = vxMapUserDataObject(outArgs, 0, sizeof(TIDL_outArgs), &map_id,
                               (void**)&outargs_buffer, VX_WRITE_ONLY,
                               VX_MEMORY_TYPE_HOST, 0);

  if (VX_SUCCESS != status) {
    CALMCAR_ERROR_C(PERCEPTION, "Unable to map dl_outArgs!");
    status = (vx_status)VX_FAILURE;
  }
  if (outargs_buffer) {
    TIDL_outArgs* prms = (TIDL_outArgs*)outargs_buffer;
    for (int id = 0; id < prms->numOutBufs; ++id) {
      // CALMCAR_INFO_C(PERCEPTION, "scale: {}", prms->scale[id]);
      output_scales_.push_back(1.0f / prms->scale[id]);
    }
  }
  vxUnmapUserDataObject(outArgs, map_id);

  return status;
}

float* Tda4vmInfererv1::GetInferResult(size_t output_index) {
  int length = 0;
  for (size_t i = 0; i < output_index; ++i) {
    length += output_result_length_[i];
  }

  return output_result_ + length;
}

float* Tda4vmInfererv1::GetInferResult(float* output_result,
                                       size_t output_index) {
  int length = 0;
  for (size_t i = 0; i < output_index; ++i) {
    length += output_result_length_[i];
  }

  return output_result + length;
}

float* Tda4vmInfererv1::GetInferOutput() { return output_result_; }

}  // namespace model
}  // namespace perception
