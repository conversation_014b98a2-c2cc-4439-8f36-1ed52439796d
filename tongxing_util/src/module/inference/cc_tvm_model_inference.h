#ifndef _CC_TVM_MODEL_INFERENCE_H_
#define _CC_TVM_MODEL_INFERENCE_H_
#include "cc_module.h"
#include "cc_tensor.h"
#include "json.h"
#include <memory>
#include <tvm/runtime/c_runtime_api.h>
#include <tvm/runtime/module.h>
#include <tvm/runtime/packed_func.h>
#include <tvm/runtime/registry.h>

namespace tongxing
{
  // using namespace name
  std::string tvm_models_path;
  class cc_tvm_model_init
  {
  

  public:
    tvm::runtime::PackedFunc run;
    tvm::runtime::PackedFunc set_input;
    DLTensor *input_x;
    size_t _input_size;
    int device_id = 0;
    tvm::runtime::PackedFunc get_output;
    std::vector<DLTensor *> _output_tensors;
    std::vector<size_t> _output_size;
    std::vector<std::vector<int>> _output_shape;
    int output_node_num;
    std::string mode_mame;

 private:
    tvm::runtime::Module mod_dylib;

    DLDevice dev{static_cast<DLDeviceType>(kDLSlimAI), device_id};
    tvm::runtime::Module mod;
    tvm::runtime::PackedFunc get_num_inputs;
    tvm::runtime::PackedFunc get_num_outputs;
    tvm::runtime::PackedFunc get_input;

    tvm::runtime::PackedFunc load_params;
    
  public:
    cc_tvm_model_init(/* args */);
    ~cc_tvm_model_init();
    int init(const std::string &name, const std::string &name_input);
  };

  class CcTvmModelInference : public CcModule
  {
  public:
    ~CcTvmModelInference();
    CcTvmModelInference();

    int init(const Json::Value &root);
    int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:
    std::shared_ptr<cc_tvm_model_init> tvm_model;

    std::vector<std::vector<float>> output_y;

    std::vector<std::shared_ptr<CcTensor<float>>> input_tensor_ptr;
    Json::Value root_;

    std::vector<std::string> vecStrOutputNames;
    std::vector<std::string> vecStrInputNames;

    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;
  };

} // namespace tongxing

#endif
