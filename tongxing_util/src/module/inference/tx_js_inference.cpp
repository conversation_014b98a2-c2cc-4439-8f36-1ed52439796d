#include "tx_js_inference.h"
#include "cc_resource_register.h"
#include "cc_assert.h"
#include <iostream>
#include <mutex>
#include <string.h>
#include "CalmCarLog.h"
namespace tongxing
{
    int TxJsInference::init(const Json::Value &root)
    {
        if (flag_init)
        {
            return -1;
        }
        root_ = root;
        int res = 0;
        BlobData model_data;
        if (root_["type"].asString() == "file")
        {
            cc_assert(model_data.loadFromFile(root_["filename"].asString()) == 0);
        }
        else if (root_["type"].asString() == "inside")
        {
            auto model_data_ =
                CcResourcDataRegister::instance().get_function(root_["filename"].asString());
            model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            model_data.u32Size = model_data_.first;
        }
        else
        {
            cc_assert(false);
        }
        // int ret = CVI_NN_RegisterModel(model_file.c_str(), &model);
        int ret = CVI_NN_RegisterModelFromBuffer((const int8_t *)model_data.pu8VirAddr, model_data.u32Size, &model);
        if (ret != CVI_RC_SUCCESS)
        {
            printf("CVI_NN_RegisterModel failed, err %x\n", ret);

            exit(1);
        }
        printf("CVI_NN_RegisterModel succeeded\n");
        // get input output tensors
        CVI_NN_SetConfig(model, OPTION_BATCH_SIZE, 1);
        CVI_NN_SetConfig(model, OPTION_SKIP_PREPROCESS, true);
        CVI_NN_SetConfig(model, OPTION_INPUT_MEM_TYPE, CVI_MEM_DEVICE);
        CVI_NN_GetInputOutputTensors(model, &input_tensors, &input_num,
                                     &output_tensors, &output_num);
        input =
            CVI_NN_GetTensorByName(CVI_NN_DEFAULT_TENSOR, input_tensors, input_num);

        assert(input);
        // std::cout << "output_num:" << output_num << std::endl;
        // std::cout << "input_num:" << input_num << std::endl;
        char *tensor_name;

        for (int i = 0; i < output_num; ++i)
        {

            output_vec.push_back(&output_tensors[i]);
            tensor_name = CVI_NN_TensorName(&output_tensors[i]);
            std::cout << "tensor_name:" << tensor_name << std::endl;
        }

        qscale = CVI_NN_TensorQuantScale(input);
        shape = CVI_NN_TensorShape(input);
        // std::cout << "--- mode shape 3 = " << shape.dim[3] << " 2 = " << shape.dim[2] << " 1 = " << shape.dim[1]
        //           << std::endl;
        flag_init = true;
        return 0;
    }
    int TxJsInference::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        if (input_[0]->data_blob_ptr->u64PsyAddr != NULL)
        {

            CVI_NN_SetTensorPhysicalAddr(input, (uint64_t)input_[0]->data_blob_ptr->u64PsyAddr);
        }
        else
        {
            memcpy(CVI_NN_TensorPtr(input), input_[0]->data_blob_ptr->pu8VirAddr,
                   CVI_NN_TensorSize(input));
        }
        return 0;
    }
    int TxJsInference::execute()
    {
        // run inference
        CVI_NN_Forward(model, input_tensors, input_num, output_tensors, output_num);
        std::vector<std::shared_ptr<NumArray>> outputs;
        outputs.reserve(output_num);
        for (int i = 0; i < output_num; i++)
        {
            std::shared_ptr<NumArray> na(new NumArray);
            na->type = NumArray::DataType::FLOAT32;
            na->word_size = sizeof(float);
            na->data = (unsigned char *)CVI_NN_TensorPtr(output_vec[i]);
            output_shape = CVI_NN_TensorShape(output_vec[i]);
            if (output_shape.dim_size == 3)
            {
                na->shape.push_back(output_shape.dim[0]);
                na->shape.push_back(output_shape.dim[1]);
                na->shape.push_back(output_shape.dim[2]);
            }
            else
            {
                na->shape.reserve(output_shape.dim_size);
                // std::cout << "outputTensorShape:" << std::endl;
                for (int j = 0; j < output_shape.dim_size; j++)
                {
                    na->shape.push_back(output_shape.dim[j]);
                    // std::cout << output_shape.dim[j] << std::endl;
                }
            }
            outputs.push_back(na);
        }
        output_ = outputs;
        return 0;
    }
    size_t TxJsInference::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> TxJsInference::getOutput(int index)
    {
        return output_[index];
    }
    TxJsInference::~TxJsInference()
    {
        input_.clear();
        output_.clear();
        input_tensor_ptr.clear();
        if (model)
        {
            CVI_NN_CleanupModel(model);
            printf("CVI_NN_CleanupModel succeeded\n");
        }
    }
    REGISTER_CC_MODULE(js_inference, TxJsInference)
}