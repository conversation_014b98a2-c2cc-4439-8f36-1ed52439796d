#include "cc_tidlrt_inference.h"
#include "CalmCarLog.h"
#include "cc_assert.h"
#include "cc_resource_register.h"
#include <iostream>
#include "time_profile.h"
#include "time_function.h"
#include <unistd.h>
#include <numeric>  // 添加这一行
#include <opencv2/opencv.hpp>  // 添加这一行
#include "cc_numarray_tool.h"
// #include "cnpy.h"
namespace tongxing
{

    int CcTidlrtInference::init(const Json::Value &root)
    {
        root_ = root;

        // TX_LOG_DEBUG("cc_mnn_model_inference.cpp","debug mnn ver %s",MNN_VERSION);
        BlobData model_data;
        BlobData io_data;
        if (root_["type"].asString() == "file")
        {
            net_name = root_["filename"].asString();
            io_name = root_["ioname"].asString();
        }
        else if (root_["type"].asString() == "inside")
        {
            net_name = root_["filename"].asString();
            auto model_data_ = CcResourcDataRegister::instance().get_function(net_name);
            model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            model_data.u32Size = model_data_.first;
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug model_data size %d", model_data.u32Size );

            io_name = root_["ioname"].asString();
            auto io_data_ = CcResourcDataRegister::instance().get_function(io_name);
            io_data.pu8VirAddr = (uint8_t *)io_data_.second;
            io_data.u32Size = io_data_.first;
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug io_data size %d", io_data.u32Size );
        }
        else
        {
            cc_assert(false);
        }


        scale_initialized_ = true;
        context_ = vxCreateContext();
        if (context_ == NULL) {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp", "Failed to create OpenVX context");
        }
        int ret = vxGetStatus((vx_reference)context_);
        if (ret != 0)
        {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","debug ret %d", ret);
        }

        LoadVxModelToKernel(context_, io_data, model_data );
        tivxTIDLLoadKernels(context_);

        graph_ = vxCreateGraph(context_);
        ret = vxGetStatus((vx_reference)graph_);
        if (ret != VX_SUCCESS)
        {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","vxGetStatus  ret %d", ret);
        }
        vxSetReferenceName((vx_reference)graph_,"tidl_model_graph");
        node_ = CreateVxModelNode(graph_, vx_tensor_in_, vx_tensor_out_);
        if (!node_) {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp"," Cannot create vx_node");
            return false;
        }


        //验证 graph
        ret = vxVerifyGraph(graph_);
        if ( ret != VX_SUCCESS)
        {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp"," vxVerifyGraph  ret %d" ,ret);
        }
    
        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug init end ");
        return 0;
    }


    CcTidlrtInference::~CcTidlrtInference()
    {
       if (node_ != nullptr)
       {
            vxReleaseNode(&node_);
            node_ = nullptr;
       }

       if (graph_ != nullptr)
       {
            vxReleaseGraph(&graph_);
            graph_ = nullptr;
       }

       VxModelRemoveKernel();
       tivxTIDLUnLoadKernels(context_);

       if (context_ != nullptr) {
            vxReleaseContext(&context_);
            context_ = nullptr;
        } 
    }

    int CcTidlrtInference::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }

    int CcTidlrtInference::execute()
    {
        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug execute start");
        // TimeFunction time_f("execute","cc_tidlrt_inference.cpp");
        FillInputTensor();

        vx_status status =vxProcessGraph(graph_);
        if ( status != VX_SUCCESS)
        {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","debug---------------");
        }
        
        if (scale_initialized_) {
            TIDLScaleData(dl_args_out_);
            scale_initialized_ = false;
        }

        TensorFillOutput();
        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug execute end");
        return 0;
    }

    size_t CcTidlrtInference::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcTidlrtInference::getOutput(int index)
    {
        return output_[index];
    }



    int CcTidlrtInference::LoadVxModelToKernel(
            vx_context context, BlobData& tidl_network,
            BlobData& tidl_config) {
                
        int status = VX_SUCCESS;
        dl_config_ = ReadModelStruct(context, tidl_network);
        if (!dl_config_) {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","debug dl_config_ is null");
            return VX_FAILURE;
        }

        dl_network_ = ReadModelParams(context, tidl_config);
        if (!dl_network_) {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","debug dl_network_ is null");
            return VX_FAILURE;
        }

        dl_params_ = SetCreateParams(context);
        if (!dl_params_) {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","Cannot create dl_params_");
            return VX_FAILURE;
        }

        dl_args_in_ = SetInArgs(context);
        if (!dl_args_in_) {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","Cannot create dl_args_in_");
            return VX_FAILURE;
        }

        dl_args_out_ = SetOutArgs(context);
        if (!dl_args_out_) {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","Cannot create dl_args_out_");
            return VX_FAILURE;
        }

        kernel_ = tivxAddKernelTIDL(context, model_desc_.numInputBuf,
                                    model_desc_.numOutputBuf);
        if (!kernel_) {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","Cannot create kernel_");
            return VX_FAILURE;
        }

        CreateInputTensors(&model_desc_, context, vx_tensor_in_);
        CreateOutputTensors(&model_desc_, context, vx_tensor_out_);

        return status;
    }


    vx_user_data_object CcTidlrtInference::ReadModelStruct(
            vx_context context, BlobData& tidl_network) {

        vx_status status = VX_SUCCESS;
        vx_user_data_object config = NULL;
        vx_map_id map_id;
        FILE* fp_config;
        vx_size read_count;
        tivxTIDLJ7Params* tidl_params = nullptr;

        if (tidl_network.u32Size != sizeof(sTIDL_IOBufDesc_t)) {
            TX_LOG_FATAL("cc_tidlrt_inference.cpp","debug  tidl_network.u32Size != sizeof(sTIDL_IOBufDesc_t) ");
            return NULL;
        }

        config = vxCreateUserDataObject(context, "tivxTIDLJ7Params",
                                        sizeof(tivxTIDLJ7Params), NULL);
        status = vxGetStatus((vx_reference)config);
        if (VX_SUCCESS == status) {
            status = vxMapUserDataObject(config, 0, sizeof(tivxTIDLJ7Params), &map_id,
                                        (void**)&tidl_params, VX_WRITE_ONLY,
                                        VX_MEMORY_TYPE_HOST, 0);

            if (VX_SUCCESS == status) {
            if (tidl_params == NULL) {
                TX_LOG_FATAL("cc_tidlrt_inference.cpp"," vxMapUserDataObject ERROR");
                return NULL;
            }

            tivx_tidl_j7_params_init(tidl_params);
            memcpy(&(tidl_params->ioBufDesc), tidl_network.pu8VirAddr, tidl_network.u32Size);

            model_desc_ = tidl_params->ioBufDesc;
            vxUnmapUserDataObject(config, map_id);
            }
        }

        return config;
    }

    vx_user_data_object CcTidlrtInference::ReadModelParams(
            vx_context context, BlobData& params_file) {
            
        vx_map_id map_id;
        vx_uint32 capacity = params_file.u32Size;
        void* network_buffer = nullptr;
        // 创建用户数据对象
        vx_user_data_object network =
            vxCreateUserDataObject(context, "TIDL_network", capacity, NULL); 
        vx_status status = vxGetStatus((vx_reference)network);
        if (VX_SUCCESS == status) {
            status = vxMapUserDataObject(network, 0, capacity, &map_id,
                                        (void**)&network_buffer, VX_WRITE_ONLY,
                                        VX_MEMORY_TYPE_HOST, 0);
            if (VX_SUCCESS == status) {
                 memcpy(network_buffer, params_file.pu8VirAddr, capacity);
                //反映射
                vxUnmapUserDataObject(network, map_id);
            }
        }
        return network;
    }

    // 设置创建参数函数，用于初始化推理器
    // 参数: vx_context context - OpenVX上下文
    // 返回值: vx_user_data_object - 创建的用户数据对象
    vx_user_data_object CcTidlrtInference::SetCreateParams(vx_context context) {
  
        // 定义映射ID
        vx_map_id map_id;
        // 定义创建参数的缓冲区指针
        void* createParams_buffer = nullptr;
        // 定义容量为TIDL_CreateParams结构体大小
        vx_uint32 capacity = sizeof(TIDL_CreateParams);
        // 创建用户数据对象
        vx_user_data_object create_params =
            vxCreateUserDataObject(context, "TIDL_CreateParams", capacity, NULL);
        // 获取创建状态
        vx_status status = vxGetStatus((vx_reference)create_params);
        // 如果创建成功
        if (VX_SUCCESS == status) {
            // 映射用户数据对象以进行写操作
            status = vxMapUserDataObject(create_params, 0, capacity, &map_id,
                                        (void**)&createParams_buffer, VX_WRITE_ONLY,
                                        VX_MEMORY_TYPE_HOST, 0);

            // 如果映射成功
            if (VX_SUCCESS != status) 
            {
                TX_LOG_FATAL("cc_tidlrt_inference.cpp"," vxMapUserDataObject ERROR");
                return NULL;
            }


            // 如果缓冲区分配成功
            // 将缓冲区转换为TIDL_CreateParams结构体指针
            TIDL_CreateParams* prms = (TIDL_CreateParams*)createParams_buffer;
            // 初始化创建参数
            TIDL_createParamsInit(prms);
            // 设置参数属性
            prms->isInbufsPaded = 1;
            prms->quantRangeExpansionFactor = 1.0;
            prms->quantRangeUpdateFactor = 0.0;
            // 根据是否启用数据追踪设置日志级别
            if (enable_datatrace_) {
                prms->traceLogLevel = 1;
                prms->traceWriteLevel = 1;
            } else {
                prms->traceLogLevel = 0;
                prms->traceWriteLevel = 0;
            }
            // 解映射用户数据对象
            vxUnmapUserDataObject(create_params, map_id);
        }
        // 返回创建的用户数据对象
        return create_params;
    }

    /**
 * 设置输入参数对象
 * 
 * 此函数负责在给定的上下文中创建并初始化一个用户数据对象，该对象包含了模型推理所需的输入参数
 * 它首先计算输入参数的大小，然后创建一个用户数据对象并尝试映射它以填充必要的数据
 * 如果映射成功，它将初始化输入参数的结构，并在完成后再将对象解除映射
 * 
 * @param context VX框架的上下文，用于创建用户数据对象
 * @return 返回创建的用户数据对象，如果创建过程中出现错误，则该对象可能不是有效的
 */
    vx_user_data_object CcTidlrtInference::SetInArgs(vx_context context) {
        // 初始化状态变量和输入参数对象
        vx_status status;
        vx_user_data_object inArgs;
        vx_map_id map_id;
        vx_uint32 capacity;
        void* inArgs_buffer = NULL;

        // 计算输入参数的容量
        capacity = sizeof(TIDL_InArgs);
        // 创建用户数据对象
        inArgs = vxCreateUserDataObject(context, "TIDL_InArgs", capacity, NULL);
        // 获取创建状态
        status = vxGetStatus((vx_reference)inArgs);
        // 如果创建成功，尝试映射对象以填充数据
        if (VX_SUCCESS != status) {
            TX_LOG_FATAL("cc_tidlrt_inference.cpp"," vxCreateUserDataObject ERROR");
            return NULL;
        }
        status = vxMapUserDataObject(inArgs, 0, capacity, &map_id,
                                        (void**)&inArgs_buffer, VX_WRITE_ONLY,
                                        VX_MEMORY_TYPE_HOST, 0);
            // 如果映射成功，初始化输入参数
        if (VX_SUCCESS != status) {
            TX_LOG_FATAL("cc_tidlrt_inference.cpp"," vxMapUserDataObject ERROR");
            return NULL;
        }
           
        TIDL_InArgs* prms = (TIDL_InArgs*)inArgs_buffer;
        prms->iVisionInArgs.size = sizeof(TIDL_InArgs);
        prms->iVisionInArgs.subFrameInfo = 0; // 默认为0
          
        // 不再需要映射，解除映射
        vxUnmapUserDataObject(inArgs, map_id);
        // 返回创建的用户数据对象
        return inArgs;
    }


    vx_user_data_object CcTidlrtInference::SetOutArgs(vx_context context) {
        vx_status status;
        vx_user_data_object outArgs;
        vx_map_id map_id;
        vx_uint32 capacity;
        void* outArgs_buffer = NULL;

        capacity = sizeof(TIDL_outArgs);
        outArgs = vxCreateUserDataObject(context, "TIDL_outArgs", capacity, NULL);
        status = vxGetStatus((vx_reference)outArgs);
        if (VX_SUCCESS != status) {
             TX_LOG_FATAL("cc_tidlrt_inference.cpp"," vxCreateUserDataObject ERROR");
            return NULL;
        }
        status = vxMapUserDataObject(outArgs, 0, capacity, &map_id,
                                        (void**)&outArgs_buffer, VX_WRITE_ONLY,
                                        VX_MEMORY_TYPE_HOST, 0);
        if (VX_SUCCESS != status) {
            TX_LOG_FATAL("cc_tidlrt_inference.cpp"," vxMapUserDataObject ERROR");
            return NULL;
        }

        TIDL_outArgs* prms = (TIDL_outArgs*)outArgs_buffer;
        prms->iVisionOutArgs.size = sizeof(TIDL_outArgs);
        vxUnmapUserDataObject(outArgs, map_id);
        return outArgs;
    }


    /**
 * @brief 生成输出参数对象
 * 
 * 本函数负责创建并填充输出参数对象outArgs，该对象用于存储推理输出数据
 * 它首先创建一个用户数据对象，然后映射该对象到主机内存中进行数据填充，
 * 填充完成后解除映射并返回该对象
 * 
 * @return vx_user_data_object 返回创建的输出参数对象，如果创建失败则返回nullptr
 */
    vx_user_data_object CcTidlrtInference::GenerateOutArgs()  {
    // 创建输出参数对象
        vx_status status;
        vx_user_data_object outArgs;
        // 映射ID，用于标识映射的用户数据对象
        vx_map_id map_id;
        // 定义输出参数对象的容量
        vx_uint32 capacity = sizeof(TIDL_outArgs);
        // 指向输出参数对象的缓冲区
        void* outArgs_buffer = NULL;

        // 创建用户数据对象，用于存储输出参数
        outArgs = vxCreateUserDataObject(context_, "TIDL_outArgs", capacity, NULL);

        status = vxGetStatus((vx_reference)outArgs);
        if (VX_SUCCESS != status) {
            TX_LOG_FATAL("cc_tidlrt_inference.cpp"," vxCreateUserDataObject ERROR");
            return NULL;
        }

        // 映射用户数据对象到主机内存，以便填充数据
        status = vxMapUserDataObject(outArgs, 0, capacity, &map_id,
                                            (void**)&outArgs_buffer, VX_WRITE_ONLY,
                                            VX_MEMORY_TYPE_HOST, 0);
        if (VX_SUCCESS != status) {
            TX_LOG_FATAL("cc_tidlrt_inference.cpp"," vxMapUserDataObject ERROR");
            return NULL;
        }

        TIDL_outArgs* prms = (TIDL_outArgs*)outArgs_buffer;
        prms->iVisionOutArgs.size = sizeof(TIDL_outArgs);
            // 解除用户数据对象的映射
        vxUnmapUserDataObject(outArgs, map_id);
            // 返回创建的输出参数对象
        return outArgs;
    }

    
/**
 * @brief 创建输入张量
 * 
 * 根据模型描述和IO缓冲区描述，为模型推理创建所需的输入张量
 * 
 * @param io_desc IO缓冲区描述符，包含输入数据的布局和格式信息
 * @param context VX框架上下文，用于创建VX对象
 * @param input_tensors 输出参数，用于存储创建的输入张量数组
 * @return vx_status 返回状态码，表示创建是否成功
 */

    vx_status CcTidlrtInference::CreateInputTensors(sTIDL_IOBufDesc_t* io_desc,
                                                    vx_context context,
                                                    vx_tensor* input_tensors) {
        // 遍历每个输入缓冲区，创建相应的输入张量
        for (uint32_t id = 0; id < (uint32_t)model_desc_.numInputBuf; ++id) {
            // 日志输出：记录每个输入缓冲区的尺寸和通道数
            input_w = model_desc_.inWidth[id];
            input_h = model_desc_.inHeight[id];
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp", "inWidth=%d, inHeight=%d, inChannels=%d",input_w,
                         input_h, model_desc_.inNumChannels[id]);
            // 日志输出：记录每个输入缓冲区的数据名称和ID
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","sTIDL_IOBufDesc_t inDataName=%d, inDataId= %d",
                    model_desc_.inDataName[id], model_desc_.inDataId[id]);

            // 根据元素类型获取张量数据类型
            vx_size data_type = GetTensorDataType(model_desc_.inElementType[id]);
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","data_type %d", data_type);
       
            // 获取输入张量的尺寸
            vx_size input_sizes[APP_MAX_TENSOR_DIMS];
            GetInputSize(input_sizes, io_desc, id);
            // 日志输出：记录输入张量尺寸
            TX_LOG_INFO("cc_tidlrt_inference.cpp","input_sizes={%d}x{%d}x{%d}", input_sizes[0], input_sizes[1],
                    input_sizes[2]);
          
            input_tensors[id] = vxCreateTensor(context, 3, input_sizes, data_type, 0);  
        }

        // 返回成功
        return VX_SUCCESS;
    }

    vx_status CcTidlrtInference::CreateOutputTensors(
            sTIDL_IOBufDesc_t* io_buffer_descriptor, vx_context context,
                                                vx_tensor* output_tensors) {

        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","sTIDL_IOBufDesc_t numOutputBuf={%d}",model_desc_.numOutputBuf);
        out_shape.clear();
        for (uint32_t id = 0; id < (uint32_t)model_desc_.numOutputBuf; ++id) {

            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","sTIDL_IOBufDesc_t outWidth={%d}, outHeight={%d}, outChannels={%d}",
                             model_desc_.outWidth[id], model_desc_.outHeight[id], model_desc_.outNumChannels[id]);
            // 日志输出：记录当前输出缓冲区的数据名称和ID
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","sTIDL_IOBufDesc_t outDataName={%s}, outDataId={%d}",
                    model_desc_.outDataName[id], model_desc_.outDataId[id]);

            // 获取当前输出缓冲区的数据类型
            vx_size data_type = GetTensorDataType(model_desc_.outElementType[id]);

            // 计算当前输出缓冲区的数据长度
            int data_length = model_desc_.outHeight[id] * model_desc_.outWidth[id] *
                            model_desc_.outNumChannels[id];
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","output data_length={%d}", data_length);
            // 将数据长度添加到输出结果长度列表中

            // 准备存储输出张量尺寸的数组
            vx_size output_sizes[APP_MAX_TENSOR_DIMS];
            // 获取当前输出缓冲区的尺寸信息
            GetOutputSize(output_sizes, &model_desc_, id);
            // 日志输出：记录输出张量的尺寸
            std::vector<int> tmp_shape;
            tmp_shape.push_back(1);
            tmp_shape.push_back(output_sizes[2]);
            tmp_shape.push_back(output_sizes[1]);
            tmp_shape.push_back(output_sizes[0]);
            out_shape.push_back(tmp_shape);

            std::shared_ptr<NumArray> num_output = creat_numarray(tmp_shape, NumArray::DataType::FLOAT32);
            output_.push_back(num_output);
            TX_LOG_INFO("cc_tidlrt_inference.cpp","output_sizes={%d}x{%d}x{%d}", output_sizes[0], output_sizes[1],
                    output_sizes[2]);

            output_tensors[id] = vxCreateTensor(context, 3, output_sizes, data_type, 0);
        }

        // 如果所有输出缓冲区的张量创建成功，返回成功状态
        return VX_SUCCESS;
    }

    vx_size CcTidlrtInference::GetTensorDataType(vx_int32 tidl_type) {
        vx_size openvx_type = VX_TYPE_INVALID;
        if (tidl_type == TIDL_UnsignedChar) {
            openvx_type = VX_TYPE_UINT8;
        } else if (tidl_type == TIDL_SignedChar) {
            openvx_type = VX_TYPE_INT8;
        } else if (tidl_type == TIDL_UnsignedShort) {
            openvx_type = VX_TYPE_UINT16;
        } else if (tidl_type == TIDL_SignedShort) {
            openvx_type = VX_TYPE_INT16;
        } else if (tidl_type == TIDL_UnsignedWord) {
            openvx_type = VX_TYPE_UINT32;
        } else if (tidl_type == TIDL_SignedWord) {
            openvx_type = VX_TYPE_INT32;
        } else if (tidl_type == TIDL_SinglePrecFloat) {
            openvx_type = VX_TYPE_FLOAT32;
        }

        return openvx_type;
    }

    vx_tensor CcTidlrtInference::CreateInputTensor(vx_size input_sizes[],
                                             vx_size data_type)  {


        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug");                               
        vx_tensor input_tensor =vxCreateTensor(context_, 3, input_sizes, data_type, 0);

        vx_status status = vxGetStatus((vx_reference)input_tensor);
        if (VX_SUCCESS != status) {
            TX_LOG_FATAL("cc_tidlrt_inference.cpp"," vxCreateTensor ERROR");
            return NULL;
        }

        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug");

        vx_size start[4] = {0, 0, 0, 0};
        vx_map_id map_id_input;
        vx_size input_strides[TC_MAX_TENSORS_D];
        void* input_buffer = nullptr;
        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug");
        status = tivxMapTensorPatch(input_tensor, 3, start, input_sizes,
                                            &map_id_input, input_strides, &input_buffer,
                                            VX_WRITE_ONLY, VX_MEMORY_TYPE_NONE);
        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug");
        if (VX_SUCCESS != status) {
            TX_LOG_FATAL("cc_tidlrt_inference.cpp"," vxMapUserDataObject ERROR");
            return NULL;
        }
        // TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug %d, %d, %d", input_sizes[0], input_sizes[1], input_sizes[2]);                             
        memset(input_buffer, 0, input_sizes[0] * input_sizes[1] *  input_sizes[2]);
        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug");
        tivxUnmapTensorPatch(input_tensor, map_id_input);
        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug");
        return input_tensor;
    }


    void CcTidlrtInference::GetInputSize(vx_size* input_sizes,
                                   sTIDL_IOBufDesc_t* io_desc, int id) {
        input_sizes[0] =
            io_desc->inWidth[id] + io_desc->inPadL[id] + io_desc->inPadR[id];
        input_sizes[1] =
            io_desc->inHeight[id] + io_desc->inPadT[id] + io_desc->inPadB[id];
        input_sizes[2] = io_desc->inNumChannels[id];
    }



    vx_status CcTidlrtInference::TIDLScaleData(vx_user_data_object outArgs) {
        vx_status status = vxGetStatus((vx_reference)outArgs);
        if ((vx_status)VX_SUCCESS != status) {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","Unable to map dl_outArgs!");
            status = (vx_status)VX_FAILURE;
        }
        void* outargs_buffer = nullptr;
        vx_map_id map_id = 0;
        status = vxMapUserDataObject(outArgs, 0, sizeof(TIDL_outArgs), &map_id,
                                    (void**)&outargs_buffer, VX_WRITE_ONLY,
                                    VX_MEMORY_TYPE_HOST, 0);

        if (VX_SUCCESS != status) {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","Unable to map dl_outArgs!");
            status = (vx_status)VX_FAILURE;
        }
        if (outargs_buffer) {
            TIDL_outArgs* prms = (TIDL_outArgs*)outargs_buffer;
            for (int id = 0; id < prms->numOutBufs; ++id) {
            // CALMCAR_INFO_C(PERCEPTION, "scale: {}", prms->scale[id]);
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","scale debug %0.2f ",  prms->scale[id] );
            output_scales_.push_back(1.0f / prms->scale[id]);
            }
        }
        vxUnmapUserDataObject(outArgs, map_id);

        return status;
    }

    void CcTidlrtInference::CopyOutputToHost() {
        vx_size output_sizes[4] = {0};

        for (int id = 0; id < model_desc_.numOutputBuf; ++id) {
            std::vector<size_t> paddings{};
            std::vector<size_t> dims{};
            vx_size data_type = GetTensorDataType(model_desc_.outElementType[id]);
            GetOutputSize(output_sizes, &model_desc_, id);
            vx_size start[4] = {0};
            vx_size output_strides[4] = {0};
            void* output_buffer = nullptr;
            vx_map_id map_id = 0;
            tivxMapTensorPatch(vx_tensor_out_[id], 3, start, output_sizes, &map_id,
                            output_strides, &output_buffer, VX_READ_ONLY,
                            VX_MEMORY_TYPE_HOST);

       
            bool dequantization = false;
            float* scale{nullptr};
            scale = &output_scales_[id];
            std::string output_name = (char*)model_desc_.outDataName[id];
            memcpy(output_[id]->data, output_buffer, 11111 * sizeof(float));
            tivxUnmapTensorPatch(vx_tensor_out_[id], map_id);
        }

    }

    void CcTidlrtInference::GetOutputSize(vx_size* output_sizes,
                                    sTIDL_IOBufDesc_t* io_desc, int id) {
        output_sizes[0] =
            io_desc->outWidth[id] + io_desc->outPadL[id] + io_desc->outPadR[id];
        output_sizes[1] =
            io_desc->outHeight[id] + io_desc->outPadT[id] + io_desc->outPadB[id];
        output_sizes[2] = io_desc->outNumChannels[id];
    }

    /**
     * @brief 创建Vx模型节点
     * 
     * 本函数负责在给定的图形中创建一个Vx节点，该节点用于执行模型推理。
     * 它接受输入和输出张量数组，并根据是否启用数据跟踪来配置节点参数。
     * 
     * @param graph 图形对象，用于将节点添加到计算图中
     * @param _tensor_in 输入张量数组，包含模型推理所需的输入数据
     * @param _tensor_out 输出张量数组，用于存储模型推理结果
     * @return vx_node 返回创建的Vx节点，如果创建失败则返回nullptr
     */

    vx_node CcTidlrtInference::CreateVxModelNode(vx_graph graph,
                                                vx_tensor _tensor_in[],
                                                vx_tensor _tensor_out[]) {


        vx_reference params[TIVX_KERNEL_TIDL_NUM_BASE_PARAMETERS];

        // 设置节点参数，包括配置、网络、创建参数、输入参数和输出参数
        params[TIVX_KERNEL_TIDL_IN_CONFIG_IDX] = (vx_reference)dl_config_;
        params[TIVX_KERNEL_TIDL_IN_NETWORK_IDX] = (vx_reference)dl_network_;
        params[TIVX_KERNEL_TIDL_IN_CREATE_PARAMS_IDX] = (vx_reference)dl_params_;
        params[TIVX_KERNEL_TIDL_IN_IN_ARGS_IDX] = (vx_reference)dl_args_in_;
        params[TIVX_KERNEL_TIDL_IN_OUT_ARGS_IDX] = (vx_reference)dl_args_out_;
        params[TIVX_KERNEL_TIDL_IN_TRACE_DATA_IDX] = (vx_reference)NULL;


        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug");
        // 创建TIDL节点，如果创建失败则返回nullptr
        node_ = tivxTIDLNode(graph_, kernel_, params, _tensor_in, _tensor_out);
        if (!node_) {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","Cannot create vx_node!");
            return nullptr;
        }

        vxSetNodeTarget(node_, VX_TARGET_STRING, TIVX_TARGET_DSP_C7_1);
        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug");
        // 为相关引用设置名称，便于调试和日志记录

        vx_status status = vxSetReferenceName((vx_reference)dl_config_, "tidl_config");
        if (status != VX_SUCCESS)
        {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","vxSetReferenceName !!!!!");
            return nullptr;
        }
        
        status = vxSetReferenceName((vx_reference)dl_network_, "tidl_network");
         if (status != VX_SUCCESS)
        {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","vxSetReferenceName !!!!!");
            return nullptr;
        }
        
        status =vxSetReferenceName((vx_reference)dl_params_, "tidl_createparams");
         if (status != VX_SUCCESS)
        {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","vxSetReferenceName !!!!!");
            return nullptr;
        }
        status = vxSetReferenceName((vx_reference)dl_args_in_,"tidl_inargs");
         if (status != VX_SUCCESS)
        {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","vxSetReferenceName !!!!!");
            return nullptr;
        }
        status = vxSetReferenceName((vx_reference)dl_args_out_,"tidl_outargs");
         if (status != VX_SUCCESS)
        {
            TX_LOG_ERROR("cc_tidlrt_inference.cpp","vxSetReferenceName !!!!!");
            return nullptr;
        }

        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug");
        // 为输入张量设置名称
        for (uint32_t id = 0; id < model_desc_.numInputBuf; ++id) {
            std::string input_name =  "tidl_intensor_"+std::to_string(id);
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug  in  %s " , input_name.c_str());
            status = vxSetReferenceName((vx_reference)_tensor_in[id], input_name.c_str());
            if (status != VX_SUCCESS)
            {
                TX_LOG_ERROR("cc_tidlrt_inference.cpp","vxSetReferenceName !!!!!");
                return nullptr;
            }
        }

        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug");
        // 为输出张量设置名称
        for (uint32_t id = 0; id < model_desc_.numOutputBuf; ++id) {
            
            std::string output_name =  "tidl_outtensor_"+std::to_string(id);
            TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug  out  %s " , output_name.c_str());
            status = vxSetReferenceName((vx_reference)_tensor_out[id], output_name.c_str());
            if (status != VX_SUCCESS)
            {
                TX_LOG_ERROR("cc_tidlrt_inference.cpp","vxSetReferenceName !!!!!");
                return nullptr;
            }
        }

        TX_LOG_DEBUG("cc_tidlrt_inference.cpp","debug");
        // 为内核引用设置名称
        status = vxSetReferenceName((vx_reference)kernel_, "tidl_kernel");
        if (status != VX_SUCCESS)
            {
                TX_LOG_ERROR("cc_tidlrt_inference.cpp","vxSetReferenceName !!!!!");
                return nullptr;
            }
    
        status = vxSetReferenceName((vx_reference)node_, "tidl_node");
        if (status != VX_SUCCESS)
            {
                TX_LOG_ERROR("cc_tidlrt_inference.cpp","vxSetReferenceName !!!!!");
                return nullptr;
            }


        return node_;
    }

    void CcTidlrtInference::VxModelRemoveKernel() {
        if (kernel_ != nullptr) {
            vx_status status = vxRemoveKernel(kernel_);
            if (status != (vx_status)VX_SUCCESS) {
                TX_LOG_ERROR("cc_tidlrt_inference.cpp","Unable to remove kernel: %d", status);
            }
            kernel_ = nullptr;
        }

        if (dl_config_ != nullptr) {
            vxReleaseUserDataObject(&dl_config_);
            dl_config_ = nullptr;
        }

        if (dl_network_ != nullptr) {
            vxReleaseUserDataObject(&dl_network_);
            dl_network_ = nullptr;
        }

        if (dl_params_ != nullptr) {
            vxReleaseUserDataObject(&dl_params_);
            dl_params_ = nullptr;
        }

        if (dl_args_in_ != nullptr) {
            vxReleaseUserDataObject(&dl_args_in_);
            dl_args_in_ = nullptr;
        }

        if (dl_args_out_ != nullptr) {
            vxReleaseUserDataObject(&dl_args_out_);
            dl_args_out_ = nullptr;
        }

        if (enable_datatrace_) {
            // if (dl_debugger_ != nullptr) {
            // vxReleaseUserDataObject(&dl_debugger_);
            // dl_debugger_ = nullptr;
            // }
        }

        uint32_t num_input_tensors = model_desc_.numInputBuf;
        for (uint32_t id = 0; id < num_input_tensors; ++id) {
            if (vx_tensor_in_[id] != nullptr) {
                vxReleaseTensor(&vx_tensor_in_[id]);
                vx_tensor_in_[id] = nullptr;
            }
        }

        uint32_t num_output_tensors = model_desc_.numOutputBuf;
        for (uint32_t id = 0; id < num_output_tensors; ++id) {
            if (vx_tensor_out_[id] != nullptr) {
                vxReleaseTensor(&vx_tensor_out_[id]);
                vx_tensor_out_[id] = nullptr;
            }
        }

        return;
    }


    void CcTidlrtInference::FillInputTensor()
    {
        for (int32_t id = 0; id < model_desc_.numInputBuf; ++id)
        {
            vx_size input_sizes[TC_MAX_TENSORS_D];
            vx_size start[4] = {0};
            vx_map_id map_id_input;
            vx_size input_strides[TC_MAX_TENSORS_D];
            void* input_buffer = nullptr;

            GetInputSize(input_sizes, &model_desc_, id);
            vx_size data_type = GetTensorDataType(model_desc_.inElementType[id]);

            tivxMapTensorPatch(vx_tensor_in_[id], 1, start, input_sizes,
                                    &map_id_input, input_strides, &input_buffer,
                                    VX_WRITE_ONLY, VX_MEMORY_TYPE_NONE);

            // 获取当前输入张量的数据类型
            int padL = model_desc_.inPadL[id];
            int padR = model_desc_.inPadR[id];
            int padT = model_desc_.inPadT[id];
            int padB = model_desc_.inPadB[id];

            vx_uint8* tmp_model_data = (vx_uint8*)input_buffer + input_sizes[0]*padT + padL ;
            vx_uint8* img_data = (vx_uint8*)input_[id]->data;

            for (size_t i = 0; i < input_h; i++)
            {
                memcpy(tmp_model_data, img_data, input_w*data_type);
                img_data += input_w;
                tmp_model_data += input_sizes[0];
            }

            tivxUnmapTensorPatch(vx_tensor_in_[id], map_id_input);
        }  
    }

    void CcTidlrtInference::TensorFillOutput()
    {
        vx_size output_sizes[4] = {0};
        for (size_t id = 0; id < model_desc_.numOutputBuf ; id++)
        {
            vx_size start[4] = {0};
            vx_map_id map_id = 0;
            vx_size output_strides[4] = {0};
            void* output_buffer = nullptr;
            vx_size data_type = GetTensorDataType(model_desc_.outElementType[id]);
            GetOutputSize(output_sizes, &model_desc_, id);

            vx_status status = vxGetStatus((vx_reference)vx_tensor_out_[id]);
            if (status != VX_SUCCESS)
            {
                TX_LOG_DEBUG("cc_tidlrt_inference.cpp"," get output tensot status error");
            }
            
            tivxMapTensorPatch(vx_tensor_out_[id], 3, start, output_sizes, &map_id,
                       output_strides, &output_buffer, VX_READ_ONLY,
                       VX_MEMORY_TYPE_HOST);

            vx_int16 *pOut;
            int padL = model_desc_.outPadL[id];
            int padT = model_desc_.outPadT[id];
            int outwindth = model_desc_.outWidth[id];
            int outheight = model_desc_.outHeight[id];
            int outchannel = model_desc_.outNumChannels[id];

            pOut = (vx_int16 *)output_buffer + (padT * output_sizes[0]) + padL;

            float* output_temp = (float*)output_[id]->data;
            for (size_t i = 0; i <  outwindth * outheight * outchannel  ; i++)
            {
                float tmp = pOut[i] * output_scales_[id];
                output_temp[i] = tmp;
            }
            
            tivxUnmapTensorPatch(vx_tensor_out_[id], map_id);
        }
        
    }


    REGISTER_CC_MODULE(tidlrt_inference, CcTidlrtInference)

} // namespace tongxing
