#include <iostream>
#include "tx_oax4600_inference.h"
#include "libovt.h"
#include "libcnnapi.h"
#include <stdio.h>
#include "cc_resource_register.h"
#define OVM_ALIGN(a) ((void *)((((uintptr_t)(a)) + (256 - 1)) & ~(256 - 1)))
namespace tongxing
{
    static FILE * f_ini(const char * fname, size_t * size)
    {
        FILE * f;

        f = fopen(fname, "rb");
        if (!f) {
            fprintf(stderr, "%s: Err, open '%s' err\n", __func__, fname);
            return NULL;
        }
        if (fseek(f, 0, SEEK_END)) {
            fprintf(stderr, "%s: Err: fseek end '%s'\n", __func__, fname);
            fclose(f);
            return NULL;
        }
        *size = ftell(f);
        rewind(f);
        //	printf("Info: '%s' size is %zu\n", fname, *size);
        return f;
    }
    static void * load_ovm(const char * fname, size_t * size)
    {
        void * ovm_addr;
        FILE * f;
        size_t fsize, sz;

        f = f_ini(fname, &fsize);
        if (!f) {
            return NULL;
        }

        //make ovm buffer size cache line aligned
        sz = (size_t)OVM_ALIGN(fsize);
        if (!sz) {
            fprintf(stderr, "%s(%s): Err: sz=%zu f=%zu\n", __func__,
                    fname, sz, fsize);
            goto l_err;
        }
        ovm_addr = ovt_cma_alloc(sz);
        if (!ovm_addr) {
            fprintf(stderr, "%s(%s): Err: cnn_get_ovmbuf %zu failed\n",
                    __func__, fname, sz);
            goto l_err;
        }

        sz = fread(OVM_ALIGN(ovm_addr), 1, fsize, f);
        if (sz != fsize) {
            fprintf(stderr, "%s(%s): Err: read size %zu != %zu\n",
                    __func__, fname, fsize, sz);
            ovt_cma_free(ovm_addr);
        l_err:
            fclose(f);
            return NULL;
        }

        fclose(f);

        if (size) {
            *size = fsize;
        }

        return ovm_addr;
    }

    int TXOAX4600Inference::init(const Json::Value& root){
        int ret=0;
        root_=root;
        if(root_["norm"].isDouble()){
            norm=root_["norm"].asFloat();
        }
        if(root_["std"].isArray()){
            int array_size=root_["std"].size();
            for(int i=0;i<array_size&&i<3;i++){
                std[i]=root_["std"][i].asFloat();
            }
        }
        if(root_["mean"].isArray()){
            int array_size=root_["mean"].size();
            for(int i=0;i<array_size&&i<3;i++){
                mean[i]=root_["mean"][i].asFloat();
            }
        }
        if (root_["type"].asString() == "file"){
            net_name = root_["filename"].asString();
            ovm_data_ptr=load_ovm(net_name.c_str(),&ovm_model_size);
        }
           
        else if (root_["type"].asString() == "inside")
        {
            net_name = root_["filename"].asString();
            auto model_data_ =
                CcResourcDataRegister::instance().get_function(root_["filename"].asString());
            ovm_data_ptr=ovt_cma_alloc((size_t)OVM_ALIGN(model_data_.first));
            ovm_model_size=model_data_.first;
            memcpy(OVM_ALIGN(ovm_data_ptr),model_data_.second,ovm_model_size);
            // model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            // model_data.u32Size = model_data_.first;
        }
        else
        {
            cc_assert(false);
        }
        if(ovm_data_ptr==NULL){
        fprintf(stderr, "%s(%s): Err: load ovm model file %zu failed\n",
                    __func__, net_name.c_str());
            return -1;
        }
        cnn3_load_t load[1];
        cnn_std_t std_param = { norm, mean, std};
        load[0].std = &std_param;
        load[0].csc_mode = CNN_CSC_NONE;
        hdl = cnn3_load_persistent_output((char*)OVM_ALIGN(ovm_data_ptr), ovm_model_size,
                                        (void **)NULL, NULL, NULL, 0, 0, load);
        if(hdl==NULL){
            fprintf(stderr, "%s(%s): Err: load ovm model %zu failed\n",
                    __func__, net_name.c_str());
            goto free_ovm_data_;
        }
        
        ret=cnn_get_output_list(hdl,&output_count,&tid);
        // printf(" ===============================%s :: output_count=%d\n",net_name.c_str(),output_count);
        if(ret!=0){
            fprintf(stderr, "%s(%s): Err: %s cnn_get_output_list failed\n",
                    __func__, net_name.c_str());
        }
        flag_init=true;
        return 0;
unload_ovm_model:
    cnn_unload(hdl);
    hdl=NULL;
free_ovm_data_:
    ovt_cma_free(ovm_data_ptr);
    ovm_data_ptr=NULL;
    ovm_model_size=0;
    return ret;
    }
    int TXOAX4600Inference::deinit(){
        if(flag_init){
            cnn_unload(hdl);
            hdl=NULL;
            ovt_cma_free(ovm_data_ptr);
            ovm_data_ptr=NULL;
            ovm_model_size=0;
            flag_init=false;
        }
        return 0;
    }
    TXOAX4600Inference::~TXOAX4600Inference(){
        deinit();
    }
    int TXOAX4600Inference::setInput(const std::vector<std::shared_ptr<NumArray>>& in){
        input_=in;
        return 0;
    }
    int TXOAX4600Inference::execute(){
        // printf(" ===============================%s :: TXOAX4600Inference::execute start\n",net_name.c_str());
        void * input_addr=input_[0]->data;
		int ret=cnn3_inference_with_outputs_f(hdl, &input_addr, output_count,
		                              output_f, element_count);
        if(ret!=0){

            return ret;
        }
        std::vector<std::shared_ptr<NumArray>> outputs;
        for(int i=0;i<output_count;i++){
            std::shared_ptr<NumArray> output(new NumArray);
            int32_t  channel=0;
            int32_t  height=0;
            int32_t  width=0;
            
            cnn_get_output_shape(hdl,tid[i],&channel,&height,&width);
            // printf(" ===============================%s :: output_count=%d i=%d channel=%d height=%d width=%d\n",net_name.c_str(),output_count,i,channel,height,width);
            output->word_size=sizeof(float);
            output->shape.push_back(1);
            output->shape.push_back(channel);
            output->shape.push_back(height);
            output->shape.push_back(width);
            output->data=(unsigned char*)output_f[i];
            outputs.push_back(output);
        }
        output_=outputs;
        // printf(" ===============================%s :: TXOAX4600Inference::execute end\n",net_name.c_str());
        return 0;
    }
    size_t TXOAX4600Inference::getOutputNum(){
        return output_.size();
    }
    std::shared_ptr<NumArray> TXOAX4600Inference::getOutput(int index){
        return output_[index];
    }
    REGISTER_CC_MODULE(oax4600_inference, TXOAX4600Inference)
}  // namespace tongxing