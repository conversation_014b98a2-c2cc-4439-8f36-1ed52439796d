#ifndef _TX_OAX4600_INFERENCE_H_
#define _TX_OAX4600_INFERENCE_H_
#include <memory>
#include "cc_module.h"
namespace tongxing {
class TXOAX4600Inference : public CcModule {
  public:
    int init(const Json::Value& root);
    const std::vector<std::shared_ptr<CcTensor<float>>>& getInputTensor();
    int setInputTensor(std::vector<std::shared_ptr<CcTensor<float>>>& input_tensor);
    int deinit();
    ~TXOAX4600Inference();
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:


    Json::Value root_;

    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;
    std::string net_name;

    size_t ovm_model_size=0;
    void* ovm_data_ptr=NULL;
    void* hdl=NULL;

    float norm=255.0;
    float std[3]={0,0,0};
    float mean[3]={1,1,1};

    int32_t*  tid;
    float * output_f[64];
    size_t element_count[64];
    size_t output_count=0;
    bool flag_init=false;
};

}  // namespace tongxing

#endif
