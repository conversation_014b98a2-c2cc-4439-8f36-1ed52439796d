#ifndef __CC_AMBA_INFERENCE_H__
#define __CC_AMBA_INFERENCE_H__
#include "cc_module.h"
#include "json.h"
extern "C"
{
#include "cvapi_ambacv_flexidag.h"
#include "cvapi_flexidag.h"
#include "idsp_roi_msg.h"
#include "cvapi_memio_interface.h"
}
namespace tongxing{

    
    #define CC_CACHE_ALIGN(x)              ((x + 0x3F)& ~0x3F)
    #define CACHE_ALIGN(x)              ((x + 0x3F)& ~0x3F)
    static inline uint32_t CC_ALIGN128(uint32_t X) {return ((X + 127U) & 0xFFFFFF80U);}

    

    class tx_amba_model_init{
        public:
        tx_amba_model_init();
        ~tx_amba_model_init();
        int init(const std::string &path_name , int width, int height);
        int DagUtil_InitDag(flexidag_memblk_t *temp_buf);
        int Algo_0_Cfg();
        int Flexidag_Run(std::vector<std::shared_ptr<NumArray>> &in, std::vector<std::shared_ptr<NumArray>> &out);

        uint32_t size_align;

        flexidag_memblk_t bin_buf;
        AMBA_CV_FLEXIDAG_HANDLE_s fd_gen_handle;
        AMBA_CV_FLEXIDAG_INIT_s    init_amba;
        AMBA_CV_FLEXIDAG_IO_s      in;
        flexidag_memblk_t image_buff;
        AMBA_CV_FLEXIDAG_IO_s      out;
        amba_roi_config_t RoiCfg;
        AMBA_CV_FLEXIDAG_IO_s AMBA_Resultl_;
        
        int input_width;
        int input_height;

    };





    
    class CcAmbaFiexiInference :public CcModule{
    public:
        int init(const Json::Value& config);
        int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
        ~CcAmbaFiexiInference();
    private:
        std::vector<std::shared_ptr<NumArray>> input_;
        std::vector<std::shared_ptr<NumArray> > output_;
        
       
        uint8_t flag_init=false;
        // size_t* tensor_shape=NULL;
        int tensor_c = 0;
        int tensor_h = 0;
        int tensor_w = 0;
        bool flag_crop_ = false;
        Json::Value root_;
        long image_mun_=0;
        std::vector<std::vector<int>> out_size;

        std::string model_path;
        std::string model_name;
        std::shared_ptr<tx_amba_model_init> amba_model;
       
       

    public:
        
    };
}


#endif