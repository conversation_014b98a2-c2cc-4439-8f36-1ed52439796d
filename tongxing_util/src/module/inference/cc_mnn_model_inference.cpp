#include "cc_mnn_model_inference.h"
#include "CalmCarLog.h"
#include "cc_assert.h"
#include "cc_resource_register.h"
// #include "MNN/Backend.hpp"
#include <iostream>
#include "time_profile.h"
#include "time_function.h"
#include <unistd.h>
// #include "cnpy.h"
namespace tongxing
{
    void delete_tensor_fun(void *ptr)
    {
        delete ((MNN::Tensor *)ptr);
    }
    static std::function<void(void *)> release_tensor_fun = delete_tensor_fun;
    int CcMnnModelInference::init(const Json::Value &root)
    {
        root_ = root;

        TX_LOG_DEBUG("cc_mnn_model_inference.cpp","debug mnn ver %s",MNN_VERSION);
        BlobData model_data;
        if (root_["type"].asString() == "file")
        {
            net_name = root_["filename"].asString();
            cc_assert(model_data.loadFromFile(root_["filename"].asString()) == 0);
        }
        else if (root_["type"].asString() == "inside")
        {
            net_name = root_["filename"].asString();
            auto model_data_ =
                CcResourcDataRegister::instance().get_function(root_["filename"].asString());
            model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            model_data.u32Size = model_data_.first;
        }
        else
        {
            cc_assert(false);
        }

        net_ptr = std::shared_ptr<MNN::Interpreter>(
            MNN::Interpreter::createFromBuffer(model_data.pu8VirAddr, model_data.u32Size));
        // net_ptr = std::shared_ptr<MNN::Interpreter>(
        //     MNN::Interpreter::createFromFile(root_["filename"].asString().c_str()));

        // 根据配置参数初始化mnn类型
        if (root_.isMember("mnn_type"))
            mnn_type = (MNNForwardType)root_["mnn_type"].asInt();

        MNN::ScheduleConfig netConfig;
        MNN::BackendConfig backendConfig; //默认为normal

        netConfig.type = mnn_type;

        if(mnn_type==MNN_FORWARD_CPU){
            if (root_.isMember("thread_num"))
            {
                netConfig.numThread = root_["thread_num"].asInt();
            }
            else
            {
                netConfig.numThread = 1;
            }
        }
        else if (mnn_type == MNN_FORWARD_OPENCL || mnn_type == MNN_FORWARD_VULKAN || mnn_type == MNN_FORWARD_OPENGL){
            netConfig.mode = MNN_GPU_TUNING_NONE;
        }
        TX_LOG_DEBUG("cc_mnn_model_inference.cpp","debug model ver %s" , net_ptr->getModelVersion());
        if (mnn_type != MNN_FORWARD_OPENCL) {
            backendConfig.memory = MNN::BackendConfig::MemoryMode::Memory_Normal;
            backendConfig.power = MNN::BackendConfig::PowerMode::Power_Normal;
            backendConfig.precision = MNN::BackendConfig::PrecisionMode::Precision_High;
            netConfig.backendConfig = &backendConfig;
        }
        net_ptr->setSessionMode(MNN::Interpreter::SessionMode::Session_Input_Inside);
        TX_LOG_DEBUG("cc_mnn_model_inference.cpp", "debug %s", net_name.c_str());
        session = net_ptr->createSession(netConfig);
        // 如果 session 创建失败，并且当前 backend 不是 CPU，尝试使用 CPU backend
        if (session == NULL && mnn_type != MNN_FORWARD_CPU) {
            TX_LOG_WARN("cc_mnn_model_inference.cpp",
                        "Session creation failed with backend %d. Retrying with CPU backend.",
                        mnn_type);

            // 切换到 CPU backend
            mnn_type = MNN_FORWARD_CPU;
            netConfig.type = mnn_type;
            netConfig.numThread = root_.isMember("thread_num") ? root_["thread_num"].asInt() : 1;

            session = net_ptr->createSession(netConfig);

            // 如果仍然失败，返回错误
            if (session == NULL) {
                TX_LOG_ERROR("cc_mnn_model_inference.cpp",
                             "Session creation failed with CPU backend.");
                return -1;
            }
        }
        TX_LOG_DEBUG("cc_mnn_model_inference.cpp", "Session creation succeeded with backend %d",
                     mnn_type);
        TX_LOG_DEBUG("cc_mnn_model_inference.cpp","debug");
        // int backend_type = 0;
        // net_ptr->getSessionInfo(session, MNN::Interpreter::SessionInfoCode::BACKENDS, &backend_type);
        // std::cout << root_["filename"] << "::backend type:" << backend_type << std::endl;
        if (root_.isMember("inputname"))
        {
            for (int i = 0; i < root_["inputname"].size(); ++i)
            {
                vecStrInputNames.push_back(root_["inputname"][i].asString());
            }
        }

        inputs.clear();
        if (!vecStrInputNames.empty())
        {
            for (auto &strInputName : vecStrInputNames)
            {
                MNN::Tensor *input = net_ptr->getSessionInput(session, strInputName.c_str());
                if (input == NULL)
                {
                    return -2;
                }

                inputs.insert(std::pair<std::string, MNN::Tensor *>(strInputName, input));
            }
        }
        else
        {
            inputs = net_ptr->getSessionInputAll(session);
            for (auto &input_pair : inputs)
            {
                auto temp_tensor = input_pair.second;
                TX_LOG_INFO("CcMnnInference", "in c:%d h:%d w:%d", temp_tensor->channel(), temp_tensor->height(), temp_tensor->width());
            }
        }
        if (root_.isMember("width") && root_.isMember("height") && root_.isMember("channel"))
        {
            if (root_["width"].asInt() != 0 && root_["height"].asInt() != 0 && root_["channel"].asInt() != 0)
            {
                for (auto &input_pair : inputs)
                {
                    net_ptr->resizeTensor(input_pair.second,
                                          {1, root_["channel"].asInt(), root_["height"].asInt(), root_["width"].asInt()});
                    net_ptr->resizeSession(session);
                    std::cout << "resize: " << root_["channel"].asInt() << " " << root_["width"].asInt() << " " << root_["height"].asInt() << std::endl;
                }
            }
        }

        if (root_.isMember("outputname"))
        {
            for (int i = 0; i < root_["outputname"].size(); ++i)
            {
                vecStrOutputNames.push_back(root_["outputname"][i].asString());
            }
        }

        outputs.clear();
        if (!vecStrOutputNames.empty())
        {
            for (auto &strOutputName : vecStrOutputNames)
            {
                // std::cout<<strOutputName<<std::endl;
                MNN::Tensor *output = net_ptr->getSessionOutput(session, strOutputName.c_str());
                if (output == NULL)
                {
                    return -3;
                }
                outputs.insert(std::pair<std::string, MNN::Tensor *>(strOutputName, output));
            }
        }
        else
        {
            outputs = net_ptr->getSessionOutputAll(session);
            for (auto &output_pair : outputs)
            {
                auto temp_tensor = output_pair.second;
                TX_LOG_INFO("CcMnnInference", "out c:%d h:%d w:%d", temp_tensor->channel(), temp_tensor->height(), temp_tensor->width());
            }
        }

        return 0;
    }

    const std::vector<std::shared_ptr<CcTensor<float>>> &CcMnnModelInference::getInputTensor()
    {
        input_tensor_ptr.clear();
        input_tensor_ptr.reserve(inputs.size());
        for (auto input_pair : inputs)
        {
            MNN::Tensor *input = new MNN::Tensor(input_pair.second, MNN::Tensor::CAFFE);
            std::shared_ptr<BlobData> blob(
                new BlobData(std::function<int(uint32_t, BlobData *)>(), release_tensor_fun));
            uint32_t u32Size = 4;
            for (auto dim : input->shape())
            {
                u32Size *= dim;
            }
            blob->init(u32Size, input_pair.first, input->host<uint8_t>(), 0, (void *)input);

            std::shared_ptr<CcTensor<float>> tenser(
                new CcTensor<float>(input->shape(), (float *)input->host<uint8_t>()));
            tenser->data_blob_ptr = blob;
            input_tensor_ptr.push_back(tenser);
        }
        return input_tensor_ptr;
    }

    int CcMnnModelInference::setInputTensor(
        std::vector<std::shared_ptr<CcTensor<float>>> &input_tensor)
    {
        input_tensor_ptr = input_tensor;
        return 0;
    }

    int CcMnnModelInference::doInference(std::vector<std::shared_ptr<CcTensor<float>>> &output_tensor)
    {
        int i = 0;
        for (auto input_pair : inputs)
        {
            std::shared_ptr<MNN::Tensor> input =
                std::shared_ptr<MNN::Tensor>(MNN::Tensor::create<float>(
                    input_pair.second->shape(), (void *)input_tensor_ptr[i]->data_blob_ptr->pu8VirAddr,
                    MNN::Tensor::CAFFE));
            input_pair.second->copyFromHostTensor(input.get());
            i++;
        }
        net_ptr->runSession(session);
        // usleep(5* 1000);
        output_tensor.clear();
        output_tensor.reserve(outputs.size());
        
        for (auto &output_pair : outputs)
        {
            int blob_size = sizeof(float);
            for (auto s : output_pair.second->shape())
            {
                blob_size *= s;
                // std::cout << "output: " << s << std::endl;
            }
            MNN::Tensor *output = new MNN::Tensor(output_pair.second, MNN::Tensor::CAFFE);
            output_pair.second->copyToHostTensor(output);
            // 打印 tensor 数据
            // auto data = output->host<float>();
            // for (int i = 0; i < 10; ++i)
            // {
            //     std::cout << "data:" << data[i] << " ";
            // }
            // 使用 cnpy 库保存数组到 .npy 文件
            // std::string filename = output_pair.first+"_"+std::to_string(output_pair.second->shape()[0])+"x"+std::to_string(output_pair.second->shape()[1])+"x"+std::to_string(output_pair.second->shape()[2])+"x"+std::to_string(output_pair.second->shape()[3])+".npy";
            // cnpy::npy_save(filename, data,
            //                {output_pair.second->shape()[0], output_pair.second->shape()[1], output_pair.second->shape()[2], output_pair.second->shape()[3]}, "w");

            // std::cout << "Data saved to output.npy" << std::endl;
//             auto baseback = net_ptr->getBackend(session,  output_pair.second);
//             // 获取当前使用的后端类型
//             if (baseback==nullptr)
//             {
//                 /* code */
//                 std::cout<<"null!!!!"<<std::endl;
// #ifdef __ANDROID__
//                     LOGE("nullptr!");
// #endif
//             }
//             else
//             {
// #ifdef __ANDROID__
//                     LOGD("MNN backend type:%d session name: %s \n", baseback->type(), output_pair.first.c_str());
// #endif
//                 printf("MNN backend type:%d session name: %s \n", baseback->type(), output_pair.first.c_str());
//             }
            std::shared_ptr<BlobData> blob(
                new BlobData(std::function<int(uint32_t, BlobData *)>(), release_tensor_fun));
            blob->init(blob_size, output_pair.first, output->host<uint8_t>(), 0, output);

            auto dim = output->shape();
            // std::reverse(dim.begin() + 1, dim.end());
            // dim.erase(dim.begin());
            std::shared_ptr<CcTensor<float>> tenser(

                new CcTensor<float>(dim, (float *)output->host<uint8_t>()));
            tenser->data_blob_ptr = blob;
            output_tensor.push_back(tenser);
        }
        return 0;
    }
    int CcMnnModelInference::deinit()
    {
        return 0;
    }
    CcMnnModelInference::~CcMnnModelInference()
    {
        inputs.clear();
        outputs.clear();
        input_.clear();
        output_.clear();
        if (session)
        {
            net_ptr->releaseSession(session);
        }
        net_ptr.reset();
        // if (session)
        // {
        //     delete session;
        // }
        input_tensor_ptr.clear();
    }

    int CcMnnModelInference::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        // std::cout<<"test"<<std::endl;
        input_ = in;

        return 0;
    }

    int CcMnnModelInference::execute()
    {
        // 1.从input层获取数据
        // 2.转换成内部tensor数据类型进行计算处理
        // 3.将结果转换为numarrray类型
        //  std::cout<<"test"<<std::endl;
        TimeFunction time_f("mnn "+net_name);        

        TX_LOG_DEBUG("inference ", "---- %s---- start", net_name.c_str());
        std::vector<std::shared_ptr<CcTensor<float>>> input_tensor;
        for (auto in : input_)
        {
            input_tensor.push_back(in->getTensor<float>());
        }
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        setInputTensor(input_tensor);
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        std::vector<std::shared_ptr<CcTensor<float>>> output_tensor;
        doInference(output_tensor);
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        std::vector<std::shared_ptr<NumArray>> outputs;
        outputs.reserve(output_tensor.size());
        for (int i = 0; i < output_tensor.size(); i++)
        {
            std::shared_ptr<NumArray> na(new NumArray);
            na->type = NumArray::DataType::FLOAT32;
            na->word_size = sizeof(float);
            na->data = (unsigned char *)output_tensor[i]->data_;
            if (output_tensor[i]->index_.dim_.size() == 3)
            {
                na->shape = output_tensor[i]->index_.dim_;
            }
            else
            {
                na->shape.reserve(output_tensor[i]->index_.dim_.size());
                for (int j = 0; j < output_tensor[i]->index_.dim_.size(); j++)
                {
                    na->shape.push_back(output_tensor[i]->index_.dim_[j]);
                }
            }
            na->data_blob_ptr = output_tensor[i]->data_blob_ptr;
            outputs.push_back(na);
        }
        output_ = outputs;
        TX_LOG_DEBUG("inference ", "---- execute---- end");
        return 0;
    }

    size_t CcMnnModelInference::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcMnnModelInference::getOutput(int index)
    {
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;

        return output_[index];
    }

    REGISTER_CC_MODULE(mnn_inference, CcMnnModelInference)

} // namespace tongxing

// extern bool MNN_InserVulkanExtraRuntimeCreator();
// extern bool MNN_InserOpenClExtraRuntimeCreator();
// __attribute__((constructor, used)) static void register_module_mnn_backend(void)
// {
//     MNN_InserVulkanExtraRuntimeCreator();
//     MNN_InserOpenClExtraRuntimeCreator();
// }