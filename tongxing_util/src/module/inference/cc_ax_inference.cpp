#include "cc_ax_inference.h"
#include "cc_resource_register.h"
#include "cc_assert.h"
#include <string.h>
#include "CalmCarLog.h"

namespace tongxing
{
    static int g_evn_init_count = 0;
    static int g_flag_init= 0;
    static void InitEvn()
    {
        if (g_flag_init==0)
        {
            AX_JOINT_SDK_ATTR_T npuAttr;
            npuAttr.eNpuMode = AX_NPU_VIRTUAL_1_1;
            // npuAttr.eHardMode = get_npu_mode_by_dot_neu_data(model_data.pu8VirAddr, model_data.u32Size);

            // int res = AX_NPU_SDK_EX_Get_Attr(&npuAttr);
            // std::cout<<(void*)res<<std::endl;
            // cc_assert(res == 0);
            // if (npuAttr.eHardMode != AX_NPU_VIRTUAL_DISABLE)
            // {
            TX_LOG_DEBUG("CcAxInference", "AX_JOINT_Adv_Init");
            int res = AX_JOINT_Adv_Init(&npuAttr);
            TX_LOG_DEBUG("CcAxInference", "AX_JOINT_Adv_Init res=0x%x",res);
            //    std::cout<<(void*)res<<std::endl;
            
            cc_assert(res == 0);
            g_flag_init=1;
        }
        g_evn_init_count++;
    }
    static void DeinitEvn()
    {
        
        if (g_evn_init_count > 0)
        {
            g_evn_init_count--;
        }
        else if(g_flag_init==1)
        {
            TX_LOG_DEBUG("CcAxInference", "AX_JOINT_Adv_Deinit");
            int res = AX_JOINT_Adv_Deinit();
            //    std::cout<<(void*)res<<std::endl;
            cc_assert(res == 0);
            g_flag_init=0;
        }
    }
    static int creat_bolb_mem(uint32_t size ,BlobData* bolb){
        bolb->pu8Reserve=malloc(size);
        bolb->pu8VirAddr=bolb->pu8Reserve;
        bolb->u32Size=size;
        return 0;
    }
    static void free_bolb_mem(void* ptr){
        free(ptr);
    }
    int CcAxInference::init(const Json::Value &config)
    {
        BlobData model_data(creat_bolb_mem,free_bolb_mem);
        if (config["type"].asString() == "file")
            cc_assert(model_data.loadFromFile(config["filename"].asString()) == 0);
        else if (config["type"].asString() == "inside")
        {
            auto model_data_ =
                CcResourcDataRegister::instance().get_function(config["filename"].asString());
            model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            model_data.u32Size = model_data_.first;
        }
        else
        {
            cc_assert(false);
        }
        TX_LOG_DEBUG("CcAxInference", "model_data.pu8VirAddr=0x%x,model_data.u32Size=%d", model_data.pu8VirAddr, model_data.u32Size);
        InitEvn();
        // }
        TX_LOG_DEBUG("CcAxInference", "AX_JOINT_CreateHandle");
        handle = NULL;
        int res = AX_JOINT_CreateHandle(&handle, model_data.pu8VirAddr, model_data.u32Size);
        TX_LOG_DEBUG("CcAxInference", "AX_JOINT_CreateHandle res=0x%x",res);
        cc_assert(res == 0);
        TX_LOG_DEBUG("CcAxInference", "AX_JOINT_GetIOInfo");
        const AX_JOINT_IO_INFO_T *io_info = AX_JOINT_GetIOInfo(handle);
        TX_LOG_DEBUG("CcAxInference", "io_info=0x%x", io_info);
        // in_buffers.resize(io_info->nInputSize);
        // for (int i = 0; i < io_info->nInputSize; i++)
        // {
        //     AX_JOINT_IO_BUFFER_T buf;
        //     AX_JOINT_AllocBuffer(&io_info->pInputs[i], &buf, AX_JOINT_ABST_DEFAULT);
        //     in_buffers[i] = buf;
        // }
        model_io_info = *io_info;
        out_buffers.resize(io_info->nOutputSize);
        for (int i = 0; i < io_info->nOutputSize; i++)
        {
            AX_JOINT_IO_BUFFER_T buf;
            AX_JOINT_AllocBuffer(&io_info->pOutputs[i], &buf, AX_JOINT_ABST_DEFAULT);
            out_buffers[i] = buf;
        }
        context=NULL;
        res =  AX_JOINT_CreateExecutionContext(handle,&context);
        cc_assert(res == 0);
        flag_init = true;
        
        //  AX_NPU_SDK_EX_Alloc_buffer();
        return 0;
    }
    int CcAxInference::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_=in;
        in_buffers.resize(in.size());
        in_strides.resize(in.size());
        // in_strides.reserve()
        for (int i = 0; i < in.size(); i++)
        {
            
            in_buffers[i].pVirAddr=in[i]->data_blob_ptr->pu8VirAddr;
            in_buffers[i].phyAddr=in[i]->data_blob_ptr->u64PsyAddr;
            in_buffers[i].nSize=in[i]->data_blob_ptr->u32Size;
            in_buffers[i].nStrideSize=in[i]->shape.size();
            in_strides[i].resize(in[i]->shape.size());
            int stride_size=in[i]->word_size;
            for(int j=0;j<in_buffers[i].nStrideSize;j++){
                in_strides[i][j]=stride_size;
                stride_size*=in[i]->shape[j];
            }
            std::reverse(in_strides[i].begin(),in_strides[i].end());
            in_buffers[i].pStride=in_strides[i].data();
            // memcpy(dst_ptr, src_ptr, in_buffers[i].nSize);
            // std::cout<<in_buffers[i].nSize<<" "<<in[i]->data_blob_ptr->u32Size<<std::endl;
        }
        // std::cout<<"info:"<<std::endl;
        // for (int i = 0; i < model_io_info.pInputs[0].nShapeSize; i++)
        // {
        //     std::cout<<model_io_info.pInputs[0].pShape[i]<<std::endl;;
        // }
        return 0;
    }
    int CcAxInference::execute()
    {
         AX_JOINT_IO_T io={0};
        io.nInputSize = in_buffers.size();
        io.pInputs = in_buffers.data();
        io.nOutputSize = out_buffers.size();
        io.pOutputs = out_buffers.data();
        AX_JOINT_IO_SETTING_T joint_io_setting={0};
        io.pIoSetting=&joint_io_setting;
        // memset(out_buffers[0].pVirAddr,0,out_buffers[0].nSize);
        // std::cout<<((float*)out_buffers[0].pVirAddr)[0]<<" "<<((float*)out_buffers[0].pVirAddr)[(out_buffers[0].nSize/4)-1]<<" "<<((float*)out_buffers[0].pVirAddr)[(out_buffers[0].nSize/4)/2]<<std::endl;
        int res=AX_JOINT_RunSync(handle,context, &io);
        // std::cout<<((float*)out_buffers[0].pVirAddr)[0]<<" "<<((float*)out_buffers[0].pVirAddr)[(out_buffers[0].nSize/4)-1]<<" "<<((float*)out_buffers[0].pVirAddr)[(out_buffers[0].nSize/4)/2]<<std::endl;
        // TX_LOG_DEBUG("CcAxInference", "AX_JOINT_RunSync res=0x%x",res);
        cc_assert(res == 0);
        return res;
    }
    size_t CcAxInference::getOutputNum()
    {
        return out_buffers.size();
    }
    std::shared_ptr<NumArray> CcAxInference::getOutput(int index)
    {
        std::shared_ptr<NumArray> ret(new NumArray);
        ret->shape.reserve(model_io_info.pOutputs[index].nShapeSize);
        for (int i = 0; i < model_io_info.pOutputs[index].nShapeSize; i++)
        {
            // std::cout<<model_io_info.pOutputs[index].pShape[i]<<" "<<std::endl;
            ret->shape.push_back(model_io_info.pOutputs[index].pShape[i]);
        }

        ret->word_size = 4;
        ret->type = NumArray::DataType::FLOAT32;
        ret->data = (unsigned char *)out_buffers[index].pVirAddr;
        // std::cout<<(void*)ret->data<<" "<<out_buffers[index].nSize<<std::endl;
        return ret;
    }
    CcAxInference::~CcAxInference()
    {
        if (flag_init)
        {
            AX_JOINT_DestroyExecutionContext(context);
            AX_JOINT_DestroyHandle(handle);
            // for (auto it = in_buffers.begin(); it != in_buffers.end(); it++)
            // {
            //     AX_JOINT_FreeBuffer(&(*it));
            //     in_buffers.erase(it);
            // }
            for (auto it = out_buffers.begin(); it != out_buffers.end(); it++)
            {
                AX_JOINT_FreeBuffer(&(*it));
                out_buffers.erase(it);
            }
            DeinitEvn();
        }
    }
    REGISTER_CC_MODULE(ax_inference, CcAxInference)
}