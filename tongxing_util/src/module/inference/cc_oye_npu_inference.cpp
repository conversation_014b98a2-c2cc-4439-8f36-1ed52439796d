#include "cc_oye_npu_inference.h"
#include "CalmCarLog.h"
#include "cc_assert.h"
#include "cc_resource_register.h"
#include "cc_numarray_tool.h"
#include <iostream>
#include "time_profile.h"
#include "time_function.h"
#include <unistd.h>
#include "ot_smr.h"
#include <opencv2/opencv.hpp>
// #include "ot_eis_media_sys.h"
// #include "cnpy.h"
namespace tongxing
{

    int CcOyeNpuInference::init(const Json::Value &root)
    {
        root_ = root;
        int ret = 0;
        TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug");
        BlobData model_data;
        if (root_["type"].asString() == "file")
        {
            net_name = root_["filename"].asString();
            cc_assert(model_data.loadFromFile(root_["filename"].asString()) == 0);
        }
        else if (root_["type"].asString() == "inside")
        {
            net_name = root_["filename"].asString();
            auto model_data_ =
                CcResourcDataRegister::instance().get_function(root_["filename"].asString());
            model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            model_data.u32Size = model_data_.first;
            TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug size ;%d", model_data.u32Size);
        }
        else
        {
            cc_assert(false);
        }
        TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug");
        if (!initialized_)
        {
            // ot_eis_media_init();
            ot_smr_init();
            // ot_avp_npu_config config = {
            //     .device_ids = 0,
            //     .dump_layer = OT_FALSE,   // dump layer
            //     .perf_mode = OT_FALSE,  // perf_mode
            //     .log_level = 2,   // log level
            // };
            extra_config extra_npu_config[2];
            char* layer_path_key   = "DUMP_PATH";
            char* layer_path_value = "LayerDump";
            memcpy(&(extra_npu_config[0].key), layer_path_key, strlen(layer_path_key) + 1);
            memcpy(&(extra_npu_config[0].value), layer_path_value, strlen(layer_path_value) + 1);
            ot_avp_npu_config config = {
                .core_ids = 0,
                .dump_layer = OT_FALSE,   // dump layer
                .perf_mode = OT_FALSE,  // perf_mode
                .log_level = 2,   // log level info
                .extra_config_num = 2,
                .extra_configs = extra_npu_config,
            };
            ret = ot_avp_npu_init(&config);
            if (ret != 0) {
                TX_LOG_ERROR("cc_oye_npu_inference.cpp"," npu init failed");
            }
            initialized_ = true;
        }
        
        TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug");
        // ret = ot_avp_npu_load_model(net_name.c_str(), &handle_);
        ret = ot_avp_npu_load_model_from_mem( model_data.pu8VirAddr, model_data.u32Size, &handle_);
        if (ret != 0)
        {
            TX_LOG_ERROR("cc_oye_npu_inference.cpp"," load model failed");
        }



        TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug");
        ot_avp_npu_get_input_num(handle_, &input_num);
        ot_avp_npu_get_output_num(handle_, &output_num);
        ot_avp_npu_get_input_shape_by_index(handle_, 0, &input_shape);
        in_buf.buf_len = ot_avp_npu_get_input_size_by_index(handle_, 0);
        
        TX_LOG_DEBUG("cc_oye_npu_inference.cpp","in_buf.buf_len: %d",in_buf.buf_len);
        input_dataset = ot_avp_npu_create_dataset();
        output_dataset = ot_avp_npu_create_dataset();
        data_buf* input_data = (data_buf*)malloc(sizeof(data_buf)* input_num);
        data_buf* output_data = (data_buf*)malloc(sizeof(data_buf) * output_num);
        ret = ot_avp_npu_malloc(&in_buf.buf, in_buf.buf_len);
        if (ret != 0) {
            printf("malloc failed");
        }
        input_stride = ot_avp_npu_get_input_default_stride(handle_, 0);
        ot_avp_npu_add_buffer(input_dataset, in_buf.buf, 0, in_buf.buf_len, input_stride);

        TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug");
        for (size_t i = 0; i < output_num; i++)
        {
            ot_avp_npu_shape output_shape;
            data_buf tmp_buf = {0};; 
            uint32_t output_stride = ot_avp_npu_get_output_default_stride(handle_, i);
            ot_avp_npu_get_output_shape_by_index(handle_, i, &output_shape);
            ot_u32 buf_size = ot_avp_npu_get_output_size_by_index(handle_, i);

            TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug output_num: %d ,output_stride %d", buf_size,output_stride);
            std::vector<int> tmp_shape;

            for (size_t j = 0; j < output_shape.dim_size; j++)
            {
                TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug %d",output_shape.dims[j] );
                tmp_shape.push_back(output_shape.dims[j]);
            }
            ret = ot_avp_npu_malloc(&tmp_buf.buf, buf_size);
            if (ret != 0) {
                TX_LOG_ERROR("cc_oye_npu_inference.cpp","malloc failed");
            }
            tmp_buf.buf_len = buf_size;
            std::shared_ptr<NumArray> res = creat_numarray(tmp_shape, NumArray::DataType::FLOAT32);
            ot_avp_npu_add_buffer(output_dataset, tmp_buf.buf, 0, buf_size, output_stride);
            out_buf.push_back(tmp_buf);
            output_.push_back(res);
            out_shape.push_back(tmp_shape);
        }
    
    TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug");

        return 0;
    }

       CcOyeNpuInference::~CcOyeNpuInference()
    {
       
        if (out_buf.size()>0)
        {
            for (size_t i = 0; i < out_buf.size(); i++)
            {
                 ot_avp_npu_free(out_buf[i].buf);
            }
            out_buf.clear();
        }
        input_.clear();
        output_.clear();
        ot_avp_npu_destroy_dataset(input_dataset);
        ot_avp_npu_destroy_dataset(output_dataset);
        ot_avp_npu_unload_model(handle_);
        ot_avp_npu_deinit();
    }

    int CcOyeNpuInference::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        // std::cout<<"test"<<std::endl;
        input_ = in;

        return 0;
    }

    int CcOyeNpuInference::execute()
    {
        // TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug input_stride: %d",input_stride);
        TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug");
        // cv::Mat img(96, 160, CV_8UC1, input_[0]->data);
        // cv::imwrite("test11111.jpg", img);

        // return 1;
        // 1.从input层获取数据
        memcpy(in_buf.buf, input_[0]->data, in_buf.buf_len);
        // ot_avp_npu_add_buffer(input_dataset, in_buf.buf, 0, in_buf.buf_len, input_stride);
        int ret = ot_avp_npu_execute(handle_, input_dataset, output_dataset);
        if (ret != 0) {
            printf("run model error\n");
        }
        

        for (size_t i = 0; i < out_buf.size(); i++)
        {

            
            TX_LOG_DEBUG("cc_oye_npu_inference.cpp","debug");
            float* model_buf = (float*)out_buf[i].buf;
            float* out_buf_ = (float*)output_[i]->data;


            // std::ofstream file("face.bin", std::ios::binary);
            // if (!file.is_open()) {
            //     std::cerr << "Failed to open file: "  << std::endl;
            //     return;
            // }

            //    // 写入数据到文件
            // file.write(reinterpret_cast<const char*>(model_buf), out_buf[i].buf_len);

            //   // 关闭文件
            // file.close();
            // return 0;

            int hout = out_shape[i].size()-1;
            int data_line_long = out_shape[i][hout];
            int data_line_long_32 =  CC_CACHE_8_ALIGN(data_line_long);
            // std::cout << "data_line_long_32 : "<< data_line_long_32 << std::endl;
            // int data_skip_long = (data_line_long_32 - data_line_long);
            
            int mun = 1;
         
            for (size_t j = 0; j < out_shape[i].size() - 1; j++)
            {
                mun =mun * out_shape[i][j];
            }
            
            // int temp_num = mun * data_line_long ;
           
            for (size_t i = 0; i < mun; i++)
            {
                
                memcpy(out_buf_, model_buf, data_line_long *sizeof(float) );
                // // if (out_buf_[0]> 0)
                // {
                //     for (size_t k = 0; k < 5; k++)
                //     {
                //         printf(" %0.2f ", model_buf[k]);
                //     }
                //     printf("  i %d  %0.2f\n", i, out_buf_[0]);
                // }
                out_buf_ += data_line_long;
                model_buf += data_line_long_32;
            } 
           
        }
        return 0;
    }

    size_t CcOyeNpuInference::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcOyeNpuInference::getOutput(int index)
    {
        return output_[index];
    }

    REGISTER_CC_MODULE(oyenpu_inference, CcOyeNpuInference)

} // namespace tongxing

