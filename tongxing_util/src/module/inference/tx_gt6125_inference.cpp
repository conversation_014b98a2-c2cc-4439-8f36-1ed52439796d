#include "CalmCarLog.h"
#include "cc_numarray_tool.h"
#include "cc_resource_register.h"
#include <cassert>
#include <iostream>
#include <opencv2/opencv.hpp>
#include "tx_gt6125_inference.h"
#include <fstream>
#include "time_profile.h"
#include <time.h>
#include "time_function.h"

namespace tongxing
{


    void CreateUserBuffer(zdl::DlSystem::UserBufferMap& userBufferMap,
                      std::unordered_map<std::string, std::vector<float>>& applicationBuffers,
                      std::vector<std::unique_ptr<zdl::DlSystem::IUserBuffer>>& snpeUserBackedBuffers,
                      const zdl::DlSystem::TensorShape& bufferShape,
                      const char* name);
   int TxGT6125Inference::init(const Json::Value &root)
    {
        TX_LOG_DEBUG("TxGT6125Inference", " init  ---start---");
        root_=root;

        zdl::DlSystem::Runtime_t m_runtime = zdl::DlSystem::Runtime_t::DSP;  //
        zdl::DlSystem::PerformanceProfile_t m_profile = zdl::DlSystem::PerformanceProfile_t::HIGH_PERFORMANCE;

        
        static zdl::DlSystem::Version_t Version = zdl::SNPE::SNPEFactory::getLibraryVersion();
        TX_LOG_INFO("TxGT6125Inference", " init  SNPE Version: %s", Version.asString().c_str());
        if (!zdl::SNPE::SNPEFactory::isRuntimeAvailable(m_runtime))
        {
            TX_LOG_WARN("TxGT6125Inference", " Selected runtime not present. Falling back to CPU. ");
            printf(" Selected runtime not present. Falling back to CPU. ");
            m_runtime = zdl::DlSystem::Runtime_t::CPU;
        }
        TX_LOG_DEBUG("TxGT6125Inference", " init  ---isRuntimeAvailable---");
        if (root_["type"].asString() == "file"){
            net_name = root_["filename"].asString();
            m_container = zdl::DlContainer::IDlContainer::open(zdl::DlSystem::String(net_name)); //可以用把内存地址解决

        }
        else if (root_["type"].asString() == "inside")
        {
            net_name = root_["filename"].asString();
            auto model_data_ = CcResourcDataRegister::instance().get_function(root_["filename"].asString());
            m_container = zdl::DlContainer::IDlContainer::open((uint8_t *)model_data_.second,model_data_.first); //可以用把内存地址解决
            // model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            // model_data.u32Size = model_data_.first;
        }
        else
        {
            cc_assert(false);
        }
        TX_LOG_DEBUG("TxGT6125Inference", " init  ---name--- %s ", net_name.c_str());

        if (m_container == nullptr){
            TX_LOG_FATAL("TxGT6125Inference", " Error while opening the container file");
            cc_assert(false);
        }

        if(root_["output"].isArray())
        {
            Json::Value output = root_["output"];
            
            for (Json::ArrayIndex i = 0; i < output.size(); ++i) {
                // std::cout << "Element " << i << ": " << output[i].asString() << std::endl;
                m_outputTensor.append(output[i].asString().c_str());
                //  m_outputLouter_name.push_back(output[i].asString());
            }
        }
        zdl::SNPE::SNPEBuilder snpeBuilder(m_container.get());
        TX_LOG_DEBUG("TxGT6125Inference", " init  ---snpeBuilder---");
        m_snpe = snpeBuilder.setOutputLayers(m_outputLayers)
        .setOutputTensors(m_outputTensor)
        .setRuntimeProcessorOrder(m_runtime)
        .setPerformanceProfile(m_profile)
        .setUseUserSuppliedBuffers(true)
        .build();
        
        if (m_snpe == nullptr){
            TX_LOG_FATAL("TxGT6125Inference", " Error while building SNPE object.");
            cc_assert(false);
        }

        TX_LOG_DEBUG("TxGT6125Inference", " init  ---snpeBuilder1---");
        const auto& inputNamesOpt = m_snpe->getInputTensorNames();
        for( auto& name : *inputNamesOpt)
        {
            TX_LOG_DEBUG("TxGT6125Inference", " init  ---snpeBuilder3---");
            auto bufferAttributesOpt = m_snpe->getInputOutputBufferAttributes(name);
            const zdl::DlSystem::TensorShape& bufferShape = (*bufferAttributesOpt)->getDims();
            std::vector<size_t> tensorShape;
            for (size_t j = 0; j < bufferShape.rank(); j++) {
                TX_LOG_INFO("TxGT6125Inference", "input name: %s shape :%d",name ,bufferShape[j]);
                tensorShape.push_back(bufferShape[j]);
            }
            m_inputShapes.emplace(name, tensorShape);
            CreateUserBuffer(m_inputUserBufferMap, m_applicationInputBuffers, m_inputUserBuffers, bufferShape, name);
        }

        TX_LOG_DEBUG("TxGT6125Inference", " init  ---snpeBuilder2---");

        const auto& outputNamesOpt = m_snpe->getOutputTensorNames();
        const zdl::DlSystem::StringList& outputNames = *outputNamesOpt;
        std::cout << "  outputNamesOpt : "<< outputNames.size() << std::endl;
        for (auto& name : outputNames) {
            std::cout << " out name : "<< name << std::endl;
            auto bufferAttributesOpt = m_snpe->getInputOutputBufferAttributes(name);
            const zdl::DlSystem::TensorShape& bufferShape = (*bufferAttributesOpt)->getDims();
            std::vector<size_t> tensorShape;
            for (size_t j = 0; j < bufferShape.rank(); j++)
            {
                TX_LOG_INFO("TxGT6125Inference", "out name: %s shape :%d",name ,bufferShape[j]);
                tensorShape.push_back(bufferShape[j]);
            }
            m_outputShapes.emplace(name, tensorShape);
            CreateUserBuffer(m_outputUserBufferMap, m_applicationOutputBuffers, m_outputUserBuffers, bufferShape, name);

        }
       TX_LOG_DEBUG("TxGT6125Inference", " init  ---end---");
        return 0;
    }
    int TxGT6125Inference::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int TxGT6125Inference::execute()
    {
        TimeFunction time_f(net_name,"tx_gt6125_inference.cpp");
        TX_LOG_DEBUG("TxGT6125Inference", " execute -----start---- %s " ,net_name.c_str());
        unsigned char *in_data_  = input_[0]->data_blob_ptr->pu8VirAddr;
        size_t in_data_size_ = input_[0]->data_blob_ptr->u32Size / sizeof(float);
        // std::cout << "image size : "<< in_data_size_ << std::endl;
   
        // size_t found = net_name.find("face_rec");
        // if (found != std::string::npos) {
        //     cv::Mat img_in(112,112,CV_32FC3,in_data_);
        //     cv::imwrite("./face_rec_input.jpg",img_in);
        // }
        

        for (auto& pair : m_applicationInputBuffers) {
            std::copy_n((float*)in_data_,in_data_size_,pair.second.data());
        }

        bool execStatus = m_snpe->execute(m_inputUserBufferMap, m_outputUserBufferMap);
        if (execStatus == false)
        {
           TX_LOG_FATAL("TxGT6125Inference", " Error SNPE execute.");
           return -1;
        }
        std::vector<std::shared_ptr<NumArray>> outputs;
        for (auto& pair : m_applicationOutputBuffers) {
            std::string key = pair.first;
            std::shared_ptr<NumArray> output(new NumArray);
            // std::copy_n(in_data_,in_data_size_,pair.second.data());
            output->word_size=sizeof(float);
            auto shape_out = m_outputShapes.at(key);
            int size_out = 1;
            for (size_t i = 0; i < shape_out.size(); i++)
            {
                size_out *=  shape_out[i];
                output->shape.push_back(shape_out[i]);
            }
            output->data=(unsigned char*)pair.second.data();
            outputs.push_back(output);
        }
        output_=outputs;
        TX_LOG_DEBUG("TxGT6125Inference", " execute -----end----  ");
        return 0;
    }
    size_t TxGT6125Inference::getOutputNum()
    {
        return output_.size();
    }
    
    std::shared_ptr<NumArray> TxGT6125Inference::getOutput(int index)
    {
        return output_[index];
    }

    TxGT6125Inference::~TxGT6125Inference()
    {
        if (nullptr != m_snpe) 
            m_snpe.reset(nullptr);
        for (auto [k, v] : m_applicationInputBuffers) ClearVector(v);
        for (auto [k, v] : m_applicationOutputBuffers) ClearVector(v);
    }

    static size_t calcSizeFromDims(const zdl::DlSystem::Dimension* dims, size_t rank, size_t elementSize)
    {
        if (rank == 0) return 0;
        size_t size = elementSize;
        while (rank--) {
            size *= *dims;
            dims++;
        }
        return size;
    }

    void CreateUserBuffer(zdl::DlSystem::UserBufferMap& userBufferMap,
                      std::unordered_map<std::string, std::vector<float>>& applicationBuffers,
                      std::vector<std::unique_ptr<zdl::DlSystem::IUserBuffer>>& snpeUserBackedBuffers,
                      const zdl::DlSystem::TensorShape& bufferShape,
                      const char* name)
    {
        size_t bufferElementSize = sizeof(float);

        // calculate stride based on buffer strides
        // Note: Strides = Number of bytes to advance to the next element in each dimension.
        // For example, if a float tensor of dimension 2x4x3 is tightly packed in a buffer of 96 bytes, then the strides would be (48,12,4)
        std::vector<size_t> strides(bufferShape.rank());
        strides[strides.size() - 1] = bufferElementSize;
        size_t stride = strides[strides.size() - 1];
        for (size_t i = bufferShape.rank() - 1; i > 0; i--)
        {
            stride *= bufferShape[i];
            strides[i - 1] = stride;
        }

        size_t bufSize = calcSizeFromDims(bufferShape.getDimensions(), bufferShape.rank(), bufferElementSize);

        // set the buffer encoding type
        zdl::DlSystem::UserBufferEncodingFloat userBufferEncodingFloat;
        // create user-backed storage to load input data onto it
        applicationBuffers.emplace(name, std::vector<float>(bufSize / bufferElementSize));
        // create SNPE user buffer from the user-backed buffer
        zdl::DlSystem::IUserBufferFactory& ubFactory = zdl::SNPE::SNPEFactory::getUserBufferFactory();
        snpeUserBackedBuffers.push_back(ubFactory.createUserBuffer((void*)applicationBuffers.at(name).data(),
                                                                    bufSize,
                                                                    strides,
                                                                    &userBufferEncodingFloat));
        // add the user-backed buffer to the inputMap, which is later on fed to the network for execution
        userBufferMap.add(name, snpeUserBackedBuffers.back().get());
    }



   REGISTER_CC_MODULE(gt6125_inference, TxGT6125Inference)
} // namespace tongxing



