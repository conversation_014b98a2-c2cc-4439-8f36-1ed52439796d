/*
 * @file tda4vm_inferer.h
 * @brief
 *
 * <AUTHOR>
 * @version
 * @date Oct 24, 2020
 */

#ifndef _CCPERCEPTION_PERCEPTION_MODEL_TDA4VM_INFERENCE_H_
#define _CCPERCEPTION_PERCEPTION_MODEL_TDA4VM_INFERENCE_H_

#include <TI/tivx.h>
#include <TI/j7_tidl.h>
#include <TI/dl_kernels.h>
#include <tivx_tidl_trace.h>
#include <util/util.hpp>
#include "model/nn_inference.h"

namespace perception {
namespace model {
#define TRACE_VX(fmt, ...) \
  CALMCAR_TRACE(FMT_STRING("[{:s}] " fmt), name_, ##__VA_ARGS__)
#define DEBUG_VX(fmt, ...) \
  CALMCAR_DEBUG(FMT_STRING("[{:s}] " fmt), name_, ##__VA_ARGS__)
#define INFO_VX(fmt, ...) \
  CALMCAR_INFO(FMT_STRING("[{:s}] " fmt), name_, ##__VA_ARGS__)
#define WARNING_VX(fmt, ...) \
  CALMCAR_WARNING(FMT_STRING("[{:s}] " fmt), name_, ##__VA_ARGS__)
#define ERROR_VX(fmt, ...) \
  CALMCAR_ERROR(FMT_STRING("[{:s}] " fmt), name_, ##__VA_ARGS__)
#define CRIT_VX(fmt, ...) \
  CALMCAR_CRIT(FMT_STRING("[{:s}] " fmt), name_, ##__VA_ARGS__)

// Call vx function, check result, if failed, record log and raise exception
#define CHECK_VX_EXCEPT(expr)                                               \
  do {                                                                      \
    vx_status __calmcar_vx__status__ = expr;                                \
    if (VX_SUCCESS != __calmcar_vx_status__) {                              \
      std::string err = fmt::format(                                        \
          FMT_STRING("Failed to execute {:s} with error code {:d}"), #expr, \
          __calmcar_vx_status__);                                           \
      throw std::runtime_error(err);                                        \
    }                                                                       \
  } while (0)

// Call vx function, check result, if failed, record error msg and raise
// exception
#define CHECK_VX_EXCEPT_X(expr, error_fmt, ...)                           \
  do {                                                                    \
    vx_status __calmcar_vx__status__ = expr;                              \
    if (VX_SUCCESS != __calmcar_vx_status__) {                            \
      std::string err = fmt::format(                                      \
          FMT_STRING(                                                     \
              "Failed to execute {:s} with error code {:d}: " error_fmt), \
          #expr, __calmcar_vx_status__, ##VA_ARGS);                       \
      ERROR_VX("{:s}", err);                                              \
      throw std::runtime_error(err);                                      \
    }                                                                     \
  } while (0)

// Call vx function, check result, if failed, record log and return
#define CHECK_VX(expr)                                               \
  do {                                                               \
    vx_status __calmcar_vx_status__ = expr;                          \
    if (VX_SUCCESS != __calmcar_vx_status__) {                       \
      ERROR_VX("Failed to execute {:s} with error code {:d}", #expr, \
               __calmcar_vx_status__);                               \
      return;                                                        \
    }                                                                \
  } while (0)

// Call vx function, check result, if failed, record error msg and return
#define CHECK_VX_X(expr, error_fmt, ...)                                  \
  do {                                                                    \
    vx_status __calmcar_vx_status__ = expr;                               \
    if (VX_SUCCESS != __calmcar_vx_status__) {                            \
      ERROR_VX("Failed to execute {:s} with error code {:d}: " error_fmt, \
               #expr, __calmcar_vx_status__, ##VA_ARGS);                  \
      return;                                                             \
    }                                                                     \
  } while (0)

// Call vx function, check result, if failed, record log and return ret
#define CHECK_VX_RET_V(expr, ret)                                    \
  do {                                                               \
    vx_status __calmcar_vx_status__ = expr;                          \
    if (VX_SUCCESS != __calmcar_vx_status__) {                       \
      ERROR_VX("Failed to execute {:s} with error code {:d}", #expr, \
               __calmcar_vx_status__);                               \
      return ret;                                                    \
    }                                                                \
  } while (0)

// Call vx function, check result, if failed, record error msg and return ret
#define CHECK_VX_RET_V_X(expr, ret, error_fmt, ...)                       \
  do {                                                                    \
    vx_status __calmcar_vx_status__ = expr;                               \
    if (VX_SUCCESS != __calmcar_vx_status__) {                            \
      ERROR_VX("Failed to execute {:s} with error code {:d}: " error_fmt, \
               #expr, __calmcar_vx_status__, ##__VA_ARGS__);              \
      return ret;                                                         \
    }                                                                     \
  } while (0)

// Call vx function, check result, if failed, record log and return status
#define CHECK_VX_RET(expr) CHECK_VX_RET_V(expr, __calmcar_vx_status__)

// Call vx function, check result, if failed, record error msg and return status
#define CHECK_VX_RET_X(expr, error_fmt, ...) \
  CHECK_VX_RET_V_X(expr, __calmcar_vx_status__, error_fmt, __VA_ARGS__)

// Call vx function, check result, if failed, record log and return false
#define CHECK_VX_RET_B(expr) CHECK_VX_RET_V(expr, false)

// Call vx function, check result, if failed, record error msg and return false
#define CHECK_VX_RET_B_X(expr, error_fmt, ...) \
  CHECK_VX_RET_V_X(expr, false, error_fmt, __VA_ARGS__)

static const uint32_t TC_MAX_TENSORS_D = 18;
static const uint32_t APP_MAX_TENSOR_DIMS = 18u;
static const uint32_t APP_TIDL_MAX_PARAMS = 18u;

class Tda4vmInfererv1 : public NNInference {
 public:
  Tda4vmInfererv1();
  virtual ~Tda4vmInfererv1();

  struct InferenceInput {
    enum Format { RGB, BGR, NV12, NV21 };
    // 相机的数据指针，指向推理硬件使用的内存地址（如GPU)
    Format format{};
    int stride[3] = {0, 0, 0};
    // 相机的数据指针，指向推理硬件使用的内存地址（如GPU)
    void* ptr[3] = {nullptr, nullptr, nullptr};
    // 相机图像的宽度.
    int width{0};
    // 相机图像的高度.
    int height{0};
    // 目前格式支持BGR(channel=3) 或RGBA(channel=4).
    int channel{0};
  };

  /** \brief  outputs_host_ and outputs_device_ MUST be initialized after Init.
   * \details When use customize initialization, DO NOT forget to call
   *          NNInference::Init at the beginning of your initialization method.
   * \param profiling   Whether to enable profiling, this is just a flag, can be
   *                    used for runtime analyzing such as profile or tracing.
   * \param model_path  model load path
   * \param batch_size  default batch size
   * \param name inferer name
   * \return  True if initialization of the inference model and resource
   *          allocation is succeeded. */
  virtual int Init(const std::string& model_path, size_t batch_size = 1,
                   bool profiling = false, const std::string& name = {});

  bool AsyncProcess(const std::vector<Input>& inputs, size_t batch_size,
                    bool preprocess = true) override;
  void CopyOutputToHost() override;
  void Synchronize() override;

  float* GetInferResult(size_t output_index);
  float* GetInferOutput();
  void Release();

  size_t input_width(size_t input_index) const override;
  size_t input_height(size_t input_index) const override;
  size_t output_dimension(size_t output_index, size_t dim_index) const override;

  using NNInference::batch_size;
  using NNInference::device_type;
  using NNInference::outputs_device;
  using NNInference::outputs_host;
  using NNInference::profiling_enabled;

 private:
  void GetTensorProperties();
  void VxModelRemoveKernel();
  vx_size GetTensorDataType(vx_int32 tidl_type);
  vx_user_data_object GenerateOutArgs() const;
  std::string ReferName(const std::string& name);
  vx_user_data_object SetInArgs(vx_context context);
  vx_user_data_object SetOutArgs(vx_context context);
  vx_status TIDLScaleData(vx_user_data_object outArgs);
  vx_status TIDLTraceData(vx_user_data_object traceData);
  vx_status FillInputTensors(const InferenceInput& input);
  vx_user_data_object SetCreateParams(vx_context context);
  void TensorOutputWithPad(sTIDL_IOBufDesc_t* buf_desc_);
  void TensorOutputWithoutPad(sTIDL_IOBufDesc_t* buf_desc_);
  float* GetInferResult(float* output_result, size_t output_index);
  vx_tensor CreateInputTensor(vx_size input_sizes[], vx_size data_type) const;
  void GetInputSize(vx_size* input_sizes, sTIDL_IOBufDesc_t* io_desc, int id);

  vx_status CreateOutputTensors(sTIDL_IOBufDesc_t* io_buffer_descriptor,
                                vx_context context, vx_tensor* output_tensors);
  vx_status CreateInputTensors(sTIDL_IOBufDesc_t* io_desc, vx_context context,
                               vx_tensor* input_tensors);
  vx_status LoadVxModelToKernel(vx_context context,
                                const std::string& tidl_network_file_path,
                                const std::string& tidl_config_file_path);
  vx_user_data_object ReadModelParams(vx_context context,
                                      const std::string& params_file);
  vx_user_data_object ReadModelStruct(vx_context context,
                                      const std::string& struct_file);
  vx_node CreateVxModelNode(vx_graph graph, vx_tensor _tensor_in[],
                            vx_tensor _tensor_out[]);
  void FillRgbU8(void* input_buffer, sTIDL_IOBufDesc_t* io_desc,
                 unsigned char* image_data, bool is_bgr, int channel,
                 vx_size* input_sizes, int32_t id);
  void FillRgbU16(void* input_buffer, sTIDL_IOBufDesc_t* io_desc,
                  unsigned char* image_data, bool is_bgr, int channel,
                  vx_size* input_sizes, int32_t id);
  vx_status TensorRead(sTIDL_IOBufDesc_t* buf_desc, unsigned char* image_data,
                       bool is_bgr = false, int channel = 3);
  vx_status TensorReadLimited(sTIDL_IOBufDesc_t* buf_desc,
                              unsigned char* image_data, bool is_bgr = false,
                              int channel = 3);
  void GetOutputSize(vx_size* output_sizes, sTIDL_IOBufDesc_t* io_desc,
                     int id = 0);
  vx_image GetCroppedCameraImage(const vx_image& input_image, int roi_x,
                                 int roi_y, int roi_w, int roi_h);
  std::shared_ptr<void> GetImagePtr(const vx_image& image, int plane_index);
  
  std::string name_{};
  bool profiling_{false};
  size_t max_batch_size_{1};
  size_t actual_batch_size_{1};
  float* hwc_data_ = nullptr;
  bool enable_datatrace_ = false;
  bool scale_initialized_ = true;
  float* output_result_ = nullptr;
  std::vector<int> output_result_length_{};
  std::vector<float> output_scales_{};
  calmcar::utils::TimeProfiler profiler_;
  std::string reference_prefix_ = "tidl_";

  /* OpenVX references */
  vx_node node_ = nullptr;
  uint32_t num_params_ = 0;
  uint32_t max_params_ = 0;
  vx_graph graph_ = nullptr;
  sTIDL_IOBufDesc_t model_desc_;
  vx_kernel kernel_ = nullptr;
  vx_context context_ = nullptr;
  vx_user_data_object dl_config_ = 0;
  vx_user_data_object dl_network_ = 0;
  vx_user_data_object dl_params_ = 0;
  vx_user_data_object dl_args_in_ = 0;
  vx_user_data_object dl_args_out_ = 0;
  vx_user_data_object dl_debugger_ = 0;

  vx_tensor vx_tensor_in_[TC_MAX_TENSORS_D] = {};
  vx_tensor vx_tensor_out_[TC_MAX_TENSORS_D] = {};

  std::vector<vx_user_data_object> dl_args_out_batch_{};
  std::vector<vx_user_data_object> dl_debugger_batch_{};
  std::vector<vx_tensor> vx_tensor_in_batch_[TC_MAX_TENSORS_D];
  std::vector<vx_tensor> vx_tensor_out_batch_[TC_MAX_TENSORS_D];
};

}  // namespace model
}  // namespace perception

#endif
