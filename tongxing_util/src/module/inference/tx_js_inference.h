#ifndef __TX_JS_INFERENCE_H__
#define __TX_JS_INFERENCE_H__
#include "cc_module.h"
#include "json.h"
#include "cc_blob_data.h"
#include "cviruntime.h"
#include <opencv2/opencv.hpp>

namespace tongxing
{
    class TxJsInference : public CcModule
    {
    public:
        int init(const Json::Value &root);
        int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
        ~TxJsInference();

    private:
        std::vector<std::shared_ptr<CcTensor<float>>> input_tensor_ptr;
        std::vector<std::shared_ptr<NumArray>> input_;
        std::vector<std::shared_ptr<NumArray>> output_;
        bool flag_init = false;
        Json::Value root_;

    public:
        CVI_TENSOR *input;
        CVI_TENSOR *output;
        std::vector<CVI_TENSOR *>output_vec;
        float qscale;

    private:
        CVI_MODEL_HANDLE model = nullptr;
        CVI_TENSOR *input_tensors;
        CVI_TENSOR *output_tensors;
        int32_t input_num;
        int32_t output_num;
        CVI_SHAPE shape;
        CVI_SHAPE output_shape;
        bool HavePhysAddr=false;
        
    };

}
#endif