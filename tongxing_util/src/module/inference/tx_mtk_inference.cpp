#include "tx_mtk_inference.h"
#include "CalmCarLog.h"
#include "cc_assert.h"
#include "cc_resource_register.h"
// #include "cnpy.h"
#include <fcntl.h>
#include <stdio.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/stat.h>

namespace tongxing {

int TxMtkInference::init(const Json::Value& root) {
    root_ = root;
    int res = 0;

    BlobData model_data;
    std::string modelFile("");

    if (root_["type"].asString() == "file") {
        cc_assert(model_data.loadFromFile(root_["filename"].asString()) == 0);

    } else if (root_["type"].asString() == "inside") {
        auto model_data_ =
            CcResourcDataRegister::instance().get_function(root_["filename"].asString());
        model_data.pu8VirAddr = (uint8_t*)model_data_.second;
        model_data.u32Size = model_data_.first;

    } else {
        cc_assert(false);
    }

    auto ret = ANeuroPilotTFLiteWrapper_makeTFLiteWithBuffer(&mTFLite, (char*)model_data.pu8VirAddr,
                                                             model_data.u32Size);

    if (ANEURALNETWORKS_NO_ERROR != ret) {
        printf("ANeuroPilotTFLiteWrapper_makeTFLiteWithBuffer error! ret=%d\n", ret);
        return -1;
    }

    ret = ANeuroPilotTFLiteWrapper_getTensorByteSize(mTFLite, TFLITE_BUFFER_TYPE_INPUT, 0,
                                                     &mInputTensorByteSize);
    if (ANEURALNETWORKS_NO_ERROR != ret) {
        printf("ANeuroPilotTFLiteWrapper_getTensorByteSize error! ret=%d\n", ret);
        return -1;
    }

    ret = ANeuroPilotTFLiteWrapper_getTensorCount(mTFLite, TFLITE_BUFFER_TYPE_OUTPUT, &output_num);
    if (ANEURALNETWORKS_NO_ERROR != ret) {
        printf("ANeuroPilotTFLiteWrapper_getTensorCount error! ret=%d\n", ret);
        return -1;
    }

    //根据输出通道个数，确定每个通道的buffer
    for (int j = 0; j < output_num; j++) {
        size_t mOutputSize;
        ret = ANeuroPilotTFLiteWrapper_getTensorByteSize(mTFLite, TFLITE_BUFFER_TYPE_OUTPUT, j,
                                                         &mOutputSize);
        if (ANEURALNETWORKS_NO_ERROR != ret) {
            printf("ANeuroPilotTFLiteWrapper_getTensorByteSize error! ret=%d\n,j=%d", ret, j);
            return -1;
        }

        void* mOutputBoxesBuffer = nullptr;
        mOutputBoxesBuffer = reinterpret_cast<void*>(calloc(1, mOutputSize));
        if (mOutputBoxesBuffer == nullptr) {
            printf("Fail to allocate memory!");
            return -1;
        }
        buffer_vec.push_back(mOutputBoxesBuffer);
    }
    return 0;
}

int TxMtkInference::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}

int TxMtkInference::execute() {
    void* input_addr = input_[0]->data;
    int ret =
        ANeuroPilotTFLiteWrapper_setInputTensorData(mTFLite, 0, input_addr, mInputTensorByteSize);
    if (ret != ANEURALNETWORKS_NO_ERROR) {
        printf("ANeuroPilotTFLiteWrapper_setInputTensorData error! ret=%d\n", ret);
        return ret;
    }

    ret = ANeuroPilotTFLiteWrapper_invoke(mTFLite);
    if (ret != ANEURALNETWORKS_NO_ERROR) {
        printf("ANeuroPilotTFLiteWrapper_invoke error! ret=%d\n", ret);
        return ret;
    }

    std::vector<std::shared_ptr<NumArray>> outputs;
    for (int i = 0; i < output_num; i++) {
        std::shared_ptr<NumArray> output(new NumArray);
        int32_t channel = 0;
        int32_t height = 0;
        int32_t width = 0;

        int dims[4] = {1, 1, 1, 1};  //维度输出

        ret = ANeuroPilotTFLiteWrapper_getTensorDimensions(mTFLite, TFLITE_BUFFER_TYPE_OUTPUT, i,
                                                           dims);
        if (ret != ANEURALNETWORKS_NO_ERROR) {
            printf("ANeuroPilotTFLiteWrapper_getTensorDimensions error! ret=%d\n", ret);
            return ret;
        }
        // printf("output shape:[%d,%d,%d,%d]\n", dims[0], dims[1], dims[2], dims[3]);

        ret = ANeuroPilotTFLiteWrapper_getTensorByteSize(mTFLite, TFLITE_BUFFER_TYPE_OUTPUT, i,
                                                         &mOutputTensorByteSize);
        if (ret != ANEURALNETWORKS_NO_ERROR) {
            printf("ANeuroPilotTFLiteWrapper_getTensorByteSize error! ret=%d\n", ret);
            return ret;
        }

        ret = ANeuroPilotTFLiteWrapper_getOutputTensorData(mTFLite, i, buffer_vec[i],
                                                           mOutputTensorByteSize);
        if (ret != ANEURALNETWORKS_NO_ERROR) {
            printf("ANeuroPilotTFLiteWrapper_getOutputTensorData error! ret=%d\n", ret);
            return ret;
        }

        height = dims[1];
        width = dims[2];
        channel = dims[3];

        output->word_size = sizeof(float);
        output->shape.push_back(1);
        output->shape.push_back(height);
        output->shape.push_back(width);
        output->shape.push_back(channel);
        output->data = (unsigned char*)buffer_vec[i];
        outputs.push_back(output);
    }
    output_ = outputs;

    return 0;
}

size_t TxMtkInference::getOutputNum() {
    return output_.size();
}

std::shared_ptr<NumArray> TxMtkInference::getOutput(int index) {
    return output_[index];
}

TxMtkInference::~TxMtkInference() {
    input_.clear();
    output_.clear();

    if (mTFLite != nullptr) {
        ANeuroPilotTFLiteWrapper_free(mTFLite);
        mTFLite = nullptr;
    }

    for (int j = 0; j < output_num; j++) {
        void* handle = buffer_vec[j];
        if (handle != nullptr) {
            free(handle);
            handle = nullptr;
        }
    }
}

REGISTER_CC_MODULE(mtk_inference, TxMtkInference)
}  // namespace tongxing