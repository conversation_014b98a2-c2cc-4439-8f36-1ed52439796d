#include "tx_t527_inference.h"
#include "CalmCarLog.h"
#include "cc_assert.h"
#include "cc_resource_register.h"
// #include "cnpy.h"
#include <fcntl.h>
#include <stdio.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/stat.h>

namespace tongxing {

int TxT527Inference::init(const Json::Value& root) {
    root_ = root;
    int res = 0;

    BlobData model_data;
    std::string modelFile("");

    if (root_["type"].asString() == "file") {
        cc_assert(model_data.loadFromFile(root_["filename"].asString()) == 0);

    } else if (root_["type"].asString() == "inside") {
        auto model_data_ =
            CcResourcDataRegister::instance().get_function(root_["filename"].asString());
        model_data.pu8VirAddr = (uint8_t*)model_data_.second;
        model_data.u32Size = model_data_.first;

    } else {
        cc_assert(false);
    }

    // npu init
    awnn_init();
    // create network
    if (root_["type"].asString() == "file") {
        context =
            awnn_create(root_["filename"].asString().c_str(), VIP_CREATE_NETWORK_FROM_FILE, 0);
    } else {
        context = awnn_create((char*)model_data.pu8VirAddr, VIP_CREATE_NETWORK_FROM_MEMORY,
                              model_data.u32Size);
    }
    if (context == NULL) {
        printf("awnn_create failed\n");
        return -1;
    }

    for (int k = 0; k < context->output_count; k++) {
        printf("output_name %s \n", context->output_params[k].name);
        int output_shape_dims = context->output_params[k].vip_param.num_of_dims;
        printf("output_shape_dims %d \n", output_shape_dims);

        std::vector<int> shape_out;
        for (int i = 0; i < output_shape_dims; i++) {
            // printf("%d ", context->output_params[k].vip_param.sizes[i]);
            shape_out.push_back(context->output_params[k].vip_param.sizes[i]);
        }
        // tensor shape 进行反转
        reverse(shape_out.begin(), shape_out.end());

        output_shape.push_back(shape_out);
    }

    return 0;
}

int TxT527Inference::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}

int TxT527Inference::execute() {
    void* input_buffers[] = {input_[0]->data};
    awnn_set_input_buffers(context, input_buffers);
    // process network
    awnn_run(context);
    // get result
    float** out_results = awnn_get_output_buffers(context);

    std::vector<std::shared_ptr<NumArray>> outputs;
    for (int i = 0; i < context->output_count; i++) {
        std::shared_ptr<NumArray> output(new NumArray);

        output->word_size = sizeof(float);
        output->shape = output_shape[i];
        output->data = (unsigned char*)out_results[i];
        outputs.push_back(output);
    }
    output_ = outputs;

    return 0;
}

size_t TxT527Inference::getOutputNum() {
    return output_.size();
}

std::shared_ptr<NumArray> TxT527Inference::getOutput(int index) {
    return output_[index];
}

TxT527Inference::~TxT527Inference() {
    // destroy network
    awnn_destroy(context);
    // npu uninit
    awnn_uninit();
}

REGISTER_CC_MODULE(allwinner_inference, TxT527Inference)
}  // namespace tongxing