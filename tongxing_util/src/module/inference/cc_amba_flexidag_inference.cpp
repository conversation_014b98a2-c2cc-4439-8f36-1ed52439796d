#include "cc_amba_flexidag_inference.h"
#include "CalmCarLog.h"
#include "cc_assert.h"
#include "cc_resource_register.h"
#include "cc_numarray_tool.h"
// #include "opencv2/opencv.hpp"
#include <fstream>
#include <string.h>

static bool SchdrStart_boot = false;
static bool for_boot_temp = false;

namespace tongxing
{


    tx_amba_model_init::tx_amba_model_init()
    {
        ;
    }
    tx_amba_model_init::~tx_amba_model_init()
    {
        for (size_t i = 0; i < in.num_of_buf; i++)
        {
            if (in.buf[i].pBuffer != NULL)
            {
                AmbaCV_UtilityCmaMemFree(&(in.buf[i]));
            }
        }

        AmbaCV_FlexidagClose(&fd_gen_handle);
        AmbaCV_UtilityCmaMemFree(&bin_buf);
        AmbaCV_UtilityCmaMemFree(&init_amba.state_buf);

        for (size_t i = 0; i < out.num_of_buf; i++)
        {
           if (out.buf[i].pBuffer != NULL)
           {
                AmbaCV_UtilityCmaMemFree(&(out.buf[i]));
           } 
        }  
    }
   
    int tx_amba_model_init::Algo_0_Cfg()
    {
    // UUID is defined in flow table
    #define OPENOD_VP  (2U)
        AMBA_CV_FLEXIDAG_MSG_s Msg;
        uint16_t FlowID = 0U;
        int Ret = 0U;

        (void) AmbaCV_FlexidagGetFlowIdByUUID(&fd_gen_handle, OPENOD_VP, &FlowID);
        Msg.flow_id = FlowID;
        
        RoiCfg.msg_type = AMBA_ROI_CONFIG_MSG;
        RoiCfg.image_pyramid_index = 0U;
        RoiCfg.source_vin = 0U;
        RoiCfg.roi_start_col = 0U;
        RoiCfg.roi_start_row = 0U;
        RoiCfg.roi_width = input_width;
        RoiCfg.roi_height = input_height;


        Msg.vpMessage = (void *)&RoiCfg;
        Msg.length = sizeof(amba_roi_config_t);
        (void) AmbaCV_FlexidagSendMsg(&fd_gen_handle, &Msg);

        in.num_of_buf = 1;
        if (in.buf[0].pBuffer == NULL)
        {
            Ret = AmbaCV_UtilityCmaMemAlloc(CC_ALIGN128(sizeof(memio_source_recv_raw_t)),1, &(in.buf[0]));
            if (Ret != 0) {
                TX_LOG_FATAL("cc_amba_flexidag_inference.cpp"," MemAlloc ret = %u", Ret);
                return Ret;
            }
            
            int frame_size = CACHE_ALIGN(input_width) * input_height ;
            Ret = AmbaCV_UtilityCmaMemAlloc(frame_size,1, &image_buff);
            if (Ret != 0) {
                TX_LOG_FATAL("cc_amba_flexidag_inference.cpp"," MemAlloc ret = %u", Ret);
                return Ret;
            }
        }
        
    
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");

        return Ret;
    }

    std::map<std::string, std::shared_ptr<tx_amba_model_init>> g_amba_model_map;
    flexidag_memblk_t temp_buf;
    int tx_amba_model_init::init(const std::string &path_name , int width, int height)
    {
        if (!SchdrStart_boot)
        {
            AMBA_CV_FLEXIDAG_SCHDR_CFG_s cfg;
                /* init scheduler */
            cfg.cpu_map = 0x9;
            cfg.log_level = LVL_DEBUG;
            AmbaCV_FlexidagSchdrStart(&cfg);
            SchdrStart_boot = true;
        }

        input_height = height;
        input_width = width;


         //-----------------------
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug model_path=%s",path_name.c_str());
        
        int ret = AmbaCV_UtilityFileSize(path_name.c_str(), &size_align);
        if (ret != 0) {
            TX_LOG_FATAL("cc_amba_flexidag_inference.cpp"," path = %s\n", path_name.c_str());
            return ret;
        }
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug, size_align=%d ", size_align);

        ret = AmbaCV_UtilityCmaMemAlloc(size_align, 1, &bin_buf);
        if(ret != 0) {
                TX_LOG_FATAL("cc_amba_flexidag_inference.cpp"," MemAlloc ret = %u ", ret);
                return ret;
        }
        
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
        ret = AmbaCV_UtilityFileLoad(path_name.c_str(), &bin_buf);
        if (ret != 0) {
            TX_LOG_FATAL("cc_amba_flexidag_inference.cpp","FileLoad path = %s\n", path_name.c_str());
            return ret;
        } 
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
        ret = AmbaCV_FlexidagOpen(&bin_buf, &fd_gen_handle);
        if (ret != 0) {
            TX_LOG_FATAL("cc_amba_flexidag_inference.cpp","Fileopen path = %s\n", path_name.c_str());
            return ret;
        }
        int temp = fd_gen_handle.mem_req.flexidag_temp_buffer_size;
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug temp= %d ",temp);
       
        return 0;
    }

    int tx_amba_model_init::DagUtil_InitDag(flexidag_memblk_t *temp_buf)
    {
        int ret = 0;

        if(init_amba.state_buf.pBuffer == NULL){
            ret = AmbaCV_UtilityCmaMemAlloc(CC_CACHE_ALIGN(fd_gen_handle.mem_req.flexidag_state_buffer_size), 1, &init_amba.state_buf);
            if (ret != 0)
            {
                TX_LOG_FATAL("cc_amba_flexidag_inference.cpp"," MemAlloc ret = %u ", ret);
                return -1;
            }
        }

        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
        if(init_amba.temp_buf.pBuffer == NULL) {
            //all dags share same temp buffer
            init_amba.temp_buf.buffer_size = temp_buf->buffer_size;
            init_amba.temp_buf.buffer_daddr = temp_buf->buffer_daddr;
            init_amba.temp_buf.buffer_cacheable = temp_buf->buffer_cacheable;
            init_amba.temp_buf.pBuffer = temp_buf->pBuffer;
            if (ret != 0) {
                TX_LOG_FATAL("cc_amba_flexidag_inference.cpp","DagUtil_InitDag: assign temp_buf fail\n");
                return -1;
            }
        }
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");

        in.num_of_buf = 0;
        out.num_of_buf = fd_gen_handle.mem_req.flexidag_num_outputs;

        for (int i = 0; i < out.num_of_buf; i++)
        {
            int temp_buf_size = fd_gen_handle.mem_req.flexidag_output_buffer_size[i];
            if (out.buf[i].pBuffer == NULL)
            {
                TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug temp_buf_size=%d " ,CC_ALIGN128(temp_buf_size));
                ret = AmbaCV_UtilityCmaMemAlloc(CC_ALIGN128(temp_buf_size),1, &out.buf[i]);
                out.buf[i].buffer_size = CC_ALIGN128(temp_buf_size);
            }
            if (ret != 0)
            {
                TX_LOG_FATAL("cc_amba_flexidag_inference.cpp"," MemAlloc ret = %u", ret);
                return -1;
            }
        }

        ret = AmbaCV_FlexidagInit(&fd_gen_handle, &init_amba);
        if (ret != 0)
        {
             TX_LOG_FATAL("cc_amba_flexidag_inference.cpp"," FlexidagInit ret = %u", ret);
            //  memio_source_recv_picinfo_t
        }
        
        return 0;
        
    }


    int tx_amba_model_init::Flexidag_Run(std::vector<std::shared_ptr<NumArray>> &in_data, std::vector<std::shared_ptr<NumArray>> &out_data)
    {
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
        int ret = 0;
        AMBA_CV_FLEXIDAG_RUN_INFO_s run_info;
         memio_source_recv_raw_t* raw;
        char *p_in_buf = (char*)in_data[0]->data;
        char *p_im_buf = (char*)image_buff.pBuffer;
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
        for (size_t i = 0; i < input_height; i++)
        {
            memcpy(p_im_buf, p_in_buf, input_width);
            p_in_buf += input_width;
            p_im_buf += CC_CACHE_ALIGN(input_width);
        }
        AmbaCV_UtilityCmaMemClean(&image_buff); //虚拟地址映射到物理地址

        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
        // in.buf[0].buffer_size = CC_CACHE_ALIGN(input_width) * input_height;
        raw = (memio_source_recv_raw_t*)in.buf[0].pBuffer;
        raw->size = CC_CACHE_ALIGN(input_width )* input_height;
        raw->pitch = CC_CACHE_ALIGN(input_width); /* CV5 128bytes align */
        uint32_t start_addr = image_buff.buffer_daddr;  
        raw->addr = start_addr;
        in.buf[0].buffer_size = CC_ALIGN128(sizeof(memio_source_recv_raw_t));
        AmbaCV_UtilityCmaMemClean(&in.buf[0]);


        for( int i = 0; i < out.num_of_buf; i++) { 
        /* since the output came from ORC/VP, ARM should invalidate cache area before access it. */
            AmbaCV_UtilityCmaMemInvalid(&(out.buf[i]));
        }
        ret = AmbaCV_FlexidagRun(&fd_gen_handle, &in, &out, &run_info);

        for (size_t i = 0; i < out.num_of_buf; i++)
        {
            TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
            float* model_buf = (float*)out.buf[i].pBuffer;
            float* out_buf = (float*)out_data[i]->data;

            TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");

            int hout = out_data[i]->shape.size()-1;
            int data_line_long = out_data[i]->shape[hout] * sizeof(float);
            int data_line_long_64 =  CC_CACHE_ALIGN(data_line_long);
            int data_skip_long = (data_line_long_64 - data_line_long)/sizeof(float);
            
            // size_t num_lines = out.buf[i].buffer_size / data_line_long_128;
            int mun = 0;
            if (hout>2)
            {
                mun = out_data[i]->shape[1] * out_data[i]->shape[2] ;
            }else{
                mun = 1;
            }
            
           

            TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug data_line_long: %d data_line_long_128: %d data_skip_long: %d mun: %d",
                         data_line_long, data_line_long_64, data_skip_long, mun);
            for (size_t i = 0; i < mun; i++)
            {
                memcpy(out_buf, model_buf, data_line_long );
                out_buf += data_line_long/sizeof(float);
                model_buf += data_line_long_64/sizeof(float);
            }


            // float* out_buf1 = (float*)out_data[i]->data;
            // for (size_t i = 0; i < mun; i++)
            // {
            //     if (out_buf1[i*5]>0)
            //     {
            //           TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug %0.2f ", out_buf1[i*5]);
            //     }
            // }
            
        }
        return 0;
    }


    int CcAmbaFiexiInference::init(const Json::Value &config)
    {
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp"," init start");
        if (flag_init)
        {
            return -1;
        }
        root_ = config;
        int res = 0;
        BlobData model_data;
        if (root_["type"].asString() == "file")
        {
            model_path = root_["filename"].asString();
            // cc_assert(model_data.loadFromFile(model_path) == 0);
        }
        else if (root_["type"].asString() == "inside")
        {
            auto model_data_ =
                CcResourcDataRegister::instance().get_function(root_["filename"].asString());
            model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            model_data.u32Size = model_data_.first;
        }
        else
        {
            cc_assert(false);
        }
        if (root_["flag_crop"].isBool())
        {
            flag_crop_ = root_["flag_crop"].asBool();
        }
        model_name = root_["m_name"].asString();
        int model_width = root_["width"].asInt();
        int model_height = root_["height"].asInt();
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug ,model_name: %s",model_name.c_str());
        cc_assert(root_["shape"].isArray());
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
        for (int i = 0; i < root_["shape"].size(); i++)
        {
            TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
            std::vector<int> tmp;
            for (int j = 0; j < root_["shape"][i].size(); j++)
            {
                int tmp_val = root_["shape"][i][j].asInt();
                TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug %d" , tmp_val);
                tmp.push_back(root_["shape"][i][j].asInt());
            }
            out_size.push_back(tmp);
        }
     
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
       
        auto item = g_amba_model_map.find(model_name);
  
        if (item == g_amba_model_map.end()){
            std::shared_ptr<tx_amba_model_init> ptr(new tx_amba_model_init);
            ptr->init(model_path, model_width, model_height);
            g_amba_model_map[model_name] = amba_model = ptr;
            TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
        }else{
            amba_model = g_amba_model_map[model_name];
        }

        if (model_name== "eye" && for_boot_temp == false )
        {
            int32_t max_val = 0, temp = 0;
            for (auto it = g_amba_model_map.begin(); it != g_amba_model_map.end(); ++it) {

                std::cout << "Key: " << it->first << ", Value: " << it->second << std::endl;
                temp = it->second->fd_gen_handle.mem_req.flexidag_temp_buffer_size;
                TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug temp : %d", temp);
                if(temp > max_val){
                    max_val = temp;
                }
            }

            if(max_val > 0){
                int ret = AmbaCV_UtilityCmaMemAlloc(CC_CACHE_ALIGN(max_val), 1, &temp_buf);
                if (ret != 0) {
                    printf("[Algo_0_Init] MemUtil_MemblkAlloc temp_buf Fail.\n");
                    return ret;
                }
            }
           
            for (auto it = g_amba_model_map.begin(); it != g_amba_model_map.end(); ++it) {

                std::cout << "Key: " << it->first << ", Value: " << it->second << std::endl;
                int ret = it->second->DagUtil_InitDag(&temp_buf);
                if(ret !=0 ){
                    TX_LOG_FATAL("cc_amba_flexidag_inference.cpp","debug DagUtil_InitDag fail");
                }

                ret =  it->second->Algo_0_Cfg();
                if(ret !=0 ){
                    TX_LOG_FATAL("cc_amba_flexidag_inference.cpp","debug Algo_0_Cfg fail");
                }
            }
            for_boot_temp = true;
        }
        
        

        for (size_t i = 0; i < out_size.size(); i++)
        {
            TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug ");
           std::shared_ptr<NumArray> res = creat_numarray(out_size[i], NumArray::DataType::FLOAT32);
           output_.push_back(res);
        }


        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
        return 0;
    }
    int CcAmbaFiexiInference::setInput(const std::vector<std::shared_ptr<NumArray> > &in)
    {
        input_ = in;
        return 0;
    }
    int num_image = 0;
    int CcAmbaFiexiInference::execute()
    {
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
        int ret = amba_model->Flexidag_Run(input_, output_);
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
        return ret;
    }
    size_t CcAmbaFiexiInference::getOutputNum()
    {
        return 0;
    }
    std::shared_ptr<NumArray> CcAmbaFiexiInference::getOutput(int index)
    {
        // std::shared_ptr<tx_amba_model_init> amba_model;
       
        return output_[index];
    }
    CcAmbaFiexiInference::~CcAmbaFiexiInference()
    {
        TX_LOG_DEBUG("cc_amba_flexidag_inference.cpp","debug");
  
    }
    REGISTER_CC_MODULE(amba_fiexi_inference, CcAmbaFiexiInference)
}