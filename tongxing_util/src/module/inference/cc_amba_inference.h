#ifndef __CC_AMBA_INFERENCE_H__
#define __CC_AMBA_INFERENCE_H__
#include "cc_module.h"
#include "json.h"
#include "eazyai.h"
namespace tongxing{
    #define CC_CACHE_ALIGN(x)              ((x + 0x3F)& ~0x3F)
    class CcAmbaInference :public CcModule{
    public:
        int init(const Json::Value& config);
        int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
        ~CcAmbaInference();
    private:
        std::vector<std::shared_ptr<NumArray> > input_;
        std::vector<std::shared_ptr<NumArray> > output_;
        
        ea_net_t *net=NULL;
        ea_tensor_t* input_tensor=NULL;
        uint8_t flag_init=false;
        // size_t* tensor_shape=NULL;
        int tensor_c = 0;
        int tensor_h = 0;
        int tensor_w = 0;
        int in_pitch = 0;
    

        bool flag_crop_ = false;
        Json::Value root_;
        std::string m_name_;
        long image_mun_=0;

    public:
        
    };
}


#endif