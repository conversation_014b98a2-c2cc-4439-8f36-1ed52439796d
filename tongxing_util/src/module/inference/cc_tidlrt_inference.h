#ifndef _CC_TIDLRT_INFERENCE_H_
#define _CC_TIDLRT_INFERENCE_H_
#include <memory>
#include <TI/tivx.h>
#include <TI/j7_tidl.h>
#include <TI/dl_kernels.h>
#include <tivx_tidl_trace.h>
#include <itidl_rt.h>
#include "cc_module.h"
namespace tongxing {
static const uint32_t TC_MAX_TENSORS_D = 18;
static const uint32_t APP_MAX_TENSOR_DIMS = 18u;

class CcTidlrtInference : public CcModule {
  public:
    int init(const Json::Value& root);
    ~CcTidlrtInference();
    // int init(const Json::Value& config);
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:
    int LoadVxModelToKernel(
            vx_context context, BlobData& tidl_network,
            BlobData& tidl_config);
    vx_user_data_object ReadModelStruct(
            vx_context context, BlobData& tidl_network);
    
    vx_user_data_object ReadModelParams(
            vx_context context, BlobData& params_file);

    vx_user_data_object SetCreateParams(vx_context context);
    vx_user_data_object SetInArgs(vx_context context);
    vx_user_data_object SetOutArgs(vx_context context);
    vx_user_data_object GenerateOutArgs();
    vx_status CreateInputTensors(sTIDL_IOBufDesc_t* io_desc,
                                  vx_context context,
                                  vx_tensor* input_tensors);
    vx_status CreateOutputTensors(sTIDL_IOBufDesc_t* io_buffer_descriptor, 
                              vx_context context, vx_tensor* output_tensors);
                      
    vx_size GetTensorDataType(vx_int32 tidl_type);
    vx_tensor CreateInputTensor(vx_size input_sizes[],
                                             vx_size data_type);
    void GetInputSize(vx_size* input_sizes,
                              sTIDL_IOBufDesc_t* io_desc, int id);

    vx_status TIDLScaleData(vx_user_data_object outArgs);
    void CopyOutputToHost();
    void GetOutputSize(vx_size* output_sizes,
                                    sTIDL_IOBufDesc_t* io_desc, int id);
    vx_node CreateVxModelNode(vx_graph graph, vx_tensor _tensor_in[],
                               vx_tensor _tensor_out[]);
    void VxModelRemoveKernel();
    void FillInputTensor();
    void TensorFillOutput();


    std::vector<std::shared_ptr<CcTensor<float>>> input_tensor_ptr;
    Json::Value root_;

    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;
    vx_context context_ = nullptr;
    vx_graph graph_ = nullptr;
    vx_node node_ = nullptr;
    vx_kernel kernel_ = nullptr;
    sTIDL_IOBufDesc_t model_desc_;
    bool enable_datatrace_ = false;
    bool scale_initialized_ = true;
    size_t max_batch_size_{1};
    vx_user_data_object dl_config_ = 0;
    vx_user_data_object dl_network_ = 0;
    vx_user_data_object dl_params_ = 0;
    vx_user_data_object dl_args_in_ = 0;
    vx_user_data_object dl_args_out_ = 0;
    vx_tensor vx_tensor_in_[TC_MAX_TENSORS_D] = {};
    vx_tensor vx_tensor_out_[TC_MAX_TENSORS_D] = {};
    std::vector<float> output_scales_{};




    std::string net_name;
    std::string io_name;
    int input_w = 0;
    int input_h = 0;

    std::vector<std::vector<int>> out_shape;
    std::vector<int> out_size ;
    sTIDLRT_Tensor_t in_tensor;
    sTIDLRT_Tensor_t out_tensor;
    sTIDLRT_Tensor_t *in_[16];
    sTIDLRT_Tensor_t *out_[16];

};

}  // namespace tongxing

#endif
