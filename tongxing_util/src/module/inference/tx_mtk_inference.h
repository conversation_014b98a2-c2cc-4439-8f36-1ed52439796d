#ifndef __TX_MTK_INFERENCE_H__
#define __TX_MTK_INFERENCE_H__
#include <android/NeuralNetworks.h>
#include "NeuroPilotTFLiteShim.h"
#include "cc_blob_data.h"
#include "cc_module.h"
#include "json.h"

namespace tongxing {
class TxMtkInference : public CcModule {
  public:
    int init(const Json::Value& root);
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);
    ~TxMtkInference();

  private:
    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;
    bool flag_init = false;
    Json::Value root_;

  private:
    ANeuralNetworksTFLite* mTFLite = nullptr;          // mtk推理句柄
    ANeuralNetworksTFLiteOptions* mOptions = nullptr;  // mtk配置选项

    size_t mInputTensorByteSize = 0;   //输入buffer size
    size_t mOutputTensorByteSize = 0;  //输出buffer size
    void* mOutputBuffer = nullptr;
    void* mInputBuffer = nullptr;

    std::vector<void*> buffer_vec;

    int output_num = 1;  //默认输出维度数量
};
}  // namespace tongxing
#endif