#ifndef __TX_GT6125_INFERENCE_H__
#define __TX_GT6125_INFERENCE_H__
#include "cc_module.h"
#include "json.h"
#include "cc_blob_data.h"
#include "cc_tensor.h"
#include "DlSystem/DlError.hpp"
#include "DlSystem/RuntimeList.hpp"
#include "DlSystem/UserBufferMap.hpp"
#include "DlSystem/ITensorFactory.hpp"
#include "DlSystem/IUserBuffer.hpp"
#include "DlContainer/IDlContainer.hpp"
#include "SNPE/SNPE.hpp"
#include "SNPE/SNPEFactory.hpp"
#include "SNPE/SNPEBuilder.hpp"
#include "DiagLog/IDiagLog.hpp"
#include "DlSystem/IUserBufferFactory.hpp"

namespace tongxing
{
    class TxGT6125Inference : public CcModule
    {
    public:
        int init(const Json::Value &root);
        int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
        ~TxGT6125Inference();

    private:
        std::vector<std::shared_ptr<CcTensor<float>>> input_tensor_ptr;
        std::vector<std::shared_ptr<NumArray>> input_;
        std::vector<std::shared_ptr<NumArray>> output_;
        //
        std::unique_ptr<zdl::DlContainer::IDlContainer> m_container;
        zdl::DlSystem::StringList m_outputLayers;
        zdl::DlSystem::StringList m_outputTensor;
        std::unique_ptr<zdl::SNPE::SNPE> m_snpe;
        std::map<std::string, std::vector<size_t> > m_inputShapes;
        std::map<std::string, std::vector<size_t> > m_outputShapes;
        zdl::DlSystem::UserBufferMap m_inputUserBufferMap;
        zdl::DlSystem::UserBufferMap m_outputUserBufferMap;
        std::unordered_map<std::string, std::vector<float>> m_applicationInputBuffers;
        std::unordered_map<std::string, std::vector<float>> m_applicationOutputBuffers;
        std::vector<std::unique_ptr<zdl::DlSystem::IUserBuffer>> m_inputUserBuffers;
        std::vector<std::unique_ptr<zdl::DlSystem::IUserBuffer>> m_outputUserBuffers;
        std::vector<std::string> m_outputLouter_name;

        bool flag_init = false;
        Json::Value root_;
        std::string net_name;
    };

    template <class T>
    void ClearVector(std::vector<T>& vt) 
    {
        std::vector<T> vtTemp; 
        vtTemp.swap(vt);
    }

}
#endif