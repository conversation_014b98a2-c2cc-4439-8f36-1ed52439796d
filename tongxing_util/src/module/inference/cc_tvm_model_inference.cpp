#include "cc_tvm_model_inference.h"
#include "CalmCarLog.h"
#include "cc_numarray_tool.h"
#include "cc_resource_register.h"
#include <cassert>
#include <iostream>
#include <opencv2/opencv.hpp>
#include "time_function.h"

namespace tongxing
{

    std::map<std::string, std::shared_ptr<cc_tvm_model_init>> g_tvm_model_map;

    int cc_tvm_model_init::init(const std::string &name, const std::string &name_input)
    {
        //TX_LOG_INFO("tvm_inference", "init start %s",name.c_str());
        if (tvm_models_path.size()==0)
        {
            tvm_models_path = "/system/app/ZHDms/lib/arm64/";
        }
        
        std::cout << "name : " << tvm_models_path + name << std::endl;
        TX_LOG_INFO("cc_tvm_model_inference.cpp","debug tvm_models_path : %s  name : %s" , tvm_models_path.c_str(),name.c_str());
        mode_mame = name;
        mod_dylib = tvm::runtime::Module::LoadFromFile(tvm_models_path+name);

        mod = mod_dylib.GetFunction("default")(dev);
        tvm::runtime::PackedFunc get_version = mod.GetFunction("get_version");
        std::string version = get_version();
        //TX_LOG_INFO("tvm_inference", "sdnn ver: %s", version.c_str());
        // get the number of input node
        get_num_inputs = mod.GetFunction("get_num_inputs");
        // get the number of output node
        get_num_outputs = mod.GetFunction("get_num_outputs");
        // get the tensor of input node
        get_input = mod.GetFunction("get_input");
        // get set input data function
        set_input = mod.GetFunction("set_input");
        // get run function
        run = mod.GetFunction("run");
        // get output data function
        get_output = mod.GetFunction("get_output");
        tvm::runtime::NDArray in_data = get_input(name_input);
        const DLTensor *in_tensor = in_data.operator->();
        TVMArrayAlloc(in_tensor->shape, in_tensor->ndim, in_tensor->dtype.code,
                      in_tensor->dtype.bits, in_tensor->dtype.lanes, kDLSlimAI, device_id,
                      &input_x);
        _input_size = tvm::runtime::GetDataSize(*input_x);
        TX_LOG_INFO("tvm_inference", "c:%d ,h:%d, w:%d", input_x->shape[1], input_x->shape[2], input_x->shape[3]);
        output_node_num = get_num_outputs();
        _output_tensors.clear();
        _output_shape.clear();
        TX_LOG_INFO("tvm_inference", "out_num:%d", output_node_num);
        _output_shape.resize(output_node_num);
        for (int i = 0; i < output_node_num; i++)
        {
            DLTensor *y;
            tvm::runtime::NDArray out_data = get_output(i);
            const DLTensor *out_tensor = out_data.operator->();
            TVMArrayAlloc(out_tensor->shape, out_tensor->ndim, out_tensor->dtype.code,
                          out_tensor->dtype.bits, out_tensor->dtype.lanes, kDLSlimAI, device_id,
                          &y);
            _output_size.push_back(tvm::runtime::GetDataSize(*y));
            _output_tensors.push_back(y);
            for (int j = 0; j < out_tensor->ndim ; j++)
            {
                _output_shape[i].push_back(out_tensor->shape[j]);
                TX_LOG_INFO("tvm_inference", "i:%d, %d",j, out_tensor->shape[j]);
            }

        }

        TX_LOG_DEBUG("tvm_inference", "init end");
        return 0;
    }

    cc_tvm_model_init::cc_tvm_model_init()
    {
        ;
    }

    cc_tvm_model_init::~cc_tvm_model_init()
    {
        if (input_x != nullptr)
        {
            TVMArrayFree(input_x);
        }
        for (auto &tensor : _output_tensors)
        {
            TVMArrayFree(tensor);
        }
        _output_tensors.clear();
    }

    int CcTvmModelInference::init(const Json::Value &root)
    {
        root_ = root;
        
        auto item = g_tvm_model_map.find(root_["filename"].asString());
        // std::cout << "  g_tvm_model_map : "<<  << std::endl;
        if (item == g_tvm_model_map.end())
        {
            std::shared_ptr<cc_tvm_model_init> ptr(new cc_tvm_model_init);
            ptr->init(root_["filename"].asString(), root_["inputname"].asString());
            g_tvm_model_map[root_["filename"].asString()] = tvm_model = ptr;
        }
        else
        {
            tvm_model = g_tvm_model_map[root_["filename"].asString()];
        }
        //TX_LOG_DEBUG("tvm_inference", " CcTvmModelInference init end");
        return 0;
    }

    int CcTvmModelInference::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;

        return 0;
    }

    size_t CcTvmModelInference::getOutputNum()
    {
        return output_.size();
    }
    long frame_id___=0;
    int CcTvmModelInference::execute()
    {
        TimeFunction time_f("cc_tvm_model_inference.cpp execute "+tvm_model->mode_mame);
        TX_LOG_DEBUG("tvm_inference", " CcTvmModelInference -----start---- %s",tvm_model->mode_mame.c_str());
    
        TVMArrayCopyFromBytes(tvm_model->input_x, (unsigned char *)input_[0]->data_blob_ptr->pu8VirAddr, tvm_model->_input_size);
        tvm_model->set_input(root_["inputname"].asString(), tvm_model->input_x);
        tvm_model->run();
        TVMSynchronize(kDLSlimAI, tvm_model->device_id, nullptr);

       
        output_.clear();
        for (int i = 0; i < tvm_model->output_node_num; ++i)
        { 
            // std::shared_ptr<NumArray> num_output = creat_numarray(
            //     tvm_model->_output_shape, NumArray::DataType::FLOAT32);
            std::shared_ptr<NumArray> num_output = 
            creat_numarray(tvm_model->_output_shape[i], NumArray::DataType::FLOAT32);

            tvm_model->get_output(i, tvm_model->_output_tensors[i]);
            TVMArrayCopyToBytes(tvm_model->_output_tensors[i], (void*)num_output->data, tvm_model->_output_size[i]);
            output_.push_back(num_output);
        }
      
       
        TX_LOG_DEBUG("tvm_inference", " CcTvmModelInference ---end---");
        return 0;
    }

    std::shared_ptr<NumArray> CcTvmModelInference::getOutput(int index)
    {

        return output_[index];
    }

    CcTvmModelInference::~CcTvmModelInference()
    {
        ;
    }
    CcTvmModelInference::CcTvmModelInference()
    {
        ;
    }

    REGISTER_CC_MODULE(tvm_inference, CcTvmModelInference)

} // namespace tongxing
