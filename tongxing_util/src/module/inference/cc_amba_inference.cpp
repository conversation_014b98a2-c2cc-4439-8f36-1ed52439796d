#include "cc_amba_inference.h"
#include "CalmCarLog.h"
#include "cc_assert.h"
#include "cc_resource_register.h"
#include "opencv2/opencv.hpp"
#include <fstream>
#include <string.h>
#include "cc_numarray_tool.h"
#include "time_function.h"
namespace tongxing
{
    int CcAmbaInference::init(const Json::Value &config)
    {
        TX_LOG_DEBUG("cc_amba_inference.cpp","debug");
        ea_net_params_t ea_net_params = {0};
        if (flag_init)
        {
            return -1;
        }
        root_ = config;
        // std::string n_name = config[""].asString();
        m_name_ = root_["m_name"].asString();
      
        int res = 0;
        BlobData model_data;
        if (root_["type"].asString() == "file")
        {
            std::string model_path = root_["filename"].asString();
            cc_assert(model_data.loadFromFile(model_path) == 0);
        }
        else if (root_["type"].asString() == "inside")
        {
            auto model_data_ =
                CcResourcDataRegister::instance().get_function(root_["filename"].asString());
            model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            model_data.u32Size = model_data_.first;
        }
        else
        {
            cc_assert(false);
        }
        net = ea_net_new(NULL);
        if (net == NULL)
        {
            exit(-1);
            return -2;
        }
        if (config["flag_crop"].isBool())
        {
            flag_crop_ = config["flag_crop"].asBool();
        }
        std::string in_name = config["in_name"].asString();


        res = ea_net_load(net, EA_NET_LOAD_DRAM, (void *)model_data.pu8VirAddr, 1);
        if (res != 0)
        {
            ea_net_free(net);
            net = NULL;
            exit(-1);
            return -3;
        }

        input_tensor = ea_net_input_by_index(net, 0);

        const size_t *tensor_shape = ea_tensor_shape(input_tensor);
        tensor_c = tensor_shape[1];
        tensor_h = tensor_shape[2];
        tensor_w = tensor_shape[3];
        in_pitch = ea_tensor_pitch(input_tensor);
        TX_LOG_INFO("cc_amba_inference.cpp","in_pitch %d", in_pitch);
        TX_LOG_INFO("CcAmbaInference", "%s c:%d h:%d w:%d", m_name_.c_str(),
                 tensor_c, tensor_h, tensor_w);
        flag_init = true;
        return 0;
    }
    int CcAmbaInference::setInput(const std::vector<std::shared_ptr<NumArray> > &in)
    {
        input_ = in;
        return 0;
    }
    int num_image = 0;
    int CcAmbaInference::execute()
    {
        // TimeFunction time_f(" ab run","cc_amba_inference.cpp");
        TX_LOG_DEBUG("CcAmbaInference", "--execute--- START %s", m_name_.c_str());
        if(input_.size()==1){
            uint8_t* in=input_[0]->data;
            uint8_t* data_tensor = (uint8_t *)ea_tensor_data(input_tensor);
            for (size_t i = 0; i < tensor_h; i++)
            {
                memcpy(data_tensor,in,tensor_w);
                in += tensor_w;
                data_tensor += in_pitch;
            }
            ea_tensor_sync_cache(input_tensor,EA_CPU,EA_VP); 
        }
        ea_net_forward(net, 1);      
        TX_LOG_DEBUG("CcAmbaInference", "--execute--- end ");
        return 0;
    }
    size_t CcAmbaInference::getOutputNum()
    {
        return ea_net_output_num(net);
    }
    std::shared_ptr<NumArray> CcAmbaInference::getOutput(int index)
    {
        ea_tensor_t *output_tensor = ea_net_output_by_index(net, index);
        ea_tensor_sync_cache(output_tensor, EA_VP, EA_CPU);
        float *model_buf = (float *)ea_tensor_data(output_tensor);
     
        std::vector<int> out_size;
        const size_t *shape = ea_tensor_shape(output_tensor);
        
        out_size.push_back(shape[0]);
        out_size.push_back(shape[1]);
        out_size.push_back(shape[2]);
        out_size.push_back(shape[3]);
        int out_pitch = ea_tensor_pitch(output_tensor);
        TX_LOG_DEBUG("cc_amba_inference.cpp","debug out [%d,%d,%d,%d] pitch:%d", shape[0], shape[1], shape[2], shape[3],out_pitch);
        std::shared_ptr<NumArray> ouput_numarray = creat_numarray(out_size, NumArray::DataType::FLOAT32);
        ouput_numarray->type = NumArray::FLOAT32;

        int mum = shape[0]*shape[1]*shape[2];
        TX_LOG_DEBUG("cc_amba_inference.cpp","mum : %d ", mum);
        float* out_buf = (float*)ouput_numarray->data;

        for (size_t i = 0; i < mum; i++)
        {
            memcpy(out_buf, model_buf, shape[3]*sizeof(float));
            out_buf += shape[3];
            model_buf += out_pitch/sizeof(float);
           
        }
        
        return ouput_numarray;
    }
    CcAmbaInference::~CcAmbaInference()
    {
        ea_net_free(net);
    }
    REGISTER_CC_MODULE(amba_inference, CcAmbaInference)
}