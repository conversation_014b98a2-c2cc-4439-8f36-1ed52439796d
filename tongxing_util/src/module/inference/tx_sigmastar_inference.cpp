#include "tx_sigmastar_inference.h"
#include "cc_resource_register.h"
#include "cc_assert.h"
#include <string.h>
#include "CalmCarLog.h"
#include "mi_sys_datatype.h"
#include "mi_ipu.h"
#include <sys/stat.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <stdio.h>
#include "cnpy.h"
#include "CalmCarLog.h"
// #include "opencv2/opencv.hpp"
// #include "cc_numarray_tool.h"

namespace tongxing
{
    MI_U32 u32BufSize = (2473344);//跑多个模型需要设置成最大模型的bufsize
    // dst_buf 数据存放地址
    // offset 文件指针偏移量
    // size 读取大小
    // ctx 文件路径
    // 读取文件的函数，符合 SerializedReadFunc 的要求
    static int H2SerializedReadFunc(void *dst_buf, int offset, int size, char *ctx)
    {
        // read data from buf
        memcpy(dst_buf, ctx + offset, size);
        return 0;
    }
    std::string GetTensorTypeName(MI_IPU_ELEMENT_FORMAT eIPUFormat) {
        switch (eIPUFormat) {
            case MI_IPU_FORMAT_FP32:
                return "FLOAT32";
            case MI_IPU_FORMAT_INT32:
                return "INT32";
            case MI_IPU_FORMAT_INT16:
                return "INT16";
            case MI_IPU_FORMAT_U8:
                return "UINT8";
            case MI_IPU_FORMAT_INT8:
                return "INT8";
            case MI_IPU_FORMAT_NV12:
                return "YUV_NV12";
            case MI_IPU_FORMAT_ARGB8888:
                return "BGRA";
            case MI_IPU_FORMAT_ABGR8888:
                return "RGBA";
            case MI_IPU_FORMAT_UNKNOWN:
                return "UNKNOWN";
        }
        return "NOTYPE";
    }
    MI_S32 IPUCreateDevice(char *pFirmwarePath, MI_U32 u32VarBufSize)
    {
        MI_S32 s32Ret = MI_SUCCESS;
        MI_IPU_DevAttr_t stDevAttr;
        stDevAttr.u32MaxVariableBufSize = u32BufSize;
        stDevAttr.u32YUV420_W_Pitch_Alignment = 16;
        stDevAttr.u32YUV420_H_Pitch_Alignment = 2;
        stDevAttr.u32XRGB_W_Pitch_Alignment = 16;
        s32Ret = MI_IPU_CreateDevice(&stDevAttr, NULL, pFirmwarePath, 0);
        return s32Ret;
    }
    MI_S32 IPUCreateChannel(MI_U32* u32Channel, char* pModelImage)
    {
        MI_SYS_GlobalPrivPoolConfig_t stGlobalPrivPoolConf;
        MI_IPUChnAttr_t stChnAttr;

        //create channel
        memset(&stChnAttr, 0, sizeof(stChnAttr));
        stChnAttr.u32InputBufDepth = 1;
        stChnAttr.u32OutputBufDepth = 1;
        return MI_IPU_CreateCHN(u32Channel, &stChnAttr, NULL, pModelImage);
    }
    MI_S32 IPUCreateChannel_FromMemory(MI_U32 *s32Channel, char *pModelImage)
    {

        MI_S32 s32Ret;
        MI_SYS_GlobalPrivPoolConfig_t stGlobalPrivPoolConf;
        MI_IPUChnAttr_t stChnAttr;

        // create channel
        memset(&stChnAttr, 0, sizeof(stChnAttr));
        stChnAttr.u32InputBufDepth = 1;
        stChnAttr.u32OutputBufDepth = 1;

        return MI_IPU_CreateCHN(s32Channel, &stChnAttr, H2SerializedReadFunc, pModelImage);
    }
    int TxSigmaStarInference::init(const Json::Value &root)
    {
        if (flag_init)
        {
            return -1;
        }
        root_ = root;
        int res = 0;
        MI_S32 s32Ret;
        BlobData model_data;
        std::string pFirmwarePath = root_["firmware"].asString();
        std::string modelFile("");
        if (root_["type"].asString() == "file")
        {
            cc_assert(model_data.loadFromFile(root_["filename"].asString()) == 0);
        }
        else if (root_["type"].asString() == "inside")
        {
            #if LDMODEL_FROM_MEM
            auto model_data_ =
                CcResourcDataRegister::instance().get_function(modelFile);
            model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            model_data.u32Size = model_data_.first;
            #else
            modelFile = root_["filename"].asString();
            #endif
        }
        else
        {
            cc_assert(false);
        }
        // 1.create device
        #if LDMODEL_FROM_MEM
        if (MI_SUCCESS != MI_IPU_GetOfflineModeStaticInfo(H2SerializedReadFunc, (char *)model_data.pu8VirAddr, &OfflineModelInfo))
        #else
        if (MI_SUCCESS != MI_IPU_GetOfflineModeStaticInfo(NULL, const_cast<char *>(modelFile.c_str()), &OfflineModelInfo))
        #endif
        {
            std::cout << "get model variable buffer size failed!" << std::endl;
            return -1;
        }

        if (MI_SUCCESS != IPUCreateDevice(NULL, OfflineModelInfo.u32VariableBufferSize))
        {
            std::cout << "create ipu device failed!" << std::endl;
            return -1;
        }

        // 2.create channel
        /*case 0 create module from path*/
        #if LDMODEL_FROM_MEM
        if (MI_SUCCESS != IPUCreateChannel_FromMemory(&u32ChannelID, (char *)model_data.pu8VirAddr))
        {
            std::cout << "create ipu channel failed!" << std::endl;
            MI_IPU_DestroyDevice();
            return -1;
        }
        #else 
        if(MI_SUCCESS !=IPUCreateChannel(&u32ChannelID, const_cast<char *>(modelFile.c_str())))
        {
            std::cout<<"create ipu channel failed!"<<std::endl;
            MI_IPU_DestroyDevice();
            return -1;
        }
        #endif
        // 3.get input/output tensor
        s32Ret = MI_IPU_GetInOutTensorDesc(u32ChannelID, &stIpuDesc);
        std::cout << "--------------model in/out info start-----------------" << std::endl;

        std::cout << modelFile << "(" << u32ChannelID << "):" << std::endl;
        for (MI_U32 idx = 0; idx < stIpuDesc.u32InputTensorCount; idx++) {
            std::cout << "Input(" << idx << "):" << std::endl;
            std::cout << "    name:\t" << stIpuDesc.astMI_InputTensorDescs[idx].name << std::endl;
            std::cout << "    dtype:\t" << GetTensorTypeName(stIpuDesc.astMI_InputTensorDescs[idx].eElmFormat) << std::endl;
            std::cout << "    shape:\t[";
            for (MI_U32 jdx = 0; jdx < stIpuDesc.astMI_InputTensorDescs[idx].u32TensorDim; jdx++) {
                std::cout << stIpuDesc.astMI_InputTensorDescs[idx].u32TensorShape[jdx];
                if (jdx < stIpuDesc.astMI_InputTensorDescs[idx].u32TensorDim - 1) {
                    std::cout << ", ";
                }
            }
            std::cout << "]" << std::endl;
            std::cout << "    size:\t" << stIpuDesc.astMI_InputTensorDescs[idx].s32AlignedBufSize << std::endl;
        }
        for (MI_U32 idx = 0; idx < stIpuDesc.u32OutputTensorCount; idx++) {
            std::cout << "Output(" << idx << "):" << std::endl;
            std::cout << "    name:\t" << stIpuDesc.astMI_OutputTensorDescs[idx].name << std::endl;
            std::cout << "    dtype:\t" << GetTensorTypeName(stIpuDesc.astMI_OutputTensorDescs[idx].eElmFormat) << std::endl;
            std::cout << "    shape:\t[";
            for (MI_U32 jdx = 0; jdx < stIpuDesc.astMI_OutputTensorDescs[idx].u32TensorDim; jdx++) {
                std::cout << stIpuDesc.astMI_OutputTensorDescs[idx].u32TensorShape[jdx];
                if (jdx < stIpuDesc.astMI_OutputTensorDescs[idx].u32TensorDim - 1) {
                    std::cout << ", ";
                }
            }
            std::cout << "]" << std::endl;
            std::cout << "    size:\t" << stIpuDesc.astMI_OutputTensorDescs[idx].s32AlignedBufSize << std::endl;
        }
        std::cout << "--------------model in/out info end-----------------" << std::endl;

        s32Ret = MI_IPU_GetInputTensors(u32ChannelID, &stInputTensorVector);
        if (s32Ret != MI_SUCCESS)
        {
            TX_LOG_DEBUG("TxSigmaStarInference", " MI_IPU_GetInputTensors failed: 0x%x ", s32Ret);
            return s32Ret;
        }
        s32Ret = MI_IPU_GetOutputTensors(u32ChannelID, &stOutputTensorVector);
        if (s32Ret != MI_SUCCESS)
        {
            TX_LOG_DEBUG("TxSigmaStarInference", " MI_IPU_GetOutputTensors failed: 0x%x ", s32Ret);
            return s32Ret;
        }
        flag_init = true;
        return 0;
    }
    int TxSigmaStarInference::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        memcpy(stInputTensorVector.astArrayTensors[0].ptTensorData[0], input_[0]->data, input_[0]->data_blob_ptr->u32Size);
        MI_SYS_FlushInvCache(stInputTensorVector.astArrayTensors[0].ptTensorData[0], input_[0]->data_blob_ptr->u32Size);
        return 0;
    }
    int TxSigmaStarInference::execute()
    {
        MI_S32 s32Ret = MI_SUCCESS;
        TX_LOG_DEBUG("sigmastar_inference", "---- execute start----  shape[0]:%d", input_[0]->shape[0]);
        s32Ret = MI_IPU_Invoke(u32ChannelID, &stInputTensorVector, &stOutputTensorVector);
        if (s32Ret != MI_SUCCESS)
        {
            printf("MI_IPU_Invoke error, ret[0x%x]\n", s32Ret);
            return s32Ret;
        }
        float *pfData = (float *)stOutputTensorVector.astArrayTensors[0].ptTensorData[0];
        // 使用 cnpy 库保存数组到 .npy 文件
        // cnpy::npy_save("output.npy", pfData,
        //                {stIpuDesc.astMI_OutputTensorDescs[0].u32TensorShape[0], stIpuDesc.astMI_OutputTensorDescs[0].u32TensorShape[1], stIpuDesc.astMI_OutputTensorDescs[0].u32TensorShape[2], stIpuDesc.astMI_OutputTensorDescs[0].u32TensorShape[3]}, "w");

        // std::cout << "Data saved to output.npy" << std::endl;
        int outputTensorCount = stOutputTensorVector.u32TensorCount;
        std::vector<std::shared_ptr<NumArray>> outputs;
        outputs.reserve(outputTensorCount);
        for (int i = 0; i < outputTensorCount; i++)
        {
            std::shared_ptr<NumArray> na(new NumArray);
            na->type = NumArray::DataType::FLOAT32;
            na->word_size = sizeof(float);
            na->data = (unsigned char *)stOutputTensorVector.astArrayTensors[i].ptTensorData[0];
            if (stIpuDesc.astMI_OutputTensorDescs[i].u32TensorDim == 3)
            {
                na->shape.push_back(stIpuDesc.astMI_OutputTensorDescs[i].u32TensorShape[0]);
                na->shape.push_back(stIpuDesc.astMI_OutputTensorDescs[i].u32TensorShape[1]);
                na->shape.push_back(stIpuDesc.astMI_OutputTensorDescs[i].u32TensorShape[2]);
            }
            else
            {
                na->shape.reserve(stIpuDesc.astMI_OutputTensorDescs[i].u32TensorDim);
                for (int j = 0; j < stIpuDesc.astMI_OutputTensorDescs[i].u32TensorDim; j++)
                {
                    na->shape.push_back(stIpuDesc.astMI_OutputTensorDescs[i].u32TensorShape[j]);
                }
            }
            outputs.push_back(na);
        }
        output_ = outputs;
        TX_LOG_DEBUG("sigmastar_inference", "---- execute end---- ");
        return 0;
    }
    size_t TxSigmaStarInference::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> TxSigmaStarInference::getOutput(int index)
    {
        return output_[index];
    }
    TxSigmaStarInference::~TxSigmaStarInference()
    {
        input_.clear();
        output_.clear();
        MI_IPU_PutInputTensors(u32ChannelID, &stInputTensorVector);
        MI_IPU_PutOutputTensors(u32ChannelID, &stOutputTensorVector);
        MI_IPU_DestroyCHN(u32ChannelID);
        MI_IPU_DestroyDevice();
    }
    REGISTER_CC_MODULE(sigmastar_inference, TxSigmaStarInference)
}