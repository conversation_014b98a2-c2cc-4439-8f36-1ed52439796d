#include "luminance_analyse.h"
#include <math.h>
#include <algorithm>
#include <numeric>
#include <opencv2/imgcodecs.hpp>
#include <opencv2/imgproc.hpp>
#include "../log/CalmCarLog.h"
#include "../util/cc_numarray_tool.h"

namespace tongxing {

int LuminanceAnalyzer::init(const Json::Value& config) {
    TX_LOG_DEBUG("lum_analyzer", "Initializing ROI luminance analyzer");

    if (config.isMember("flag_nchw")) {
        flag_nchw = config["flag_nchw"].asBool();
    }
    return 0;
}

int LuminanceAnalyzer::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    TX_LOG_DEBUG("lum_analyzer", "Setting %zu input arrays", in.size());
    return 0;
}

// 计算旋转ROI的仿射变换矩阵
static void calculateRotationMatrix(float angle, float centerX, float centerY, float matrix[2][3]) {
    float rad = angle * M_PI / 180.0f;
    float cosVal = cosf(rad);
    float sinVal = sinf(rad);

    // 旋转矩阵
    matrix[0][0] = cosVal;
    matrix[0][1] = -sinVal;
    matrix[0][2] = centerX - cosVal * centerX + sinVal * centerY;

    matrix[1][0] = sinVal;
    matrix[1][1] = cosVal;
    matrix[1][2] = centerY - sinVal * centerX - cosVal * centerY;
}

// 双线性插值采样
static float bilinearInterpolation(const uint8_t* img, int width, int height, float x, float y) {
    int x0 = static_cast<int>(x);
    int y0 = static_cast<int>(y);
    int x1 = std::min(x0 + 1, width - 1);
    int y1 = std::min(y0 + 1, height - 1);

    float dx = x - x0;
    float dy = y - y0;

    // 计算四个邻近点的加权平均值
    float val = (1 - dx) * (1 - dy) * img[y0 * width + x0] + dx * (1 - dy) * img[y0 * width + x1] +
        (1 - dx) * dy * img[y1 * width + x0] + dx * dy * img[y1 * width + x1];

    return val;
}

static void save_roi_debug_image(const std::shared_ptr<NumArray>& input,
                                 std::vector<RotatedROI>& rois,
                                 int width,
                                 int height,
                                 int index) {
    if (index != 0)
        return;
    static int cnt = 0;
    cv::Mat debug_img = cv::Mat::zeros(height, width, CV_8UC3);
    std::string info = "";
    std::time_t now = std::time(nullptr);
    char time_str[20];
    std::strftime(time_str, sizeof(time_str), "%Y%m%d_%H%M%S", std::localtime(&now));

    for (size_t roi_idx = 0; roi_idx < rois.size(); roi_idx++) {
        const auto& roi = rois[roi_idx];
        float matrix[2][3];
        calculateRotationMatrix(roi.angle, roi.x + roi.width / 2.0f, roi.y + roi.height / 2.0f,
                                matrix);
        std::vector<cv::Point2f> corners = {
            cv::Point2f(roi.x - roi.width / 2, roi.y - roi.height / 2),
            cv::Point2f(roi.x + roi.width / 2, roi.y - roi.height / 2),
            cv::Point2f(roi.x + roi.width / 2, roi.y + roi.height / 2),
            cv::Point2f(roi.x - roi.width / 2, roi.y + roi.height / 2)};

        for (auto& p : corners) {
            float tx = matrix[0][0] * p.x + matrix[0][1] * p.y + matrix[0][2];
            float ty = matrix[1][0] * p.x + matrix[1][1] * p.y + matrix[1][2];
            p.x = tx;
            p.y = ty;
        }
        // 创建ROI区域的二值图像
        cv::Mat binary_img = cv::Mat::zeros(roi.height, roi.width, CV_8UC1);
        // ROI局部坐标系中心点
        float center_x = roi.width / 2.0f;
        float center_y = roi.height / 2.0f;
        cv::Point image_center(center_x, center_y);

        int sample_cnt = 0;
        int count_255 = 0;
        int count_mid_lum = 0;
        for (size_t i = 0; i < corners.size(); i++) {
            cv::line(debug_img, corners[i], corners[(i + 1) % 4], cv::Scalar(0, 0, 255), 2);
        }
        // 在ROI局部坐标系中采样
        for (int y = 0; y < roi.height; y++) {
            for (int x = 0; x < roi.width; x++) {
                // 计算原图坐标(用于debug_img)
                float gx = roi.x - roi.width / 2.0f + x;
                float gy = roi.y - roi.height / 2.0f + y;
                // 计算旋转后坐标(用于采样)
                float rx = matrix[0][0] * gx + matrix[0][1] * gy + matrix[0][2];
                float ry = matrix[1][0] * gx + matrix[1][1] * gy + matrix[1][2];
                if (rx >= 0 && rx < width && ry >= 0 && ry < height) {
                    const uint8_t* img_data = input->data;
                    float val = bilinearInterpolation(img_data, width, height, rx, ry);
                    if (val >= 200 && val <= 245) {
                        count_mid_lum ++;
                        cv::circle(debug_img, cv::Point(rx, ry), 1, cv::Scalar(255, 255, 255), 1);
                        binary_img.at<uchar>(y, x) = 255;  // ROI局部坐标
                    } else if (val >= 250) {
                        count_255++;
                        cv::circle(debug_img, cv::Point(rx, ry), 1, cv::Scalar(128, 128, 0), 1);
                    }
                    sample_cnt++;
                }
            }
        }
        std::cout << "----------roi " << roi_idx << ":[" << roi.x << "," << roi.y << ","
                  << roi.width << "," << roi.height << "," << roi.angle << "]----------"
                  << std::endl;
        info += cv::format("sample_cnt=%d,count_255=%d,ratio=%.2f,count_mid_lum:%d,ratio=%.2f|", sample_cnt, count_255,
                           float(count_255) / float(sample_cnt), count_mid_lum, float(count_mid_lum) / float(sample_cnt));
        if (roi_idx == rois.size()-1)
            std::cout << "info:" << info.c_str() << std::endl;
        cv::putText(debug_img, info, cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 0.6,
                    cv::Scalar(255, 255, 255), 1);

        // 椭圆检测逻辑
        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(binary_img, contours, cv::RETR_LIST, cv::CHAIN_APPROX_SIMPLE);

        float ratio = float(count_mid_lum) / float(sample_cnt);
        if (!contours.empty()) {
            std::vector<cv::RotatedRect> ellipses;
            for (const auto& contour : contours) {
                // std::cout << "contour.size():" << contour.size() << std::endl;
                if (contour.size() >= 5) {  // 拟合椭圆至少需要5个点
                    cv::RotatedRect ellipse = cv::fitEllipse(contour);
                    ellipses.push_back(ellipse);
                    std::cout << "filter contour.size():" << contour.size() << std::endl;
                }
            }
            // 转换了用于保存调试信息的图片
            cv::Mat debug_binary_img;
            cv::cvtColor(binary_img, debug_binary_img, cv::COLOR_GRAY2BGR);
            // 选择最接近roi中心的椭圆
            if (!ellipses.empty()) {
                cv::RotatedRect selected_ellipse;

                double min_distance = std::numeric_limits<double>::max();
                for (const auto& ellipse : ellipses) {
                    cv::Point2f ellipse_center(ellipse.center.x, ellipse.center.y);
                    // std::cout << "ellipse_center:" << ellipse_center << std::endl;
                    double dx = ellipse_center.x - image_center.x;
                    double dy = ellipse_center.y - image_center.y;
                    double distance = std::sqrt(dx * dx + dy * dy);
                    if (distance < min_distance) {
                        min_distance = distance;
                        selected_ellipse = ellipse;
                    }
                    //debug
                    if ((roi_idx == 0 || roi_idx == 1) && ratio > 0.03 && ratio < 0.08)
                        cv::ellipse(debug_binary_img, ellipse, cv::Scalar(255, 0, 0), 2);
                }
                // 绘制选中的椭圆
                if ((roi_idx == 0 || roi_idx == 1) && ratio < 0.08) {
                    cv::ellipse(debug_binary_img, selected_ellipse, cv::Scalar(0, 255, 0), 2);
                    cv::circle(debug_binary_img, selected_ellipse.center, 1, cv::Scalar(0, 0, 255),
                               -1);
                    // std::cout << "selected_ellipse.center:" << selected_ellipse.center << std::endl;
                }
                // 转换椭圆中心到原始图像坐标系
                if (selected_ellipse.center.y < roi.height - 10 && ratio < 0.08) {
                    float gx = roi.x - roi.width / 2.0f + selected_ellipse.center.x;
                    float gy = roi.y - roi.height / 2.0f + selected_ellipse.center.y;
                    float rx = matrix[0][0] * gx + matrix[0][1] * gy + matrix[0][2];
                    float ry = matrix[1][0] * gx + matrix[1][1] * gy + matrix[1][2];
                    cv::Point orig_center(rx, ry);
                    cv::Point2f raw_origin_center(rx, ry);
                    std::cout << "--- debug info:raw_origin_center:" << raw_origin_center
                              << std::endl;
                    cv::circle(debug_img, orig_center, 5, cv::Scalar(0, 255, 0), -1);
                }
            }
            // 保存二值图像
            if (roi_idx == 0 || roi_idx == 1) {
                std::string bin_path = "../tmp/debug/roi_debug_" + std::string(time_str) + "_" +
                    std::to_string(cnt) + "_bin" + std::to_string(roi_idx) + ".png";
                cv::imwrite(bin_path, debug_binary_img);
            }
        }
    }

    std::string debug_path =
        "../tmp/debug/roi_debug_" + std::string(time_str) + "_" + std::to_string(cnt) + ".png";
    cv::imwrite(debug_path, debug_img);
    cnt++;
    std::cout << "save image in :" << debug_path << std::endl;
}

std::shared_ptr<NumArray> analyse_luminance_info(const std::shared_ptr<NumArray>& input,
                                                 std::vector<RotatedROI> rois,
                                                 bool flag_input_nchw = true) {
    cc_assert(input);
    cc_assert(input->shape.size() == 4);

    int batch = input->shape[0];
    int ch = input->shape[1];
    int width = input->shape[3];
    int height = input->shape[2];

    if (!flag_input_nchw) {
        ch = input->shape[3];
        width = input->shape[2];
        height = input->shape[1];
    }
    // std::cout << "width:" <<width << " height:" << height << std::endl;
    // 创建输出数组: 每个ROI的[均值, 峰值]
    std::vector<int> output_shape;
    const int output_feature_size = 5;
    output_shape.push_back(rois.size());
    output_shape.push_back(output_feature_size);
    auto output = creat_numarray(output_shape, NumArray::DataType::FLOAT32);

    for (int i = 0; i < batch; i++) {
        float* output_ptr = output->getTensor<float>()->operator[](i).data_;
        auto input_tensor = input->getTensor<unsigned char>()->operator[](i);

        for (size_t roi_idx = 0; roi_idx < rois.size(); roi_idx++) {
            const auto& roi = rois[roi_idx];
            float matrix[2][3];
            calculateRotationMatrix(roi.angle, roi.x + roi.width / 2.0f, roi.y + roi.height / 2.0f,
                                    matrix);

            // 采样ROI区域
            float sum = 0.0f;
            float max_val = 0.0f;
            int sample_count = 0;
            // 统计255的数量
            int count_255 = 0;

            int count_mid_lum = 0;
            // 保存图像调试roi亮度分析
            // save_roi_debug_image(input, rois, width, height, roi_idx);

            // 创建ROI区域的二值图像
            cv::Mat binary_img = cv::Mat::zeros(roi.height, roi.width, CV_8UC1);
            // ROI局部坐标系中心点
            float center_x = roi.width / 2.0f;
            float center_y = roi.height / 2.0f;
            cv::Point image_center(center_x, center_y);

            // std::cout << "roi:[" << roi.x << "," << roi.y << "," << roi.width << "," << roi.height << "," << roi.angle << "]" << std::endl;
            // 在旋转后的ROI内均匀采样
            for (int y = 0; y < roi.height; y++) {
                for (int x = 0; x < roi.width; x++) {
                    // 计算原图坐标(用于debug_img)
                    float gx = roi.x - roi.width / 2.0f + x;
                    float gy = roi.y - roi.height / 2.0f + y;
                    // 计算旋转后坐标(用于采样)
                    float rx = matrix[0][0] * gx + matrix[0][1] * gy + matrix[0][2];
                    float ry = matrix[1][0] * gx + matrix[1][1] * gy + matrix[1][2];
                    if (rx >= 0 && rx < width && ry >= 0 && ry < height) {
                        const uint8_t* img_data = input->data;
                        float val = bilinearInterpolation(img_data, width, height, rx, ry);
                        sum += val;
                        max_val = std::max(max_val, val);
                        sample_count++;
                        if (val >= 250) {
                            count_255++;
                        } else if (val >= 200 && val <= 245) {
                            binary_img.at<uchar>(y, x) = 255;  // ROI局部坐标
                            count_mid_lum ++;
                        }
                    }
                }
            }

            cv::Point2f raw_origin_center(-1.0, -1.0);
            if (roi_idx == 0 || roi_idx == 1) {
                // 椭圆检测逻辑
                std::vector<std::vector<cv::Point>> contours;
                cv::findContours(binary_img, contours, cv::RETR_LIST, cv::CHAIN_APPROX_SIMPLE);

                float ratio = (float)count_mid_lum / (float) sample_count; 
                if (!contours.empty() && ratio > 0.03 && ratio < 0.08) {
                    std::vector<cv::RotatedRect> ellipses;
                    for (const auto& contour : contours) {
                        // std::cout << "contour.size():" << contour.size() << std::endl;
                        if (contour.size() >= 5) {  // 拟合椭圆至少需要5个点
                            cv::RotatedRect ellipse = cv::fitEllipse(contour);
                            ellipses.push_back(ellipse);
                            // std::cout << "filter contour.size():" << contour.size() << std::endl;
                        }
                    }
                    // 选择最接近roi中心的椭圆
                    if (!ellipses.empty()) {
                        cv::RotatedRect selected_ellipse;

                        double min_distance = std::numeric_limits<double>::max();
                        for (const auto& ellipse : ellipses) {
                            cv::Point2f ellipse_center(ellipse.center.x, ellipse.center.y);
                            // std::cout << "ellipse_center:" << ellipse_center << std::endl;
                            double dx = ellipse_center.x - image_center.x;
                            double dy = ellipse_center.y - image_center.y;
                            double distance = std::sqrt(dx * dx + dy * dy);
                            if (distance < min_distance) {
                                min_distance = distance;
                                selected_ellipse = ellipse;
                            }
                        }
                        // 转换椭圆中心到原始图像坐标系
                        if (selected_ellipse.center.y < roi.height - 10) {
                            float gx = roi.x - roi.width / 2.0f + selected_ellipse.center.x;
                            float gy = roi.y - roi.height / 2.0f + selected_ellipse.center.y;
                            float rx = matrix[0][0] * gx + matrix[0][1] * gy + matrix[0][2];
                            float ry = matrix[1][0] * gx + matrix[1][1] * gy + matrix[1][2];
                            raw_origin_center = cv::Point2f(rx, ry);
                        }
                    }
                }
                // std::cout << "raw_origin_center:" << raw_origin_center << std::endl;
            }
        
            // 存储统计结果
            if (sample_count > 0) {
                output_ptr[roi_idx * output_feature_size] = (sum - count_255*250) / (sample_count-count_255);  // 均值 (去除极端值的影响)
                output_ptr[roi_idx * output_feature_size + 1] = max_val;         // 峰值
                output_ptr[roi_idx * output_feature_size + 2] =
                    (float)count_255 / sample_count * 100;
                output_ptr[roi_idx * output_feature_size + 3] = raw_origin_center.x;
                output_ptr[roi_idx * output_feature_size + 4] = raw_origin_center.y;

            }
            // std::cout << "sample_count:" << sample_count
            //           << " o1:"
            //           << output_ptr[roi_idx * output_feature_size]
            //           << " o2: "
            //           << output_ptr[roi_idx * output_feature_size + 1]
            //           << " o3:"
            //           << output_ptr[roi_idx * output_feature_size + 2] << std::endl;
        }
    }
    return output;
}

int LuminanceAnalyzer::execute() {
    cc_assert(input_.size() >= 1);

    // if (m_rois.empty()) {
    //     //TODO:无roi设置时的处理逻辑
    // }

    if (input_.size() > 1) {
        m_rois.clear();
        for (size_t i = 1; i < input_.size(); i++) {
            // cc_assert(input_[i]->shape == 5);
            RotatedROI roi;
            float* input_data = input_[i]->getTensor<float>()->operator[](0).data_;
            roi.x = input_data[0];
            roi.y = input_data[1];
            roi.width = input_data[2];
            roi.height = input_data[3];
            roi.angle = input_data[4];
            m_rois.push_back(roi);
        }
    }
    // std::cout << "m_rois.size():"<< m_rois.size() << std::endl;
    auto output = analyse_luminance_info(input_[0], m_rois, flag_nchw);
    std::vector<std::shared_ptr<NumArray>> outputs;
    outputs.push_back(output);
    output_ = outputs;
    TX_LOG_DEBUG("lum_analyzer", "Calculated luminance for %zu ROIs", m_rois.size());

    return 0;
}

size_t LuminanceAnalyzer::getOutputNum() {
    return output_.size();
}

std::shared_ptr<NumArray> LuminanceAnalyzer::getOutput(int index) {
    return output_[index];
}

REGISTER_CC_MODULE(CcLuminanceAnalyzer, LuminanceAnalyzer)

}  // namespace tongxing