#include "occlusion_detector.h"
#include "opencv2/imgproc/imgproc.hpp"
#include "cc_numarray_tool.h"
namespace tongxing
{

  int OcclusionDetector::init(const Json::Value &config)
  {
    if (config.isMember("flag_input_nchw"))
    {
      flag_input_nchw = config["flag_input_nchw"].asBool();
    }
    return 0;
  }
  int OcclusionDetector::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
  {
    input_ = in;
    return 0;
  }
  int OcclusionDetector::execute()
  {
    cc_assert(input_.size() == 1);
    int batch = input_[0]->shape[0];
    int ch = input_[0]->shape[1];
    int height = input_[0]->shape[2];
    int width = input_[0]->shape[3];
    int type = CV_8UC1;
    if (ch == 1)
    {
      type = CV_8UC1;
    }
    else if (ch == 3)
    {
      type = CV_8UC3;
    }
    if (!flag_input_nchw)
    {
      height = input_[0]->shape[1];
      width = input_[0]->shape[2];
    }
    std::shared_ptr<NumArray> output = creat_numarray({batch, 1}, NumArray::DataType::FLOAT32);
    for (int i = 0; i < batch; i++)
    {
      cv::Mat src_img(height, width, type, input_[0]->getTensor<unsigned char>()->operator[](i).data_);
      output->getTensor<float>()->operator[](i).get()=(float)Detect(src_img);
      // printf("%f\n",output->getTensor<float>()->operator[](i).get());
    }
    output_={output};
    return 0;
  }
  size_t OcclusionDetector::getOutputNum()
  {
    return output_.size();
  }
  std::shared_ptr<NumArray> OcclusionDetector::getOutput(int index)
  {
    return output_[index];
  }

  OcclusionDetector::OcclusionDetector()
  {
    InitTable();
    sample_w = 128;
  }

  OcclusionDetector::~OcclusionDetector() {}

  bool OcclusionDetector::Detect(cv::Mat gray)
  {
    cv::Mat edge;
    int w_scale = gray.cols / sample_w;
    if (w_scale)
    {
      int sample_h = gray.rows / w_scale;
      cv::Mat sample_mat = cv::Mat(sample_h, sample_w, CV_8UC1);
      int sample_size = sample_h * sample_h;
      for (unsigned int i = 0; i < sample_h; i++)
      {
        uchar *gray_mat_ptr = gray.ptr<uchar>(i * w_scale);
        uchar *sample_mat_ptr = sample_mat.ptr<uchar>(i);
        for (unsigned int j = 0; j < sample_w; j++)
        {
          sample_mat_ptr[j] = gray_mat_ptr[j * w_scale];
        }
      }
      Edgextract(sample_mat, edge);
    }
    else
    {
      Edgextract(gray, edge);
    }
    cv::Scalar scalar = cv::mean(edge);
    // printf("%f\n",scalar.val[0]);
    if (scalar.val[0] > 6)
    {
      return false;
    }
    return true;
  }

  void OcclusionDetector::InitTable()
  {
    for (unsigned int i = 0; i < 256; ++i)
    {
      table_r[i] = i * 77;
      table_r[i] = table_r[i] >> 8;
      table_g[i] = i * 151;
      table_g[i] = table_g[i] >> 8;
      table_b[i] = i * 28;
      table_b[i] = table_b[i] >> 8;
    }
  }

  void OcclusionDetector::Edgextract(cv::Mat srcImg, cv::Mat &dstImg,
                                     int threshold, int Filter_size)
  {
    cv::medianBlur(srcImg, dstImg, Filter_size);
    cv::Canny(dstImg, dstImg, threshold, threshold * 2);
  }
REGISTER_CC_MODULE(OcclusionDetector, OcclusionDetector)
} // namespace cc_dms
