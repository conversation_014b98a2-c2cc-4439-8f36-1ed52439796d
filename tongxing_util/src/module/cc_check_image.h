#ifndef _CHECK_IMAGE_H_
#define _CHECK_IMAGE_H_

#define OPENCV 1
#define AMBACV 2

#if(AMBA_SDK)
#define CV_VERSION_ AMBACV
#else
#define CV_VERSION_ OPENCV
#endif

#include <opencv2/opencv.hpp>
#include "cc_module.h"


namespace tongxing
{
enum ImageStatus{
    Norm = 0,//正常
    Overexposure = 1,//过曝
    Underexpose = 2,//欠曝
    Blur = 3,//模糊
    Occlusion = 4,//遮挡
    Other = 5
};
class CcCheckImage : public CcModule
{
     public:
        int init(const Json::Value &config);
        int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
        CcCheckImage();
        ~CcCheckImage();

    private:
        std::vector<std::shared_ptr<NumArray>> input_;
        std::vector<std::shared_ptr<NumArray>> output_;
        bool flag_input_nchw=true;

        // check image config
        int cornerneeded = 1000;
        int hist_window_size = 5;
        double percent_threshold = 0.4;
        double blurstd_threshold = 3.0;
        int overexposure_threshold = 130;
        int underexpose_threshold = 50;

        // double occulusionDetect(cv::Mat &image); 
        ImageStatus checkImageStatus(cv::Mat &image);
        bool blurDetect(cv::Mat &image, const double stddev_threshold, const int cornerneeded);
};

}
#endif
