#ifndef _ROI_LUMINANCE_ANALYSE_H_
#define _ROI_LUMINANCE_ANALYSE_H_

#include "cc_module.h"
#include "cc_numarray_tool.h"
#include "json.h"
// 自定义图像数据结构
struct ImageData {
    const unsigned char* data;
    int width;
    int height;
    int channels;
};

// 带旋转角度的ROI结构体
struct RotatedROI {
    int x;
    int y;
    int width;
    int height;
    float angle;  // 旋转角度(度)
};

namespace tongxing {

class LuminanceAnalyzer : public CcModule {
  public:
    int init(const Json::Value& config);
    int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:
    std::vector<std::shared_ptr<NumArray> > input_;
    std::vector<std::shared_ptr<NumArray> > output_;
    std::vector<RotatedROI> m_rois;

    std::vector<float> m_luminanceValues;

    std::vector<float> getLuminanceValues() const;

    bool flag_nchw = true;
};

}  // namespace tongxing

#endif  // _LUMINANCE_ANALYSE_H_