#include "cc_module.h"

#include "cc_assert.h"
#include "opencv2/opencv.hpp"

namespace tongxing{
    CcModule::CcModule(){
        // std::cout<<"CcModule::constructor"<<std::endl;
    }
    CcModule::~CcModule(){
        //  std::cout<<"CcModule::deconstructor"<<std::endl;
    }
    int CcModule::init(const Json::Value& config){
        // std::cout<<config["name"].asString()<<std::endl;
        // std::cout<<"CcModule::init"<<std::endl;
        return 0;
    }
    int CcModule::setInput(const std::vector< std::shared_ptr<NumArray> >& in){
        
        input_=in;
        output_=in;
        return 0;
    }


    int CcModule::execute(){

        return 0;
    }

    size_t CcModule::getOutputNum(){
        return output_.size();
    }
    std::shared_ptr<NumArray> CcModule::getOutput(int index){
            //         cv::Mat m(output_[index]->shape[2],output_[index]->shape[3],CV_8UC1,output_[index]->data);
            // cv::imwrite("calmcar_test_image.jpg",m);
        return output_[index];
    }


 static CcModuleRegister g_register __attribute__((init_priority(102)));
 CcModuleRegister::CcModuleRegister()
 {
    // std::cout<<"CcModuleRegister constructor"<<std::endl;
 }
 CcModuleRegister& CcModuleRegister::instance() {

    return g_register;

   
}
void CcModuleRegister::module_register(const std::string& key, CreatCcModule_fun fun) {
    fun_map[key] = fun;
}
std::shared_ptr<CcModule> CcModuleRegister::get_module(const std::string& key,
                                                       const Json::Value& config) {
    auto iter = fun_map.find(key);
    cc_assert(iter != fun_map.end());
    

    std::shared_ptr<CcModule> ret(iter->second(config));
    // std::shared_ptr<NumArray> image_numArray(new NumArray());
    // ret->setInput({image_numArray});
    // std::cout<<key<<" "<<(void*)iter->second<<" "<<ret.get()<<std::endl;
    // for(auto i:fun_map)
    // {
    //     std::cout<<i.first<<" "<<(void*)i.second<<std::endl;
    // }
    // std::cout<<(void*)iter->second<<std::endl;
    return ret;
}
void CcModuleRegister::del_function(const std::string& key) {
    auto iter = fun_map.find(key);
    if (iter == fun_map.end()) {
        return;
    }
    fun_map.erase(iter);
}


    std::shared_ptr<CcModule>  get_cc_module(const Json::Value& config){
        std::string key;
        key=config["class_name"].asString();
        // std::cout<<"key  :"<<key<<std::endl;
        auto ret=tongxing::CcModuleRegister::instance().get_module(key,config["config"]);
        //  std::cout<<"ret   :"<<ret.get()<<std::endl;
        return ret;
    }
 
    REGISTER_CC_MODULE(InputLayer,CcModule)
}



