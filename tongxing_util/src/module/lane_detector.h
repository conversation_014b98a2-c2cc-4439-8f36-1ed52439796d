#ifndef _LANE_DETECTOR_
#define _LANE_DETECTOR_

#include "opencv2/core/core.hpp"
#include "cc_module.h"
namespace tongxing
{
    class LaneDetector : public CcModule
    {
    public:
        int init(const Json::Value &config);
        int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
        LaneDetector();
        ~LaneDetector();

    private:
        std::vector<std::shared_ptr<NumArray>> input_;
        std::vector<std::shared_ptr<NumArray>> output_;
        bool flag_input_nchw = true;
        int sample_w;
        std::pair<float, float> get_slope_intercept(int x1, int y1, int x2, int y2);
        std::vector<cv::Point> unify_output_format(cv::Vec4i avg_line);
        std::vector<std::vector<cv::Point>> process_detect_lines(cv::Mat &gray);
        std::vector<std::vector<cv::Point>> Detect(cv::Mat gray);

    public:
        cv::Rect lane_roi;

    private:
        int max_output_num = 1024;
        bool keep_ratio = true;
        bool flag_image_shape_nchw = false;
        int class_num_ = 1;
    private:
        cv::Vec4i prev_left_lines;
        cv::Vec4i prev_right_lines;
        bool is_first_frame;
        int index = 0;
    };

} // namespace tongxing
#endif
