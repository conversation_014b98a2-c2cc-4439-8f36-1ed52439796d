#include "svm_model.h"
#include "cc_numarray_tool.h"
#include "cc_assert.h"
#include <opencv2/opencv.hpp>

namespace tongxing
{
    CcSvmModel::CcSvmModel(){}
    CcSvmModel::~CcSvmModel() {}

    std::shared_ptr<CcSvmModel> CcSvmModel::instance = std::make_shared<CcSvmModel>();

    int CcSvmModel::init(int mapping_width, int mapping_height, 
        const std::vector<std::vector<cv::Point2f>> &hull, float tolerate_percentage)
    {
        if (instance->init_flag == true)
            return -1;

        if (hull.size() > 0 /*&& mapping_width > 0 && mapping_height > 0*/) {
            instance->tolerate_percentage = tolerate_percentage;
            instance->mapping_width = mapping_width;
            instance->mapping_height = mapping_height;
            instance->hull_.clear();
            instance->hull_.push_back(hull);
            // for (auto &h:hull)
            // {
            //     std::cout << "CcSvmModel::init h:" << h << std::endl;
            // }
            // 避免重复初始化
            instance->init_flag = true;
        }
        else {
            return -1;
        }

        return 0;
    }
 
    std::shared_ptr<CcSvmModel> CcSvmModel::getInstance()
    {
        if (instance.get() == nullptr)
        {
            instance.reset(new CcSvmModel);
        }
        return instance;
    }

    int CcSvmModel::set_cali_angle3d(float pitch_cali, float yaw_cali,  float roll_cali)
    {
        // 获得标定的初始角度值
        instance->cali_angle3d_.resize(3);
        instance->cali_angle3d_[0] = pitch_cali;
        instance->cali_angle3d_[1] = yaw_cali;
        instance->cali_angle3d_[2] = roll_cali;

        return 0;
    }

    int CcSvmModel::predict(float pitch, float yaw,  float roll, int region, cv::Point2f &point, double &distance_to_hull)
    {   
        if (instance->cali_angle3d_.size() != 3)
        {
            std::cout << "Not calibration already..." << std::endl;
            return -1;

        }

        //2.data parse
        std::vector<float> angle3d = {pitch-instance->cali_angle3d_[0], yaw-instance->cali_angle3d_[1], -(roll-instance->cali_angle3d_[2])};
        cv::Point2f mapping2d = angle3d_mappingto_len2d(angle3d);

        //3.model predict
        eyesight_region predict_result = eyesight_region_none;
        int index = -1;
        // std::cout << "region:"<< region << " 2 mapping_point:" << mapping2d << std::endl;
        point = mapping2d;

        if (region == 0)
        {
            index = isinHull(mapping2d, instance->hull_[0], instance->tolerate_percentage, distance_to_hull);
            // printf("\033[33m --hjh-- file:svm_model.cpp line:%d info:index:%d \033[0m \n ",__LINE__, index);


        }
        // else if (region == 1)
        // {
        //     index = isinHull(mapping2d, instance->hull_[1], instance->tolerate_percentage);

        // }
        // if (index == 0)
        // {
        //     predict_result = eyesight_region_front;
        // }
        // else if (index == 1)
        // {
        //     predict_result = eyesight_region_left;
        // }
        // else if (index == 2)
        // {
        //     predict_result = eyesight_region_right;
        // }
        // else if (index == 3)
        // {
        //     predict_result = eyesight_region_center_panel;
        // }
        // return predict_result;
        return index;
    }

    static inline void clipToBounds(float &value, float up_limit, float down_limit) {
    if (value > up_limit)
        value = up_limit;
    else if (value < down_limit)
        value = down_limit;
    }

    //  angle 3d->len 2d mapping。(0,0,1)向量与前方固定长度的x-y平面交点求解问题。
    cv::Point2f CcSvmModel::angle3d_mappingto_len2d(std::vector<float>& angle3d) {
        float L = 100.0;
        cv::Point2f mapping_point(0, 0);

        if (angle3d[0] < -85 || angle3d[0] > 85 || angle3d[1] < -85 || angle3d[1] > 85)
        {
            std::cout << "angle pith or yaw is out of range[-85, 85]..." << std::endl;
            return mapping_point;
        }

        float angle_pitch = angle3d[0] * CV_PI / 180.0;
        float angle_yaw = angle3d[1] * CV_PI / 180.0;
        float angle_roll = -angle3d[2] * CV_PI / 180.0;

        float cos_theta = std::cos(angle_pitch);
        float sin_theta = std::sin(angle_pitch);
        float cos_psi = std::cos(angle_yaw);
        float sin_psi = std::sin(angle_yaw);
        float cos_phi = std::cos(angle_roll);
        float sin_phi = std::sin(angle_roll);

        float x = L * (sin_psi/cos_psi); //1°的角度偏移约等于1.75的距离偏移
        float y = L * (sin_theta/(cos_theta*cos_psi));
        // 处理越界的投射点（8L的原因是70°的投射距离约为8L）
        clipToBounds(x, 4*L, -4*L);
        clipToBounds(y, 4*L, -4*L);

        // 加入roll的影响
        float rotated_x = cos_phi * x - sin_phi * y;
        float rotated_y = sin_phi * x + cos_phi * y;
        mapping_point = cv::Point2f(rotated_x, rotated_y);

        // 坐标系转换
        mapping_point.x = instance->mapping_width*0.5 + mapping_point.x;
        mapping_point.y = instance->mapping_height*0.5 - mapping_point.y; 

        return mapping_point;
    }

    int CcSvmModel::isinHull(cv::Point2f point, std::vector<std::vector<cv::Point2f>> hulls, double tolerate_percentage, double &distance_to_hull)
    {
        // 允许设置一定的容忍距离来处理模型结果的抖动带来的漏检
        double p2r_tolerate_dis = (tolerate_percentage*mapping_width);
        int max_index = -1;
        int index = 0;
        double max_value = 0.0;
        for (auto &hull:hulls)
        {
            double retval = cv::pointPolygonTest(hull, point, true);
            if (retval >= max_value)
            {
                max_value = retval;
                max_index = index; //根据和边界的距离得到最符合的索引
            }

            index++;
        }

        // 如果上一帧不为正常区域且当前帧为正常区域，需要对其进行容忍处理
        static int tolerate_count = 0;
        constexpr int TOLERATE_FRAME_COUNT = 5;
        if (-1 == last_region_index && 0 ==
            max_index && max_value <= p2r_tolerate_dis) {
            if (tolerate_count++ >= TOLERATE_FRAME_COUNT) { // 容忍5帧
                last_region_index = 0;
                tolerate_count = 0;
            } else {
                max_index = last_region_index;
            }
        } else {
            last_region_index = max_index;
            tolerate_count = 0;
        }

        // 将点和边界的距离传出
        distance_to_hull = max_value;

        return max_index;
    }
    void CcSvmModel::clear()
    {
        instance->init_flag = false;
        instance->hull_.clear();
        instance->cali_angle3d_.clear();
    }

    REGISTER_CC_MODULE(SvmModel, CcSvmModel)
}