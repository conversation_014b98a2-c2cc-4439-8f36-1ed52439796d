#include "cc_check_image.h"
#include <sys/time.h>
#include "cc_numarray_tool.h"
#if CV_VERSION_ == OPENCV
#include "opencv2/core/core.hpp"
#elif CV_VERSION_ == AMBACV
#include <opencv2/opencv.hpp>
#include "cavalry_mem.h"
#include "eazyai.h"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/imgproc/imgproc.hpp"
#include "vproc.h"

#else

#endif
#define DEBUG_OCC 0
#define ALIGN_PITCH(x) (((x) + (CAVALRY_PORT_PITCH_ALIGN)-1) & ~((CAVALRY_PORT_PITCH_ALIGN)-1))
namespace tongxing {
typedef struct cv_mem_s {
    void* virt;
    unsigned long phys;
    unsigned long size;
} cv_mem_t;

#if CV_VERSION_ == AMBACV

int create_vect(
    vect_desc_t* vect, cv_mem_t* vm, int h, int w, int d, int data_size, int with_cache) {
    vect->shape.p = 1;
    vect->shape.d = d;
    vect->shape.h = h;
    vect->shape.w = w;
    vect->pitch = ALIGN_PITCH(w * data_size);
    vect->color_space = CS_VECT;
    vm->size = vect->pitch * vect->shape.h * vect->shape.d;
    if (cavalry_mem_alloc(&vm->size, &vm->phys, &vm->virt, with_cache) < 0) {
        perror("cavalry_mem_alloc err\n");
        return -1;
    }
    vect->data_addr = vm->phys;

    return vm->size;
}

int free_vmem(cv_mem_t* vm) {
    if (vm->virt != NULL) {
        if (cavalry_mem_free(vm->size, vm->phys, vm->virt) < 0) {
            printf("cavalry_mem_free err\n");
            return -1;
        }
    }
    return 0;
}
#endif

int CcCheckImage::init(const Json::Value& config) {
    if (config.isMember("flag_input_nchw")) {
        flag_input_nchw = config["flag_input_nchw"].asBool();
    }
    // add check image config
    if (config.isMember("hist_window_size")) {
        hist_window_size = config["hist_window_size"].asInt();
    }
    if (config.isMember("percent_threshold")) {
        percent_threshold = config["percent_threshold"].asDouble();
    }
    if (config.isMember("blurstd_threshold")) {
        blurstd_threshold = config["blurstd_threshold"].asDouble();
    }
    if (config.isMember("overexposure_threshold")) {
        overexposure_threshold = config["overexposure_threshold"].asInt();
    }
    if (config.isMember("underexpose_threshold")) {
        underexpose_threshold = config["underexpose_threshold"].asInt();
    }
    if (config.isMember("cornerneeded")) {
        cornerneeded = config["cornerneeded"].asInt();
    }

    return 0;
}
int CcCheckImage::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}

size_t CcCheckImage::getOutputNum() {
    return output_.size();
}
std::shared_ptr<NumArray> CcCheckImage::getOutput(int index) {
    return output_[index];
}

CcCheckImage::CcCheckImage() {}

CcCheckImage::~CcCheckImage() {}

int CcCheckImage::execute() {
    cc_assert(input_.size() == 1);
    int batch = 1;     // input_[0]->shape[0];
    int ch = 1;        // input_[0]->shape[1];
    int height = 720;  // input_[0]->shape[2];
    int width = 1280;  // input_[0]->shape[3];
    if (flag_input_nchw) {
        batch = input_[0]->shape[0];
        ch = input_[0]->shape[1];
        height = input_[0]->shape[2];
        width = input_[0]->shape[3];
    } else {
        batch = input_[0]->shape[0];
        ch = input_[0]->shape[3];
        height = input_[0]->shape[1];
        width = input_[0]->shape[2];
    }

    int type = CV_8UC1;
    if (ch == 1) {
        type = CV_8UC1;
    } else if (ch == 3) {
        type = CV_8UC3;
    }
    // if (!flag_input_nchw)
    // {
    //     height = input_[0]->shape[1];
    //     width = input_[0]->shape[2];
    // }

    std::shared_ptr<NumArray> output = creat_numarray({batch, 1}, NumArray::DataType::FLOAT32);

    for (int i = 0; i < batch; i++) {
#if 0
            // ea_tensor_t *in = (ea_tensor_t *)input_[0]->data_handle;
            // size_t shape[4];
            // shape[0] = batch;
            // shape[1] = ch;
            // shape[2] = height;
            // shape[3] = width;
            // ea_tensor_t *phone_gray = ea_tensor_new(EA_U8, shape, 0);
            // ea_crop_resize(&in, 1, &phone_gray, 1, NULL, EA_TENSOR_COLOR_MODE_GRAY, EA_VP);
            // // ea_tensor_sync_cache(phone_gray, EA_VP, EA_CPU);
            // char *gray_tensor = (char *)ea_tensor_data(phone_gray);
            cv::Mat gray_img(height, width, type, input_[0]->data);
            // memcpy(gray_img.data, gray_tensor, height * width * ch);

            //  ea_tensor_sync_cache(input_tensor,EA_VP,EA_CPU);
            //         char* data_tensor = (char *)ea_tensor_data(input_tensor);

            //  cv::Mat img(tensor_h,tensor_w,CV_8UC1,data_tensor);

            //          char outfile[50];

            //          sprintf(outfile,"./%dx%d_image_%d.jpg",tensor_h,tensor_w,image_mun_);
            //          image_mun_++;
            //         cv::imwrite(outfile,img);

            //  cv::Mat img(256,256,CV_8UC1,in);
            //  cv::imwrite("write.jpg",gray_img);
            // ea_tensor_free(phone_gray);
#else
        cv::Mat gray_img;
        if (type == CV_8UC1 || flag_input_nchw == false) {
            gray_img = cv::Mat(height, width, type, input_[0]->data);
        } else {
            cv::Mat r_img(height, width, CV_8UC1, input_[0]->data);
            cv::Mat g_img(height, width, CV_8UC1, input_[0]->data + (height * width));
            cv::Mat b_img(height, width, CV_8UC1, input_[0]->data + (height * width * 2));
            gray_img = r_img / 3 + g_img / 3 + b_img / 3;
        }
        cv::resize(gray_img, gray_img, cv::Size(256, 256));
#endif
        ImageStatus out_data;

        // double ret = occulusionDetect(gray_img);
        out_data = checkImageStatus(gray_img);
        #if DEBUG_OCC
        //test
        static int cnt = 0;
        static int cnt0 = 0;
        static int cnt1 = 0;
        static int cnt2 = 0;
        static int cnt3 = 0;
        static int cnt4 = 0;
        cnt++;
        if (out_data == 0) {
            cnt0++;
        }
        else if (out_data == 1) {
            cnt1++;
        }
        else if (out_data == 2) {
            cnt2++;
        }
        else if (out_data == 3) {
            cnt3++;
        }
        else if (out_data == 4) {
            cnt4++;
        }
        std::cout << "occ_status:" << out_data << std::endl;
        std::cout << "res:" << cnt << " " << cnt0 << " " << cnt1 << " "\
            << cnt2 << " " << cnt3 << " " << cnt4 << std::endl;
        #endif

        output->getTensor<float>()->operator[](i).get() = out_data;
    }

    output_ = {output};
    return 0;
}

#if CV_VERSION_ == OPENCV

bool CcCheckImage::blurDetect(cv::Mat& image, const double mean_threshold, const int cornerneeded) {
    bool is_blur = false;
    // 判断图像模糊状态
    cv::Mat lap_image;
    cv::Laplacian(image, lap_image, -1);  // 使用Laplacian算子计算拉普拉斯幅度图
    // cv::Scalar fuzziness = cv::mean(lap_image);
    cv::Scalar mean;    // 均值
    cv::Scalar stddev;  // 标准差
    int corner_count = 0;

    cv::meanStdDev(lap_image, mean, stddev);  // 计算均值和标准差
    double mean_pxl = mean.val[0];
    double stddev_pxl = stddev.val[0];
    if (mean_pxl < mean_threshold) {
        is_blur = true;
    } else {
        //降采样减少角点检测时间
        cv::resize(image, image, cv::Size(), 0.5, 0.5);
        // 高斯模糊
        cv::Mat gray_blurred;
        GaussianBlur(image, gray_blurred, cv::Size(5, 5), 0);

        // 转换为32位浮点类型
        cv::Mat gray_float;
        gray_blurred.convertTo(gray_float, CV_32F);

        // 进行角点检测
        cv::Mat dst;
        cornerHarris(gray_float, dst, 2, 3, 0.04);

        // 计算角点数量
        double minVal, maxVal;
        cv::minMaxLoc(dst, &minVal, &maxVal);
        // cv::Mat result = image.clone();
        // cv::cvtColor(result, result, cv::COLOR_GRAY2BGR);
        for (int i = 0; i < dst.rows; i++) {
            for (int j = 0; j < dst.cols; j++) {
                if (dst.at<float>(i, j) > (0.01 * maxVal)) {
                    corner_count++;
                    // cv::circle(result, cv::Point(j, i), 5, cv::Scalar(0, 0, 255), 2);
                }
            }
        }
        // cv::imwrite("harriscorner.jpg", result);

        //增加角点检测解决摄像头遮挡漏检导致报出的人脸遮挡问题
        if (corner_count >= cornerneeded) {
            is_blur = true;
        }
    }
    #if DEBUG_OCC
    std::cout << "mean_pxl:" << mean_pxl << std::endl;
    std::cout << "corner_count:" << corner_count << std::endl;
    #endif
    return is_blur;
}

ImageStatus CcCheckImage::checkImageStatus(cv::Mat& image) {
    cv::Mat srcImage = image;
    ImageStatus status = Norm;
    double meanBrightness = 0.0;

    // 计算亮度直方图和亮度总和
    std::vector<int> histogram(256, 0);
    long long totalBrightness = 0;
    int totalPixels = srcImage.rows * srcImage.cols;

    for (int y = 0; y < srcImage.rows; y++) {
        for (int x = 0; x < srcImage.cols; x++) {
            int pixelValue = srcImage.at<uchar>(y, x);
            histogram[pixelValue]++;
            totalBrightness += pixelValue;
        }
    }

    meanBrightness = static_cast<double>(totalBrightness) / totalPixels;

    // 寻找直方图最大亮度值窗口，并计算该窗口内像素点个数与总像素点数的比值
    int pixsum_max = 0;
    int peak_idx = 0;
    if (histogram.size() < hist_window_size) {
        std::cout << "Histogram size is smaller than the window size." << std::endl;
    }

    for (int i = 0; i <= histogram.size() - hist_window_size; i++) {
        int pix_sum = 0;
        for (int j = 0; j < hist_window_size; j++) {
            pix_sum += histogram[i + j];
        }

        if (pixsum_max < pix_sum) {
            pixsum_max = pix_sum;
            peak_idx = i;
        }
    }

    // 计算最大像素点个数与总像素点数的比值
    double area_percent = (double)pixsum_max / (double)totalPixels;

    // TX_LOG_INFO("cc_check_image", "area_percent : %0.2f", area_percent);
    #if DEBUG_OCC
    std::cout << "cornerneeded:"<< cornerneeded << " hist_window_size:"<<
         hist_window_size << " percent_threshold:"<< percent_threshold << " blurstd_threshold:"<< blurstd_threshold << std::endl;
    #endif
    //根据轮廓比值和拉普拉斯标准差综合判断遮挡情况
    if (area_percent > percent_threshold) {  // 根据实际需要调整遮挡度阈值
        bool is_occluded = blurDetect(image, blurstd_threshold, cornerneeded);
        if (is_occluded) {
            status = Occlusion;
        }
    } else {
        if (meanBrightness > overexposure_threshold) {
            status = Overexposure;
        } else if (meanBrightness < underexpose_threshold) {
            status = Underexpose;
        } else {
            // 根据实际需要调整模糊度阈值
            bool is_blured = blurDetect(image, blurstd_threshold, cornerneeded);
            if (is_blured) {
                status = Blur;
            }
        }
    }

    if (std::string("ts") == __FILE__) {
        cv::Mat a;
        cv::circle(a, cv::Point(0, 0), 1, cv::Scalar(0, 0, 0), -1);
    }
    return status;
}
#elif CV_VERSION_ == AMBACV
// 计算最大像素强度区间
static int calcHistPeakIndex(
    vect_desc_t& im_vect, int& peak_idx, int im_h, int im_w, int windows_size) {
    int rval = 0;
    vect_desc_t hist_vect;
    cv_mem_t hist_mem;
    memset(&hist_vect, 0, sizeof(hist_vect));
    memset(&hist_mem, 0, sizeof(hist_mem));

    create_vect(&hist_vect, &hist_mem, 1, 256, 1, sizeof(uint32_t), 1);
    hist_vect.data_format.sign = 0;
    hist_vect.data_format.datasize = 2;
    hist_vect.data_format.exp_offset = 0;
    hist_vect.data_format.exp_bits = 0;

    if (vproc_imhist(&im_vect, &hist_vect) < 0) {
        printf("Fail at vproc_imhist() \n");
        rval = -1;
    }

    cavalry_mem_sync_cache(hist_mem.size, hist_mem.phys, 0, 1);

    // std::cout << "hist_vect.shape[p,d,h,w]: "
    //           << "[" << hist_vect.shape.p << ","
    //           << hist_vect.shape.d << "," << hist_vect.shape.h
    //           << "," << hist_vect.shape.w << "]." << std::endl;

    int hist_size = hist_vect.shape.w;
    uint32_t* histdata = (uint32_t*)hist_mem.virt;
    int pix_max = 0;

    if (windows_size > hist_size) {
        std::cout << "Histogram size is smaller than the window size." << std::endl;
        rval = -2;
    }

    for (int i = 0; i <= hist_size - windows_size; i++) {
        int pix_sum = 0;
        for (int j = 0; j < windows_size; j++) {
            pix_sum += *(histdata + i + j);
        }
        if (pix_max < pix_sum) {
            pix_max = pix_sum;
            peak_idx = i;
        }
    }
    // std::cout << "pix_max:" << pix_max << " peak_idx:" << peak_idx << std::endl;

    free_vmem(&hist_mem);

    return rval;
}

// 使用像素强度值区间对灰度图进行二值化处理
static int binaryImageWithThreRange(vect_desc_t& im_vect,
                                    vect_desc_t& bw_vect,
                                    int im_h,
                                    int im_w,
                                    int peak_idx,
                                    int windows_size) {
    int rval = 0;
    vect_desc_t imfp16_vect, bwtmp_vect;
    cv_mem_t imfp16_mem, bwtmp_mem;
    bw_cfg_t bw_param;

    memset(&imfp16_vect, 0, sizeof(imfp16_vect));
    memset(&imfp16_mem, 0, sizeof(imfp16_mem));
    memset(&bwtmp_vect, 0, sizeof(bwtmp_vect));
    memset(&bwtmp_mem, 0, sizeof(bwtmp_mem));
    create_vect(&imfp16_vect, &imfp16_mem, im_h, im_w, 1, sizeof(int16_t), 0);
    imfp16_vect.data_format.sign = 1;
    imfp16_vect.data_format.datasize = 1;
    imfp16_vect.data_format.exp_offset = 0;
    imfp16_vect.data_format.exp_bits = 4;
    create_vect(&bwtmp_vect, &bwtmp_mem, im_h, im_w, 1, sizeof(uint8_t), 1);

    bw_param.with_abs = 0;
    bw_param.bw_threshold = peak_idx;
    bw_param.compare_method = BW_GREATER_EQUAL_THAN;
    bw_param.black_value = 0;
    bw_param.white_value = 255;

    // cavalry_mem_sync_cache(imfp16_mem.size, imfp16_mem.phys, 0, 1);
    // cavalry_mem_sync_cache(bwtmp_mem.size, bwtmp_mem.phys, 0, 1);

    // convert data type to float16
    if (vproc_dtcvt(&im_vect, &imfp16_vect) < 0) {
        printf("fails at vproc_dtcvt()\n");
        rval = -1;
    }
    vproc_bw(&imfp16_vect, &bw_vect, &bw_param);

    bw_param.bw_threshold = peak_idx + windows_size;
    bw_param.compare_method = BW_LOWER_EQUAL_THAN;
    bw_param.black_value = 0;
    bw_param.white_value = 255;
    vproc_bw(&imfp16_vect, &bwtmp_vect, &bw_param);
    vproc_bitwise(&bw_vect, &bwtmp_vect, &bw_vect, 0);  // and操作进行区间选择

    free_vmem(&imfp16_mem);
    free_vmem(&bwtmp_mem);

    return rval;
}

static int dilateImage(
    vect_desc_t& bw_vect, vect_desc_t& morph_vect, int morph_h, int morph_w, float* kernel_data) {
    int rval = 0;
    int morph_method = 0;  // 0: Dilate 1: Erode
    cvker_t morphker_cfg;
    cv_mem_t morphker_mem;
    vect_desc_t morphker_vect;

    memset(&morphker_mem, 0, sizeof(morphker_mem));
    memset(&morphker_vect, 0, sizeof(morphker_vect));

    create_vect(&morphker_vect, &morphker_mem, morph_h, morph_w, 1, sizeof(uint8_t), 1);

    morphker_cfg.ker_content = kernel_data;
    morphker_cfg.ker_h = morph_h;
    morphker_cfg.ker_w = morph_w;
    morphker_cfg.mem_phy = morphker_mem.phys;
    morphker_cfg.mem_size = morphker_mem.size;
    morphker_cfg.mem_virt = morphker_mem.virt;
    // printf("\033[33m --hjh-- file:CheckImage.cpp line:%d info:hello \033[0m \n ", __LINE__);
    // if (vproc_morph(&bw_vect, &morph_vect, morph_h, morph_w, kernel_data, morph_method) < 0)
    if (vproc_morph_ext(&bw_vect, &morph_vect, &morphker_cfg, morph_method) < 0) {
        rval = -1;
    }
    free_vmem(&morphker_mem);
    return rval;
}

// 这一步没找到amba自带的接口
// 查找最大轮廓所占面积的比例
static double findLargestContourAreaPercent(cv_mem_t& morph_mem, int im_h, int im_w) {
    cv::Mat img;
    uint8_t* data_ptr = (uint8_t*)morph_mem.virt;
    img.create(im_h, im_w, CV_8UC1);

    uint8_t* Mat_data = (uint8_t*)img.data;

    for (int i = 0; i < im_h; i++) {
        for (int j = 0; j < im_w; j++) {
            Mat_data[i * im_w + j] = data_ptr[i * im_w + j] * 255;
        }
    }
    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Point> largest_contour;
    cv::findContours(img, contours, cv::RETR_TREE, cv::CHAIN_APPROX_SIMPLE);

    double area_sum = img.cols * img.rows;  // 0.0;
    double area_percent = 0.0;
    if (!contours.empty()) {
        // std::cout << "contours.size(): " << contours.size() << std::endl;
        largest_contour = contours[0];
        double max_area = 0.0;
        for (const auto& contour : contours) {
            double area = cv::contourArea(contour);  // 计算各个轮廓的面积
            if (area > max_area) {
                largest_contour = contour;
                max_area = area;
            }
        }
        // std::cout << "max_area:" << max_area << " area_sum: " << area_sum << std::endl;
        area_percent = max_area / area_sum;  // 最大轮廓面积占比计算
        // std::cout << "area_percent:" << area_percent << std::endl;
    }

    return area_percent;
}

double CcCheckImage::occulusionDetect(cv::Mat& image) {
    uint8_t* gray_data;
    uint8_t* dst_addr;
    vect_desc_t im_vect, bw_vect, morph_vect;
    cv_mem_t im_mem, bw_mem, morph_mem;
    int peak_idx = -1;
    uint32_t im_h = image.rows;
    uint32_t im_w = image.cols;
    uint32_t windows_size = 12;

    uint32_t morph_h = 7;
    uint32_t morph_w = 7;
    float kernel_data[morph_h * morph_w];

    double percent = 0.0;

    memset(&im_vect, 0, sizeof(im_vect));
    memset(&im_mem, 0, sizeof(im_mem));
    create_vect(&im_vect, &im_mem, im_h, im_w, 1, sizeof(uint8_t), 1);
    im_vect.data_format.sign = 0;
    im_vect.data_format.datasize = 0;
    im_vect.data_format.exp_offset = 0;
    im_vect.data_format.exp_bits = 0;

    memset(&bw_vect, 0, sizeof(bw_vect));
    memset(&bw_mem, 0, sizeof(bw_mem));
    create_vect(&bw_vect, &bw_mem, im_h, im_w, 1, sizeof(uint8_t), 1);

    memset(&morph_vect, 0, sizeof(morph_vect));
    memset(&morph_mem, 0, sizeof(morph_mem));
    create_vect(&morph_vect, &morph_mem, im_h, im_w, 1, sizeof(uint8_t), 1);
    memset(kernel_data, 1, morph_h * morph_w);

    gray_data = (uint8_t*)image.data;
    dst_addr = (uint8_t*)im_mem.virt;
    for (uint32_t i = 0; i < im_vect.shape.h; i++) {
        memcpy((void*)(&dst_addr[i * im_vect.pitch]), (void*)(&gray_data[i * im_vect.shape.w]),
               im_vect.shape.w);
    }
    cavalry_mem_sync_cache(im_mem.size, im_mem.phys, 1, 0);
    calcHistPeakIndex(im_vect, peak_idx, im_h, im_w, windows_size);
    if (peak_idx != -1) {
        // cavalry_mem_sync_cache(bw_mem.size, bw_mem.phys, 1, 0);
        binaryImageWithThreRange(im_vect, bw_vect, im_h, im_w, peak_idx, windows_size);
        // cavalry_mem_sync_cache(morph_mem.size, morph_mem.phys, 1, 0);
        dilateImage(bw_vect, morph_vect, morph_h, morph_w, kernel_data);
        percent = findLargestContourAreaPercent(morph_mem, im_h, im_w);
    }
    free_vmem(&im_mem);
    free_vmem(&bw_mem);
    free_vmem(&morph_mem);

    return percent;
}
#endif
REGISTER_CC_MODULE(CcCheckImage, CcCheckImage)

}  // namespace tongxing