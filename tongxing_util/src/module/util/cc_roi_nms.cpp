#include "cc_roi_nms.h"
#include <string.h>
#include <cmath>
#include "cc_assert.h"
#include "cc_numarray_tool.h"
namespace tongxing {
int CcRoiNms::init(const Json::Value& config) {
    if (config.isMember("roi")) {
        roi_isConst = config["roi"]["isConst"].asBool();
        if (roi_isConst) {
            roi_x = config["roi"]["x"].asInt();
            roi_y = config["roi"]["y"].asInt();
            roi_w = config["roi"]["w"].asInt();
            roi_h = config["roi"]["h"].asInt();
        }
    }
    // std::cout<<roi_x<<" "<<roi_y<<" "<<roi_w<<" "<<roi_h<<std::endl;

    iou = config["min_iou"].asFloat();
    if (config.isMember("min_size")) {
        min_size = config["min_size"].asInt();
    } else {
        min_size = 0;
    }
    return 0;
}
int CcRoiNms::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}

float CcRoiNms::getIou(CcTensor<float>& t1, int roi_x, int roi_y, int roi_w, int roi_h) {
    int xx1, yy1, xx2, yy2;

    xx1 = std::max(roi_x, (int)t1[2].get());
    yy1 = std::max(roi_y, (int)t1[3].get());
    xx2 = std::min(roi_x + roi_w - 1, (int)t1[2].get() + (int)t1[4].get() - 1);
    yy2 = std::min(roi_y + roi_h - 1, (int)t1[3].get() + (int)t1[5].get() - 1);

    int insection_width, insection_height;
    insection_width = std::max(0, xx2 - xx1 + 1);
    insection_height = std::max(0, yy2 - yy1 + 1);

    float insection_area, union_area, iou;
    insection_area = float(insection_width) * insection_height;
    union_area = float((int)t1[4].get() * (int)t1[5].get());
    iou = insection_area / union_area;
    // std::cout<<iou<<std::endl;
    return iou;
}
int CcRoiNms::execute() {
    cc_assert(input_.size() == 1 || input_.size() == 2 || input_.size() == 3);
    std::vector<int> shape = input_[0]->shape;
    cc_assert(shape.size() == 3);
    std::vector<int> save_index;

    std::shared_ptr<NumArray> res = creat_numarray({shape[0], 1, 6}, NumArray::DataType::FLOAT32);
    int roi_x_ = roi_x;
    int roi_y_ = roi_y;
    int roi_w_ = roi_w;
    int roi_h_ = roi_h;
    // 用于更精确的roi区域来匹配
    int distance_reference_point = -1;
    int exact_roi_x = 0;
    int exact_roi_y = 0;
    int exact_roi_w = 0;
    int exact_roi_h = 0;

    for (int b = 0; b < shape[0]; b++) {
        if (input_.size() == 2 || input_.size() == 3) {
            roi_x_ = input_[1]->getTensor<float>()->operator[](b)[0].get();
            roi_y_ = input_[1]->getTensor<float>()->operator[](b)[1].get();
            roi_w_ = input_[1]->getTensor<float>()->operator[](b)[2].get();
            roi_h_ = input_[1]->getTensor<float>()->operator[](b)[3].get();
            // std::cout << "roi_x_ :" << roi_x_ << " roi_y_ " << roi_y_ << " roi_w_ " << roi_w_
            //           << " roi_h_ " << roi_h_ << std::endl;
        }
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;

        if (input_.size() == 3) {
            distance_reference_point = input_[2]->getTensor<float>()->operator[](b)[0].get();
            exact_roi_x = input_[2]->getTensor<float>()->operator[](b)[1].get();
            exact_roi_y = input_[2]->getTensor<float>()->operator[](b)[2].get();
            exact_roi_w = input_[2]->getTensor<float>()->operator[](b)[3].get();
            exact_roi_h = input_[2]->getTensor<float>()->operator[](b)[4].get();
            // std::cout << "exact_roi_x :" << exact_roi_x << " exact_roi_y " << exact_roi_y
            //           << " exact_roi_w " << roi_w_ << " exact_roi_h " << exact_roi_h << std::endl;
        }
        int max_index = -1;
        int max_sorce = 0;
        int face_index = -1;
        constexpr float MaxIouThr = 0.2f;
        float temp_distance = 0.0f;

        for (int i = 0; i < shape[1]; i++) {
            // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
            auto input_tensor = input_[0]->getTensor<float>()->operator[](b)[i];
            // std::cout << "input_tensor:" << input_tensor[0].get() << " " << input_tensor[1].get()
            //           << " " << input_tensor[2].get() << " " << input_tensor[3].get() << " "
            //           << input_tensor[4].get() << " " << input_tensor[5].get() << std::endl;
            // std::cout << std::min((int)input_tensor[4].get(), (int)input_tensor[5].get()) << " "
            //           << min_size << std::endl;
            // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
            if ((getIou(input_tensor, roi_x_, roi_y_, roi_w_, roi_h_) >= iou || roi_w_ == 0 ||
                 roi_h_ == 0) &&
                std::min((int)input_tensor[4].get(), (int)input_tensor[5].get()) > min_size) {
                if (input_.size() == 3) {
                    float exact_iou =
                        getIou(input_tensor, exact_roi_x, exact_roi_y, exact_roi_w, exact_roi_h);
                    // std::cout << "exact_iou:" << exact_iou << std::endl;
                    if (exact_iou >= MaxIouThr || exact_roi_w == 0 || exact_roi_h == 0) {
                        // ==0选择距离左上角更远的目标，==1选择距离右上角更远的目标
                        float sq_distance_to_roi_point_x = -1.0f;
                        if (distance_reference_point == 0) {
                            sq_distance_to_roi_point_x = (input_tensor[2].get() - roi_x_);
                        } else if (distance_reference_point == 1) {
                            sq_distance_to_roi_point_x =
                                ((roi_x_ + roi_w_) - input_tensor[2].get());
                        }

                        if (sq_distance_to_roi_point_x > temp_distance) {
                            temp_distance = sq_distance_to_roi_point_x;
                            face_index = i;
                        }
                        // std::cout << "sq_distance_to_roi_point_x:" << sq_distance_to_roi_point_x << std::endl;
                    }
                }

                if (max_sorce <=
                    std::abs((int)input_tensor[4].get() * (int)input_tensor[5].get())) {
                    max_sorce = std::abs((int)input_tensor[4].get() * (int)input_tensor[5].get());
                    max_index = i;
                }
            }
            //  std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        }
        if (face_index >= 0)
            max_index = face_index;
        // std::cout << "max_index:" << max_index << std::endl;
        //  std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        auto res_buf = res->getTensor<float>()->operator[](b)[0].data_;
        if (max_index >= 0) {
            auto output_tensor_buf = input_[0]->getTensor<float>()->operator[](b)[max_index].data_;

            for (int i = 0; i < 6; i++) {
                res_buf[i] = output_tensor_buf[i];
            }
        } else {
            for (int i = 0; i < 6; i++) {
                res_buf[i] = 0;
            }
        }
    }

    std::vector<std::shared_ptr<NumArray>> outputs;
    outputs.push_back(res);
    output_ = outputs;
    //  std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
    return 0;
}
size_t CcRoiNms::getOutputNum() {
    return output_.size();
}
std::shared_ptr<NumArray> CcRoiNms::getOutput(int index) {
    //  std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
    //  output_[index]->getTensor<float>()->printShape();
    return output_[index];
}
REGISTER_CC_MODULE(RoiNms, CcRoiNms)
}  // namespace tongxing