#include "cc_bbox_tracker.h"
#include "kalmanTracker.h"
#include "cc_numarray_tool.h"
#include "time_profile.h"
#ifdef __ANDROID__
#include <log_android.h>
#endif
namespace tongxing
{

//TODO:可以用cc_roi_nms模块中的函数进行替换?暂定
double GetIOU(cv::Rect_<float> bb_test,
              cv::Rect_<float> bb_gt) // 计算两个框的交并比
{
  float in = (bb_test & bb_gt).area();
  float un = bb_test.area() + bb_gt.area() - in;

  if (un < DBL_EPSILON)
    return 0;

  return (double)(in / un);
}
bool isOverlap(cv::Rect_<float> bb_test,
               cv::Rect_<float> bb_gt)
{
  float in = (bb_test & bb_gt).area();
  float un = bb_test.area() + bb_gt.area() - in;
  double iou = 0;
  if (un < DBL_EPSILON)
  {
    iou = 0;
  }
  else
  {
    iou = (double)(in / un);
    // std::cout<<in<<"/"<<un<<"="<<iou<<std::endl;
  }
  if (iou >= 0.6)
  {
    // std::cout<<"true1"<<std::endl;
    return true;
  }
  if ((in / bb_test.area()) > 0.4 || (in / bb_gt.area()) > 0.4)
  {
    return true;
  }
  if (in == bb_test.area() || in == bb_gt.area())
  {
    //  std::cout<<"true2"<<std::endl;
    return true;
  }
  //  std::cout<<"false"<<std::endl;
  return false;
}


int CcBboxTracker::init(const Json::Value &config)
{
  frameTrackingResult.reset(new std::vector<TrackingBox>);
  return 0;
}

int CcBboxTracker::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
{
  input_ = in;
  return 0;
}

int CcBboxTracker::execute()
{
#ifdef __ANDROID__
        TimeProfile trackerTime;
        trackerTime.Reset();
#elif __DEBUG__
        TimeProfile trackerTime;
        trackerTime.Reset();
#endif
        GetTrackedObject();
#ifdef __ANDROID__
        trackerTime.Update("od_tracker");
        std::cout << trackerTime.GetTimeProfileString() << std::endl;
        LOGD("od_tracker Time: %s", trackerTime.GetTimeProfileString());
        trackerTime.Reset();
#elif __DEBUG__
        trackerTime.Update("od_tracker");
        std::cout << trackerTime.GetTimeProfileString() << std::endl;
        trackerTime.Reset();
#endif
    return 0;
}

size_t CcBboxTracker::getOutputNum()
{
  return output_.size();
}

std::shared_ptr<NumArray> CcBboxTracker::getOutput(int index)
{
  return output_[index];
}

  void CcBboxTracker::Initial(std::vector<KalmanTracker> &trackers,
                        std::vector<TrackingBox> &tempVec)
  {
    for (unsigned int i = 0; i < tempVec.size(); i++) // 第几帧，该帧有目标
    {
      KalmanTracker trk = KalmanTracker(
          tempVec[i].box, tempVec[i].classid, tempVec[i].score,
          tempVec[i].left_front_edge, tempVec[i].left_behind_edge,
          tempVec[i].right_front_edge, tempVec[i].right_behind_edge,
          tempVec[i].orientation); // 该帧的第i目标，其实这里就是用第1帧的所有行进行初始化
      trackers.push_back(trk);     // 初始化后的结果放进一个vector中
    }
  }
  /********************************************************************
   * CcBboxTracker::predict():经卡尔曼滤波预测出目标位置
   * *********************************************************************/
  void CcBboxTracker::Predict(std::vector<KalmanTracker> &trackers,
                        std::vector<cv::Rect_<float>> &predictedBoxes)
  {
    // 3.1. get predicted locations from existing trackers.
    // 通过现有的追踪器来得到预测位置

    predictedBoxes.clear();

    for (auto it = trackers.begin(); it != trackers.end();)
    {

      cv::Rect_<float> pBox = (*it).predict();

      if (pBox.x >= 0 && pBox.y >= 0)
      {
        predictedBoxes.push_back(pBox);
        it++;
      }
      else
      {
        it = trackers.erase(it);
      }
    }
  }
  /**************************************************************
   * CcBboxTracker::getMatchedPair():
   * 通过模型检测得到的目标位，配合预测位置，和以前模型，进行匹配追踪
   * ************************************************************/
  void CcBboxTracker::GetMatchedPair(std::vector<KalmanTracker> &trackers,
                                std::vector<TrackingBox> &tempVec,
                                std::vector<cv::Rect_<float>> &predictedBoxes)
  {
    // 3.2. associate detections to tracked object (both represented as bounding
    // boxes)		将检测关联到追踪对象（均表现为标定框） dets :
    // detFrameData[fi]
    trkNum = predictedBoxes.size(); // 预测框有多少个目标
    detNum = tempVec.size();        // 一帧有多少目标
    // std::cout << "trkNum: " << trkNum << " detNum:" << detNum << std::endl;
    if (trkNum == 0 || detNum == 0)
    {
      return;
    }
    if (iouMatrix.size() > 0)
      iouMatrix.clear();

    iouMatrix.resize(trkNum, std::vector<double>(detNum, 0));

    for (unsigned int i = 0; i < trkNum; i++) // compute iou matrix as a distance
                                              // matrix 计算iou矩阵作为距离矩阵
    {
      for (unsigned int j = 0; j < detNum; j++)
      {
        // use 1-iou because the hungarian algorithm computes a minimum-cost
        // assignment.	使用1-iou因为匈牙利算法计算最小成本分配
        iouMatrix[i][j] =
            1 - GetIOU(predictedBoxes[i],
                       tempVec[j].box); // 每一个预测框和该帧中所有行计算
      }
    }

    // solve the assignment problem using hungarian algorithm.
    // 使用匈牙利算法来解决分配问题 the resulting assignment is [track(prediction)
    // : detection], with len=preNum		分配结果是[track(prediction) :
    // detection]，长度等于preNum
    HungarianAlgorithm HungAlgo;
    assignment.clear();
    HungAlgo.Solve(iouMatrix, assignment);
    // for (auto i=0; i<iouMatrix.size(); i++)
    // {
    //   for (auto j=0;j<iouMatrix[i].size();j++)
    //   {
    //     std::cout << "iouMatrix[" << i << "][" << j << "]:" << iouMatrix[i][j] << std::endl;

    //   }
    // }
    // for(auto m=0;m<assignment.size();m++)
    // {
    //   std::cout << "assignment[" << m <<"]:" << assignment[m] << std::endl;
    // }
    unmatchedTrajectories.clear();
    unmatchedDetections.clear();
    allItems.clear();
    matchedItems.clear();

    if (detNum > trkNum) //	there are unmatched detections
                         // 如果一帧的目标数大于预测目标的数量，即未匹配的检测
    {
      for (unsigned int n = 0; n < detNum; n++)
        allItems.insert(n); // 把所有检测到的目标全部插入allItems

      for (unsigned int i = 0; i < trkNum; ++i)
      {
        matchedItems.insert(
            assignment[i]); // 把预测中，有检测目标匹配 的标号插入进去
      }
      set_difference(allItems.begin(), allItems.end(), matchedItems.begin(),
                      matchedItems.end(),
                      insert_iterator<set<int>>(unmatchedDetections,
                                                unmatchedDetections.begin()));
    }
    else if (detNum <
             trkNum) // there are unmatched trajectory/predictions
                     // 一帧的行数小于预测框的数量，即有未匹配的追踪/预测
    {
      for (unsigned int i = 0; i < trkNum; ++i)
        if (assignment[i] == -1)           // unassigned label will be set as -1 in the assignment algorithm
                                           // 未分配的标签将在分配算法中设置为 -1
          unmatchedTrajectories.insert(i); // 记录未匹配的预测框
      
    }

    // filter out matched with low IOU		过滤掉低IOU的匹配
    matchedPairs.clear();
    for (unsigned int i = 0; i < trkNum; ++i)
    {
      if (assignment[i] == -1) // pass over invalid values
        continue;
      if (1 - iouMatrix[i][assignment[i]] < iouThreshold)
      {
        unmatchedTrajectories.insert(i);
        unmatchedDetections.insert(assignment[i]);
      }
      else
      {
        matchedPairs.push_back(cv::Point(i, assignment[i]));
      }
    }
    // std::cout << "matchedPairs.size():" << matchedPairs.size() << "unmatchedDetections.size():" << unmatchedDetections.size() << std::endl;
  }
  /**********************************************************************
   * CcBboxTracker::getResult():整合得到最新的目标追踪数据，存储
   * ********************************************************************/
  void CcBboxTracker::GetResult(std::vector<cv::Point> &matchedPairs,
                          std::vector<KalmanTracker> &trackers,
                          std::vector<TrackingBox> &tempVec)
  {
    // 3.3. updating trackers
    // update matched trackers with assigned detections.
    // each prediction is corresponding to a tracker
    int detIdx, trkIdx;

    for (unsigned int i = 0; i < matchedPairs.size(); i++)
    {
      trkIdx = matchedPairs[i].x;
      detIdx = matchedPairs[i].y;
      // 检查索引范围
      assert(trkIdx >= 0 && trkIdx < trackers.size());
      assert(detIdx >= 0 && detIdx < tempVec.size());
      if (tempVec.size() == 0)
      {
        // std::cout << "tempVec is empty" << std::endl;
        continue;
      }
      trackers[trkIdx].update(
          tempVec[detIdx].box, tempVec[detIdx].classid, tempVec[detIdx].score,
          tempVec[detIdx].left_front_edge, tempVec[detIdx].left_behind_edge,
          tempVec[detIdx].right_front_edge, tempVec[detIdx].right_behind_edge,
          tempVec[detIdx].orientation);
    }

    // create and initialise new trackers for unmatched detections
    for (auto umd : unmatchedDetections)
    {
      if (tempVec.size() == 0)
      {
        // std::cout << "tempVec is empty" << std::endl;
        continue;
      }
      KalmanTracker tracker = KalmanTracker(
          tempVec[umd].box, tempVec[umd].classid, tempVec[umd].score,
          tempVec[umd].left_front_edge, tempVec[umd].left_behind_edge,
          tempVec[umd].right_front_edge, tempVec[umd].right_behind_edge,
          tempVec[umd].orientation);
      trackers.push_back(tracker);
    }

    // get trackers' output
    std::shared_ptr<std::vector<TrackingBox>> tmp_result(
        new std::vector<TrackingBox>);
    tmp_result->clear();
    for (auto it = trackers.begin(); it != trackers.end();)
    {
      // std::cout << "(*it).m_time_since_update:" << (*it).m_time_since_update << "(*it).m_hit_streak:" << (*it).m_hit_streak << "(*it).classId:" << (*it).classId << std::endl;
      if (((*it).m_time_since_update < showDisappearMax) && ((*it).m_hit_streak >= min_hits || ((*it).m_hit_streak > min_hits_score && (*it).score > score_Threshold)) // ((*it).m_hit_streak > 2 && (*it).score > 0.8) 如果自信很高，则测那么多次
          && (*it).classId < 5)
      {
        TrackingBox res;
        if ((*it).m_time_since_update > 0)
        {
          res.box = (*it).get_target_history();
        }
        else
        {
          res.box = (*it).get_state();
        }
        res.id = (*it).m_id + 1;
        res.classid = (*it).classId;
        res.score = (*it).score;
        res.left_front_edge = it->left_front_edge;
        res.left_behind_edge = it->left_behind_edge;
        res.right_front_edge = it->right_front_edge;
        res.right_behind_edge = it->right_behind_edge;
        res.orientation = it->orientation;
        if ((*it).m_time_since_update < 2)
        {
          res.flag_warning = 1;
        }
        else
        {
          res.flag_warning = 0;
        }
        if ((res.box.width > 5) && (res.box.height > 5))
        {
          tmp_result->push_back(res);
        }
      }
      else if (((*it).m_time_since_update <= 1) &&
                ((*it).m_hit_streak >= min_hits) && (*it).classId >= 5)
      {
        TrackingBox res;
        if ((*it).m_time_since_update > 0)
        {
          res.box = (*it).get_target_history();
        }
        else
        {
          res.box = (*it).get_state();
        }

        res.id = (*it).m_id + 1;
        res.classid = (*it).classId;
        res.score = (*it).score;

        res.orientation = 0;
        if ((res.box.width) > 5 && (res.box.height > 5))
        {
          tmp_result->push_back(res);
        }
      }
      // remove dead tracklet
      if (it != trackers.end() && (*it).m_time_since_update > max_age)
      {
        it = trackers.erase(it);
      }
      else
      {
        it++;
      }
    }
    frameTrackingResult = tmp_result;
  }
  int CcBboxTracker::GetTrackedObject()
  {
    cc_assert(input_.size() == 1);
    cc_assert(input_[0]->shape.size() == 3 || input_[0]->shape.size() == 4);
    std::shared_ptr<NumArray> res_num = creat_numarray({1}, NumArray::DataType::INT32);
    int *tensor_num_ptr = (int *)res_num->data;
    std::vector<std::shared_ptr<NumArray>> outputs;
    std::vector<int> shape = input_[0]->shape;
    int output_num = input_[0]->getTensor<int>()->shape()[1];
    // std::cout << "output_num:" << output_num << std::endl;
    if (output_num == 0)
    {
        outputs.push_back(input_[0]);
        output_ = outputs;
        return -1;
    }

    std::shared_ptr<std::vector<ObjBBox>> tempVec(new std::vector<ObjBBox>);
    std::shared_ptr<std::vector<ObjBBox>> tempVec_(new std::vector<ObjBBox>);
    std::shared_ptr<std::vector<TrackingBox>> trackingResultVec(new std::vector<TrackingBox>);
    ObjBBox objBox;
    auto input_tensor = input_[0]->getTensor<float>()->operator[](0);
    for (int i = 0; i < output_num; i++)
    {
      if(shape[2]==6)
      {
        objBox.score = input_tensor[i][0].get();
        objBox.label = input_tensor[i][1].get();
        objBox.bbox.x = input_tensor[i][2].get();
        objBox.bbox.y = input_tensor[i][3].get();
        objBox.bbox.width = input_tensor[i][4].get();
        objBox.bbox.height = input_tensor[i][5].get();
      }
      else if(shape[2]==15)
      {
        objBox.score = input_tensor[i][0].get();
        objBox.label = input_tensor[i][1].get();
        objBox.bbox.x = input_tensor[i][2].get();
        objBox.bbox.y = input_tensor[i][3].get();
        objBox.bbox.width = input_tensor[i][4].get();
        objBox.bbox.height = input_tensor[i][5].get();
        objBox.orientation = (int)input_tensor[i][6].get();
        objBox.left_front_edge.x = (int)input_tensor[i][7].get();
        objBox.left_front_edge.y = (int)input_tensor[i][8].get();
        objBox.left_behind_edge.x = (int)input_tensor[i][9].get();
        objBox.left_behind_edge.y = (int)input_tensor[i][10].get();
        objBox.right_front_edge.x = (int)input_tensor[i][11].get();
        objBox.right_front_edge.y = (int)input_tensor[i][12].get();
        objBox.right_behind_edge.x = (int)input_tensor[i][13].get();
        objBox.right_behind_edge.y = (int)input_tensor[i][14].get();
      }
      tempVec->push_back(objBox);
    }
    // std::cout << "tempVec->size():" << tempVec->size() << std::endl;
    (*tempVec_) = (*tempVec);
    std::vector<TrackingBox> tempTrack;
    TrackingBox temp{0};
    for (int i = 0; i < tempVec->size(); i++) //未resize，resize放在了后面的层
    {
      temp.classid = (*tempVec)[i].label;
      temp.score = (*tempVec)[i].score;
      temp.box.x = (*tempVec)[i].bbox.x;
      temp.box.y = (*tempVec)[i].bbox.y;
      temp.box.width = (*tempVec)[i].bbox.width;
      temp.box.height = (*tempVec)[i].bbox.height;
      tempTrack.push_back(temp);
    }

    if (trackers.size() == 0)
    {
      Initial(trackers, tempTrack);
      frameTrackingResult->clear();
    }
    else
    {
        // 得到预测框
        Predict(trackers, predictedBoxes);
        // 得到匹配的和未匹配的
        GetMatchedPair(trackers, tempTrack, predictedBoxes);
        matchedPairs = MatchedPairResult();
        unmatchedDetections = unmatchedDetectionsResult();

        //得到追踪结果
        GetResult(matchedPairs, trackers, tempTrack);
      
    }
    bool flag_all_not_overlap = false;
    int i = 10;
    std::vector<TrackingBox> vec = *frameTrackingResult;

    do {
        i--;
        flag_all_not_overlap = false;

        // 用于存储当前处理的结果
        std::vector<TrackingBox> currentResultVec;

        while (!vec.empty()) {
            TrackingBox tmpObjBox = vec.back();
            vec.pop_back();

            bool flag_overlap = false;
            for (auto it = vec.begin(); it != vec.end();) {
                if (isOverlap(tmpObjBox.box, it->box)) {
                    if (tmpObjBox.score >= it->score) {
                        currentResultVec.push_back(tmpObjBox);
                    } else {
                        currentResultVec.push_back(*it);
                    }
                    it = vec.erase(it); // 删除重叠框并更新迭代器
                    flag_overlap = true;
                    flag_all_not_overlap = true;
                    break;
                } else {
                    ++it;
                }
            }

            if (!flag_overlap) {
                currentResultVec.push_back(tmpObjBox);
            }
        }

        vec = std::move(currentResultVec);
    } while (flag_all_not_overlap && i > 0);

    *frameTrackingResult = std::move(vec);
    trackingResultVec->clear();

    if(frameTrackingResult->size() <= output_num)
    {
      // std::cout << "frameTrackingResult->size():" << frameTrackingResult->size() << std::endl;
      outputs.push_back(input_[0]);
      output_ = outputs;
    }
    else
    {
      tensor_num_ptr[0] = frameTrackingResult->size(); 
      int batch = shape[0]; 
      int ch = tensor_num_ptr[0];//shape[1];//追踪后的数量可能会多于检出的数量，所以可能会resize outputsize
      // std::cout << "after resize ch:" << ch << std::endl;
      int other = shape[2];
      if (shape.size() > 3)
      {
          for (int i = 1; i < shape.size() - 2; i++)
          {
              batch *= shape[i];
          }
          ch = shape[shape.size() - 2];
          other = shape[shape.size() - 1];
      }
      std::shared_ptr<NumArray> res = creat_numarray({batch, max_output_num, other}, NumArray::DataType::FLOAT32);
      std::shared_ptr<CcTensor<float>> tensor_ptr = res->getTensor<float>();
      //追踪结果还原
      int j=0;
      for (auto &tracker : *frameTrackingResult)
      {
        float *output_data = tensor_ptr->operator[](0)[j].data_;
        output_data[0] = tracker.score;
        output_data[1] = tracker.classid;
        output_data[2] = tracker.box.x;
        output_data[3] = tracker.box.y;
        output_data[4] = tracker.box.width;
        output_data[5] = tracker.box.height;

        j++;
        if (j >= max_output_num)
        {
            break;
        }
      }
      res_num->data[0] = j;
      res->shape[1] = j;  

      std::vector<std::shared_ptr<NumArray>> res_vec;
      res_vec.push_back(res);
      res_vec.push_back(res_num);
      output_ = res_vec;
    }
    return 0;
  }
  /********************************************
   * CcBboxTracker::unmatchedDetectionsResult()：没有匹配到的目标
   * ****************************************/
  set<int> CcBboxTracker::unmatchedDetectionsResult() { return unmatchedDetections; }

  /************************************************
   * CcBboxTracker::MatchedPairResult()：新目标和旧目标匹配结果
   * ***********************************************/
  std::vector<cv::Point> CcBboxTracker::MatchedPairResult() { return matchedPairs; }

  REGISTER_CC_MODULE(BboxTracker, CcBboxTracker)

}
