#include "cc_const.h"
#include "cc_numarray_tool.h"
namespace tongxing
{

    int CcConst::init(const Json::Value &config)
    {
        std::vector<int> dim;
        Json::Value dim_value=config["dim"];
        Json::Value data_value=config["data"];
        if(dim_value.isArray()){
            size_t dim_size=dim_value.size();
            for(int i=0;i<dim_size;i++){
                dim.push_back(dim_value[i].asInt());
            }
        }
        output_.push_back(creat_numarray(dim,NumArray::FLOAT32));
        float* data_ptr=(float*)output_[0]->data;
        if(data_value.isArray()){
            size_t dim_size=data_value.size();
            for(int i=0;i<dim_size;i++){
                data_ptr[i]=data_value[i].asFloat();
            }
        }
        return 0;
    }
    int CcConst::setInput(const std::vector<std::shared_ptr<NumArray>> &in){
        // input_=in;
        return 0;
    }
    int CcConst::execute(){
        
        // std::shared_ptr<NumArray>  output=numarray_extend(input_[0],dim_,extend_num_,true);
        // output->getTensor<unsigned char>()->printShape();
        // std::vector<std::shared_ptr<NumArray> > outputs;
        // outputs.push_back(output);
        // output_=outputs;
        // return 0;
        return 0;
    }
    size_t CcConst::getOutputNum(){
        return output_.size();
    }
    std::shared_ptr<NumArray> CcConst::getOutput(int index){
        return output_[index];

    }
    REGISTER_CC_MODULE(Const, CcConst);
}