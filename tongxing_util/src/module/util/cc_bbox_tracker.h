#ifndef _CC_BBOX_TRACKER_H_
#define _CC_BBOX_TRACKER_H_
#include "cc_module.h"
// #include "cc_face_bbox_decoder.h"
#include "hungarian.h"
#include "kalmanTracker.h"
#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/video/tracking.hpp"
#include <deque>
#include <set>


typedef struct TrackingBox //定义一个结构体，来存储帧序号，车id，车框，其中车框是rect类型的，默认车id为-1
{
  long long id = -1;    //目标追踪得到的唯一id
  int classid = -1;     //目标类别
  float score = -1;     //目标可靠度
  int flag_warning=0;
  cv::Rect_<float> box; //目标在图片中位置
  cv::Point left_front_edge;
  cv::Point left_behind_edge;
  cv::Point right_front_edge;
  cv::Point right_behind_edge;
  int orientation;
} TrackingBox;


typedef struct ObjBBox {
  int label;
  float score;
  cv::Rect_<float> bbox;
  cv::Point left_front_edge;
  cv::Point left_behind_edge;
  cv::Point right_front_edge;
  cv::Point right_behind_edge;
  int orientation;

} ObjBBox;
// Computes IOU between two boundboxes   计算两个标定框之间的IOU值
// double GetIOU(cv::Rect_<float> bb_test, cv::Rect_<float> bb_gt);

namespace tongxing
{
    class CcBboxTracker : public CcModule
    {
        public:
            int init(const Json::Value &config);
            int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
            int execute();
            size_t getOutputNum();
            std::shared_ptr<NumArray> getOutput(int index);

        private:
            std::vector<std::shared_ptr<NumArray>> input_;
            std::vector<std::shared_ptr<NumArray>> output_;

            std::shared_ptr<std::vector<TrackingBox>> frameTrackingResult;
            std::vector<KalmanTracker> trackers;
  
            int max_age = 15; //目标消失后，第 max_age 次保留
            double iouThreshold = 0.35;
            float score_Threshold = 0.6; //当自信度为0.8时候，只需要检测 min_hits_score这个帧数即可输出结果 和min_hits_score配和使用
            int min_hits_score = 0;  //当自信度为0.8时候，只需要检测 min_hits_score这个帧数即可输出结果 和score_Threshold配合使用
            
            std::vector<cv::Rect_<float>> predictedBoxes;

            std::vector<std::vector<double>> iouMatrix;
            std::vector<int> assignment;
            set<int> unmatchedDetections;
            set<int> unmatchedTrajectories;
            set<int> allItems;
            set<int> matchedItems;
            std::vector<cv::Point> matchedPairs;
            std::vector<KalmanTracker> trackersResult;

            unsigned int trkNum = 0;
            unsigned int detNum = 0;

            int total_frames = 0;
            unsigned int showDisappearMax = 5; //目标消失后，继续显示最大次_
            int min_hits = 1; //目标可以显示的帧数

            double total_time = 0.0;
            int max_output_num = 30;
        
        private:
            std::vector<cv::Point> MatchedPairResult();

            set<int> unmatchedDetectionsResult();
            std::vector<KalmanTracker> GetBackTrackers();

            void Initial(std::vector<KalmanTracker> &trackers,
                        std::vector<TrackingBox> &tempVec);

            void Predict(std::vector<KalmanTracker> &trackers,
                        std::vector<cv::Rect_<float>> &predictedBoxes);

            void GetMatchedPair(std::vector<KalmanTracker> &trackers,
                                std::vector<TrackingBox> &tempVec,
                                std::vector<cv::Rect_<float>> &predictedBoxes);

            void GetResult(std::vector<cv::Point> &matchedPairs,
                            std::vector<KalmanTracker> &trackers,
                            std::vector<TrackingBox> &tempVec);
            int GetTrackedObject();
    };
} // namespace tongxing


#endif 