#ifndef __CC_SQUARE_CORP_H__
#define __CC_SQUARE_CORP_H__
#include "cc_module.h"
#include "json.h"

namespace tongxing {

    class CcSquareRoi: public CcModule
    {
    public:
        int init(const Json::Value& config);
        int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
    private:
        std::vector<std::shared_ptr<NumArray> > input_;
        std::vector<std::shared_ptr<NumArray> > output_;
        float extend=0;
        bool isBbox=true;
        int x_index;
        int y_index;

    };


}



#endif
