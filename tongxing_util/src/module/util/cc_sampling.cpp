#include "cc_sampling.h"
#include <string.h>
#include "cc_numarray_tool.h"
namespace tongxing
{

    int CcSampling::init(const Json::Value &config)
    {

        cc_assert(config["indexs"].isArray());
        for (int i = 0; i < config["indexs"].size(); i++)
        {
            indexs.push_back(config["indexs"][i].asInt());
        }
        return 0;
    }
    int CcSampling::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcSampling::execute()
    {
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        cc_assert(input_.size() == 1);
        std::vector<int> shape = input_[0]->shape;
        int batch = input_[0]->shape[0];

        std::vector<int> output_shape;
        output_shape.push_back(batch);
        output_shape.push_back(indexs.size());
        size_t one_ch_size = sizeof(float);

        for (int i = 2; i < shape.size(); i++)
        {
            output_shape.push_back(shape[i]);
            one_ch_size *= shape[i];
        }
        std::shared_ptr<NumArray> output = creat_numarray(output_shape, NumArray::DataType::FLOAT32);
        for (int b = 0; b < batch; b++)
        {
            auto output_tensor = output->getTensor<float>()->operator[](b);
            // std::cout<<input_[0]->type<<std::endl;
            // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
            auto input_tensor = input_[0]->getTensor<float>()->operator[](b);
            // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;

            for (int i = 0; i < indexs.size(); i++)
            {
                // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
                auto src = (unsigned char *)input_tensor[indexs[i]].data_;
                // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
                auto dst = (unsigned char *)output_tensor[i].data_;
                // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
                memcpy(dst, src, one_ch_size);
            }
        }
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        std::vector<std::shared_ptr<NumArray>> outputs;
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        outputs.push_back(output);
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        output_ = outputs;
        return 0;
    }
    size_t CcSampling::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcSampling::getOutput(int index)
    {
        // //std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        return output_[index];
    }
    REGISTER_CC_MODULE(Sampling, CcSampling)

}