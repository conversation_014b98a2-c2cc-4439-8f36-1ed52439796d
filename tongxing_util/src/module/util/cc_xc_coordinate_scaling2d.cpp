#include "cc_xc_coordinate_scaling2d.h"
#include "cc_assert.h"
#include "cc_numarray_tool.h"
#include <iostream>
#include <string.h>
#include "CalmCarLog.h"
namespace tongxing
{

    int CcXcCoordinateScaling2D::init(const Json::Value &config)
    {
        cc_assert(config["width_ram"].isInt());
        cc_assert(config["height_ram"].isInt());
        cc_assert(config["width_resize"].isInt());
        cc_assert(config["height_resize"].isInt());

        input_max_width  = config["width_ram"].asInt();
        input_max_height = config["height_ram"].asInt();
        width_resize = config["width_resize"].asInt();
        height_resize = config["height_resize"].asInt();

        if (config.isMember("keep_ratio"))
        {
          keep_ratio = config["keep_ratio"].asBool();
        }
        


        return 0;
    }
    int CcXcCoordinateScaling2D::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcXcCoordinateScaling2D::execute()
    {

        cc_assert(input_.size() == 2);
        output_.clear();
        std::shared_ptr<NumArray> res;
 
        if (keep_ratio)
        {
            res = creat_numarray({1, input_[1]->data[0], 6}, NumArray::DataType::FLOAT32);
            
            std::shared_ptr<CcTensor<float>> tensor_ptr = res->getTensor<float>();
            
            float scale_h = (float)(input_max_width)  / (float)width_resize;
            float scale_w = (float)(input_max_height) / (float)height_resize;
            for (int i = 0; i < input_[1]->data[0]; i++)
            {

                float *input_data = input_[0]->getTensor<float>()->operator[](0)[i].data_;
                float *output_data = tensor_ptr->operator[](0)[i].data_;
                output_data[0] = input_data[0];
                output_data[1] = input_data[1];
                output_data[2] = input_data[2] * scale_w ;
                output_data[3] = input_data[3] * scale_h ;
                output_data[4] = input_data[4] * scale_w ;
                output_data[5] = input_data[5] * scale_h ;
            }
           
            output_.push_back(res);
            
        }
        else
        {
            res = creat_numarray({1, 10, 2}, NumArray::DataType::FLOAT32);
            auto output_tensor = res->getTensor<float>()->operator[](0);
            auto input_tensor = input_[0]->getTensor<float>()->operator[](0);
            float* input_tensor2 = (float*)input_[1]->data;
            roi_x = input_tensor2[0];
            roi_y = input_tensor2[1];
            roi_w = input_tensor2[2];
            roi_h = input_tensor2[3];
            float scale_h = (float)(roi_h) / (float)height_resize;
            float scale_w = (float)(roi_w) / (float)width_resize;
            for (int i = 0; i < 10; i++)
            {              
                output_tensor[i][0].get() = input_tensor[i][1].get()*scale_w + roi_x;
                output_tensor[i][1].get() = input_tensor[i][2].get()*scale_h + roi_y;
            }
             output_.push_back(res);
           
        }
        return 0;
    }
    size_t CcXcCoordinateScaling2D::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcXcCoordinateScaling2D::getOutput(int index)
    {
        return output_[index];
    }

    REGISTER_CC_MODULE(xcCoordinateScaling2D, CcXcCoordinateScaling2D)

}