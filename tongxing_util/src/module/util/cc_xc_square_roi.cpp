#include "cc_xc_square_roi.h"
#include "cc_assert.h"
#include "cc_numarray_tool.h"
#include <algorithm>
namespace tongxing
{
    int CcXcSquareRoi::init(const Json::Value &config)
    {
        cc_assert(config["width_ram"].isInt());
        cc_assert(config["height_ram"].isInt());
        cc_assert(config["width_resize"].isInt());
        cc_assert(config["height_resize"].isInt());
        cc_assert(config["roi_type"].isInt());

        input_max_width = config["width_ram"].asInt();
        input_max_height = config["height_ram"].asInt();
        width_resize = config["width_resize"].asInt();
        height_resize = config["height_resize"].asInt();
        roi_type = config["roi_type"].asInt();
        return 0;
    }
    int CcXcSquareRoi::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcXcSquareRoi::execute()
    {
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        cc_assert(input_.size() == 1);

        std::shared_ptr<NumArray> output = creat_numarray({1, 4}, NumArray::DataType::FLOAT32);

        auto input_tensor = input_[0]->getTensor<float>();
        // auto output_tensor = output->getTensor<float>();
        output_.clear();
        if (roi_type == 1)
        {
            float *input_data = input_tensor->operator[](0)[0].data_;
            float *output_data = (float *)output->data;
            int x1 = input_data[2];
            int y1 = input_data[3];
            int w = input_data[4];
            int h = input_data[5];

            int max_wh = (w > h) ? w : h;
            int iCenterX = x1 + (w >> 1);
            int iCenterY = y1 + (h >> 1);

            if ((iCenterX - max_wh) < 0)
                max_wh = iCenterX;

            if ((iCenterY - max_wh) < 0)
                max_wh = iCenterY;

            if ((iCenterX + max_wh) > (input_max_width - 1))
                max_wh = input_max_width - iCenterX - 1;

            if ((iCenterY + max_wh) > (input_max_height - 1))
                max_wh = input_max_height - iCenterY - 1;

            output_data[0] = iCenterX - max_wh;
            output_data[1] = iCenterY - max_wh;
            output_data[2] = max_wh * 2;
            output_data[3] = max_wh * 2;
          
        }
        else if (roi_type == 2 || roi_type == 3)
        {
            // float *input_data = input_tensor->operator[](0)[0].data_;
            auto input_tensor = input_[0]->getTensor<float>()->operator[](0);
            float *output_data = (float *)output->data;

            int distance;
            int center_x, center_y;
            if (roi_type == 2) 
            {
                center_x = (input_tensor[0][0].get() + input_tensor[1][0].get()) / 2;
                center_y = (input_tensor[0][1].get() + input_tensor[1][1].get()) / 2;
                distance = std::abs(input_tensor[1][0].get() - input_tensor[0][0].get()) * 1.1;
                    
            }
            else
            {
                center_x = (input_tensor[2][0].get() + input_tensor[3][0].get()) / 2;
                center_y = (input_tensor[2][1].get() + input_tensor[3][1].get()) / 2;
                distance = std::abs(input_tensor[3][0].get() - input_tensor[2][0].get()) * 1.1;
        
            }
            if ((center_x + distance) > (input_max_width - 1))
                distance = input_max_width - distance - 1;

            if ((center_y + distance) > (input_max_height - 1))
                distance = input_max_height - center_y - 1;


            output_data[0] = std::max(0, center_x - distance);
            output_data[1] = std::max(0, center_y - distance);
            output_data[2] = distance * 2;
            output_data[3] = distance * 2;

        }

        output_.push_back(output);

        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        return 0;
    }
    size_t CcXcSquareRoi::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcXcSquareRoi::getOutput(int index)
    {
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        //  std::cout<<output_.size()<<" "<<index<<std::endl;
        return output_[index];
    }
    REGISTER_CC_MODULE(XcSquareRoi, CcXcSquareRoi)
}