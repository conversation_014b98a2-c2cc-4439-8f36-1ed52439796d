#include "cc_coordinate_scaling_rect_offset2d.h"
#include <iostream>
#include "cc_assert.h"
#include "cc_numarray_tool.h"

namespace tongxing {

int CcCoordinateScalingRectOffset2D::init(const Json::Value& config) {
    // cc_assert(config["x_index"].isArray());
    // cc_assert(config["y_index"].isArray());

    x_index = config["x_index"].asInt();

    y_index = config["y_index"].asInt();

    input_max_width = config["input_max_width"].asInt();
    input_max_height = config["input_max_height"].asInt();
    keep_ratio = config["keep_ratio"].asBool();
    if (config["cropping"].isBool()) {
        cropping_ = config["cropping"].asBool();
    }
    return 0;
}
int CcCoordinateScalingRectOffset2D::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}
int CcCoordinateScalingRectOffset2D::execute() {
    // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
    // input_[1]->getTensor<int>()->printShape();
    // input_[2]->getTensor<int>()->printShape();
    TX_LOG_DEBUG("cc_coordinate_scaling_rect_offset2d.cpp", "debug execute start");
    cc_assert(input_.size() == 2);
    float scale_h = 1;
    float scale_w = 1;

    int output_max_height = 0;
    int output_max_width = 0;
    if (!cropping_) {
        output_max_height = input_[1]->getTensor<int>()->operator[](2).get();
        output_max_width = input_[1]->getTensor<int>()->operator[](3).get();
    } else {
        output_max_height = input_[1]->getTensor<float>()->operator[](0)[0][4].get();
        output_max_width = input_[1]->getTensor<float>()->operator[](0)[0][5].get();
    }

    scale_h = static_cast<float>(output_max_height) / input_max_height;
    scale_w = static_cast<float>(output_max_width) / input_max_width;

    if (keep_ratio) {
        if (output_max_height > output_max_width || !cropping_) {
            scale_h = scale_w;

        } else {
            scale_w = scale_h;
        }
    }
    float offset_x = 0.0;
    float offset_y = 0.0;
    if (cropping_) {
        auto tensor = input_[1]->getTensor<float>();
        auto data = tensor->operator[](0)[0];
        offset_x = data[2].get();
        ;
        offset_y = data[3].get();
        ;
    }
    std::vector<int> shape = input_[0]->shape;
    std::shared_ptr<NumArray> output =
        creat_numarray({shape[0], shape[1], shape[2]}, NumArray::DataType::FLOAT32);
    // TX_LOG_DEBUG("cc_coordinate_scaling_offset2d.cpp","debug [%d, %d, %d]",shape[0],shape[1],shape[2]);
    for (int b = 0; b < shape[0]; b++) {
        auto output_tensor = output->getTensor<float>()->operator[](b);
        auto input_tensor = input_[0]->getTensor<float>()->operator[](b);
        for (int i = 0; i < shape[1]; i++) {
            auto output_data = output_tensor[i].data_;
            auto input_data = input_tensor[i].data_;

            // printf("CcCoordinateScalingRectOffset2D:output_data[0]:%f,output_data[1]:%f \n",
            //        output_data[0], output_data[1]);
            output_data[0] = input_data[0];
            output_data[1] = input_data[1];
            output_data[2] = input_data[2] * scale_w + offset_x;
            output_data[3] = input_data[3] * scale_h + offset_y;
            output_data[4] = input_data[4] * scale_w;
            output_data[5] = input_data[5] * scale_h;
        }
    }
    // TX_LOG_DEBUG("cc_coordinate_scaling_offset2d.cpp","debug");
    // std::cout<<scale_h<<" "<<scale_w<<std::endl;
    std::vector<std::shared_ptr<NumArray>> outputs;
    outputs.push_back(output);
    output_ = outputs;
    TX_LOG_DEBUG("cc_coordinate_scaling_offset2d.cpp", "debug execute end");
    // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
    return 0;
}
size_t CcCoordinateScalingRectOffset2D::getOutputNum() {
    return output_.size();
}
std::shared_ptr<NumArray> CcCoordinateScalingRectOffset2D::getOutput(int index) {
    // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
    return output_[index];
}

REGISTER_CC_MODULE(CoordinateScalingRectOffset2D, CcCoordinateScalingRectOffset2D)

}  // namespace tongxing