#ifndef __CC_XC_SQUARE_CORP_H__
#define __CC_XC_SQUARE_CORP_H__
#include "cc_module.h"
#include "json.h"

namespace tongxing
{

    class CcXcSquareRoi : public CcModule
    {
    public:
        int init(const Json::Value &config);
        int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);

    private:
        std::vector<std::shared_ptr<NumArray>> input_;
        std::vector<std::shared_ptr<NumArray>> output_;
        int input_max_width;
        int input_max_height;

        int width_resize;
        int height_resize;
        int roi_type;
    };

}

#endif
