#include "cc_shape.h"
#include "cc_blob_data.h"
namespace tongxing{


int CcShape::setInput(const std::vector<std::shared_ptr<NumArray> >& in){
    input_=in;
    return 0;
}
size_t CcShape::getOutputNum(){
    return input_.size();
}
std::shared_ptr<NumArray> CcShape::getOutput(int index){
    //std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
    std::shared_ptr<NumArray> in=input_[index];
     //std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
    std::shared_ptr<BlobData> bolb(new BlobData);
     //std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
    bolb->init(in->shape.size()*sizeof(int));
     //std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
    std::shared_ptr<NumArray> res(new NumArray);
     //std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
    res->type=NumArray::DataType::INT32;
    res->word_size=sizeof(int);
    res->data=bolb->pu8VirAddr;
    res->data_blob_ptr=bolb;
    res->shape.push_back(in->shape.size());
     //std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
    for(int i=0;i<in->shape.size();i++){
         //std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        res->getTensor<int>()->operator[](i).get()=in->shape[i];
         //std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
    }
     //std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
    return res;
}
REGISTER_CC_MODULE(shape, CcShape)
    
}