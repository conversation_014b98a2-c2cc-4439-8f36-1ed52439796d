#ifndef __CC_IMAGE_ALIGN_H__
#define __CC_IMAGE_ALIGN_H__
#include "cc_module.h"
#include "json.h"
#include <vector>
#include "opencv2/opencv.hpp"
namespace tongxing {
    class CcImageAlign: public CcModule
    {
        public:
            int init(const Json::Value& config);
            int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
            int execute();
            size_t getOutputNum();
            std::shared_ptr<NumArray> getOutput(int index);
        private:
            std::vector<std::shared_ptr<NumArray> > input_;
            std::vector<std::shared_ptr<NumArray> > output_;
            std::vector<cv::Point2f> align_points;
            int img_x_index;
            int img_y_index;
            int image_width;
            int image_height;
    };



}



#endif