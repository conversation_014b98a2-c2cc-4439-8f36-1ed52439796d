#include "cc_coordinate_scaling2d.h"
#include <iostream>
#include "cc_assert.h"
#include "cc_numarray_tool.h"
#include<string.h>
#include <time_profile.h>
namespace tongxing
{

    int CcCoordinateScaling2D::init(const Json::Value &config)
    {
        cc_assert(config["x_index"].isArray());
        cc_assert(config["y_index"].isArray());
        for (int i = 0; i < config["x_index"].size(); i++)
        {
            x_index.push_back(config["x_index"][i].asInt());
        }
        for (int i = 0; i < config["y_index"].size(); i++)
        {
            y_index.push_back(config["y_index"][i].asInt());
        }

        input_max_width = config["input_max_width"].asInt();
        input_max_height = config["input_max_height"].asInt();
        keep_ratio = config["keep_ratio"].asBool();
        if (config.isMember("flag_nchw"))
        {
            flag_nchw = config["flag_nchw"].asBool();
        }
        return 0;
    }
    int CcCoordinateScaling2D::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcCoordinateScaling2D::execute()
    {
        cc_assert(input_.size() == 2);
        std::vector<std::shared_ptr<NumArray>> outputs;
        std::vector<int> shape = input_[0]->shape;
        // std::cout<<shape[0]<<" "<<shape[1]<<std::endl;
        int batch = shape[0];
        int ch = shape[1];
        int other = shape[2];
        if (shape.size() > 3)
        {
            for (int i = 1; i < shape.size() - 2; i++)
            {
                batch *= shape[i];
            }
            ch = shape[shape.size() - 2];
            other = shape[shape.size() - 1];
        }
        if(ch<=0)
        {
            outputs.push_back(input_[0]);
            output_ = outputs;
            return 0;
        }
        float scale_h = 1;
        float scale_w = 1;

        int output_max_height = input_[1]->getTensor<int>()->operator[](2).get();
        int output_max_width = input_[1]->getTensor<int>()->operator[](3).get();
        if (flag_nchw==false)
        {
            output_max_height = input_[1]->getTensor<int>()->operator[](1).get();
            output_max_width = input_[1]->getTensor<int>()->operator[](2).get();
        }
        // std::cout<<"---------"<<output_max_height<<" "<<output_max_width<<std::endl;
        scale_h = (float)(output_max_height) / (float)input_max_height;
        scale_w = (float)(output_max_width) / (float)input_max_width;
        if (keep_ratio)
        {
            if (scale_h < scale_w)
            {
                scale_h = scale_w;
            }
            else
            {
                scale_w = scale_h;
            }
        }
        //  std::cout<<"---------"<<scale_h<<" "<<scale_w<<std::endl;
        // std::cout<<shape.size()<<" "<<batch<<" "<<ch<<" "<<other<<std::endl;
        input_[0] = numarray_reshape(input_[0], {batch, ch, other});
        std::shared_ptr<NumArray> output = creat_numarray({batch, ch, other}, NumArray::DataType::FLOAT32);
        auto output_tensor = output->getTensor<float>();
        auto input_tensor = input_[0]->getTensor<float>();
        // memcpy(output_tensor->data_blob_ptr->pu8VirAddr,input_tensor->data_blob_ptr->pu8VirAddr,output_tensor->data_blob_ptr->u32Size);
        for (int b = 0; b < batch; b++)
        {
            for (int i = 0; i < ch; i++)
            {
                for (int j = 0; j < other; j++)
                {
                    float scale_w_h =1;
                    if (j==2 || j==4)
                    {
                       scale_w_h = scale_w;
                    }else if (j==3 || j==5)
                    {
                        scale_w_h = scale_h;
                    }
                    output_tensor->operator[](b)[i][j].get() = input_tensor->operator[](b)[i][j].get() * scale_w_h;
                }
            }
        }
        input_[0] = numarray_reshape(input_[0], shape);
        output = numarray_reshape(output, shape);
        // std::cout<<scale_h<<" "<<scale_w<<std::endl;
        // output->getTensor<float>()->printShape();
        outputs.push_back(output);
        output_ = outputs;
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        return 0;
    }
    size_t CcCoordinateScaling2D::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcCoordinateScaling2D::getOutput(int index)
    {
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        // output_[index]->getTensor<float>()->printShape();
        return output_[index];
    }

    REGISTER_CC_MODULE(CoordinateScaling2D, CcCoordinateScaling2D)

}