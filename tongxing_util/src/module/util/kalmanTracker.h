#ifndef _KALMAN_H_
#define _KALMAN_H_

#include "opencv2/highgui/highgui.hpp"
#include "opencv2/video/tracking.hpp"
#include <deque>

using namespace std;

#define StateType cv::Rect_<float>

//此类表示作为边界框观察的各个跟踪对象的内部状态。
class KalmanTracker {
public:
  KalmanTracker() {
    init_kf(StateType(), int(), float());
    m_time_since_update = 0;
    m_hits = 0;
    m_hit_streak = 0;
    m_age = 0;
    m_id = kf_count;
    kf_count++;
    classId;
    score;
  }
  KalmanTracker(StateType initRect, int classId_, float score_, cv::Point lf,
                cv::Point lb, cv::Point rf, cv::Point rb, int o) {
    init_kf(initRect, int(), float());
    m_time_since_update = 0;
    m_hits = 0;
    m_hit_streak = 0;
    m_age = 0;
    m_id = kf_count;
    kf_count++;
    classId = classId_;
    score = score_;
    left_front_edge=lf;
    left_behind_edge=lb;
    right_front_edge=rf;
    right_behind_edge=rb;
    bbox_temp = initRect;
    orientation=o;
  }

  ~KalmanTracker() { m_history.clear(); }

  StateType predict();
  void update(StateType stateMat, int ClassId, float Score, cv::Point lf,
              cv::Point lb, cv::Point rf, cv::Point rb, int o);
  StateType get_state();
  StateType get_rect_xysr(float cx, float cy, float s, float r);
  StateType get_target_history();

  static int kf_count;

  int m_time_since_update;
  int m_hits;
  int m_hit_streak;
  int m_age;
  int m_id;
  int classId;
  float score;

  cv::Point left_front_edge;
  cv::Point left_behind_edge;
  cv::Point right_front_edge;
  cv::Point right_behind_edge;
  cv::Rect_<float> bbox_temp;
  int orientation;

private:
  void init_kf(StateType stateMat, int ClassId, float Score);

  cv::KalmanFilter kf;
  cv::Mat measurement;
  std::vector<StateType> m_history;
  // int target_history_deque_size = 12;
  // std::deque<StateType>  target_history_deque;
};

#endif
