#ifndef __CC_ROI_NMS_H__
#define __CC_ROI_NMS_H__
#include "cc_module.h"
#include "json.h"

namespace tongxing {

    class CcRoiNms: public CcModule
    {
    public:
        int init(const Json::Value& config);
        int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
        int execute();
        size_t getOutputNum();
        std::shared_ptr<NumArray> getOutput(int index);
    private:
        float getIou(CcTensor<float>& t1,int roi_x,int roi_y,int roi_w,int roi_h);
        std::vector<std::shared_ptr<NumArray> > input_;
        std::vector<std::shared_ptr<NumArray> > output_;
         bool roi_isConst;
         int roi_x;
         int roi_y;
         int roi_w;
         int roi_h;
         float iou;
         int min_size;
    };


}



#endif