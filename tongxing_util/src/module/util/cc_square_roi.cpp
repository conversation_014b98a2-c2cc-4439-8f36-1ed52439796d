#include "cc_square_roi.h"
#include "cc_assert.h"
#include "cc_numarray_tool.h"
namespace tongxing
{
    int CcSquareRoi::init(const Json::Value &config)
    {
        if (config.isMember("extend"))
        {
            extend = config["extend"].asFloat();
        }
        if (config.isMember("isBbox"))
        {
            isBbox = config["isBbox"].asBool();
        }
        if (!isBbox)
        {
            x_index = config["x_index"].asInt();
            y_index = config["y_index"].asInt();
        }
        return 0;
    }
    int CcSquareRoi::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcSquareRoi::execute()
    {
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        cc_assert(input_.size() == 1);
        // float max_w = input_[1]->getTensor<int>()->operator[](2).get();
        // float max_h = input_[1]->getTensor<int>()->operator[](1).get();
        int ch = input_[0]->shape[1];
        int batch = input_[0]->shape[0];
        std::shared_ptr<NumArray> output;
        auto input_tensor = input_[0]->getTensor<float>();
        if (isBbox)
        {
            // std::cout<<ch<<std::endl;
            output = creat_numarray({batch, ch, 6}, NumArray::DataType::FLOAT32);

            auto output_tensor = output->getTensor<float>();
            // input_[0]->getTensor<float>()->printShape();
            // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
            for (int b = 0; b < batch; b++)
            {
                for (int i = 0; i < ch; i++)
                {

                    for (int j = 0; j < 6; j++)
                    {
                        output_tensor->operator[](b)[i][j].get() = input_tensor->operator[](b)[i][j].get();
                    }

                    float w = std::max(output_tensor->operator[](b)[i][4].get(), output_tensor->operator[](b)[i][5].get()) * (1 + extend);
                    float cx = output_tensor->operator[](b)[i][4].get() / 2 + output_tensor->operator[](b)[i][2].get();
                    float cy = output_tensor->operator[](b)[i][5].get() / 2 + output_tensor->operator[](b)[i][3].get();
                    float xmax = (float)(cx + w / 2);
                    float ymax = (float)(cy + w / 2);
                    float xmin = (float)(cx - w / 2);
                    float ymin = (float)(cy - w / 2);
                    output_tensor->operator[](b)[i][2].get() = xmin;
                    output_tensor->operator[](b)[i][3].get() = ymin;
                    output_tensor->operator[](b)[i][4].get() = xmax - xmin;
                    output_tensor->operator[](b)[i][5].get() = ymax - ymin;
                    //  std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
                }
            }
        }
        else
        {
            cc_assert(ch > 1);
            output = creat_numarray({batch, 1, 6}, NumArray::DataType::FLOAT32);
            auto output_tensor = output->getTensor<float>();
            // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
            for (int b = 0; b < batch; b++)
            {
                float xmin = 1000000;
                float ymin = 1000000;
                float xmax = 0;
                float ymax = 0;
                for (int i = 0; i < ch; i++)
                {
                    if (xmin > input_tensor->operator[](b)[i][x_index].get())
                    {
                        xmin = input_tensor->operator[](b)[i][x_index].get();
                    }
                    if (ymin > input_tensor->operator[](b)[i][y_index].get())
                    {
                        ymin = input_tensor->operator[](b)[i][y_index].get();
                    }

                    if (xmax < input_tensor->operator[](b)[i][x_index].get())
                    {
                        xmax = input_tensor->operator[](b)[i][x_index].get();
                    }
                    if (ymax < input_tensor->operator[](b)[i][y_index].get())
                    {
                        ymax = input_tensor->operator[](b)[i][y_index].get();
                    }
                }

                // std::cout<<xmin<<" "<<ymin<<" "<<xmax<<" "<<ymax<<std::endl;
                // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
                float w = std::max(xmax - xmin, ymax - ymin) * (1 + extend);
                // printf("w=%f\n",w);
                float cx = (xmax + xmin) / 2;
                float cy = (ymax + ymin) / 2;
                xmax = (float)(cx + w / 2);
                ymax = (float)(cy + w / 2);
                xmin = std::max(0.0f, (float)(cx - w / 2));
                ymin = std::max(0.0f, (float)(cy - w / 2));
                // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
                output_tensor->operator[](b)[0][0].get() = 1;
                // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
                output_tensor->operator[](b)[0][2].get() = xmin;
                // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
                output_tensor->operator[](b)[0][3].get() = ymin;
                // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
                output_tensor->operator[](b)[0][4].get() = xmax - xmin;
                // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
                output_tensor->operator[](b)[0][5].get() = ymax - ymin;
                // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
            }
        }
        std::vector<std::shared_ptr<NumArray>> outputs;
        outputs.push_back(output);
        output_ = outputs;
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        return 0;
    }
    size_t CcSquareRoi::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcSquareRoi::getOutput(int index)
    {
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        //  std::cout<<output_.size()<<" "<<index<<std::endl;
        return output_[index];
    }
    REGISTER_CC_MODULE(SquareRoi, CcSquareRoi)
}