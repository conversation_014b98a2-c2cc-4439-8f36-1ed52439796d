#include "cc_coordinate_scaling_offset2d.h"
#include <iostream>
#include "cc_assert.h"
#include "cc_numarray_tool.h"
namespace tongxing
{

    int CcCoordinateScalingOffset2D::init(const Json::Value &config)
    {
        cc_assert(config["x_index"].isArray());
        cc_assert(config["y_index"].isArray());
        for (int i = 0; i < config["x_index"].size(); i++)
        {
            x_index.push_back(config["x_index"][i].asInt());
        }
        for (int i = 0; i < config["y_index"].size(); i++)
        {
            y_index.push_back(config["y_index"][i].asInt());
        }
        input_max_width = config["input_max_width"].asInt();
        input_max_height = config["input_max_height"].asInt();
        keep_ratio = config["keep_ratio"].asBool();
        if(config["cropping"].isBool())
        {
            cropping_ = config["cropping"].asBool();
        }
        return 0;
    }
    int CcCoordinateScalingOffset2D::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcCoordinateScalingOffset2D::execute()
    {
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        // input_[1]->getTensor<int>()->printShape();
        // input_[2]->getTensor<int>()->printShape();
        TX_LOG_DEBUG("cc_coordinate_scaling_offset2d.cpp","debug execute start");
        cc_assert(input_.size() == 2);
        float scale_h = 1;
        float scale_w = 1;

        int output_max_height=0;
        int output_max_width=0;
        if (!cropping_)
        {
            output_max_height = input_[1]->getTensor<int>()->operator[](2).get();
            output_max_width = input_[1]->getTensor<int>()->operator[](3).get();
        }else{
            output_max_height = input_[1]->getTensor<float>()->operator[](0)[0][4].get();
            output_max_width = input_[1]->getTensor<float>()->operator[](0)[0][5].get();
        }

        //  std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        scale_h = (float)(output_max_height) / (float)input_max_height;
        scale_w = (float)(output_max_width) / (float)input_max_width;

        if (keep_ratio)
        {
            if (output_max_height > output_max_width || !cropping_)
            {
                scale_h = scale_w;
                
            }
            else
            {
                scale_w = scale_h;
            }
        }
        float offset_x=0.0;
        float offset_y=0.0;
        if (cropping_)
        {
             offset_x = (float)(input_[1]->getTensor<float>()->operator[](0)[0][2].get());
             offset_y = (float)(input_[1]->getTensor<float>()->operator[](0)[0][3].get());
        }
            // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        // std::cout<<offset_x<<" "<<offset_x<<std::endl;
        std::vector<int> shape = input_[0]->shape;
        // std::cout<<shape[0]<<" "<<shape[1]<<std::endl;
        std::shared_ptr<NumArray> output = creat_numarray({shape[0], shape[1],shape[2]}, NumArray::DataType::FLOAT32);
        for (int b = 0; b < shape[0]; b++)
        {
            auto output_tensor = output->getTensor<float>()->operator[](b);
            auto input_tensor = input_[0]->getTensor<float>()->operator[](b);
            for (int i = 0; i < shape[1]; i++)
            {
                for (int j = 0; j < shape[2]; j++)
                {
                    output_tensor[i][j].get() = input_tensor[i][j].get();
                }
                for (auto x : x_index)
                {
                    
                    output_tensor[i][x].get() = output_tensor[i][x].get() *scale_w+offset_x;
                    // std::cout<<i<<" x:"<<input_tensor[i][x].get()<<"*"<<scale_w<<"+"<<offset_x<<"="<<output_tensor[i][x].get()<<std::endl;
        
                }
                for (auto y : y_index)
                {
                    output_tensor[i][y].get() = output_tensor[i][y].get() *scale_h+offset_y;
                    // std::cout<<i<<" y:"<<input_tensor[i][y].get()<<"*"<<scale_h<<"+"<<offset_y<<"="<<output_tensor[i][y].get()<<std::endl;
                }
            }
        }
        // std::cout<<scale_h<<" "<<scale_w<<std::endl;
        std::vector<std::shared_ptr<NumArray>> outputs;
        outputs.push_back(output);
        output_ = outputs;
        TX_LOG_DEBUG("cc_coordinate_scaling_offset2d.cpp","debug execute end");
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        return 0;
    }
    size_t CcCoordinateScalingOffset2D::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcCoordinateScalingOffset2D::getOutput(int index)
    {
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        return output_[index];
    }

    REGISTER_CC_MODULE(CoordinateScalingOffset2D, CcCoordinateScalingOffset2D)

}