#include "cc_image_align.h"
#include "cc_numarray_tool.h"
namespace tongxing
{
    int CcImageAlign::init(const Json::Value &config)
    {

        img_x_index = config["img_x_index"].asInt();
        img_y_index = config["img_y_index"].asInt();
        image_width= config["image_width"].asInt();
        image_height= config["image_height"].asInt();
        cc_assert(config["align_points"].isArray());
        align_points.clear();
        for (int i = 0; i < config["align_points"].size(); i++)
        {
            cc_assert(config["align_points"][i].isArray());
            cc_assert(config["align_points"][i].size() == 2);
            cv::Point point;
            point.x = config["align_points"][i][0].asFloat();
            point.y = config["align_points"][i][1].asFloat();
            //  std::cout<<point<<std::endl;
            align_points.push_back(point);
        }
        return 0;
    }
    int CcImageAlign::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    int CcImageAlign::execute()
    {
        cc_assert(input_.size()==2);
        cc_assert(input_[0]->shape[0]==input_[1]->shape[0]);
        int batch=input_[0]->shape[0];
        int ch=input_[0]->shape[1];
        int point_num=input_[1]->shape[1];
        int height=input_[0]->shape[2];
        int width=input_[0]->shape[3];
         auto output_numarray=creat_numarray({batch,ch,image_height,image_width},NumArray::DataType::UINT8);
        for (int b = 0; b < batch; b++)
        {
            auto input_tensor_point_tensor = input_[1]->getTensor<float>()->operator[](b);
            auto input_image_point_tensor = input_[0]->getTensor<unsigned char>()->operator[](b);
            auto output_image_point_tensor = output_numarray->getTensor<unsigned char>()->operator[](b);
            std::vector<cv::Point2f> two_d_points;
            for(int pn=0;pn<point_num;pn++){
                cv::Point2f two_point;
                two_point.x = input_tensor_point_tensor[pn][img_x_index].get();
                two_point.y = input_tensor_point_tensor[pn][img_y_index].get();
                // std::cout<<two_point<<std::endl;
                two_d_points.push_back(two_point);
            }
            cv::Mat M1=cv::getAffineTransform(two_d_points,align_points);
            for(int i=0 ;i<ch;i++){
                cv::Mat src_img(height,width,CV_8UC1,input_image_point_tensor[i].data_);
                cv::Mat dst_img(image_height,image_width,CV_8UC1,output_image_point_tensor[i].data_);
                cv::warpAffine(src_img,dst_img,M1,cv::Size(image_width,image_height));
            }
                // cv::Mat m(image_height,image_width,CV_8UC1,output_image_point_tensor[0].data_);
                // cv::imwrite("test_warpAffine.jpg",m);
        }
        std::vector<std::shared_ptr<NumArray>> outputs;
        outputs.push_back(output_numarray);
        output_=outputs;
        return 0;
    }
    size_t CcImageAlign::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcImageAlign::getOutput(int index)
    {
        return output_[index];
    }
    REGISTER_CC_MODULE(ImageAlign, CcImageAlign)
}