#ifndef __CC_SOLVE_PNP_ANGLE_H__
#define __CC_SOLVE_PNP_ANGLE_H__
#include "cc_module.h"
#include "json.h"
#include <vector>
#include "opencv2/opencv.hpp"
namespace tongxing {
    class CcSolvePnpAngle: public CcModule
    {
        public:
            int init(const Json::Value& config);
            int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
            int execute();
            size_t getOutputNum();
            std::shared_ptr<NumArray> getOutput(int index);
        private:
            std::vector<std::shared_ptr<NumArray> > input_;
            std::vector<std::shared_ptr<NumArray> > output_;
            std::vector<cv::Point3f > three_d_points;
            int img_x_index;
            int img_y_index;
            int score_index;
    };



}



#endif