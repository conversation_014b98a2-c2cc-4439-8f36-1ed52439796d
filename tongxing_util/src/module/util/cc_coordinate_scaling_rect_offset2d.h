#ifndef __CC_COORDINATE_SCALING_RECT_OFFSET_2D_H__
#define __CC_COORDINATE_SCALING_RECT_OFFSET_2D_H__
#include "cc_module.h"
#include "json.h"

namespace tongxing {
class CcCoordinateScalingRectOffset2D : public CcModule {
  public:
    int init(const Json::Value& config);
    int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:
    std::vector<std::shared_ptr<NumArray> > input_;
    std::vector<std::shared_ptr<NumArray> > output_;
    int x_index;
    int y_index;
    int input_max_width;
    int input_max_height;
    int keep_ratio;
    bool cropping_ = true;
};

}  // namespace tongxing

#endif