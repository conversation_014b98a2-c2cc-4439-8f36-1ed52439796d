#include "cc_nhwc2chw.h"
#include "cc_numarray_tool.h"
namespace tongxing
{
    int CcNHWC2NCHW::init(const Json::Value &config)
    {
        return 0;
    }
    int CcNHWC2NCHW::setInput(const std::vector<std::shared_ptr<NumArray>> &in){
        input_ = in;
        return 0;
    }
    int CcNHWC2NCHW::execute(){
        output_={numarray_nhwc2nchw(input_[0])};
         return 0;
    }
    size_t CcNHWC2NCHW::getOutputNum(){
        return output_.size();
    }
    std::shared_ptr<NumArray> CcNHWC2NCHW::getOutput(int index){
        return output_[index];
    }
    REGISTER_CC_MODULE(NHWC2NCHW, CcNHWC2NCHW)
}