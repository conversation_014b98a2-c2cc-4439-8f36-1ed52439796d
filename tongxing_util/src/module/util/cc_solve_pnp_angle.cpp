#include "cc_solve_pnp_angle.h"
#include "cc_assert.h"
#include <math.h>
#include "cc_numarray_tool.h"
namespace tongxing
{

    int CcSolvePnpAngle::init(const Json::Value &config)
    {
        img_x_index = config["img_x_index"].asInt();
        img_y_index = config["img_y_index"].asInt();
        if(config.isMember("score_index")){
            score_index=config["score_index"].asInt();
        }
        else{
            score_index=0;
        }
        cc_assert(config["three_d_points"].isArray());
        three_d_points.clear();
        for (int i = 0; i < config["three_d_points"].size(); i++)
        {
            cc_assert(config["three_d_points"][i].isArray());
            cc_assert(config["three_d_points"][i].size() == 3);
            cv::Point3f point;
            point.x = config["three_d_points"][i][0].asFloat();
            point.y = config["three_d_points"][i][1].asFloat();
            point.z = config["three_d_points"][i][2].asFloat();
            three_d_points.push_back(point);
        }
        return 0;
    }
    int CcSolvePnpAngle::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        input_ = in;
        return 0;
    }
    static int RotationMatrixToEulerAngles(cv::Mat &R, cv::Point3f &theta)
    {
        // 计算出来的是弧度制
        double thetax = atan2(R.at<double>(2, 1), R.at<double>(2, 2));
        double thetay = atan2(-1 * R.at<double>(2, 0), sqrt(R.at<double>(2, 1) * R.at<double>(2, 1) + R.at<double>(2, 2) * R.at<double>(2, 2)));
        double thetaz = atan2(R.at<double>(1, 0), R.at<double>(0, 0));
        // 弧度转为角度制
        theta.x = thetax / CV_PI * 180;
        theta.y = thetay / CV_PI * 180;
        theta.z = thetaz / CV_PI * 180;
        return 0;
    }

    static bool CheckPointValid(std::vector<cv::Point2f> two_d_points_valid) {   
    //左眼内眼角，右眼内眼角，笔尖，左嘴角，右嘴角，相对位置是固定的
    // for (auto &point : two_d_points_valid) {
    //     std::cout << "point_:" << point << std::endl;
    // }
    if (two_d_points_valid.size() != 5) {
        return false;
    }
    if (two_d_points_valid[0].x >= two_d_points_valid[1].x) { 
        return false;
    }
    if (two_d_points_valid[3].x >= two_d_points_valid[4].x) {
        return false;
    }
    if (two_d_points_valid[0].y >= two_d_points_valid[3].y || two_d_points_valid[1].y >= two_d_points_valid[4].y) {
        return false;
    }
    // if (two_d_points_valid[0].y >= two_d_points_valid[2].y || two_d_points_valid[1].y >= two_d_points_valid[2].y) {
    //     return false;
    // }
    if (two_d_points_valid[2].y >= two_d_points_valid[3].y || two_d_points_valid[2].y >= two_d_points_valid[4].y) {
        return false;
    }
        return true;
    }

    int CcSolvePnpAngle::execute()
    {
        TX_LOG_DEBUG("cc_solve_pnp_angle.cpp","debug execute start");
        cc_assert(input_.size() == 2);
        int w = input_[1]->getTensor<int>()->operator[](3).get();
        int h = input_[1]->getTensor<int>()->operator[](2).get();
                int ch = input_[0]->shape[1];
        int batch = input_[0]->shape[0];
        std::shared_ptr<NumArray> output=creat_numarray({batch,5},NumArray::DataType::FLOAT32);
        for (int b = 0; b < batch; b++)
        {
            auto input_tensor = input_[0]->getTensor<float>()->operator[](b);
            auto output_tensor = output->getTensor<float>()->operator[](b);
             std::vector<cv::Point2f> two_d_points;
            //  std::vector<float> score_vec;
             float score=0;
             float mouth_score=0;
             float min_x=9999;
             float min_y=9999;
             float max_x=-9999;
             float max_y=-9999;
             std::vector<cv::Point2f> tmp_2d_points; 
            for (int i = 0; i < ch; i++)
            {
                tmp_2d_points.push_back(cv::Point2f(input_tensor[i][img_x_index].get(),input_tensor[i][img_y_index].get()));

                cv::Point2f two_point;
                two_point.x = input_tensor[i][img_x_index].get();
                // std::cout << " point:" << i << " "<< input_tensor[i][img_x_index].get() << " "<< input_tensor[i][img_y_index].get() << std::endl;
                if(two_point.x<min_x){
                    min_x=two_point.x;
                }
                if(two_point.x>max_x){
                        max_x=two_point.x;
                }
                two_point.y = h - input_tensor[i][img_y_index].get();
                if(two_point.y<min_y){
                    min_y=two_point.y;
                }
                if(two_point.y>max_y){
                        max_y=two_point.y;
                }
                two_d_points.push_back(two_point);
                score+=input_tensor[i][score_index].get();
                // score_vec.push_back(input_tensor[i][score_index].get());
            }
            score/=two_d_points.size();
            mouth_score=(input_tensor[5][score_index].get()+input_tensor[6][score_index].get())/2.0f;
            // std::cout << "score:" << score << " mouth_score:" << mouth_score << " two_d_points.size():" << two_d_points.size() << std::endl;

            for(auto &p:two_d_points){
                p=p-cv::Point2f(min_x,min_y);
            }
            cv::Point2f roll_vec=two_d_points[1]-two_d_points[0];
            float roll=atan2(roll_vec.y,roll_vec.x)/CV_PI*180.0f;
            cv::Mat rot_mat=cv::getRotationMatrix2D(two_d_points[2],roll,1.0);
            cv::transform(two_d_points,two_d_points,rot_mat);


            double focal_length = max_x; // Approximate focal length.
            cv::Point2d center = two_d_points[2];
            cv::Mat camera_matrix = (cv::Mat_<float>(3, 3) << focal_length, 0, center.x, 0, focal_length, center.y, 0, 0, 1);
            cv::Mat dist_coeffs = cv::Mat::zeros(4, 1, cv::DataType<float>::type); // Assuming no lens distortion
            // std::cout<<camera_matrix<<std::endl;
            cv::Mat rotation_vector; // Rotation in axis-angle form
            cv::Mat translation_vector;
    
            // std::vector<std::vector<cv::Point3f > > three_d_points_vec;
            // std::vector<std::vector<cv::Point2f > > two_d_points_vec;
            // three_d_points_vec.push_back(three_d_points);
            // two_d_points_vec.push_back(two_d_points);
            // std::cout<<three_d_points[0]<<three_d_points[1]<<three_d_points[2]<<three_d_points[3]<<std::endl;
            // std::cout<<two_d_points[0]<<two_d_points[1]<<two_d_points[2]<<two_d_points[3]<<std::endl;
  
    
            cv::solvePnP(three_d_points, two_d_points, camera_matrix, dist_coeffs, rotation_vector, translation_vector, false, cv::SOLVEPNP_EPNP);

            cv::Mat rotation = cv::Mat::zeros(3, 3, CV_64F);
            // std::cout<<rotation.type()<<std::endl;
            cv::Rodrigues(rotation_vector, rotation);
            // std::cout<<rotation.type()<<std::endl;

            // 计算欧拉角
            // cv::Point3f theta;
            // RotationMatrixToEulerAngles(rotation, theta);

            cv::Mat pose_mat ;

            cv::Mat rotMatrix, transVect;
             cv::Mat rotMatrixX ;
             cv::Mat rotMatrixY ;
             cv::Mat rotMatrixZ;
             cv::Mat eulerAngles;
            cv::hconcat(rotation, translation_vector,pose_mat);
            cv::decomposeProjectionMatrix(pose_mat,camera_matrix,rotMatrix, transVect,rotMatrixX,rotMatrixY,rotMatrixZ,eulerAngles);
            // std::cout<<eulerAngles<<std::endl;
            output_tensor[0].get() = -eulerAngles.at<double>(0, 0);
            output_tensor[1].get() = -eulerAngles.at<double>(1, 0);
            // 在关键点置信度足够的前提下，加角度范围是因为在大角度分心时，关键点的数据会试图用2d的坐标进行3d坐标的展示，所以用当初在2d中的校验规则是不太合理的
            if ((score > 0.35) && std::abs(eulerAngles.at<double>(0, 0)) < 70 && std::abs(eulerAngles.at<double>(1, 0)) < 70) {
                // 判断关键点的相对位置是否有效,分别对应于左眼角，右眼角，鼻尖，左嘴角，右嘴角，相对位置是固定的
                std::vector<cv::Point2f> two_d_points_valid = {tmp_2d_points[0], tmp_2d_points[1], tmp_2d_points[2], tmp_2d_points[5], tmp_2d_points[6]};
                if (!CheckPointValid(two_d_points_valid)) {
                    score = -1.0f;
                    mouth_score = -1.0f;
                }
                // std::cout<<"pnp score:"<<score << " " << tmp_2d_points.size() <<std::endl;
            }
            // float roll=(eulerAngles.at<double>(2, 0)>abs(eulerAngles.at<double>(2, 0)-180))?eulerAngles.at<double>(2, 0)-180:eulerAngles.at<double>(2, 0);
            // if(roll>180){
            //     roll=roll-360;
            // }
            output_tensor[2].get()=roll;
            output_tensor[3].get()=score;
            output_tensor[4].get()=mouth_score;
        }
        std::vector<std::shared_ptr<NumArray>> outputs;
        outputs.push_back(output);
        output_ = outputs;
        TX_LOG_DEBUG("cc_solve_pnp_angle.cpp","debug execute end");
        // std::cout<<__FILE__<<":"<<__LINE__<<std::endl;
        return 0;
    }
    size_t CcSolvePnpAngle::getOutputNum()
    {
        return output_.size();
    }
    std::shared_ptr<NumArray> CcSolvePnpAngle::getOutput(int index)
    {
        return output_[index];
    }
    REGISTER_CC_MODULE(SolvePnpAngle, CcSolvePnpAngle)

}