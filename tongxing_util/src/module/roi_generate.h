#ifndef _ROI_GENERATE_H_
#define _ROI_GENERATE_H_

#include "cc_module.h"
#include "json.h"
#include "cc_numarray_tool.h"

// 自定义图像数据结构
struct ImageData {
    const unsigned char* data;
    int width;
    int height;
    int channels;
};

// 带旋转角度的ROI结构体
struct RotatedROI {
    int x;
    int y;
    int width;
    int height;
    float angle;  // 旋转角度(度)
};

namespace tongxing {

class ROIGenerator : public CcModule {
  public:
    int init(const Json::Value& config);
    int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:
    std::vector<std::shared_ptr<NumArray> > input_;
    std::vector<std::shared_ptr<NumArray> > output_;
    std::vector<RotatedROI> m_rois;
    std::vector<int> indexs;
    int upper_offset = 0;
    int lower_offset = 0;
    // ROI生成相关参数
    Json::Value m_roiConfig;
    
    // ROI生成方法
    RotatedROI generateROI(int point1_x, int point1_y, int point2_x, int point2_y, int upper_offset, int lower_offset);
};

}  // namespace tongxing

#endif  // _ROI_GENERATE_H_