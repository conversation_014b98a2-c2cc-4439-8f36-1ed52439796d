#include "cc_module_grop.h"
#include "cc_assert.h"
#include <iostream>
#include "CalmCarLog.h"
namespace tongxing
{
    int CcModuleGrop::init(const Json::Value &config)
    {
        // std::cout<<config.toStyledString()<<std::endl;
        Json::Value layers = config["layers"];
        TX_LOG_DEBUG("module_grop", "init module grop");
        for (int i = 0; i < layers.size(); i++)
        {
            std::shared_ptr<CcModule> moudle = get_cc_module(layers[i]);
            std::string name = layers[i]["name"].asString();
            TX_LOG_DEBUG("module_grop", "init module %s", name.c_str());
            std::shared_ptr<SubModule> sub_module(new SubModule);
            sub_module->module = moudle;
            sub_module->name = name;
            moduleList.push_back(sub_module);
            moduleNameList.push_back(name);
        }

        for (int i = 0; i < layers.size(); i++)
        {
            std::string name = layers[i]["name"].asString();
            auto dst_iter = std::find(moduleNameList.begin(), moduleNameList.end(), name);
            cc_assert(dst_iter != moduleNameList.end());
            Json::Value inbound_nodes = layers[i]["inbound_nodes"];
            if (inbound_nodes.isArray())
            {
                for (int j = 0; j < inbound_nodes.size(); j++)
                {
                    Json::Value inbound_node = inbound_nodes[j];
                    std::shared_ptr<SubModule> module =
                        moduleList[std::distance(moduleNameList.begin(), dst_iter)];
                    if (inbound_node.size() >= 2)
                    {
                        std::vector<int> inbound_node_index;
                        auto src_iter = std::find(moduleNameList.begin(), moduleNameList.end(),
                                                  inbound_node[0].asString());
                        if (src_iter == moduleNameList.end())
                        {
                            std::cout << "node " << inbound_node[0].asString() << " not fount" << std::endl;
                            cc_assert(src_iter != moduleNameList.end());
                        }
                        int index = inbound_node[1].asInt();
                        inbound_node_index.push_back(std::distance(moduleNameList.begin(), src_iter));
                        inbound_node_index.push_back(index);
                        module->inbound_nodes.push_back(inbound_node_index);
                    }
                }
            }
        }

        Json::Value output_layers = config["output_layers"];
        for (int i = 0; i < output_layers.size(); i++)
        {
            Json::Value output_layer = output_layers[i];
            if (output_layer.size() >= 2)
            {
                std::vector<int> output_layer_index;
                auto src_iter =
                    std::find(moduleNameList.begin(), moduleNameList.end(), output_layer[0].asString());
                TX_LOG_INFO("module_grop", "find module %s", output_layer[0].asString().c_str());
                cc_assert(src_iter != moduleNameList.end());
                output_layer_index.push_back(std::distance(moduleNameList.begin(), src_iter));
                int index = output_layer[1].asInt();
                output_layer_index.push_back(index);
                output_layer_indexs.push_back(output_layer_index);
            }
        }

        Json::Value input_layers = config["input_layers"];
        if (input_layers.size() > 0)
        {
            Json::Value input_layer = input_layers[0];
            if (input_layer.size() >= 1)
            {
                auto src_iter =
                    std::find(moduleNameList.begin(), moduleNameList.end(), input_layer[0].asString());
                cc_assert(src_iter != moduleNameList.end());
                input_layer_index = std::distance(moduleNameList.begin(), src_iter);
            }
        }

        // for(auto& output_layer_index:output_layer_indexs){
        //         std::vector<int> output_dependency;
        //         output_dependency.push_back(output_layer_index[0]);
        //         find_dependency(output_layer_index[0],output_dependency);
        //         std::reverse(output_dependency.begin(),output_dependency.end());
        //         output_dependencys.push_back(output_dependency);
        // }
        clear_execute_flags();
        // std::cout<<moduleList.size()<<" "<<nodeList.size()<<std::endl;
        return 0;
    }
    void CcModuleGrop::clear_execute_flags()
    {
        // TX_LOG_DEBUG("module_grop", "clear execute flags");
        for (auto &module : moduleList)
        {
            module->execute_flag = false;
        }

        // for (auto &module : moduleList)
        // {
        //     TX_LOG_DEBUG("module_grop", "%s execute flags %d",module->name.c_str(), module->execute_flag);
           
        // }
    }
    int CcModuleGrop::execute_dependency(std::shared_ptr<SubModule> module)
    {
        //  TX_LOG_DEBUG("module_grop", "module %s execute_dependency execute_flag=%d",module->name.c_str(),module->execute_flag);
        if (module->execute_flag == false)
        {
            std::vector<std::shared_ptr<NumArray>> input;
            input.reserve(module->inbound_nodes.size());
            for (int i = 0; i < module->inbound_nodes.size(); i++)
            {
                auto &inbound_node = module->inbound_nodes[i];
                std::shared_ptr<SubModule>& sub_module = moduleList[inbound_node[0]];
                // TX_LOG_DEBUG("module_grop", "find module %s  dependency %d", sub_module->name.c_str(), sub_module->inbound_nodes.size());
                // if(sub_module->inbound_nodes.size()>0){
                execute_dependency(sub_module);
                // }
                // TX_LOG_DEBUG("module_grop", "get module %s  output %d", sub_module->name.c_str(), inbound_node[1]);
                input.push_back(sub_module->module->getOutput(inbound_node[1]));
            }
            try
            {
                // TX_LOG_DEBUG("module_grop", "set module %s input", module->name.c_str());
                module->module->setInput(input);
                // TX_LOG_DEBUG("module_grop", "execute module %s start", module->name.c_str());
                module->module->execute();
                // TX_LOG_DEBUG("module_grop", "execute module %s end", module->name.c_str());
                module->execute_flag = true;
            }
            catch (std::exception &e)
            {
                TX_LOG_FATAL("module_grop", "%s execute fali", module->name.c_str());
                TX_LOG_FATAL("module_grop", "error message\n%s", e.what());
                throw(e);
            }
        }
        return 0;
    }
    int CcModuleGrop::setInput(const std::vector<std::shared_ptr<NumArray>> &in)
    {
        //  TX_LOG_DEBUG("CCADAS", "SetInput start");
        //  std::cout<<input_layer_index<<std::endl;
        std::shared_ptr<SubModule> module = moduleList[input_layer_index];
        //  TX_LOG_DEBUG("CCADAS", "SetInput start");
        module->module->setInput(in);
        clear_execute_flags();
        module->module->execute();
        module->execute_flag = true;
        // std::cout<<"CcModuleGrop::getOutput"<<std::endl;
        return 0;
    }
    size_t CcModuleGrop::getOutputNum()
    {
        return output_layer_indexs.size();
    }
    std::shared_ptr<NumArray> CcModuleGrop::getOutput(int index)
    {
        std::shared_ptr<SubModule> module = moduleList[output_layer_indexs[index][0]];
        // TX_LOG_DEBUG("module_grop", "get module %s  output %d", module->name.c_str(), output_layer_indexs[index][1]);
        // std::cout<<"------------------------------------------------------"<<std::endl;
        //         for (auto &module : moduleList)
        // {
        //     TX_LOG_DEBUG("module_grop", "%s execute flags %d",module->name.c_str(), module->execute_flag);
           
        // }
        // std::cout<<"------------------------------------------------------"<<std::endl;
        execute_dependency(module);
        // std::vector<std::shared_ptr<NumArray>> next_input;
        // std::shared_ptr<SubModule> module = moduleList[output_dependency[0]];
        // std::shared_ptr<NumArray> res=module->module->getOutput(0);
        // next_input.push_back(res);
        // for(int i=1;i<output_dependency.size();i++){

        //     std::shared_ptr<SubModule> module = moduleList[output_dependency[i]];

        //     module->module->setInput(next_input);
        //     next_input.clear();
        //     // for(int )
        //     std::shared_ptr<NumArray> res=module->module->getOutput(0);
        //     next_input.push_back(res);
        // }
        // for (auto &module : moduleList)
        // {
        //     TX_LOG_DEBUG("module_grop", "%s execute flags %d",module->name.c_str(), module->execute_flag);
           
        // }
        // std::cout<<"------------------------------------------------------"<<std::endl;
        return module->module->getOutput(output_layer_indexs[index][1]);
    }

    REGISTER_CC_MODULE(Functional, CcModuleGrop)

} // namespace tongxing
