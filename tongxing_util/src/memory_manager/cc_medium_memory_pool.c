#include "cc_medium_memory_pool.h"
#include <pthread.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include<sys/mman.h>

#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include "cc_big_memory_pool.h"
#define CC_MEDIUM_MEMORY_POOL_SIZE 1024 * 1024*2
#define MEDIUM_MEM_ALIGN 2048
#define GET_HEAD_USE_FLAG(x) x & 0x80
#define GET_HEAD_LEN(x) x & 0x7F
#define MEDIUM_MEM_LIST_HEAD_NUM 128
typedef union medium_mem_header
{
    struct{
    void *next;
    void *ptr;
    unsigned char flag_use;
    } header;
    unsigned char data[32];
} medium_mem_header;

typedef union medium_mem_blk_link
{
    struct
    {
        void *next;
        void *ptr;
        size_t len;
    } header;
    unsigned char data[32];
} medium_mem_blk_link;

static char *g_medium_memory_pool_buffer=NULL;
medium_mem_blk_link *medium_mem_blk_link_head = NULL;
size_t g_medium_memory_offset=0;

// int g_medium_mem_fp=-1;

medium_mem_header **medium_mem_list_head=NULL;

static size_t g_medium_mem_sum = 0;
static size_t medium_mem_index = 0;
static pthread_mutex_t g_medium_memory_mutex = PTHREAD_MUTEX_INITIALIZER;

#define MEDIUM_MEM_START &g_medium_memory_pool_buffer[g_medium_memory_offset]
#define MEDIUM_MEM_END &g_medium_memory_pool_buffer[MEDIUM_MEM_LIST_HEAD_NUM-1]
#define MEDIUM_MEM_BLK_SIZE sizeof(medium_mem_header)

#define LOCK //pthread_mutex_lock(&g_medium_memory_mutex)

#define UNLOCK //pthread_mutex_unlock(&g_medium_memory_mutex)

static void push_list_first(medium_mem_header **head, medium_mem_header *ptr)
{
    if (head[0])
    {
        (*ptr).header.next = head[0];
        head[0] = ptr;
    }
    else
    {
        head[0] = ptr;
    }
}

static medium_mem_header *get_list_first(medium_mem_header **head)
{
    if (head[0])
    {
        medium_mem_header *ret = head[0];
        head[0] = (*head[0]).header.next;
        return ret;
    }

    return NULL;
}
static size_t get_ptr_size(void *ptr)
{
    medium_mem_header *ptr_blk_res = (medium_mem_header *)ptr - 1;
    size_t msize = ((*ptr_blk_res).header.flag_use & 0x7f) * MEDIUM_MEM_ALIGN;
    return msize;
}


// static void creat_medium_mem_buffer(){
    
//     if(g_medium_memory_pool_buffer==NULL){
//         g_medium_memory_pool_buffer=cc_big_mem_alloc(CC_MEDIUM_MEMORY_POOL_SIZE);
//         if(((size_t)g_medium_memory_pool_buffer%32)!=0)
//             g_medium_memory_offset=32-((size_t)g_medium_memory_pool_buffer%32);
//     }
//     // printf("%p\n",g_medium_memory_pool_buffer);
// }

static void creat_medium_mem_buffer()
{

    if (g_medium_memory_pool_buffer == NULL)
    {
        medium_mem_list_head=cc_big_mem_alloc(MEDIUM_MEM_LIST_HEAD_NUM*sizeof(medium_mem_header));
        g_medium_memory_pool_buffer = (char *)cc_big_mem_alloc(CC_MEDIUM_MEMORY_POOL_SIZE) + sizeof(medium_mem_blk_link);
        medium_mem_blk_link_head = (medium_mem_blk_link *)g_medium_memory_pool_buffer - 1;
        (*medium_mem_blk_link_head).header.len = CC_MEDIUM_MEMORY_POOL_SIZE - sizeof(medium_mem_blk_link);
        (*medium_mem_blk_link_head).header.next = NULL;
        (*medium_mem_blk_link_head).header.ptr = g_medium_memory_pool_buffer;
        if (((size_t)g_medium_memory_pool_buffer % 32) != 0)
            g_medium_memory_offset = 32 - ((size_t)g_medium_memory_pool_buffer % 32);
            (*medium_mem_blk_link_head).header.len-=g_medium_memory_offset;
            (*medium_mem_blk_link_head).header.ptr=(char*)(*medium_mem_blk_link_head).header.ptr+g_medium_memory_offset;
        medium_mem_index=0;
    }
    // printf("%p\n",g_medium_memory_pool_buffer);
}

static void add_medium_mem_buffer()
{
    if (g_medium_memory_pool_buffer == NULL)
    {
        creat_medium_mem_buffer();
    }
    else
    {
        g_medium_memory_pool_buffer = (char *)cc_big_mem_alloc(CC_MEDIUM_MEMORY_POOL_SIZE) + sizeof(medium_mem_blk_link);
        medium_mem_blk_link *old_head=medium_mem_blk_link_head;
        medium_mem_blk_link_head = (medium_mem_blk_link *)g_medium_memory_pool_buffer - 1;
        (*medium_mem_blk_link_head).header.len = CC_MEDIUM_MEMORY_POOL_SIZE - sizeof(medium_mem_blk_link);
        (*medium_mem_blk_link_head).header.next = old_head;
        (*medium_mem_blk_link_head).header.ptr = g_medium_memory_pool_buffer;
        if (((size_t)g_medium_memory_pool_buffer % 32) != 0)
            g_medium_memory_offset = 16 - ((size_t)g_medium_memory_pool_buffer % 32);
            (*medium_mem_blk_link_head).header.len-=g_medium_memory_offset;
            (*medium_mem_blk_link_head).header.ptr=(char*)(*medium_mem_blk_link_head).header.ptr+g_medium_memory_offset;
        medium_mem_index=0;
    }
}

size_t cc_get_medium_mem_max_size()
{
    return MEDIUM_MEM_LIST_HEAD_NUM * MEDIUM_MEM_ALIGN;
}
size_t cc_get_medium_mem_ptr_size(void *ptr)
{
    size_t ret = 0;
    LOCK;
    ret = get_ptr_size(ptr);
    UNLOCK;
    return ret;
}
unsigned char cc_check_medium_mem_ptr(void *ptr)
{
    LOCK;
    medium_mem_blk_link* blk_head=medium_mem_blk_link_head;
    while(blk_head){
        if((size_t)ptr > (size_t)(*blk_head).header.ptr && (size_t)ptr < (size_t)(*blk_head).header.ptr+(*blk_head).header.len){
            UNLOCK;
            return 1;
        }
        blk_head=(*blk_head).header.next;
    }
    UNLOCK;
    return 0;
}

void *cc_medium_mem_alloc(size_t size)
{
    
    void *ret_ptr = NULL;
    size_t msize = size;
    medium_mem_header *ptr_blk_res = NULL;
    if (0 != (size % MEDIUM_MEM_ALIGN))
    {
        msize += (MEDIUM_MEM_ALIGN - (size % MEDIUM_MEM_ALIGN));
        // std::cout<<"al "<<(size % 128)<<std::endl;
    }
    if (msize == 0)
    {
        return NULL;
    }
    size_t list_head_index = (msize - 1) / MEDIUM_MEM_ALIGN;
    LOCK;
    creat_medium_mem_buffer();
    if (list_head_index < MEDIUM_MEM_LIST_HEAD_NUM)
    {
        
        do
        {
            ptr_blk_res = get_list_first(&medium_mem_list_head[list_head_index]);
            if (ptr_blk_res)
            {
                // printf("%d %d %d\n",list_head_index,(*ptr_blk_res).header.flag_use&0x7f,g_medium_mem_sum);
                ret_ptr = ((unsigned char *)ptr_blk_res) + MEDIUM_MEM_BLK_SIZE;
                // if(ret_ptr!=(*ptr_blk_res).header.ptr){
                //     printf("ptr error\n");
                //     exit(0);
                // }
                (*ptr_blk_res).header.next = NULL;
                goto RETURN_PTR;
            }
            list_head_index++;
        } while (list_head_index < MEDIUM_MEM_LIST_HEAD_NUM);
    }
CREAT_NEW_MEM:
    if (g_medium_mem_sum)
    {
        if ((medium_mem_index + msize + MEDIUM_MEM_BLK_SIZE) <= CC_MEDIUM_MEMORY_POOL_SIZE)
        {
            ptr_blk_res = (medium_mem_header *)(MEDIUM_MEM_START + medium_mem_index);
            (*ptr_blk_res).header.flag_use = 0x80;
            (*ptr_blk_res).header.flag_use |= 0x7f & (unsigned char)(msize / MEDIUM_MEM_ALIGN);
            (*ptr_blk_res).header.next = NULL;
            medium_mem_index += msize + MEDIUM_MEM_BLK_SIZE;
            ret_ptr = ((unsigned char *)ptr_blk_res) + MEDIUM_MEM_BLK_SIZE;
            (*ptr_blk_res).header.ptr = ret_ptr;
            g_medium_mem_sum++;
            goto RETURN_PTR;
        }else{
            add_medium_mem_buffer();
            goto CREAT_NEW_MEM;
        }
    }
    else
    {
        ptr_blk_res = (medium_mem_header *)(MEDIUM_MEM_START);
        (*ptr_blk_res).header.flag_use = 0x80;
        (*ptr_blk_res).header.flag_use |= 0x7f & (unsigned char)(msize / MEDIUM_MEM_ALIGN);
        (*ptr_blk_res).header.next = NULL;
        g_medium_mem_sum = 1;
        medium_mem_index = MEDIUM_MEM_BLK_SIZE + msize;
        // printf("medium_mem_index=%d , msize=%d MEDIUM_MEM_BLK_SIZE=%d\n",medium_mem_index,msize,MEDIUM_MEM_BLK_SIZE);
        ret_ptr = ((unsigned char *)ptr_blk_res) + MEDIUM_MEM_BLK_SIZE;
        (*ptr_blk_res).header.ptr = ret_ptr;
        goto RETURN_PTR;
    }

RETURN_PTR:
    UNLOCK;
    // if (cc_check_medium_mem_ptr(ret_ptr))
    // {
    //     printf("%x %x %x %d %d\n", (size_t)ret_ptr, (size_t)MEDIUM_MEM_START, (size_t)MEDIUM_MEM_END, msize, get_ptr_size(ret_ptr));
    //     // exit(0);
    // }
    // printf("%d\n",(size_t)ret_ptr%32);
    return ret_ptr;
}

void *cc_medium_mem_realloc(void *ptr, size_t size)
{
    void *ret_ptr=NULL;

    if (cc_check_medium_mem_ptr(ptr))
    {
        LOCK;
        size_t old_size = get_ptr_size(ptr);
        UNLOCK;
        if (size <= old_size)
        {
            ret_ptr = ptr;
            goto RETURN_PTR;
        }
        else
        {
            ret_ptr = cc_medium_mem_alloc(size);
            size_t cp_size = old_size < size ? old_size : size;
            if (ret_ptr)
            {
                memcpy(ret_ptr, ptr, cp_size);
            }
            cc_medium_mem_free(ptr);
            goto RETURN_PTR;
        }
    }
RETURN_PTR:
    return ret_ptr;
}

void cc_medium_mem_free(void *ptr)
{
    if (cc_check_medium_mem_ptr(ptr))
    {
        // printf("%p %p\n",MEDIUM_MEM_START,MEDIUM_MEM_END);
        LOCK;
        medium_mem_header *ptr_blk_res = (medium_mem_header *)ptr - 1;
        if ((*ptr_blk_res).header.ptr != ptr)
        {
            UNLOCK;
            printf("cc_medium_mem_free invalid ptr %p\n", ptr);
            return;
        }
        //  printf("cc_medium_mem_free  ptr %p\n",ptr);
        (*ptr_blk_res).header.flag_use &= 0x7f;
        size_t msize = (*ptr_blk_res).header.flag_use & 0x7f;
        size_t list_head_index = (msize - 1);
        if (list_head_index < MEDIUM_MEM_LIST_HEAD_NUM)
        {
            push_list_first(&medium_mem_list_head[list_head_index], ptr_blk_res);
            size_t i = 0;
            // do{
            //     i++;
            //     ptr_blk_res=(*ptr_blk_res).header.next;
            // }while(ptr_blk_res);
            // printf("cc_medium_mem_free %p\n",ptr);
        }

        UNLOCK;
    }
    else{
        printf("cc_medium_mem_free invalid ptr %p\n", ptr);
    }
}
