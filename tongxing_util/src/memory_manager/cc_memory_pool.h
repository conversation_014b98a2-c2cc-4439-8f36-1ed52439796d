#ifndef __CC_MEMORY_POOL_H__
#define __CC_MEMORY_POOL_H__
#ifdef __cplusplus
extern "C"{
#endif
#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <string.h>
void *__cc_malloc(size_t size);
void *__cc_realloc(void *ptr, size_t size);
void __cc_free(void *ptr);

// void __wrap_free(void *ptr);
// void *__wrap_realloc(void *ptr, size_t size);
// void *__wrap_malloc(size_t size);
// void *__wrap_memalign(size_t __alignment,
// 			   size_t __size);
// void *__wrap_calloc (size_t __nmemb, size_t __size);
// void *__wrap_reallocarray (void *__ptr, size_t __nmemb, size_t __size);
// void *__wrap_valloc(size_t size);
// void *__wrap_pvalloc (size_t __size);


#ifdef __cplusplus
}
#endif
#endif
