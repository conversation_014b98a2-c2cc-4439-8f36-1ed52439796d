#include "cc_big_memory_pool.h"
#include <pthread.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include<sys/mman.h>

#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#define BIG_MEM_ALIGN getpagesize() * 64

typedef struct big_mem_header
{
    void *next;
    void *ptr;
    int fp;
    size_t len;
} big_mem_header;
big_mem_header *big_mem_list_head=NULL;

static pthread_mutex_t g_big_memory_mutex = PTHREAD_MUTEX_INITIALIZER;
#define LOCK pthread_mutex_lock(&g_big_memory_mutex)

#define UNLOCK pthread_mutex_unlock(&g_big_memory_mutex)

static void push_list_first(big_mem_header **head, big_mem_header *ptr)
{
    if (head[0])
    {
        ptr->next = head[0];
        head[0] = ptr;
    }
    else
    {
        head[0] = ptr;
        head[0]->next=NULL;
    }
}

static big_mem_header *creat_big_mem(size_t size)
{
    big_mem_header *ret_ptr = NULL;
    size_t msize = size;
    if (0 != (msize % BIG_MEM_ALIGN))
    {
        msize += (BIG_MEM_ALIGN - (msize % BIG_MEM_ALIGN));
    }
    int fp = open("/dev/zero", O_RDWR);
    ret_ptr = mmap(NULL, msize + getpagesize(), PROT_READ | PROT_WRITE | PROT_EXEC, MAP_PRIVATE | MAP_ANONYMOUS, (int)fp, 0);
    if (ret_ptr)
    {
        ret_ptr->next = NULL;
        ret_ptr->ptr = (char *)ret_ptr + getpagesize();
        ret_ptr->fp = fp;
        ret_ptr->len = msize;
    }
    printf("creat_big_mem(%d,%d,0x%x)---------------------------------\n",msize,BIG_MEM_ALIGN,ret_ptr);
    return ret_ptr;
}

static size_t get_big_mem_ptr_size(void *ptr)
{
    size_t ret_size = 0;
    big_mem_header *head = (big_mem_header *)((char *)ptr - getpagesize());
    if (head->ptr == ptr)
    {
        ret_size = head->len;
    }
    return ret_size;
}

void *cc_big_mem_alloc(size_t size)
{
    // printf("getpagesize()=%d\n",getpagesize());
    size_t msize = size;
    big_mem_header *head = NULL;
    if (0 != (msize % BIG_MEM_ALIGN))
    {
        msize += (BIG_MEM_ALIGN - (msize % BIG_MEM_ALIGN));
    }
LOCK;
    if(big_mem_list_head){
        head=big_mem_list_head;
        big_mem_header *last_head = NULL;
        do{
            // printf("(%d)(%d)(%p)(%p)==========================\n",head->len,msize,head,last_head);
            if(head->len>=msize){
                if(last_head&&head->next){
                    last_head->next=head->next;
                }else if(last_head){
                    last_head->next=NULL;
                }
                else{
                    big_mem_list_head=head->next;
                }
                    
                goto RETURN_PTR;
            }
            
            last_head=head;
            head=head->next;
        }while(head);
    }
    head = creat_big_mem(msize);
RETURN_PTR:
    UNLOCK;
    if (head)
        return head->ptr;
    else
        return NULL;
}
size_t cc_get_big_mem_ptr_size(void *ptr)
{
    size_t ret_size = 0;
LOCK;
    ret_size = get_big_mem_ptr_size(ptr);
    UNLOCK;
    return ret_size;
}
void *cc_big_mem_realloc(void *ptr, size_t size)
{
    void *ret_ptr = NULL;
    LOCK;
    size_t old_size = get_big_mem_ptr_size(ptr);
    UNLOCK;
    if (old_size >= size)
    {
        ret_ptr = ptr;
        goto RETURN_PTR;
    }
    else{
            ret_ptr = cc_big_mem_alloc(size);
            size_t cp_size = old_size < size ? old_size : size;
            if (ret_ptr)
            {
                memcpy(ret_ptr, ptr, cp_size);
            }
            cc_big_mem_free(ptr);
            goto RETURN_PTR;
    }

RETURN_PTR:
    return ret_ptr;
}
void cc_big_mem_free(void *ptr)
{
    LOCK;
    // printf("cc_big_mem_free start(%p)++++++++++++++++++++++++++++++++++++\n",ptr);
    big_mem_header *head = (big_mem_header *)((char *)ptr - getpagesize());
    if (head->ptr == ptr)
    {
       push_list_first(&big_mem_list_head,head);
    //    printf("cc_big_mem_free(%d)++++++++++++++++++++++++++++++++++++\n",head->len);
    }
    else{
        printf("cc_big_mem_free  error %p++++++++++++++++++++++++++++++++++++\n",ptr);
        // exit(-1);
    }
    // 
    UNLOCK;

}
