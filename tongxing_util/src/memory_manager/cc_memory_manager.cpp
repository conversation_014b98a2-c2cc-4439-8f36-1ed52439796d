#include <iostream>
#include <stdio.h>
#include <mutex>
#include <atomic>
#include <memory>
#include <map>
#include "cc_memory_pool.h"
#include <string>
#if (!MEM_ISSUE_DEBUG)
void* operator new(size_t size){
    // std::cout<<"new "<<size<<std::endl;
    return __cc_malloc(size);
}

void operator delete(void* ptr){
        __cc_free(ptr);
        // i--;
}
#endif

extern "C"{
__attribute__((visibility("default"))) void *__wrap_cvAlloc(size_t size){
	// printf("cvAlloc\n");
	return __cc_malloc(size);
}

__attribute__((visibility("default"))) void __wrap_cvFree_(void *ptr){
	__cc_free(ptr);
}
}

