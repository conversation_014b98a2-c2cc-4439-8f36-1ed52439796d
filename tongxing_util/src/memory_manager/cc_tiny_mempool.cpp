#include "cc_tiny_mempool.h"
#include <iostream>
#include <string.h>
#include "cc_assert.h"
#ifdef USE_AX620
#include "ax_sys_api.h"
#elif USE_OAX4600
#include "libovt.h"
#endif
#include "cc_memory_pool.h"
namespace tongxing
{
    void *std_small_alloc::alloc(size_t size)
    {
        return malloc(size);
    }
    void std_small_alloc::delloc(void *ptr)
    {
        free(ptr);
    }

    CcBlobData_st *std_bolb_alloc::alloc(size_t size)
    {
        CcBlobData_st *res = (CcBlobData_st *)malloc(sizeof(CcBlobData_st));
        memset(res, 0, sizeof(CcBlobData_st));
        res->u32Size = size;
        res->pu8VirAddr = (uint8_t*)malloc(size);
        return res;
    }
    void std_bolb_alloc::delloc(CcBlobData_st *ptr)
    {
        free(ptr->pu8VirAddr);
        free(ptr);
    }

#ifdef USE_AX620
    CcBlobData_st *ax_bolb_alloc::alloc(size_t size)
    {
        CcBlobData_st *res = (CcBlobData_st *)malloc(sizeof(CcBlobData_st));
        memset(res, 0, sizeof(CcBlobData_st));
        AX_U64  phyaddr;
         AX_VOID *pviraddr;
        int ret=AX_SYS_MemAlloc(&phyaddr, &pviraddr, size,8, "anonymous");
        cc_assert(ret==0);
        res->u32Size = size;
        res->pu8VirAddr=pviraddr;
        res->u64PsyAddr=(void*)phyaddr;
        return res;
    }
    void ax_bolb_alloc::delloc(CcBlobData_st *ptr)
    {
        
        int ret=AX_SYS_MemFree((AX_U64)ptr->u64PsyAddr, ptr->pu8VirAddr);
        cc_assert(ret==0);
        free(ptr);
    }
#endif

#ifdef USE_OAX4600
    CcBlobData_st *oax4600_bolb_alloc::alloc(size_t size)
    {
        CcBlobData_st *res = (CcBlobData_st *)malloc(sizeof(CcBlobData_st));
        memset(res, 0, sizeof(CcBlobData_st));
        res->u32Size = size;
        res->pu8VirAddr=ovt_cma_alloc(size);
        res->u64PsyAddr=(void*)0;
        return res;
    }
    void oax4600_bolb_alloc::delloc(CcBlobData_st *ptr)
    {
        
        // cc_assert(ret==0);
        ovt_cma_free((void*)ptr->pu8VirAddr);
        free(ptr);
    }
#endif

}