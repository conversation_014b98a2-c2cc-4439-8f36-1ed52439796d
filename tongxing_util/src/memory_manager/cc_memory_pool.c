#include "cc_memory_pool.h"
#include <malloc.h>
#include "cc_small_memory_pool.h"
#include "cc_medium_memory_pool.h"
#include "cc_big_memory_pool.h"
#include <unistd.h>
static pthread_mutex_t g_memory_hook_mutex = PTHREAD_MUTEX_INITIALIZER;
#define MEM_HOOK_LOCK pthread_mutex_lock(&g_memory_hook_mutex)

#define MEM_HOOK_UNLOCK pthread_mutex_unlock(&g_memory_hook_mutex)

void *cc_malloc(size_t size)
{
	// printf("%p\n",&heap_end);
	void *ret_ptr = NULL;
	if (size < cc_get_small_mem_max_size())
	{
		// printf("cc_small_mem_alloc1-0 %d\n", size);
		ret_ptr = cc_small_mem_alloc(size);
		// printf("cc_small_mem_alloc1-1 %p\n", ret_ptr);
		goto RETURN_PTR;
	}
	else if (size < cc_get_medium_mem_max_size())
	{
		// printf("cc_small_mem_alloc2-0 %d\n", size);
		ret_ptr = cc_medium_mem_alloc(size);
		// printf("cc_small_mem_alloc2-1 %p\n", ret_ptr);
		goto RETURN_PTR;
	}
	else
	{
		ret_ptr = cc_big_mem_alloc(size);
		// printf("std_malloc(%d)\n",size);
		goto RETURN_PTR;
	}
RETURN_PTR:
	return ret_ptr;
}

void *cc_realloc(void *ptr, size_t size)
{

	void *ret_ptr = NULL;
	// printf("cc_realloc_hook %p %d\n", ptr, size);
	if (ptr == NULL)
	{
		// printf("cc_realloc1 %d\n", size);
		if (size < cc_get_small_mem_max_size())
		{
			ret_ptr = cc_small_mem_alloc(size);
			goto RETURN_PTR;
		}
		else if (size < cc_get_medium_mem_max_size())
		{
			ret_ptr = cc_medium_mem_alloc(size);
			goto RETURN_PTR;
		}
		else
		{
			ret_ptr = cc_big_mem_alloc(size);
			goto RETURN_PTR;
		}
	}
	else if (cc_check_small_mem_ptr(ptr) && size <= cc_get_small_mem_max_size())
	{
		// printf("cc_realloc2 %d\n", size);
		ret_ptr = cc_small_mem_realloc(ptr, size);
		goto RETURN_PTR;
	}
	else if (cc_check_small_mem_ptr(ptr))
	{
		// printf("cc_realloc3 %d\n", size);
		size_t old_size = cc_get_small_mem_ptr_size(ptr);
		void *new_ptr = cc_malloc(size);
		size_t cp_size = old_size < size ? old_size : size;
		if (new_ptr)
		{
			memcpy(new_ptr, ptr, cp_size);
		}
		cc_small_mem_free(ptr);

		ret_ptr = new_ptr;
		goto RETURN_PTR;
	}
	else if (cc_check_medium_mem_ptr(ptr) && size <= cc_get_medium_mem_max_size())
	{
		// printf("cc_realloc2 %d\n", size);
		ret_ptr = cc_medium_mem_realloc(ptr, size);
		goto RETURN_PTR;
	}
	else if (cc_check_medium_mem_ptr(ptr))
	{
		// printf("cc_realloc3 %d\n", size);
		size_t old_size = cc_get_medium_mem_ptr_size(ptr);
		void *new_ptr = cc_malloc(size);
		size_t cp_size = old_size < size ? old_size : size;
		if (new_ptr)
		{
			memcpy(new_ptr, ptr, cp_size);
		}
		cc_medium_mem_free(ptr);

		ret_ptr = new_ptr;
		goto RETURN_PTR;
	}
	else
	{

		ret_ptr = cc_big_mem_realloc(ptr, size);
		// printf("std_malloc(%p,%d)\n",ptr,size);
		goto RETURN_PTR;
	}
RETURN_PTR:
	return ret_ptr;
}

void cc_free(void *ptr)
{
	if (ptr)
	{

		if (cc_check_small_mem_ptr(ptr))
		{
			// printf("cc_free_hook1-0 %p\n", ptr);
			cc_small_mem_free(ptr);
			// printf("cc_free_hook1-1 %p\n", ptr);
		}
		else if (cc_check_medium_mem_ptr(ptr))
		{
			// printf("cc_free_hook2-0 %p\n", ptr);
			cc_medium_mem_free(ptr);
			// printf("cc_free_hook2-1 %p\n", ptr);
		}
		else
		{
			//
			// printf("cc_free_hook2 %p\n", ptr);
			cc_big_mem_free(ptr);
		}
	}
}


void __cc_free(void *ptr)
{
	MEM_HOOK_LOCK;
	cc_free(ptr);
	MEM_HOOK_UNLOCK;
}
void *__cc_realloc(void *ptr, size_t size)
{
	// printf("__wrap_realloc\n");
	void *ret_ptr = NULL;
	MEM_HOOK_LOCK;
	ret_ptr = cc_realloc(ptr, size);
	MEM_HOOK_UNLOCK;
	return ret_ptr;
}
void *__cc_malloc(size_t size)
{
	// printf("malloc\n");
	void *ret_ptr = NULL;
	MEM_HOOK_LOCK;
	ret_ptr = cc_malloc(size);
	MEM_HOOK_UNLOCK;
	return ret_ptr;
}
void *__cc_memalign(size_t __alignment,
			   size_t __size)
{
	void *ret_ptr = NULL;
	size_t msize = __size;
	if (0 != (__size % __alignment))
	{
		msize += (__alignment - (__size % __alignment));
		// std::cout<<"al "<<(size % 128)<<std::endl;
	}

	MEM_HOOK_LOCK;
	if (__alignment <= 16 && msize <= cc_get_small_mem_max_size())
	{
		ret_ptr = cc_small_mem_alloc(msize);
	}
	else if (__alignment <= 32 && msize <= cc_get_medium_mem_max_size())
	{
		ret_ptr = cc_medium_mem_alloc(msize);
	}
	else
	{
		ret_ptr = cc_big_mem_alloc(msize);
	}
	MEM_HOOK_UNLOCK;
	// printf("std_memalign %d %d %p\n",__alignment,__size,ret_ptr);
	return ret_ptr;
}
void *__cc_calloc (size_t __nmemb, size_t __size){
	return __cc_malloc(__nmemb*__size);
}
void *__cc_reallocarray (void *__ptr, size_t __nmemb, size_t __size){
	return __cc_realloc(__ptr,__nmemb*__size);
}
void *__cc_valloc(size_t size)
{
	return __cc_memalign(getpagesize(), size);
}

void *__cc_pvalloc (size_t __size){
	return __cc_memalign(getpagesize(), __size); 
}




// void __wrap_free(void *ptr)
// {

// 	__cc_free(ptr);

// }
// void *__wrap_realloc(void *ptr, size_t size)
// {
// 	return __cc_realloc(ptr, size);;
// }
// void *__wrap_malloc(size_t size)
// {
// 	return __cc_malloc(size);
// }
// void *__wrap_memalign(size_t __alignment,
// 			   size_t __size)
// {
// 	return __cc_memalign(__alignment,__size);
// }
// void *__wrap_calloc (size_t __nmemb, size_t __size){
// 	return __cc_malloc(__nmemb*__size);
// }
// void *__wrap_reallocarray (void *__ptr, size_t __nmemb, size_t __size){
// 	return __cc_realloc(__ptr,__nmemb*__size);
// }
// void *__wrap_valloc(size_t size)
// {
// 	return __cc_valloc(size);
// }

// void *__wrap_pvalloc (size_t __size){
// 	return __cc_pvalloc(__size); 
// }

