#ifndef __CC_TINY_MEMPOOL_H__
#define __CC_TINY_MEMPOOL_H__
#include <mutex>
#include <atomic>
#include <memory>
#include <map>
#include <iostream>
#include <vector>
namespace tongxing
{
    template <typename T1, typename T2>
    class CcTinyMempool
    {
    public:
        static CcTinyMempool &instance()
        {
            static CcTinyMempool ins;
            return ins;
        }
        T2 *alloc(size_t size)
        {
            // std::cout<<"alloc "<<size<<std::endl;
            size_t alloc_size = size;
            if (0 != (size % 8))
            {
                alloc_size += (8 - (size % 8));
                // std::cout<<"al "<<(size % 128)<<std::endl;
            }
            // std::cout<<using_ptr_map.size()+free_ptr_map.size()<<std::endl;
            std::unique_lock<std::mutex> m(mutex);
            auto free_iter = free_ptr_map.find(alloc_size);
            if (free_iter != free_ptr_map.end())
            {
                if (free_iter->second.size() > 0)
                {
                    auto free_vec_iter = free_iter->second.begin();
                    auto ptr = *free_vec_iter;
                    free_iter->second.erase(free_vec_iter);
                    using_ptr_map[ptr] = alloc_size;
                    return ptr;
                }
            }
            else
            {
                std::vector<T2 *> ptr_list;
                ptr_list.reserve(128);
                free_ptr_map[alloc_size] = (ptr_list);
            }
            auto ptr = T1::alloc(alloc_size);
            using_ptr_map[ptr] = alloc_size;
            return ptr;
            // return T1::alloc(alloc_size);
        }
        void delloc(T2 *ptr)
        {
            std::unique_lock<std::mutex> m(mutex);
            auto using_iter = using_ptr_map.find(ptr);
            if (using_iter != using_ptr_map.end())
            {
                free_ptr_map[using_iter->second].push_back(ptr);
            }
            // T1::delloc(ptr);
        }
        ~CcTinyMempool()
        {
            std::unique_lock<std::mutex> m(mutex);
            for (auto iter = using_ptr_map.begin(); iter != using_ptr_map.end(); iter++)
            {
                // T1::delloc(iter->first);
                // using_ptr_map.erase(iter);
            }
            for (auto iter = free_ptr_map.begin(); iter != free_ptr_map.end(); iter++)
            {
                for (auto vec_iter = iter->second.begin(); vec_iter != iter->second.end(); vec_iter++)
                {
                    T1::delloc(*vec_iter);
                }
                // free_ptr_map.erase(iter);
            }
        }

    private:
        std::map<size_t, std::vector<T2 *>> free_ptr_map;
        std::map<T2 *, size_t> using_ptr_map;
        std::mutex mutex;
    };

    class std_small_alloc
    {
    public:
        static void *alloc(size_t size);
        static void delloc(void *ptr);
    };

    typedef struct CcBlobData_st_
    {
        uint8_t *pu8VirAddr;
        void *u64PsyAddr;
        uint32_t u32Size;
        uint8_t *pu8Reserve;
    } CcBlobData_st;

    class std_bolb_alloc
    {
    public:
        static CcBlobData_st *alloc(size_t size);
        static void delloc(CcBlobData_st *ptr);
    };
    typedef CcTinyMempool<std_small_alloc, void> CcSmallMempool;

template <class T>
class tiny_allocator {
  public:
    using value_type = T;
    using pointer = T*;
    using const_pointer = const T*;
    using reference = T&;
    using const_reference = const T&;
    using size_type = size_t;

    tiny_allocator() {}

    tiny_allocator(tiny_allocator const&) {}

    tiny_allocator& operator=(tiny_allocator const&) { return *this; }

    template <class Other>
    tiny_allocator(tiny_allocator<Other> const&) {}

    template <class Other>
    tiny_allocator& operator=(tiny_allocator<Other> const&) {
        return *this;
    }

    pointer allocate(size_type count) {
        return (pointer)CcSmallMempool::instance().alloc(count * sizeof(value_type));
    }

    void deallocate(pointer ptr, size_type count) {
        return CcSmallMempool::instance().delloc(ptr);
    }
};


#ifdef USE_AX620
    class ax_bolb_alloc
    {
    public:
        static CcBlobData_st *alloc(size_t size);
        static void delloc(CcBlobData_st *ptr);
    };
    typedef CcTinyMempool<ax_bolb_alloc, CcBlobData_st> CcBolbMempool;
#elif USE_OAX4600
    class oax4600_bolb_alloc
    {
    public:
        static CcBlobData_st *alloc(size_t size);
        static void delloc(CcBlobData_st *ptr);
    };
    typedef CcTinyMempool<oax4600_bolb_alloc, CcBlobData_st> CcBolbMempool;
#else
    typedef CcTinyMempool<std_bolb_alloc, CcBlobData_st> CcBolbMempool;
#endif

}
#endif