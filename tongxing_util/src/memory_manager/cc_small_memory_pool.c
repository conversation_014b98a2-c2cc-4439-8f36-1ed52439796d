#include "cc_small_memory_pool.h"
#include <pthread.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <sys/mman.h>

#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include "cc_big_memory_pool.h"
#define CC_SMALL_MEMORY_POOL_SIZE 1024 * 1024 * 1
#define SMALL_MEM_ALIGN 16
#define GET_HEAD_USE_FLAG(x) x & 0x80
#define GET_HEAD_LEN(x) x & 0x7F
#define SMALL_MEM_LIST_HEAD_NUM 128

typedef union small_mem_header
{
    struct
    {
        void *next;
        void *ptr;
        unsigned char flag_use;
    } header;
    unsigned char data[32];
} small_mem_header;

typedef union small_mem_blk_link
{
    struct
    {
        void *next;
        void *ptr;
        size_t len;
    } header;
    unsigned char data[32];
} small_mem_blk_link;

static char *g_small_memory_pool_buffer = NULL;
small_mem_blk_link *small_mem_blk_link_head = NULL;
size_t g_small_memory_offset = 0;

small_mem_header **small_mem_list_head=NULL;

static size_t g_small_mem_sum = 0;
static size_t small_mem_index = 0;
static pthread_mutex_t g_small_memory_mutex = PTHREAD_MUTEX_INITIALIZER;

#define SMALL_MEM_START &g_small_memory_pool_buffer[g_small_memory_offset]
#define SMALL_MEM_END &g_small_memory_pool_buffer[SMALL_MEM_LIST_HEAD_NUM-1]
#define SMALL_MEM_BLK_SIZE sizeof(small_mem_header)

#define LOCK //pthread_mutex_lock(&g_small_memory_mutex)

#define UNLOCK //pthread_mutex_unlock(&g_small_memory_mutex)

static void push_list_first(small_mem_header **head, small_mem_header *ptr)
{
    if (head[0])
    {
        (*ptr).header.next = head[0];
        head[0] = ptr;
    }
    else
    {
        head[0] = ptr;
    }
}

static small_mem_header *get_list_first(small_mem_header **head)
{
    if (head[0])
    {
        small_mem_header *ret = head[0];
        head[0] = (*head[0]).header.next;
        return ret;
    }

    return NULL;
}
static size_t get_ptr_size(void *ptr)
{
    small_mem_header *ptr_blk_res = (small_mem_header *)ptr - 1;
    size_t msize = ((*ptr_blk_res).header.flag_use & 0x7f) * SMALL_MEM_ALIGN;
    return msize;
}
static void creat_small_mem_buffer()
{

    if (g_small_memory_pool_buffer == NULL)
    {
        small_mem_list_head=cc_big_mem_alloc(SMALL_MEM_LIST_HEAD_NUM*sizeof(small_mem_header));
        g_small_memory_pool_buffer = (char *)cc_big_mem_alloc(CC_SMALL_MEMORY_POOL_SIZE) + sizeof(small_mem_blk_link);
        small_mem_blk_link_head = (small_mem_blk_link *)g_small_memory_pool_buffer - 1;
        (*small_mem_blk_link_head).header.len = CC_SMALL_MEMORY_POOL_SIZE - sizeof(small_mem_blk_link);
        (*small_mem_blk_link_head).header.next = NULL;
        (*small_mem_blk_link_head).header.ptr = g_small_memory_pool_buffer;
        if (((size_t)g_small_memory_pool_buffer % 16) != 0)
            g_small_memory_offset = 16 - ((size_t)g_small_memory_pool_buffer % 16);
        (*small_mem_blk_link_head).header.len -= g_small_memory_offset;
        (*small_mem_blk_link_head).header.ptr = (char *)(*small_mem_blk_link_head).header.ptr + g_small_memory_offset;
        small_mem_index = 0;
    }
    // printf("%p\n",g_small_memory_pool_buffer);
}
static void add_small_mem_buffer()
{
    if (g_small_memory_pool_buffer == NULL)
    {
        creat_small_mem_buffer();
    }
    else
    {
        g_small_memory_pool_buffer = (char *)cc_big_mem_alloc(CC_SMALL_MEMORY_POOL_SIZE) + sizeof(small_mem_blk_link);
        small_mem_blk_link *old_head = small_mem_blk_link_head;
        small_mem_blk_link_head = (small_mem_blk_link *)g_small_memory_pool_buffer - 1;
        (*small_mem_blk_link_head).header.len = CC_SMALL_MEMORY_POOL_SIZE - sizeof(small_mem_blk_link);
        (*small_mem_blk_link_head).header.next = old_head;
        (*small_mem_blk_link_head).header.ptr = g_small_memory_pool_buffer;
        if (((size_t)g_small_memory_pool_buffer % 16) != 0)
            g_small_memory_offset = 16 - ((size_t)g_small_memory_pool_buffer % 16);
        (*small_mem_blk_link_head).header.len -= g_small_memory_offset;
        (*small_mem_blk_link_head).header.ptr = (char *)(*small_mem_blk_link_head).header.ptr + g_small_memory_offset;
        small_mem_index = 0;
    }
}
size_t cc_get_small_mem_max_size()
{
    return SMALL_MEM_LIST_HEAD_NUM * SMALL_MEM_ALIGN;
}
size_t cc_get_small_mem_ptr_size(void *ptr)
{
    size_t ret = 0;
    LOCK;
    ret = get_ptr_size(ptr);
    UNLOCK;
    return ret;
}
unsigned char cc_check_small_mem_ptr(void *ptr)
{
    LOCK;
    small_mem_blk_link* blk_head=small_mem_blk_link_head;
    while(blk_head){
        if((size_t)ptr > (size_t)(*blk_head).header.ptr && (size_t)ptr < (size_t)(*blk_head).header.ptr+(*blk_head).header.len){
            UNLOCK;
            return 1;
        }
        blk_head=(*blk_head).header.next;
    }
    UNLOCK;
    return 0;
}

void *cc_small_mem_alloc(size_t size)
{

    void *ret_ptr = NULL;
    size_t msize = size;
    small_mem_header *ptr_blk_res = NULL;
    if (0 != (size % SMALL_MEM_ALIGN))
    {
        msize += (SMALL_MEM_ALIGN - (size % SMALL_MEM_ALIGN));
        // std::cout<<"al "<<(size % 128)<<std::endl;
    }
    if (msize == 0)
    {
        return NULL;
    }
    size_t list_head_index = (msize - 1) / SMALL_MEM_ALIGN;
    LOCK;
    creat_small_mem_buffer();
    if (list_head_index < SMALL_MEM_LIST_HEAD_NUM)
    {

        do
        {
            ptr_blk_res = get_list_first(&small_mem_list_head[list_head_index]);
            if (ptr_blk_res)
            {
                // printf("%d %d %d\n",list_head_index,(*ptr_blk_res).header.flag_use&0x7f,g_small_mem_sum);
                ret_ptr = ((unsigned char *)ptr_blk_res) + SMALL_MEM_BLK_SIZE;
                // if(ret_ptr!=(*ptr_blk_res).header.ptr){
                //     printf("ptr error\n");
                //     exit(0);
                // }
                (*ptr_blk_res).header.next = NULL;
                goto RETURN_PTR;
            }
            list_head_index++;
        } while (list_head_index < SMALL_MEM_LIST_HEAD_NUM);
    }
CREAT_NEW_MEM:
    if (g_small_mem_sum)
    {
        if ((small_mem_index + msize + SMALL_MEM_BLK_SIZE) <= CC_SMALL_MEMORY_POOL_SIZE)
        {
            ptr_blk_res = (small_mem_header *)(SMALL_MEM_START + small_mem_index);
            (*ptr_blk_res).header.flag_use = 0x80;
            (*ptr_blk_res).header.flag_use |= 0x7f & (unsigned char)(msize / SMALL_MEM_ALIGN);
            (*ptr_blk_res).header.next = NULL;
            small_mem_index += msize + SMALL_MEM_BLK_SIZE;
            ret_ptr = ((unsigned char *)ptr_blk_res) + SMALL_MEM_BLK_SIZE;
            (*ptr_blk_res).header.ptr = ret_ptr;
            g_small_mem_sum++;
            // printf("small_mem_index=%d , msize=%d SMALL_MEM_BLK_SIZE=%d\n", small_mem_index, msize, SMALL_MEM_BLK_SIZE);
            goto RETURN_PTR;
        }
        else
        {
            add_small_mem_buffer();
            goto CREAT_NEW_MEM;
        }
    }
    else
    {
        ptr_blk_res = (small_mem_header *)(SMALL_MEM_START);
        (*ptr_blk_res).header.flag_use = 0x80;
        (*ptr_blk_res).header.flag_use |= 0x7f & (unsigned char)(msize / SMALL_MEM_ALIGN);
        (*ptr_blk_res).header.next = NULL;
        g_small_mem_sum = 1;
        small_mem_index = SMALL_MEM_BLK_SIZE + msize;
        // printf("small_mem_index=%d , msize=%d SMALL_MEM_BLK_SIZE=%d\n", small_mem_index, msize, SMALL_MEM_BLK_SIZE);
        ret_ptr = ((unsigned char *)ptr_blk_res) + SMALL_MEM_BLK_SIZE;
        (*ptr_blk_res).header.ptr = ret_ptr;
        goto RETURN_PTR;
    }

RETURN_PTR:
    UNLOCK;
    // if (!cc_check_small_mem_ptr(ret_ptr))
    // {
    //     printf("%x %x %x %d %d\n", (size_t)ret_ptr, (size_t)SMALL_MEM_START, (size_t)SMALL_MEM_END, msize, get_ptr_size(ret_ptr));
    //     exit(0);
    // }
    // printf("%d\n",(size_t)ret_ptr%16);
    return ret_ptr;
}

void *cc_small_mem_realloc(void *ptr, size_t size)
{
    void *ret_ptr = NULL;

    if (cc_check_small_mem_ptr(ptr))
    {
        LOCK;
        size_t old_size = get_ptr_size(ptr);
        UNLOCK;
        if (size <= old_size)
        {
            ret_ptr = ptr;
            goto RETURN_PTR;
        }
        else
        {
            ret_ptr = cc_small_mem_alloc(size);
            size_t cp_size = old_size < size ? old_size : size;
            if (ret_ptr)
            {
                memcpy(ret_ptr, ptr, cp_size);
            }
            cc_small_mem_free(ptr);
            goto RETURN_PTR;
        }
    }
RETURN_PTR:
    return ret_ptr;
}

void cc_small_mem_free(void *ptr)
{
    if (cc_check_small_mem_ptr(ptr))
    {
        // printf("%p %p\n",SMALL_MEM_START,SMALL_MEM_END);
        LOCK;
        small_mem_header *ptr_blk_res = (small_mem_header *)ptr - 1;
        if ((*ptr_blk_res).header.ptr != ptr)
        {
            UNLOCK;
            printf("cc_small_mem_free invalid ptr %p\n", ptr);
            return;
        }
        //  printf("cc_small_mem_free  ptr %p\n",ptr);
        (*ptr_blk_res).header.flag_use &= 0x7f;
        size_t msize = (*ptr_blk_res).header.flag_use & 0x7f;
        size_t list_head_index = (msize - 1);
        if (list_head_index < SMALL_MEM_LIST_HEAD_NUM)
        {
            push_list_first(&small_mem_list_head[list_head_index], ptr_blk_res);
            size_t i = 0;
            // do{
            //     i++;
            //     ptr_blk_res=(*ptr_blk_res).header.next;
            // }while(ptr_blk_res);
            // printf("%d %d %d\n",small_mem_list_head[list_head_index]->flag_use&0x7f,i,g_small_mem_sum);
        }

        UNLOCK;
        // printf("cc_small_mem_free  ptr %p\n", ptr);
    }
    else{
        printf("cc_small_mem_free invalid ptr %p\n", ptr);
    }
}
