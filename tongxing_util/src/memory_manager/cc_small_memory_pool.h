#ifndef __CC_SMALL_MEMORY_POOL_H__
#define __CC_SMALL_MEMORY_POOL_H__
#ifdef __cplusplus
extern "C"{
#endif
#include <stdlib.h>
unsigned char cc_check_small_mem_ptr(void *ptr);
void *cc_small_mem_alloc(size_t size);
void *cc_small_mem_realloc(void *ptr, size_t size);
void cc_small_mem_free(void *ptr);
size_t cc_get_small_mem_ptr_size(void *ptr);
size_t cc_get_small_mem_max_size();
#ifdef __cplusplus
}
#endif
#endif
