#ifndef __CC_MEDIUM_MEMORY_POOL_H__
#define __CC_MEDIUM_MEMORY_POOL_H__

#ifdef __cplusplus
extern "C"{
#endif
#include <stdlib.h>
unsigned char cc_check_medium_mem_ptr(void *ptr);
void *cc_medium_mem_alloc(size_t size);
void *cc_medium_mem_realloc(void *ptr, size_t size);
void cc_medium_mem_free(void *ptr);
size_t cc_get_medium_mem_ptr_size(void *ptr);
size_t cc_get_medium_mem_max_size();
#ifdef __cplusplus
}
#endif






#endif
