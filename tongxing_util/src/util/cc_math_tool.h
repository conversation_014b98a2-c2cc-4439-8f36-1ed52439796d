#ifndef _CC_MATH_TOOL_H_
#define _CC_MATH_TOOL_H_
#include <opencv2/imgproc.hpp>
#include <vector>
namespace tongxing
{
    class CcObjBBox
    {
    public:
        int label;
        float score;
        cv::Rect bbox;
    };
    class ObjCubeBBox : public CcObjBBox
    {
    public:
        cv::Point left_front_edge;
        cv::Point left_behind_edge;
        cv::Point right_front_edge;
        cv::Point right_behind_edge;
        int orientation;
    };
    float sigmoid(float x);
    float inverse_sigmoid(float x);
    
    static float get_iou_value(cv::Rect rect1, cv::Rect rect2)
    {
        int xx1, yy1, xx2, yy2;

        xx1 = std::max(rect1.x, rect2.x);
        yy1 = std::max(rect1.y, rect2.y);
        xx2 = std::min(rect1.x + rect1.width - 1, rect2.x + rect2.width - 1);
        yy2 = std::min(rect1.y + rect1.height - 1, rect2.y + rect2.height - 1);

        int insection_width, insection_height;
        insection_width = std::max(0, xx2 - xx1 + 1);
        insection_height = std::max(0, yy2 - yy1 + 1);

        float insection_area, union_area, iou;
        insection_area = float(insection_width) * insection_height;
        union_area = float(rect1.width * rect1.height + rect2.width * rect2.height - insection_area);
        iou = insection_area / union_area;
        return iou;
    }
    template <class T1>
    bool cmp_max(std::pair<int, T1> &x, std::pair<int, T1> &y)
    {
        return x.second.score > y.second.score;
    }
    template <class T1>
    std::vector<int> nms(const std::vector<T1> &int_bbox, float iou_threshold, int class_num)
    {
        std::vector<std::vector<std::pair<int, T1>>> bbox_class;
        bbox_class.resize(class_num);
        for (uint32_t i = 0; i < int_bbox.size(); i++)
        {
            auto &b = int_bbox[i];
            if (b.label <class_num)
            {
                bbox_class[b.label].push_back(std::pair<int, T1>(i, b));
            }
        }
        std::vector<int> out_index;
        // std::cout<<"-----------------"<<std::endl;
        for (auto &bbox : bbox_class)
        {
            std::sort(bbox.begin(), bbox.end(), cmp_max<T1>);
            int i = 0;
            std::vector<int> indices;
            for (auto &obj : bbox)
            {
                const int idx = i;
                bool keep = true;
                for (unsigned k = 0; k < indices.size(); ++k)
                {
                    if (keep)
                    {
                        const int kept_idx = indices[k];
                        float overlap = get_iou_value(obj.second.bbox, bbox[kept_idx].second.bbox);

                        keep = overlap < iou_threshold;
                    }
                    else
                    {
                        break;
                    }
                }
                if (keep)
                {
                    indices.push_back(idx);
                    out_index.push_back(obj.first);
                    // std::cout<<obj.first<<std::endl;
                }
                i++;
            }
        }
        return out_index;
    }
    void CcObjBBoxOffsetScale(std::vector<CcObjBBox> &in_bbox, int in_w, int in_h, int out_w, int out_h, int offset_x = 0, int offset_y = 0);
    cv::Rect GetRoi(cv::Rect &bbox, int max_w, int max_h, float expand);
    int ArgMax(float *data, int num);
}

#endif
