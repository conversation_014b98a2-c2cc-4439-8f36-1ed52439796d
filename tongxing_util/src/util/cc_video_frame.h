#ifndef _CC_VIDEO_FRAME_H_
#define _CC_VIDEO_FRAME_H_
#include <stdint.h>
#include <memory>
#include "cc_blob_data.h"
#include "cc_tensor.h"
#define CC_IMAGE_MAX_DIM 3
namespace tongxing {

//图像像素格式
typedef enum _IMAGE_FORMAT_E {
    BGR = 0,
    RGB = 1,
    NV21 = 2,
    YV12 = 3,
    YUV422 = 4,
    NV12 = 5,
    GRAY = 6
} IMAGE_FORMAT_E;

class VideoFrame {
  public:
    IMAGE_FORMAT_E enType;  //图像类型
    uint32_t u32Height;     //图像高度
    uint32_t u32Width;      //图像宽度
    uint32_t u32ChwOrder[CC_IMAGE_MAX_DIM] = {0, 1, 2};
    uint32_t u32Pad[2] = {0, 0};  //图像每行像素的跨度
    std::shared_ptr<BlobData> data_blob_ptr;

  public:
    CcTensor<uint8_t> getTensor();
};

}  // namespace tongxing

#endif