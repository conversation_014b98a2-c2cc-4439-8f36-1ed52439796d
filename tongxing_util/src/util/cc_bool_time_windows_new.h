#ifndef __CC_TIME_WINDOWS_NEW_H__
#define __CC_TIME_WINDOWS_NEW_H__
#include <vector>
#include <utility>
#include <map>
#include <string>
namespace tongxing{
    class CcBoolTimeWindowsStatisticsNew{
        public:
            //时间窗单位ms
            int init(long time_width);
            float update(bool input_value,long ms_ts);
            int get_true_count(long min_time_width, long max_time_width);
            float get_ture_time(long time_width);
            long get_start_time();
            void clear();
        private:
            long  time_width_=1000;
            float percent_=0;
            bool last_status_=false;
            std::vector<std::pair<long,bool> > cache_data_;
            std::pair<long,bool> start_data_;

        public:
            std::map<std::string, int> dbg_info_;

            

    };
}


#endif