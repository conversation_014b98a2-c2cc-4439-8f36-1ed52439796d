/*
 * time_profile.cpp
 *
 *  Created on: May 5, 2017
 *      Author: chengsq
 */

#include "time_profile.h"
#include <sys/time.h>
#include <unistd.h>
#include <cstdio>
#include <cstring>

// namespace tongxing
//{

void TimeProfile::Reset(void) {
  time_pieces_.clear();
  // get the time in micro-second
  struct timeval start;
  gettimeofday(&start, NULL);
  cur_time_in_microsend = (start.tv_sec) * 1000000 + start.tv_usec;
  return;
}

void TimeProfile::Update(std::string name) {
  // make sure no other time piece with same name exists
  std::map<std::string, float>::iterator it;
  it = time_pieces_.find(name);
  if (it != time_pieces_.end()) {
    printf(
        "TimeProfile::update error: there is already a time piece called %s "
        "exists. \n",
        name.c_str());
    return;
  }

  // get the time in micro-second
  unsigned long long tt;
  struct timeval start;
  gettimeofday(&start, NULL);
  tt = (start.tv_sec) * 1000000 + start.tv_usec;

  unsigned long long diff = tt - cur_time_in_microsend;
  cur_time_in_microsend = tt;

  // insert a new value (int millisecond) in the map;
  float time_in_millisecond = float(diff / 1000.f);
  time_pieces_[name] = time_in_millisecond;

  // apply IIR filter
  float IIR_rate = 0.05f;
  it = history_.find(name);
  if (it != history_.end()) {
    history_[name] =
        IIR_rate * time_pieces_[name] + (1.0f - IIR_rate) * history_[name];
  } else {
    history_[name] = time_pieces_[name];
  }
}

float TimeProfile::GetTimePieceInMillisecend(std::string& name) {
  // make sure this name piece exists
  std::map<std::string, float>::iterator it;
  it = time_pieces_.find(name);
  if (it == time_pieces_.end()) {
    printf("TimeProfile::update error: cannot find a time piece called %s. \n",
           name.c_str());
    return -1.0f;
  }

  return time_pieces_[name];
}

char* TimeProfile::GetTimeProfileString(void) {
  std::map<std::string, float>::iterator it;

  memset(profile_string, 0, 10000);
  for (it = time_pieces_.begin(); it != time_pieces_.end(); it++) {
    char temp_buffer[1024];
    sprintf(temp_buffer, "%[%s]:%3.1f, ", it->first.c_str(),
            it->second);
    strcat(profile_string, temp_buffer);
  }
   strcat(profile_string, ". [ms].");
  // sprintf(profile_string, "%s. [ms].", profile_string);

  return profile_string;
}

char* TimeProfile::GetSmoothedTimeProfileString(void) {
  std::map<std::string, float>::iterator it;

  memset(profile_string, 0, 10000);
  for (it = history_.begin(); it != history_.end(); it++) {
    char temp_buffer[1024];
    sprintf(temp_buffer, "[%s]:%3.1f ", it->first.c_str(),
            it->second);
    strcat(profile_string, temp_buffer);
  }
   strcat(profile_string, ". [ms].");

  return profile_string;
}

float TimeProfile::GetSoomthedFps() {
  std::map<std::string, float>::iterator it;
  float time_cost = 0;
  for (it = history_.begin(); it != history_.end(); it++) {
    time_cost += it->second;
  }
  float fps = 1000 / (time_cost + 0.1);
  return fps;
}

float TimeProfile::GetFps() {
  std::map<std::string, float>::iterator it;
  float time_cost = 0;
  for (it = time_pieces_.begin(); it != time_pieces_.end(); it++) {
    time_cost += it->second;
  }
  float fps = 1000 / (time_cost + 0.1);
  return fps;
}

//}
