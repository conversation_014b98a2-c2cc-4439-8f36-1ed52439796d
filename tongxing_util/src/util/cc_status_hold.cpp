#include "cc_status_hold.h"
namespace tongxing{
int CcStatusHold::init(float entry_threshold, float exit_threshold){
    entry_threshold_=entry_threshold;
    exit_threshold_=exit_threshold;
    status_=false;
    return 0;
}
int CcStatusHold::clear(){
    status_=false;
    return 0;
}
bool CcStatusHold::update(float value){
    if(status_&&value<exit_threshold_){
        status_=false;
    }else if(status_==false && value>=entry_threshold_){
        status_=true;
    }
    return status_;
}
}