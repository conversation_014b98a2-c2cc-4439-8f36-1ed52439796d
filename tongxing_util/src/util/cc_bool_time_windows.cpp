#include "cc_bool_time_windows.h"
#include "CalmCarLog.h"
#include <iostream>
namespace tongxing
{
    //初始化时间长存储 和 百分比
    int CcBoolTimeWindowsStatistics::init(long time_width, float enter_percent_th, float esc_percent_th)
    {
        time_width_ = time_width;
        enter_percent_th_ = enter_percent_th;
        esc_percent_th_=esc_percent_th;
        return 0;
    }
    
    /**
 * 更新时间窗口内的统计数据，并根据条件判断是否达到某个百分比。
 * 
 * @param input_value 输入的布尔值数据。
 * @param ms_ts 输入数据的时间戳，单位为毫秒。
 * @param percent 引用参数，计算后返回的时间窗口内真实值占比。
 * @return 如果真实值占比达到或超过预设阈值，则返回true；否则返回false。
 */
    bool CcBoolTimeWindowsStatistics::update(bool input_value, long ms_ts, float &percent)
    {
        std::pair<long, bool> pair;
        pair.first = ms_ts;
        pair.second = input_value;
        cache_data_.push_back(pair);  //记录当前数据


        long last_ts = 0;
        bool last_value = false;
        long true_time_sum = 0;

        for (auto it = cache_data_.begin(); it != cache_data_.end(); )  //遍历一次
        {
            if (it->first > ms_ts || time_width_ < (ms_ts - it->first)) //判断遍历时间大于当前时间，或者 当前时间差大于设定的保存时间值。
            {
                // std::cout<<it->first<<std::endl;
                if (it->first >= last_ts) //读取最后值
                {
                    last_ts = it->first;
                    last_value = it->second;
                }

                cache_data_.erase(it); // 清除
            }
            else{
                it++;
            }
        }
        // std::cout<<cache_data_.size()<<std::endl;
        last_ts = ms_ts - time_width_; //算出最后的时间差
        cache_data_.insert(cache_data_.begin(), std::pair<long, bool>(last_ts, last_value)); //开始位置插入
        for (auto it = cache_data_.begin(); it != cache_data_.end(); it++) //再遍历一次
        {
            if (last_value == true && it->second == true)
            {
                true_time_sum += it->first - last_ts;
            }
            long ts = it->first;
            bool value = it->second;

            last_ts = ts;
            last_value = value;
        }
        percent = (float)true_time_sum / (float)time_width_;
        // if(percent>=0.99){
        //     percent=1.0;
        // }
        if(last_status==false){
            if (percent >= enter_percent_th_)
            {
                last_status=true;
            }
        }
        else{
            if(esc_percent_th_>0){
                if (percent <= esc_percent_th_)
                {
                    last_status=false;
                }
            }else{
                if (percent < enter_percent_th_)
                {
                    last_status=false;
                }
            }
        }
        return last_status;
    }


    bool CcBoolTimeWindowsStatistics::get_ture_time(long time_width, float percent)
    {
        bool ret = false;
        
        if (cache_data_.size() < 2)
        {
            return ret;
        }
        int num_true = 0;
        int num_all = 0;
        auto last_valve = cache_data_.back();
        auto start_valve = cache_data_.front();
        if ((last_valve.first - start_valve.first) < time_width) // 判断记录的时间是否长于要读取的时间，没有，返回失败
        {
            return ret;
        }
        bool warn_satart_one = false;
        for (auto it = cache_data_.rbegin(); it != cache_data_.rend(); ++it)
        {
            if (it->first >= (last_valve.first - time_width)) // 判断遍历时间大于当前时间，或者 当前时间差大于设定的保存时间值。
            {
               
                if (it->second)
                {
                    warn_satart_one = true;
                    num_true++;
                }else{
                    warn_satart_one = false;
                }
                num_all++;
            }else{
                break;
            }
        }
        if (num_true > 0 && warn_satart_one)
        {
           float percent_th = static_cast<float>(num_true) / num_all;
           if (percent_th>=percent)
           {
            return true;
           }
        }
        return ret;
        
    }

    int CcBoolTimeWindowsStatistics::get_true_count(long time_width)
    {
        int bool_count = 0;
        if (cache_data_.size() > 3)
        {
            long last_ts = cache_data_.begin()->first;
            bool last_value = cache_data_.begin()->second;
            long true_time_sum = 0;
            for (auto it = cache_data_.begin(); it != cache_data_.end(); it++)
            {
                if (last_value == true && it->second == true)
                {
                    true_time_sum += it->first - last_ts;
                }
                else if (last_value == true)
                {
                    if (true_time_sum >= time_width)
                    {
                        // LOG_INFO("bool wind"," bool_count %d ",bool_count);
                        bool_count++;
                    }
                    true_time_sum = 0;
                }
                long ts = it->first;
                bool value = it->second;

                last_ts = ts;
                last_value = value;
            }
            if (last_value == true)
            {
                if (true_time_sum >= time_width)
                {
                    bool_count++;
                }else{
                    bool_count=0;
                }
                true_time_sum = 0;
            }else{
                bool_count = 0;
            }
        }
        return bool_count;
    }

    void CcBoolTimeWindowsStatistics::clear()
    {
        cache_data_.clear();
    }
}