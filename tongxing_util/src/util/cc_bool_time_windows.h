#ifndef __CC_TIME_WINDOWS_H__
#define __CC_TIME_WINDOWS_H__
#include <vector>
#include <utility>
namespace tongxing{
    class CcBoolTimeWindowsStatistics{
        public:
            //时间窗单位ms
            int init(long time_width, float enter_percent_th, float esc_percent_th=-1);
            bool update(bool input_value,long ms_ts,float& percent);
            int get_true_count(long time_width);
            bool get_ture_time(long time_width,float percent=0.85);
            void clear();
        private:
            long  time_width_=1000;
            float enter_percent_th_=0.7;
            float esc_percent_th_=-1;
            bool last_status=false;
            std::vector<std::pair<long,bool> > cache_data_;
    };


}


#endif