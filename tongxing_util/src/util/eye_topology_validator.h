#pragma once

#include <opencv2/opencv.hpp>
#include <vector>
#include <memory>

namespace tongxing {

/**
 * @brief 眼睛关键点拓扑结构验证器
 * 
 * 基于17个眼睛关键点进行拓扑结构合理性验证：
 * - 0-7: 眼睛轮廓关键点
 * - 8-15: 虹膜轮廓关键点  
 * - 16: 瞳孔关键点
 */
class EyeTopologyValidator {
public:
    /**
     * @brief 眼睛关键点数据结构
     */
    struct EyeKeypoints {
        std::vector<cv::Point2f> eye_contour;    // 眼睛轮廓点 (8个点)
        std::vector<cv::Point2f> iris_contour;   // 虹膜轮廓点 (8个点)
        cv::Point2f pupil_point;                 // 瞳孔点 (1个点)
        std::vector<float> confidences;          // 所有关键点的置信度 (17个值)
        
        EyeKeypoints() {
            eye_contour.resize(8);
            iris_contour.resize(8);
            confidences.resize(17);
        }
    };

    /**
     * @brief 拓扑验证结果
     */
    struct TopologyValidationResult {
        bool is_valid;                    // 整体是否有效
        bool pupil_in_iris;              // 瞳孔是否在虹膜内
        bool iris_in_eye;                // 虹膜是否在眼睛轮廓内
        bool eye_contour_valid;          // 眼睛轮廓是否合理
        bool iris_contour_valid;         // 虹膜轮廓是否合理
        bool size_ratio_valid;           // 尺寸比例是否合理
        bool distribution_valid;         // 关键点分布是否合理
        bool smoothness_valid;           // 轮廓平滑性是否合理
        float confidence_score;          // 整体置信度评分 [0,1]
        std::string error_message;       // 错误信息

        TopologyValidationResult() : is_valid(false), pupil_in_iris(false),
                                   iris_in_eye(false), eye_contour_valid(false),
                                   iris_contour_valid(false), size_ratio_valid(false),
                                   distribution_valid(false), smoothness_valid(false),
                                   confidence_score(0.0f) {}
    };

    /**
     * @brief 构造函数
     */
    EyeTopologyValidator();

    /**
     * @brief 析构函数
     */
    ~EyeTopologyValidator() = default;

    /**
     * @brief 从原始关键点数据解析眼睛关键点结构
     * @param raw_keypoints 原始关键点数据 [置信度, x, y] * 17
     * @return 解析后的眼睛关键点结构
     */
    EyeKeypoints ParseEyeKeypoints(const float* raw_keypoints) const;

    /**
     * @brief 验证眼睛关键点的拓扑结构合理性
     * @param keypoints 眼睛关键点数据
     * @return 验证结果
     */
    TopologyValidationResult ValidateTopology(const EyeKeypoints& keypoints) const;

    /**
     * @brief 设置验证参数
     */
    void SetMinConfidenceThreshold(float threshold) { min_confidence_threshold_ = threshold; }
    void SetPupilIrisRatioRange(float min_ratio, float max_ratio) { 
        min_pupil_iris_ratio_ = min_ratio; 
        max_pupil_iris_ratio_ = max_ratio; 
    }
    void SetIrisEyeRatioRange(float min_ratio, float max_ratio) { 
        min_iris_eye_ratio_ = min_ratio; 
        max_iris_eye_ratio_ = max_ratio; 
    }

private:
    /**
     * @brief 检查瞳孔是否在虹膜轮廓内
     */
    bool CheckPupilInIris(const EyeKeypoints& keypoints) const;

    /**
     * @brief 检查虹膜是否在眼睛轮廓内
     */
    bool CheckIrisInEye(const EyeKeypoints& keypoints) const;

    /**
     * @brief 验证眼睛轮廓的合理性
     */
    bool ValidateEyeContour(const std::vector<cv::Point2f>& contour) const;

    /**
     * @brief 验证虹膜轮廓的合理性
     */
    bool ValidateIrisContour(const std::vector<cv::Point2f>& contour) const;

    /**
     * @brief 验证尺寸比例的合理性
     */
    bool ValidateSizeRatios(const EyeKeypoints& keypoints) const;

    /**
     * @brief 验证关键点分布的合理性
     */
    bool ValidateKeypointDistribution(const EyeKeypoints& keypoints) const;

    /**
     * @brief 验证轮廓平滑性
     */
    bool ValidateContourSmoothness(const EyeKeypoints& keypoints) const;

    /**
     * @brief 计算整体置信度评分
     */
    float CalculateConfidenceScore(const EyeKeypoints& keypoints,
                                  const TopologyValidationResult& partial_result) const;

    /**
     * @brief 检查点是否在多边形内
     */
    bool IsPointInPolygon(const cv::Point2f& point, 
                         const std::vector<cv::Point2f>& polygon) const;

    /**
     * @brief 检查轮廓是否自相交
     */
    bool HasSelfIntersection(const std::vector<cv::Point2f>& contour) const;

    /**
     * @brief 计算轮廓的面积
     */
    float CalculateContourArea(const std::vector<cv::Point2f>& contour) const;

    /**
     * @brief 拟合椭圆并返回参数
     */
    cv::RotatedRect FitEllipse(const std::vector<cv::Point2f>& points) const;

    /**
     * @brief 检查相邻关键点距离的合理性
     */
    bool CheckAdjacentDistances(const std::vector<cv::Point2f>& contour) const;

    /**
     * @brief 计算轮廓的曲率变化
     */
    std::vector<float> CalculateCurvature(const std::vector<cv::Point2f>& contour) const;

    /**
     * @brief 检查轮廓的凸性
     */
    bool IsContourConvex(const std::vector<cv::Point2f>& contour) const;

private:
    // 验证参数
    float min_confidence_threshold_;    // 最小置信度阈值
    float min_pupil_iris_ratio_;       // 瞳孔/虹膜最小比例
    float max_pupil_iris_ratio_;       // 瞳孔/虹膜最大比例
    float min_iris_eye_ratio_;         // 虹膜/眼睛最小比例
    float max_iris_eye_ratio_;         // 虹膜/眼睛最大比例
    float max_contour_distance_ratio_; // 相邻关键点最大距离比例
};

} // namespace tongxing
