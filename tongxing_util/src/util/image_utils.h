#ifndef IMAGE_UTILS_H
#define IMAGE_UTILS_H
#include "opencv2/opencv.hpp"
#include "cc_tensor.h"
namespace tongxing {
bool unsigned_char_to_cv_mat(unsigned char* src_image,
                             uint32_t width,
                             uint32_t height,
                             int channel,
                             cv::Mat& dst_image);  //将二进制图片数据转换成opencv类型图片数据结构

bool cv_mat_to_unsigned_char(cv::Mat& src_image,
                             uint32_t width,
                             uint32_t height,
                             int channel,
                             unsigned char* dst_image);  //将opencv类型图片数据结构转换为二进制流

void crop_img(cv::Mat& img, cv::Mat& crop_img, std::vector<int>& area);

void uchar2float_gray(const cv::<PERSON> tyuchar, cv::Mat& tyfloat);

void uchar2float_rgb(const cv::<PERSON> tyuchar, cv::<PERSON>& tyfloat);

void float2uchar_gray(const cv::Mat tyfloat, cv::<PERSON>& tyuchar);

void float2uchar_rgb(const cv::<PERSON> tyfloat, cv::<PERSON>& tyuchar);

cv::Rect resize_keep_aspectratio(const cv::Mat& image_src,
                                 cv::Mat& image_dst,
                                 const cv::Size dst_size);  //图片等比缩放填充

}  // namespace tongxing
#endif