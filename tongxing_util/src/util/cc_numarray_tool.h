#ifndef __CC_NUMARRAY_TOOL_H__
#define __CC_NUMARRAY_TOOL_H__
#include "cc_tensor.h"
#include <memory>
#include <vector>
namespace tongxing
{
    const size_t g_numarray_type_word_size_map[] = {4, 8, 1, 2, 4, 8, 1, 2, 4, 8, 1};
#define GET_NUMARRAY_TYPE_WORLD_SIZE(type) g_numarray_type_word_size_map[type]

    typedef struct NumArrayResizeNormParam_st
    {
        int output_w;
        int output_h;
        int keep_ratio;
        bool flag_norm;
        float norm_std[3];
        float norm_mean[3];
    } NumArrayResizeNormParam;

    typedef struct NumArrayCorpParam_st
    {
        bool flag_corp;
        int x;
        int y;
        int w;
        int h;

    } NumArrayCorpParam;

    std::shared_ptr<NumArray> creat_numarray(const std::vector<int> &shape, NumArray::DataType type);
    std::shared_ptr<NumArray> numarray_corp_resize_pad_normalization(const std::shared_ptr<NumArray> &input, const NumArrayResizeNormParam &param, const std::vector<NumArrayCorpParam> &corp_params, bool flag_calc_histogram, std::shared_ptr<NumArray> &pixels_histogram, bool flag_nchw = true, bool flag_input_nchw = true);
    std::shared_ptr<NumArray> numarray_reshape(const std::shared_ptr<NumArray> &input, const std::vector<int> &shape);
    std::shared_ptr<NumArray> numarray_extend(const std::shared_ptr<NumArray> &input, int dim, int extend_num, bool flag_copy);
    std::shared_ptr<NumArray> numarray_nhwc2nchw(const std::shared_ptr<NumArray> &input);
    std::shared_ptr<NumArray> numarray_nchw2nhwc(std::shared_ptr<NumArray> input);
    void creat_corp_resize_map(std::shared_ptr<std::vector<int>> &index_map, std::shared_ptr<std::vector<float>> &std_map, std::shared_ptr<std::vector<float>> &mean_map, int width, int height, int ch, const NumArrayResizeNormParam &param, const NumArrayCorpParam &corp_param, bool flag_input_nchw, bool flag_output_nchw);
    std::shared_ptr<NumArray> numarray_remap_normalization(const std::shared_ptr<NumArray> &input, const std::vector<int> &ouput_dim, std::shared_ptr<std::vector<int>> &index, std::shared_ptr<std::vector<float>> &norm_param_std, std::shared_ptr<std::vector<float>> &norm_param_mean);
    void ScalePlane(const uint8_t *src,
                    int src_stride,
                    int src_width,
                    int src_height,
                    uint8_t *dst,
                    int dst_stride,
                    int dst_width,
                    int dst_height,
                    int step_input,
                    int step_output,
                    bool flag_calc_histogram=false,
                    uint64_t *histogram_data=NULL);
    void ScaleNormPlane(const uint8_t *src,
                        int src_stride,
                        int src_width,
                        int src_height,
                        float *dst,
                        int dst_stride,
                        int dst_width,
                        int dst_height,
                        int step_input,
                        int step_output,
                        float std,
                        float mean);
}

#endif