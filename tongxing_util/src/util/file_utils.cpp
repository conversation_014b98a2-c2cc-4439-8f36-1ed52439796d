#include "file_utils.h"
#include <dirent.h>
#include <stdio.h>
#include <string.h>
namespace tongxing {
int getAbsoluteFiles(std::string directory,
                     std::vector<std::string> &filesAbsolutePath) {
  DIR *dir = opendir(directory.c_str());
  if (dir == NULL) {
    fprintf(
        stderr,
        "[func]:%s,[line]:%d,[info]: %s is not a directory or not exist!.\n",
        __FUNCTION__, __LINE__, directory.c_str());
    return -1;
  }

  struct dirent *d_ent = nullptr;
  char dot[3] = ".";
  char dotdot[6] = "..";

  while ((d_ent = readdir(dir)) != nullptr) {
    if ((strcmp(d_ent->d_name, dot) != 0) &&
        (strcmp(d_ent->d_name, dotdot) != 0)) {
      if (d_ent->d_type == DT_DIR) {
        std::string newDirectory =
            directory + std::string("/") +
            std::string(d_ent->d_name);  // d_name中存储了子目录的名字
        if (directory[directory.length() - 1] == '/') {
          newDirectory = directory + std::string(d_ent->d_name);
        }
        if (-1 == getAbsoluteFiles(newDirectory, filesAbsolutePath)) {
          return -1;
        }
      } else {
        std::string filename = std::string(d_ent->d_name);
        std::string::size_type pos = filename.find_last_of('.');
        if (pos != std::string::npos) {
          std::string ext = filename.substr(pos, std::string::npos);
          if (strcmp(ext.c_str(), ".jpg") == 0 ||
              strcmp(ext.c_str(), ".png") == 0 ||
              strcmp(ext.c_str(), ".jpeg") == 0) {
            std::string absolutePath =
                directory + std::string("/") + filename;  //构建绝对路径
            if (directory[directory.length() - 1] == '/') {
              absolutePath = directory + filename;
            }
            filesAbsolutePath.push_back(absolutePath);
          }
        }
      }
    }
  }
  closedir(dir);
  return 0;
}
}  // namespace tongxing
