#ifndef _CC_TENSER_H_
#define _CC_TENSER_H_
#include <algorithm>
#include "cc_assert.h"
#include <iostream>
#include <memory>
#include <tuple>
#include <vector>
#include "cc_blob_data.h"
#include "cc_tiny_mempool.h"
namespace tongxing
{

    class CcTensorIndex
    {
    public:
        CcTensorIndex();
        CcTensorIndex(const std::vector<int> &dim,
                      const std::vector<int> &pad = std::vector<int>(),
                      bool flag_cp_pad = false,
                      bool flag_fortran_order = true);
        void init(const std::vector<int> &dim,
                  const std::vector<int> &pad,
                  bool flag_cp_pad,
                  bool flag_fortran_order = true);
        int get_index(const std::vector<int> &dim);
        std::vector<int> index_product_;
        std::vector<int> dim_;
        std::vector<int> pad_;
    };
    template <class T1>
    class CcTensor
    {
    public:
        CcTensor() {}
        CcTensor(const std::vector<int> &dim,
                 T1 *data,
                 const std::shared_ptr<BlobData> &data_blob = std::shared_ptr<BlobData>(),
                 const std::vector<int> &pad = std::vector<int>(),
                 bool flag_cp_pad = false,
                 bool flag_fortran_order = true)
        {
            index_.init(dim, pad, flag_cp_pad, flag_fortran_order);
            data_blob_ptr = data_blob;
            data_ = data;
        }
        T1 &at(const std::vector<int> &dim)
        {
            int index = index_.get_index(dim);
            return data_[index];
        }
        T1 &get() { return data_[0]; }
        const std::vector<int> &shape() { return index_.dim_; }
        void printShape()
        {
            std::cout << "shape:(";
            for (int i : index_.dim_)
            {
                std::cout << i << ",";
            }
            std::cout << ")" << std::endl;
        }
        template <typename t, size_t n>
        void printDimensionsOfArray(const t (&a)[n])
        {
            std::cout << n;
        }
        void print()
        {
            printShape();
            std::cout << get() << std::endl;
            // printDimensionsOfArray<T1,[10]>(data_);
        }

        CcTensor<T1> operator[](int index)
        {
            cc_assert(index_.dim_.size() != 0);
            cc_assert(index_.dim_[0] > index);
            if (index_.dim_.size() > 1)
            {
                std::vector<int> dim;
                dim.resize(index_.dim_.size());
                dim[0] = index;
                for (size_t i = 1; i < dim.size(); i++)
                {
                    dim[i] = 0;
                }
                T1 *data_ptr = &at(dim);
                dim.resize(dim.size() - 1);

                std::vector<int> pad;
                pad.resize(index_.pad_.size() - 1);
                for (size_t i = 0; i < dim.size(); i++)
                {
                    dim[i] = index_.dim_[i + 1];
                    pad[i] = index_.pad_[i + 1];
                }
                CcTensor<T1> tensor = CcTensor<T1>(dim, data_ptr, data_blob_ptr, pad, true);
                return tensor;
            }
            else
            {
                std::vector<int> dim = {index};
                T1 *data_ptr = &at(dim);
                dim[0] = 1;
                CcTensor<T1> tensor = CcTensor<T1>(dim, data_ptr, data_blob_ptr);
                return tensor;
            }
            return CcTensor<T1>();
        }

        std::shared_ptr<BlobData> data_blob_ptr;

        CcTensorIndex index_;
        T1 *data_;
    };

#if __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
#define BIGENDIANNESS 1
#else
#define BIGENDIANNESS 0
#endif

    class NumArray
    {
    public:
        enum DataType
        {
            FLOAT32 = 0,
            FLOAT64,
            INT8,
            INT16,
            INT32,
            INT64,
            UINT8,
            UINT16,
            UINT32,
            UINT64,
            BOOL,
        };

    public:
        NumArray()
        {
#if BIGENDIANNESS == 1
            flag_little_endian = false;
#else
            flag_little_endian = true;
#endif
            flag_fortran_order = true;
        };
        uint8_t major_version = 0; // 保留
        uint8_t minor_version = 0; // 保留

        DataType type;           // 数据类型
        size_t word_size;        // 每个数据占的字节数
        unsigned char *data;     // 数据地址
        std::vector<int> shape;  // 数据维度
        bool flag_fortran_order; // 是否低维度地址在前
        bool flag_little_endian; // 数据是否是小端数据
        std::shared_ptr<BlobData> data_blob_ptr;
        void  *data_handle;
        // 获取tensor
        template <class T1>
        std::shared_ptr<CcTensor<T1>> getTensor()
        {
            std::shared_ptr<CcTensor<T1>> out;
            cc_assert(sizeof(T1) == word_size);
#if BIGENDIANNESS == 0
            if (flag_little_endian)
            {
                out = std::shared_ptr<CcTensor<T1>>(new CcTensor<T1>(
                    shape, (T1 *)data, data_blob_ptr, std::vector<int>(), false, flag_fortran_order));
            }
            else
            {
                size_t s = word_size;
                for (auto i : shape)
                {
                    s *= i;
                }
                std::shared_ptr<BlobData> dst_data_blob(new BlobData);
                dst_data_blob->init(s);
                for (int i = 0; i < s; i += word_size)
                {
                    uint8_t *src_data_ptr = data + i;
                    uint8_t *dst_data_ptr = dst_data_blob->pu8VirAddr + i;
                    for (int j = 0; j < word_size; j++)
                    {
                        dst_data_ptr[j] = src_data_ptr[word_size - j - 1];
                    }
                }
                out = std::shared_ptr<CcTensor<T1>>(
                    new CcTensor<T1>(shape, (T1 *)dst_data_blob->pu8VirAddr, dst_data_blob,
                                     std::vector<int>(), false, flag_fortran_order));
            }
#else
            if (!flag_little_endian)
            {
                out = std::shared_ptr<CcTensor<T1>>(new CcTensor<T1>(
                    shape, (T1 *)data, data_blob_ptr, std::vector<int>(), false, flag_fortran_order));
            }
            else
            {
                size_t s = word_size;
                for (auto i : shape)
                {
                    s *= i;
                }
                std::shared_ptr<BlobData> dst_data_blob(new BlobData);
                dst_data_blob->init(s);
                for (int i = 0; i < s; i += word_size)
                {
                    uint8_t *src_data_ptr = data + i;
                    uint8_t *dst_data_ptr = dst_data_blob->pu8VirAddr + i;
                    for (int j = 0; j < word_size; j++)
                    {
                        dst_data_ptr[j] = src_data_ptr[word_size - j - 1];
                    }
                }
                out = std::shared_ptr<CcTensor<T1>>(
                    new CcTensor<T1>(shape, (T1 *)dst_data_blob->pu8VirAddr, dst_data_blob,
                                     std::vector<int>(), false, flag_fortran_order));
            }
#endif
            return out;
        }
    };

    template <class T>
    class CcTensorTool
    {
    public:
        CcTensorTool() {}

        // 根据行号和列号获取到数据值
        T getValue(std::shared_ptr<CcTensor<T>> tensor, int row_num, int col_num)
        {
            T result = (*tensor).at({row_num, col_num, 1});
            return result;
        }

        // 根据行号范围与列号范围获取到数组中
        std::vector<T> getSlices(std::shared_ptr<CcTensor<T>> tensor,
                                 int start_row_num,
                                 int end_row_num,
                                 int start_col_num,
                                 int end_col_num)
        {
            std::vector<T> result;
            result.clear();

            for (int i = start_row_num; i < end_row_num; i++)
            {
                for (int j = start_col_num; j < end_col_num; j++)
                {
                    result.push_back(getValue(tensor, i, j));
                }
            }

            return result;
        }
    };

    class CcTensorBbox
    {
    public:
        CcTensorBbox() {}
        std::vector<std::tuple<int, float, int, int, int, int>> result_product_;
        std::vector<int> shape_;
    };

} // namespace tongxing

#endif