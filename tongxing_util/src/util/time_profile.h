/*
 * time_profile.h
 *
 *  Created on: May 5, 2017
 *      Author: chengsq
 */

#ifndef SRC_UTIL_TIME_PROFILE_H_
#define SRC_UTIL_TIME_PROFILE_H_

#include <map>
#include <string>

// namespace tongxing
//{

class TimeProfile {
 public:
  TimeProfile() {
    time_pieces_.clear();
    history_.clear();
    cur_time_in_microsend = 0;
  };
  ~TimeProfile() {
    time_pieces_.clear();
    history_.clear();
  };
  float GetSoomthedFps();
  float GetFps();

  // calibrate floor coordinate system from at least 3 points on a same plane
  // which is parrallel to floor plane
  void Reset(void);
  void Update(std::string name);
  float GetTimePieceInMillisecend(std::string& name);
  char* GetTimeProfileString(void);
  char* GetSmoothedTimeProfileString(void);

 private:
  unsigned long long cur_time_in_microsend;

  char profile_string[10000];
  std::map<std::string, float> time_pieces_;
  std::map<std::string, float> history_;
};

//} /* namespace tongxing */

#endif /* SRC_UTIL_TIME_PROFILE_H_ */
