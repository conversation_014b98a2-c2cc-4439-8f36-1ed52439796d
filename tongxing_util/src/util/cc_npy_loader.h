#ifndef __CC_NPY_LOADER_H__
#define __CC_NPY_LOADER_H__
#include <memory>
#include <vector>
#include "cc_blob_data.h"
#include "cc_tensor.h"
#include "opencv2/opencv.hpp"
namespace tongxing {

int parse_npy_data(std::shared_ptr<BlobData>& data_ptr, NumArray& out);

void transform_pixel_v2(NumArray& in,
                        const std::shared_ptr<tongxing::CcTensor<float>>& tensor,
                        bool inverse,
                        NumArray& out);

void crop_alignment_image(
    NumArray Meanface, NumArray landmarks, NumArray frame, cv::Size out_size, NumArray& out);

/*****************Opencv  Mat转vector **********************/
template <typename _Tp>
std::vector<_Tp> convertMat2Vector(const cv::Mat& mat) {
    return (std::vector<_Tp>)(mat.reshape(1, 1));  //通道数不变，按行转为一行
}

/****************** vector转Opencv Mat *********************/
template <typename _Tp>
cv::Mat convertVector2Mat(std::vector<_Tp> v, int channels, int rows) {
    cv::Mat mat = cv::Mat(v);                            //将vector变成单列的mat
    cv::Mat dest = mat.reshape(channels, rows).clone();  //PS：必须clone()一份，否则返回出错
    return dest;
}

constexpr size_t DEFAULT_MAX_LENGTH = 200;

template <typename V>
struct backingValue {
    static V value;
};
template <typename V>
V backingValue<V>::value;

template <typename V, typename... Vcount>
struct backingList {
    static std::initializer_list<V> list;
};
template <typename V, typename... Vcount>
std::initializer_list<V> backingList<V, Vcount...>::list = {(Vcount)backingValue<V>::value...};

template <size_t maxLength, typename It, typename V = typename It::value_type, typename... Vcount>
static typename std::enable_if<sizeof...(Vcount) >= maxLength, std::initializer_list<V>>::type
generate_n(It begin, It end, It current) {
    throw std::length_error("More than maxLength elements in range.");
}

template <size_t maxLength = DEFAULT_MAX_LENGTH,
          typename It,
          typename V = typename It::value_type,
          typename... Vcount>
static typename std::enable_if<sizeof...(Vcount) < maxLength, std::initializer_list<V>>::type
generate_n(It begin, It end, It current) {
    if (current != end) return generate_n<maxLength, It, V, V, Vcount...>(begin, end, ++current);

    current = begin;
    for (auto it = backingList<V, Vcount...>::list.begin();
         it != backingList<V, Vcount...>::list.end(); ++current, ++it)
        *const_cast<V*>(&*it) = *current;

    return backingList<V, Vcount...>::list;
}

template <typename It>
std::initializer_list<typename It::value_type> range_to_initializer_list(It begin, It end) {
    return generate_n(begin, end, begin);
}

}  // namespace tongxing
#endif