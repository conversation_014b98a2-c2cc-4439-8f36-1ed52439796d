
#include "time_function.h"
#include "CalmCarLog.h"
#include <cstdio>
#include <cstring>
#include <iomanip>
#include <sys/time.h>
#include <unistd.h>

namespace tongxing {



// std::ofstream TimeFunction::fileStream;
TimeFunction::TimeFunction(std::string name,std::string file_name) {
#ifdef TIME_FUNCTION_ON
  struct timeval timer;
  gettimeofday(&timer, NULL);
  m_start_time = (timer.tv_sec) * 1000000 + timer.tv_usec;
  m_name = name;
  m_file_name = file_name;
#endif
}

TimeFunction::~TimeFunction() {
#ifdef TIME_FUNCTION_ON
  struct timeval timer;
  gettimeofday(&timer, NULL);
  m_end_time = (timer.tv_sec) * 1000000 + timer.tv_usec;
  float diff = (m_end_time - m_start_time) / 1000.f;
  TX_LOG_INFO(m_file_name.c_str(), "%s time:[%0.2f]ms", m_name.c_str(), diff);
#endif
}
} // namespace tongxing
