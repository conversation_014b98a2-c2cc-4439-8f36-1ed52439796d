#include "cc_blob_data.h"
#include <stdio.h>
#include <string.h>
#include <fstream>
#include "cc_tiny_mempool.h"
namespace tongxing
{

    void* BlobData::operator new(size_t size){
        return CcSmallMempool::instance().alloc(size);
    }
    void BlobData::operator delete(void *ptr){
        CcSmallMempool::instance().delloc(ptr);
    }

    BlobData::BlobData(std::function<int(uint32_t, BlobData *)> create_memory,
                       std::function<void(void *)> release_memory)
    {
        create_memory_function_ = create_memory;
        release_memory_function_ = release_memory;
        u32NameLen = 0;
        pu8VirAddr = NULL;
        u64PsyAddr = 0;
        u32Size = 0;
        flag_release = false;
        pu8Reserve = NULL;
    }
    int BlobData::init(uint32_t size,
                       const std::string &blob_name,
                       uint8_t *data_vir_addr,
                       uint64_t data_psy_addr,
                       void *data_handle)
    {
        if (size == 0)
        {
            return -1;
        }

        if (data_vir_addr == 0 && data_psy_addr == 0)
        {
            if (create_memory_function_)
            {
                int res = create_memory_function_(size, this);
                if (res != 0)
                {
                    return res;
                }
            }
            else
            {
                #if (MEM_ISSUE_DEBUG)
                pu8Reserve = (uint8_t *)malloc(size);
                // std::cout << "ptr2 addr:" << (void *)pu8Reserve  << std::endl;
                // std::cout << "mem size:" << size << std::endl;
                pu8VirAddr = pu8Reserve;
                u64PsyAddr = pu8Reserve;
                #else
                pu8Reserve = (uint8_t *)CcBolbMempool::instance().alloc(size);
                pu8VirAddr = ((CcBlobData_st *)pu8Reserve)->pu8VirAddr;
                u64PsyAddr = ((CcBlobData_st *)pu8Reserve)->u64PsyAddr;
                #endif
                u32Size = size;
                //  memset(pu8VirAddr,0,u32Size);
            }
        }
        else
        {
            pu8Reserve = (uint8_t *)data_handle;
            pu8VirAddr = data_vir_addr;
            u64PsyAddr = (void *)data_psy_addr;
            u32Size = size;
        }
        if (!blob_name.empty())
        {
            if (blob_name.size() < CC_MAX_BLOB_NAME_LEN)
            {
                memcpy(vcBlobName, blob_name.c_str(), blob_name.size());
                u32NameLen = blob_name.size();
            }
            else
            {
                memcpy(vcBlobName, blob_name.c_str(), CC_MAX_BLOB_NAME_LEN);
                u32NameLen = CC_MAX_BLOB_NAME_LEN;
            }
        }
        else
        {
            u32NameLen = 0;
        }
        if (pu8Reserve)
        {
            flag_release = true;
        }
        //
        return 0;
    }
    int BlobData::loadFromFile(const std::string &fileName)
    {
        std::ifstream in(fileName, std::ios::in);
        if (!in.is_open())
        {
            return -1;
        }

        in.seekg(0, std::ios::end);
        int file_size = in.tellg();
        in.seekg(0, std::ios::beg);
        if (file_size == 0)
        {
            return -2;
        }
        init(file_size);
        in.read((char *)pu8VirAddr, u32Size);
        in.close();
        return 0;
    }
    int BlobData::deinit()
    {
        if (flag_release && pu8Reserve)
        {
            if (release_memory_function_)
            {
                release_memory_function_((void *)pu8Reserve);
            }
            else
            {
                #if (MEM_ISSUE_DEBUG)
                free(pu8Reserve);
                #else
                CcBolbMempool::instance().delloc(((CcBlobData_st *)pu8Reserve));
                #endif
            }
        }
        flag_release = false;
        return 0;
    }
    BlobData::~BlobData()
    {
        deinit();
    }
} // namespace tongxing