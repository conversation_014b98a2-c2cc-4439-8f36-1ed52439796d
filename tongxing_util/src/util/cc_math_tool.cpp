#include "cc_math_tool.h"
#include <cmath>
#include <iostream>
namespace tongxing {
float sigmoid(float x) {
    return (1.0 / (1.0 + exp(-x)));
}
float inverse_sigmoid(float x) {
    return log(x / (1 - x));
}



// std::vector<int> nms(const std::vector<CcObjBBox>& int_bbox, float iou_threshold, int class_num) {
//     std::vector<std::vector<std::pair<int, CcObjBBox>>> bbox_class;
//     bbox_class.resize(class_num);
//     for (uint32_t i = 0; i < int_bbox.size(); i++) {
//         auto& b = int_bbox[i];
//         if(b.label<=5){
//             bbox_class[b.label].push_back(std::pair<int, CcObjBBox>(i, b));
//         }
//     }
//     std::vector<int> out_index;
//     // std::cout<<"-----------------"<<std::endl;
//     for (auto& bbox : bbox_class) {
//         std::sort(bbox.begin(), bbox.end(), cmp_max);
//         int i = 0;
//         std::vector<int> indices;
//         for (auto& obj : bbox) {
//             const int idx = i;
//             bool keep = true;
//             for (unsigned k = 0; k < indices.size(); ++k) {
//                 if (keep) {
//                     const int kept_idx = indices[k];
//                     float overlap = get_iou_value(obj.second.bbox, bbox[kept_idx].second.bbox);

//                     keep = overlap < iou_threshold;
//                 } else {
//                     break;
//                 }
//             }
//             if (keep) {
//                 indices.push_back(idx);
//                 out_index.push_back(obj.first);
//                 // std::cout<<obj.first<<std::endl;
//             }
//             i++;
//         }
//     }
//     return out_index;
// }
void CcObjBBoxOffsetScale(std::vector<CcObjBBox>& in_bbox,
                          int in_w,
                          int in_h,
                          int out_w,
                          int out_h,
                          int offset_x,
                          int offset_y) {
    for (auto& obj : in_bbox) {
        cv::Rect& bbox = obj.bbox;
        bbox.x += offset_x;
        bbox.y += offset_y;
        float x_scale = (float)out_w / (float)in_w;
        float y_scale = (float)out_h / (float)in_h;
        // printf("%f %f\n",x_scale,y_scale);
        bbox.x = x_scale * bbox.x;
        bbox.y = y_scale * bbox.y;
        bbox.width = x_scale * bbox.width;
        bbox.height = y_scale * bbox.height;
    }
}

cv::Rect GetRoi(cv::Rect& bbox, int max_w, int max_h, float expand) {
    cv::Rect r;
    r.width = bbox.width > bbox.height ? bbox.width : bbox.height;
    r.height = r.width * (expand);
    r.width = r.height + r.height % 2;
    r.height = r.width;

    int cx, cy;
    cx = bbox.x + (bbox.width / 2);
    cy = bbox.y + (bbox.height / 2);
    r.x = cx - r.width / 2;
    r.y = cy - r.height / 2;
    if (r.x < 0) {
        r.width += r.x;
        r.x = 0;
    }
    if (r.y < 0) {
        r.height += r.y;
        r.y = 0;
    }
    if ((r.width + r.x) > max_w) {
        r.width -= (r.width + r.x) - max_w;
    }
    if ((r.height + r.y) > max_h) {
        r.height -= (r.height + r.y) - max_h;
    }
    return r;
}

int ArgMax(float* data, int num) {
    int max_index = 0;
    float max_value = -99999999;
    for (int i = 0; i < num; i++) {
        if (data[i] >= max_value) {
            max_value = data[i];
            max_index = i;
        }
    }
    return max_index;
}
}  // namespace tongxing