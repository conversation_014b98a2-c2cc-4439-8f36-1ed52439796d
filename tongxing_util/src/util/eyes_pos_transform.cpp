#include <opencv2/opencv.hpp>
#include <cmath>
#include "eyes_pos_transform.h"

namespace tongxing{
    float rad(float x) {
        return x * CV_PI / 180.0f;
    }

    cv::Mat gen_x_matrix(float a) {
        float cos_a = std::cos(rad(a));
        float sin_a = std::sin(rad(a));

        cv::Matx44f mat(1, 0, 0, 0,
                        0, cos_a, -sin_a, 0,
                        0, -sin_a, cos_a, 0,
                        0, 0, 0, 1);
        return cv::Mat(mat);
    }

    cv::Mat gen_y_matrix(float a) {
        float cos_a = std::cos(rad(a));
        float sin_a = std::sin(rad(a));

        cv::Matx44f mat(cos_a, 0, sin_a, 0,
                        0, 1, 0, 0,
                        -sin_a, 0, cos_a, 0,
                        0, 0, 0, 1);
        return cv::Mat(mat);
    }

    cv::Mat gen_z_matrix(float a) {
        float cos_a = std::cos(rad(a));
        float sin_a = std::sin(rad(a));

        cv::Matx44f mat(cos_a, sin_a, 0, 0,
                        -sin_a, cos_a, 0, 0,
                        0, 0, 1, 0,
                        0, 0, 0, 1);
        return cv::Mat(mat);
    }

    cv::Mat get_matrix(float a, float b, float c,cv::Point center, cv::Size size) {
        // a = -a;
        // b = -b;

        cv::Mat X = gen_x_matrix(a);
        cv::Mat Y = gen_y_matrix(b);
        cv::Mat Z = gen_z_matrix(c);

        cv::Mat r = X * Y * Z;

        cv::Matx41f pcenter(center.x,center.y, 0, 0);

        cv::Matx41f p1(0, 0, 0, 0);
        cv::Matx41f p2(size.width, 0, 0, 0);
        cv::Matx41f p3(0, size.height, 0, 0);
        cv::Matx41f p4(size.width, size.height, 0, 0);

        p1 = p1 - pcenter;
        p2 = p2 - pcenter;
        p3 = p3 - pcenter;
        p4 = p4 - pcenter;

        std::vector<cv::Mat> list_dst = { r * p1, r * p2, r * p3, r * p4 };

        cv::Point2f org[4] = {
            cv::Point2f(0, 0),
            cv::Point2f(size.width, 0),
            cv::Point2f(0, size.height),
            cv::Point2f(size.width, size.height)
        };
        cv::Point2f dst[4];

        float z = std::sqrt(size.width * size.width + size.height * size.height) / 2.0f / std::tan(rad(21.0f));

        for (int i = 0; i < 4; ++i) {
            dst[i].x = list_dst[i].at<float>(0, 0) * z / (z - list_dst[i].at<float>(2, 0)) + pcenter(0);
            dst[i].y = list_dst[i].at<float>(1, 0) * z / (z - list_dst[i].at<float>(2, 0)) + pcenter(1);
        }

        cv::Mat warpR = cv::getPerspectiveTransform(org, dst);
        return warpR;
    }

    cv::Point2f perspective_transform(cv::Point2f pt, cv::Mat cvt_mat_t) {
        float u = pt.x;
        float v = pt.y;

        float x = (cvt_mat_t.at<double>(0, 0) * u + cvt_mat_t.at<double>(0, 1) * v + cvt_mat_t.at<double>(0, 2)) /
                (cvt_mat_t.at<double>(2, 0) * u + cvt_mat_t.at<double>(2, 1) * v + cvt_mat_t.at<double>(2, 2));
        float y = (cvt_mat_t.at<double>(1, 0) * u + cvt_mat_t.at<double>(1, 1) * v + cvt_mat_t.at<double>(1, 2)) /
                (cvt_mat_t.at<double>(2, 0) * u + cvt_mat_t.at<double>(2, 1) * v + cvt_mat_t.at<double>(2, 2));

        return cv::Point2f(x, y);
    }
}

// int main() {
//     using namespace tongxing;
//     float x = 30.0f, y = 45.0f, z = 60.0f;
//     int h = 96, w = 96;
//     cv::Size size(h, w);

//     cv::Mat R = get_matrix(x, y, z, size);
//     cv::Point c1(10, 20);
//     std::cout << "R:" << R << std::endl;
//     cv::Point c2 = perspective_transform(c1, R);

//     std::cout << "Point: (" << c2.x << ", " << c2.y << ")" << std::endl;

//     return 0;
// }
