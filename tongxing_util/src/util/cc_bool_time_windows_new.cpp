#include "cc_bool_time_windows_new.h"
#include <iostream>
namespace tongxing
{
    //初始化时间长存储 和 百分比
    int CcBoolTimeWindowsStatisticsNew::init(long time_width)
    {
        time_width_ = time_width;
        return 0;
    }
    
    /**
 * 更新时间窗口内的统计数据，并根据条件判断是否达到某个百分比。
 * 
 * @param input_value 输入的布尔值数据。
 * @param ms_ts 输入数据的时间戳，单位为毫秒。
 * @return 存入的数据长度
 */
    float CcBoolTimeWindowsStatisticsNew::update(bool input_value, long ms_ts)
    {
        std::pair<long, bool> pair;
        pair.first = ms_ts;
        pair.second = input_value;
        cache_data_.push_back(pair);  //记录当前数据


        long last_ts = 0;
        bool last_value = false;
        long true_time_sum = 0;
        // std::cout << "ti "<< ms_ts << std::endl;
        for (auto it = cache_data_.begin(); it != cache_data_.end(); )  //遍历一次
        {
           
            if (it->first > ms_ts || time_width_ < (ms_ts - it->first)) //判断遍历时间大于当前时间，或者 当前时间差大于设定的保存时间值。
            {
                cache_data_.erase(it); // 清除
            }
            else{
                it++;
            }
        }
        dbg_info_["time_width_"] = time_width_;
        dbg_info_["cache_data_size"] = cache_data_.size();
        start_data_.first = 0L;
        start_data_.second = false;
        for (auto it = cache_data_.begin(); it != cache_data_.end(); it++) //再遍历一次
        {
            if (last_value == true && it->second == true)
            {
                true_time_sum += it->first - last_ts;  //如果最后值是true 和当前值是true，则计算时间差和累加
                if ( start_data_.first == 0L)  //记录什么时候开始有动作
                {
                    start_data_.second = true;
                    start_data_.first = it->first;
                } 
            }

            last_ts = it->first;
            last_value = it->second;
        }
        percent_ = (float)true_time_sum / (float)time_width_;
        return percent_;
    }


    float CcBoolTimeWindowsStatisticsNew::get_ture_time(long time_width)
    {
        float ret = 0;
        int num_true = 0;
        int num_all = 0;

        auto last_valve = cache_data_.back();
        auto start_valve = cache_data_.front();
        if ((last_valve.first - start_valve.first) < time_width) // 判断记录的时间是否长于要读取的时间，没有，返回失败
        {
            return ret;
        }

        bool warn_satart_one = false;
        start_data_.first = 0L;
        start_data_.second = false;
        for (auto it = cache_data_.rbegin(); it != cache_data_.rend(); ++it)
        {
            if (it->first >= (last_valve.first - time_width)) // 判断遍历时间大于当前时间，或者 当前时间差大于设定的保存时间值。
            {  
                if (it->second)
                {
                    num_true++;
                    warn_satart_one = true;
                    start_data_.first = it->first ;
                    start_data_.second = it->second;
                }
                num_all++;
            }else{
                break;
            }
        }
        if (num_true > 0 && warn_satart_one)
        {
            ret = static_cast<float>(num_true) / num_all;
        }
        return ret;  
    }

    /**
     * @brief 统计在指定时间窗口内，布尔值为真(true)的持续时间宽度符合要求的次数
     * 
     * 该函数通过遍历缓存数据，寻找满足最小和最大时间宽度限制的连续为真时间段，并统计这些时间段的数量
     * 
     * @param min_time_width 最小时间宽度限制，单位为毫秒
     * @param max_time_width 最大时间宽度限制，单位为毫秒
     * @return int 返回满足时间宽度要求的真值段的数量
     */
    int CcBoolTimeWindowsStatisticsNew::get_true_count(long min_time_width, long max_time_width)
    {
        // 初始化真值计数器
        int true_count = 0;
    
        // 如果缓存数据量不足，直接返回计数器初始值
        if (cache_data_.size() < 3)
        {
            return true_count;
        }
    
        // 初始化虚假时间总和、真值计数器以及起始和结束阀门
        long false_time_sum = 0;
       
        auto true_start_valve = cache_data_.back();
        auto true_end_valve = cache_data_.front();
        auto tmp_valve = cache_data_.front();
        start_data_.first =0L;
        start_data_.second = false;
        int test_data_count = 1;
        bool count_turn_warn = false;

        // 遍历缓存数据，从后向前寻找满足条件的真值段
        for (auto it = cache_data_.rbegin(); it != cache_data_.rend(); ++it)
        {
            // 当前数据为真时，更新真值段的起始和结束阀门，并重置假时间总和
            if (it->second == true)
            {
                if (true_start_valve.second == false)
                {
                    true_start_valve = *it;
                }
                true_end_valve = *it;  
                false_time_sum = 0;
            }
            else
            {
                // 累加虚假时间，当超过阈值时，计算并验证真值段的时间宽度
                false_time_sum +=  tmp_valve.first - it->first;
                if (false_time_sum > 500 || true_start_valve.second)
                {
                    long time_width = true_start_valve.first - true_end_valve.first;
                    // 保存或更新调试信息中真值段的时间宽度
                    if (time_width>300 )
                    {
                        dbg_info_["time_width_"+std::to_string(test_data_count++)] = time_width;
                       
                        
                        
                    }
                    // 如果时间宽度符合要求，增加真值计数，并准备下一段真值段的起始
                    if (time_width> min_time_width && (time_width < max_time_width || max_time_width==0))
                    {
                        true_count++;
                        start_data_ = true_end_valve;
                        if (!count_turn_warn && (test_data_count-1) == true_count  )
                        {
                            count_turn_warn = true;
                        }
                        
                    }
                    // 重置真值段的起始和结束阀门
                    true_end_valve.first = 0;
                    true_end_valve.second = false;
                    true_start_valve.first = 0;
                    true_start_valve.second = false;
                }
            }
            // 更新临时阀门，用于下一次迭代计算时间差
            tmp_valve = *it;    
        }
        if (true_count == 0 && true_start_valve.second)  //后面所有值都是真
        {
            long time_width = true_start_valve.first - true_end_valve.first;
            if (time_width>300 && dbg_info_["time_width_"+std::to_string(true_count+1)] < time_width)
            {
                dbg_info_["time_width_"+std::to_string(test_data_count++)] = time_width;
            }

            if (time_width> min_time_width && (time_width < max_time_width || max_time_width==0))
            {
                true_count++;
                start_data_ = true_end_valve;
            }
        }

        if (count_turn_warn == false)
        {
            true_count = 0;
        }
        
        
        // 返回满足条件的真值段计数
        return true_count;
    }

    long CcBoolTimeWindowsStatisticsNew::get_start_time()
    {
        if (start_data_.second )
        {
            return start_data_.first;
        }
        return 0;
    }


    void CcBoolTimeWindowsStatisticsNew::clear()
    {
        cache_data_.clear();
    }
}