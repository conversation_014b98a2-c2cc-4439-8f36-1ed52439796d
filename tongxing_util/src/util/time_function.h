/*
 * time_profile.h
 *
 *  Created on: May 5, 2017
 *      Author: chengsq
 */

#ifndef TIME_FUNCTION_H_
#define TIME_FUNCTION_H_

#include <string>
#include <iostream>
#include <fstream>
namespace tongxing
{
   #define TIME_FUNCTION_ON 1
class TimeFunction {


public:
  TimeFunction(std::string name,std::string file_name="time_function.cpp");
  ~TimeFunction();


private:
  unsigned long long m_start_time, m_end_time;
  std::string m_name;
  std::string m_file_name;
  // static std::ofstream fileStream;
  int m_on_off_file_flag;

};

} /* namespace tongxing */

#endif /* SRC_UTIL_TIME_PROFILE_H_ */
