#include "cc_tensor.h"

namespace tongxing
{


    CcTensorIndex::CcTensorIndex() {}
    CcTensorIndex::CcTensorIndex(const std::vector<int> &dim,
                                 const std::vector<int> &pad,
                                 bool flag_cp_pad,
                                 bool flag_fortran_order)
    {
        init(dim, pad, flag_cp_pad, flag_fortran_order);
    }
    void CcTensorIndex::init(const std::vector<int> &dim,
                             const std::vector<int> &pad,
                             bool flag_cp_pad,
                             bool flag_fortran_order)
    {
        dim_ = dim;
        if (pad.size() > 0)
        {
            cc_assert(dim.size() == pad.size());
            if (flag_cp_pad)
            {
                pad_ = pad;
            }
            else
            {
                for (int i = 0; i < dim.size(); i++)
                {
                    if ((dim[i] % pad[i]) > 0)
                    {
                        pad_.push_back(pad[i] - (dim[i] % pad[i]));
                    }
                    else
                    {
                        pad_.push_back(0);
                    }
                }
            }
        }
        else
        {
            for (int i = 0; i < dim.size(); i++)
            {
                pad_.push_back(0);
            }
        }
        std::vector<int> rdim = dim;
        std::reverse(std::begin(rdim), std::end(rdim));
        int product = 1;
        index_product_.clear();
        index_product_.push_back(product);
        for (uint32_t i = 0; i < rdim.size() - 1; i++)
        {
            product *= (rdim[i] + pad_[i]);
            index_product_.push_back(product);
            // std::cout<<product<<std::endl;
        }
        if (flag_fortran_order)
        {
            std::reverse(std::begin(index_product_), std::end(index_product_));
        }
    }

    int CcTensorIndex::get_index(const std::vector<int> &dim)
    {
        cc_assert(dim_.size() == dim.size());
        int dim_size = dim_.size();
        int index = 0;
        for (int i = 0; i < dim_size; i++)
        {
            index += index_product_[i] * dim[i];
        }
        return index;
    }
} // namespace tongxing