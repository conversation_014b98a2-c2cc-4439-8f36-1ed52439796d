#include <android/log.h>
#define  LOG    "adas_jni_log" // 这个是自定义的LOG的标识
#define  LOGV(...)  __android_log_print( ANDROID_LOG_VERBOSE,LOG,__VA_ARGS__) // 定义LOGD类型
#define  LOGD(...)  __android_log_print(ANDROID_LOG_DEBUG,LOG,__VA_ARGS__) // 定义LOGD类型
#define  LOGI(...)  __android_log_print(ANDROID_LOG_INFO,LOG,__VA_ARGS__) // 定义LOGI类型
#define  LOGW(...)  __android_log_print(ANDROID_LOG_WARN,LOG,__VA_ARGS__) // 定义LOGW类型
#define  LOGE(...)  __android_log_print(ANDROID_LOG_ERROR,LOG,__VA_ARGS__) // 定义LOGE类型
#define  LOGF(...)  __android_log_print(ANDROID_LOG_FATAL,LOG,__VA_ARGS__) // 定义LOGF类型

#define  LOGLINEE(...)  __android_log_print(ANDROID_LOG_ERROR,"adas_jni_log_lane",__VA_ARGS__) // 定义LOGE类型
#define  RADARTAG "RADAR_JNI"
#define RADARLOGE(fmt, args...) __android_log_print(ANDROID_LOG_ERROR, RADARTAG, fmt, ##args)

#define RLOGE(TAG,fmt, args...) __android_log_print(ANDROID_LOG_ERROR, TAG, fmt, ##args)