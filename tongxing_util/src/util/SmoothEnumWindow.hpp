#ifndef _SMOOTH_ENUM_WINDOW_HPP_
#define _SMOOTH_ENUM_WINDOW_HPP_
#include <deque>
#include <unordered_map>
namespace tongxing {


template <typename T>
class SmoothEnumWindow {
  public:
    T Smooth();
    SmoothEnumWindow(){

    };
    void Init(const uint32_t& size) { window_size_ = size; }

    void clear() {
        window_.clear();
        type_count_.clear();
    }

    void update(const T& data) {
        window_.emplace_back(data);
        if (window_.size() > window_size_) {
            window_.pop_front();
        }

        type_count_.clear();
        for (int i = 0; i < window_.size(); i++) {
            type_count_[window_[i]]++;
        }
    }

    bool IsQueueFull() { return window_.size() >= window_size_ ? true : false; }

  private:
    int window_size_;
    std::deque<T> window_;
    std::unordered_map<T, int> type_count_;
};

template <typename T>
T SmoothEnumWindow<T>::Smooth() {
    T res;

    int max_num = 0;
    for (const auto& entry : type_count_) {
        T candidateId = entry.first;
        int count = entry.second;

        if (count > max_num) {
            max_num = count;
            res = candidateId;
        }
    }

    return res;
}
}

#endif