#include "cc_npy_loader.h"
#include <stdint.h>
#include <algorithm>
#include <complex>
#include <cstdlib>
#include <cstring>
#include <initializer_list>
#include <iomanip>
#include <iostream>
#include <regex>
#include <stdexcept>
#include <type_traits>
#include <vector>
// #include "NumCpp.hpp"

namespace tongxing {

static NumArray::DataType map_type(const std::string& type) {
    if (type == "f4") {
        return NumArray::DataType::FLOAT32;
    } else if (type == "f8") {
        return NumArray::DataType::FLOAT64;
    } else if (type == "i1") {
        return NumArray::DataType::INT8;
    } else if (type == "i2") {
        return NumArray::DataType::INT16;
    } else if (type == "i4") {
        return NumArray::DataType::INT32;
    } else if (type == "i8") {
        return NumArray::DataType::INT64;
    } else if (type == "u1") {
        return NumArray::DataType::UINT8;
    } else if (type == "u2") {
        return NumArray::DataType::UINT16;
    } else if (type == "u4") {
        return NumArray::DataType::UINT32;
    } else if (type == "u8") {
        return NumArray::DataType::UINT64;
    } else if (type == "b1") {
        return NumArray::DataType::BOOL;
    } else {
        cc_assert(false);
    }
}

int parse_npy_data(std::shared_ptr<BlobData>& data_ptr, NumArray& out) {
    unsigned char* buffer = data_ptr->pu8VirAddr;
    //std::string magic_string(buffer,6);
    out.major_version = *reinterpret_cast<uint8_t*>(buffer + 6);
    out.minor_version = *reinterpret_cast<uint8_t*>(buffer + 7);
    uint16_t header_len = *reinterpret_cast<uint16_t*>(buffer + 8);
    std::string header(reinterpret_cast<char*>(buffer + 9), header_len);

    size_t loc1, loc2;

    //fortran order
    loc1 = header.find("fortran_order") + 16;
    out.flag_fortran_order = (header.substr(loc1, 4) == "True" ? true : false);

    //shape
    loc1 = header.find("(");
    loc2 = header.find(")");

    std::regex num_regex("[0-9][0-9]*");
    std::smatch sm;
    out.shape.clear();

    std::string str_shape = header.substr(loc1 + 1, loc2 - loc1 - 1);
    while (std::regex_search(str_shape, sm, num_regex)) {
        out.shape.push_back(std::atoi(sm[0].str().c_str()));
        str_shape = sm.suffix().str();
    }

    //endian, word size, data type
    //byte order code | stands for not applicable.
    //not sure when this applies except for byte array
    loc1 = header.find("descr") + 9;
    out.flag_little_endian = (header[loc1] == '<' || header[loc1] == '|' ? true : false);

    out.type = map_type(header.substr(loc1 + 1, 2));

    out.data = buffer + 10 + header_len;

    out.data_blob_ptr = data_ptr;

    std::string str_ws = header.substr(loc1 + 2);
    loc2 = str_ws.find("'");
    out.word_size = atoi(str_ws.substr(0, loc2).c_str());
    size_t s = out.word_size;
    for (auto i : out.shape) {
        s *= i;
    }
    s += 9 + header_len;
    cc_assert(data_ptr->u32Size >= s);
    return 0;
}

// void transform_pixel_v2(NumArray& in,
//                         const std::shared_ptr<tongxing::CcTensor<float>>& tensor,
//                         bool inverse,
//                         NumArray& out) {
//     CcTensorTool<float> tool;
//     cv::Mat cv_pt;
//     if (inverse) {
//         std::vector<float> trans_vec = tool.getSlices(tensor, 0, tensor->shape()[0], 2, 3);
//         std::cout << "trans_vec size: " << trans_vec.size() << std::endl;

//         std::vector<float> trans_vec2 = tool.getSlices(tensor, 0, tensor->shape()[0], 0, 2);
//         std::cout << "trans_vec2  size: " << trans_vec2.size() << std::endl;

//         auto np_trans =
//             nc::NdArray<float>(range_to_initializer_list(trans_vec.begin(), trans_vec.end()));
//         auto np_trans2 =
//             nc::NdArray<float>(range_to_initializer_list(trans_vec2.begin(), trans_vec2.end()));

//         np_trans.reshape(1, 2);
//         np_trans2.reshape(2, 2);

//         // std::cout << np_trans2 << std::endl;

//         // auto np_trans2T = np_trans2.transpose();  //转置
//         // std::cout << np_trans2T << std::endl;

//         // auto np_trans2Inv = nc::linalg::inv(np_trans2T);  //逆矩阵
//         // std::cout << np_trans2Inv << std::endl;

//         // std::vector<float> pt_vec =
//         //     tool.getSlices(in.getTensor<float>(), 0, in.getTensor<float>()->shape()[0], 0,
//         //                    in.getTensor<float>()->shape()[1]);
//         // std::cout << "pt_vec size: " << pt_vec.size() << std::endl;
//         // auto np_pt = nc::NdArray<float>(range_to_initializer_list(pt_vec.begin(), pt_vec.end()));
//         // std::cout << "11111: " << pt_vec.size() << std::endl;
//         // np_pt.reshape(98, 2);
//         // std::cout << np_pt.shape() << std::endl;
//         // std::cout << np_trans.shape() << std::endl;

//         // std::cout << np_trans << std::endl;
//         // std::cout << np_trans(0, 0) << " , " << np_trans(0, 1) << std::endl;
//         // std::cout << np_trans[0] << std::endl;
//         // np_pt = np_pt - np_trans;
//         // std::cout << c << std::endl;

//         cv::Mat trans_cv = convertVector2Mat(trans_vec, 1, np_trans.numRows());
//         std::cout << trans_cv.size() << trans_cv.rows << trans_cv.cols << std::endl;

//         cv::Mat trans_cv2 = convertVector2Mat(trans_vec2, 1, np_trans2.numRows());
//         std::cout << trans_cv2.size() << trans_cv2.rows << trans_cv2.cols << std::endl;

//         trans_cv2 = trans_cv2.t();    //转置
//         trans_cv2 = trans_cv2.inv();  //逆矩阵

//         // trans_tensor.printShape();
//         // cv::Mat cv_trans =
//         //     cv::Mat(trans_tensor.shape()[1], trans_tensor.shape()[2], CV_32FC1, trans_tensor.get());

//         if (in.flag_fortran_order) {
//             cv_pt = cv::Mat(in.shape[0], in.shape[1], CV_32FC1, in.data);
//         } else {
//             cv_pt = cv::Mat(in.shape[1], in.shape[0], CV_32FC1, in.data);
//         }

//         // auto dim = tensor->shape();
//         // // dim.erase(dim.end());
//         // std::shared_ptr<CcTensor<float>> inv_tensor(
//         //     new CcTensor<float>(dim, &(tensor->at(dim)), tensor->data_blob_ptr));

//         // cv::Mat cv_inv =
//         //     cv::Mat(inv_tensor->shape()[0], inv_tensor->shape()[1], CV_32FC1, inv_tensor.get());
//         // cv_inv = cv_inv.t();    //转置
//         // cv_inv = cv_inv.inv();  //逆矩阵
//         // std::cout << cv_pt << std::endl;
//         // std::cout << trans_cv << std::endl;

//         //cv_pt -= trans_cv;  98*2矩阵
//         for (int i = 0; i < cv_pt.rows; i++) {
//             cv_pt.at<float>(i, 0) = cv_pt.at<float>(i, 0) - trans_cv.at<float>(0, 0);
//             cv_pt.at<float>(i, 1) = cv_pt.at<float>(i, 1) - trans_cv.at<float>(0, 1);
//         }

//         cv_pt *= trans_cv2;

//         out.type = NumArray::DataType::FLOAT32;
//         out.word_size = sizeof(float);
//         out.data = cv_pt.data;

//         out.shape.push_back(cv_pt.rows);
//         out.shape.push_back(cv_pt.cols);
//         out.shape.push_back(cv_pt.channels());
//         // out.getTensor<float>()->printShape();

//         std::shared_ptr<tongxing::BlobData> blob_ptr = std::make_shared<BlobData>();
//         blob_ptr->init((uint32_t)(cv_pt.channels() * cv_pt.cols * cv_pt.rows * sizeof(float)));
//         memcpy(blob_ptr->pu8VirAddr, cv_pt.data,
//                cv_pt.channels() * cv_pt.cols * cv_pt.rows * sizeof(float));
//         out.data_blob_ptr = blob_ptr;
//     } else {
//         if (in.flag_fortran_order) {
//             cv_pt = cv::Mat(in.shape[0], in.shape[1], CV_32FC1, in.data);
//         } else {
//             cv_pt = cv::Mat(in.shape[1], in.shape[0], CV_32FC1, in.data);
//         }

//         tongxing::CcTensor<float> trans_tensor = (*tensor)[2];  //取出三维中的所有tensor

//         auto dim = tensor->shape();
//         dim.erase(dim.end());
//         std::shared_ptr<CcTensor<float>> inv_tensor(
//             new CcTensor<float>(dim, &(tensor->at(dim)), tensor->data_blob_ptr));

//         cv::Mat cv_inv =
//             cv::Mat(inv_tensor->shape()[0], inv_tensor->shape()[1], CV_32FC1, inv_tensor.get());
//         cv_inv = cv_inv.t();  //转置

//         cv_pt *= cv_inv;

//         cv::Mat cv_trans =
//             cv::Mat(trans_tensor.shape()[1], trans_tensor.shape()[0], CV_32FC1, trans_tensor.get());

//         cv_pt += cv_trans;

//         out.type = NumArray::DataType::FLOAT32;
//         out.word_size = sizeof(float);
//         out.data = cv_pt.data;

//         out.shape.push_back(cv_pt.rows);
//         out.shape.push_back(cv_pt.cols);
//         out.shape.push_back(cv_pt.channels());

//         std::shared_ptr<tongxing::BlobData> blob_ptr = std::make_shared<BlobData>();
//         blob_ptr->init((uint32_t)(cv_pt.channels() * cv_pt.cols * cv_pt.rows * sizeof(float)));
//         memcpy(blob_ptr->pu8VirAddr, cv_pt.data,
//                cv_pt.channels() * cv_pt.cols * cv_pt.rows * sizeof(float));
//         out.data_blob_ptr = blob_ptr;
//     }
//     return;
// }

// void crop_alignment_image(
//     NumArray Meanface, NumArray landmarks, NumArray frame, cv::Size out_size, NumArray& out) {
//     cv::Mat cv_pt1;
//     cv::Mat cv_pt2;

//     if (landmarks.flag_fortran_order) {
//         cv_pt1 = cv::Mat(landmarks.shape[0], landmarks.shape[1], CV_32FC1, landmarks.data);
//     } else {
//         cv_pt1 = cv::Mat(landmarks.shape[1], landmarks.shape[0], CV_32FC1, landmarks.data);
//     }

//     if (Meanface.flag_fortran_order) {
//         cv_pt2 = cv::Mat(Meanface.shape[0], Meanface.shape[1], CV_32FC1, Meanface.data);
//     } else {
//         cv_pt2 = cv::Mat(Meanface.shape[1], Meanface.shape[0], CV_32FC1, Meanface.data);
//     }

//     //求均值
//     //构建压缩行Mat
//     cv::Mat m(1, cv_pt1.cols, CV_32FC1);
//     cv::Mat m2(1, cv_pt2.cols, CV_32FC1);
//     // std::cout << m.size() << std::endl;

//     float value1, value2;
//     float sum1, sum2;
//     for (int i = 0; i < cv_pt1.rows; i++) {
//         sum1 += cv_pt1.at<float>(i, 0);
//         sum2 += cv_pt1.at<float>(i, 1);
//     }
//     value1 = sum1 / cv_pt1.rows;
//     value2 = sum2 / cv_pt1.rows;

//     m.at<float>(0, 0) = value1;
//     m.at<float>(0, 1) = value2;
//     // std::cout << m << std::endl;

//     sum1 = 0.0f;
//     sum2 = 0.0f;
//     for (int i = 0; i < cv_pt2.rows; i++) {
//         sum1 += cv_pt2.at<float>(i, 0);
//         sum2 += cv_pt2.at<float>(i, 1);
//     }
//     value1 = sum1 / cv_pt2.rows;
//     value2 = sum2 / cv_pt2.rows;

//     m2.at<float>(0, 0) = value1;
//     m2.at<float>(0, 1) = value2;
//     // std::cout << m2 << std::endl;

//     // cv_pt1 -= m;
//     // cv_pt2 -= m2;

//     for (int i = 0; i < cv_pt1.rows; i++) {
//         cv_pt1.at<float>(i, 0) = cv_pt1.at<float>(i, 0) - m.at<float>(0, 0);
//         cv_pt1.at<float>(i, 1) = cv_pt1.at<float>(i, 1) - m.at<float>(0, 1);
//     }

//     for (int i = 0; i < cv_pt2.rows; i++) {
//         cv_pt2.at<float>(i, 0) = cv_pt2.at<float>(i, 0) - m2.at<float>(0, 0);
//         cv_pt2.at<float>(i, 1) = cv_pt2.at<float>(i, 1) - m2.at<float>(0, 1);
//     }

//     cv::Mat mean;
//     cv::Mat stddev;
//     cv::meanStdDev(cv_pt1, mean, stddev);

//     cv::Mat mean2;
//     cv::Mat stddev2;
//     cv::meanStdDev(cv_pt2, mean2, stddev2);

//     cv_pt1 /= stddev;
//     cv_pt2 /= stddev2;

//     //SVD 奇异值分解（Singular Value Decomposition）是线性代数中一种重要的矩阵分解，奇异值分解则是特征分解在任意矩阵上的推广

//     cv::Mat cv_svd = cv_pt1.t() * cv_pt2;
//     cv_svd.convertTo(cv_svd, CV_64FC1);
//     cv::Mat U, W, V;
//     cv::SVD::compute(cv_svd, W, U, V);

//     cv::Mat R = (U * V).t();
//     // std::cout << "R : " << R.size() << std::endl;
//     // std::cout << "stddev2 : " << stddev2.size() << std::endl;
//     // std::cout << "stddev1 : " << stddev.size() << std::endl;

//     cv::Mat cv_divided(stddev2 / stddev);
//     // std::cout << "cv_divided : " << cv_divided << std::endl;
//     // std::cout << "m : " << m << std::endl;

//     // auto cv_hstack_first = (stddev2 / stddev) * R;

//     cv::Mat cv_hstack_first(R.rows, R.cols, CV_32FC1);
//     for (int i = 0; i < cv_hstack_first.rows; i++) {
//         cv_hstack_first.at<float>(i, 0) = R.at<float>(i, 0) * cv_divided.at<float>(0, 0);
//         cv_hstack_first.at<float>(i, 1) = R.at<float>(i, 1) * cv_divided.at<float>(0, 0);
//     }

//     cv::Mat cv_hstack_second = m2.t() - cv_hstack_first * m.t();
//     // std::cout << "cv_hstack_first :" << cv_hstack_first.size() << std::endl;
//     // std::cout << "cv_hstack_second :" << cv_hstack_second.size() << std::endl;

//     std::vector<float> v1 = convertMat2Vector<float>(cv_hstack_first);
//     std::vector<float> v2 = convertMat2Vector<float>(cv_hstack_second);

//     auto v1_np = nc::NdArray<float>(range_to_initializer_list(v1.begin(), v1.end()));
//     auto v2_np = nc::NdArray<float>(range_to_initializer_list(v2.begin(), v2.end()));
//     v1_np.reshape(cv_hstack_first.rows, cv_hstack_first.cols);
//     v2_np.reshape(cv_hstack_second.rows, cv_hstack_second.cols);

//     auto array1 = nc::hstack({v1_np, v2_np});
//     nc::NdArray<float> a = {{0.0, 0.0, 1.0}};

//     // std::cout << array1.numCols() << std::endl;
//     // std::cout << a.numCols() << std::endl;
//     auto array2 = nc::vstack({array1, a});

//     cv::Mat dst;
//     cv::Mat src;
//     if (frame.flag_fortran_order) {
//         src = cv::Mat(frame.shape[0], frame.shape[1], CV_32FC1, frame.data);
//     } else {
//         src = cv::Mat(frame.shape[1], frame.shape[0], CV_32FC1, frame.data);
//     }

//     std::vector<float> vec;
//     auto nc_arr = array2(nc::Slice(0, 2), array2.rSlice());
//     vec.assign(nc_arr.begin(), nc_arr.end());

//     cv::warpAffine(src, dst, convertVector2Mat(vec, 1, nc_arr.numRows()),
//                    out_size);  //仿射变化

//     out.type = NumArray::DataType::FLOAT32;
//     out.word_size = sizeof(float);
//     out.data = dst.data;

//     out.shape.push_back(dst.rows);
//     out.shape.push_back(dst.cols);
//     out.shape.push_back(dst.channels());

//     std::shared_ptr<tongxing::BlobData> blob_ptr = std::make_shared<BlobData>();
//     blob_ptr->init((uint32_t)(dst.channels() * dst.cols * dst.rows * sizeof(float)));
//     memcpy(blob_ptr->pu8VirAddr, dst.data, dst.channels() * dst.cols * dst.rows * sizeof(float));
//     out.data_blob_ptr = blob_ptr;

//     return;
// }

}  // namespace tongxing