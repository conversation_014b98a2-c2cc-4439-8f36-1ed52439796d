#ifndef _CC_BLOB_DATA_H_
#define _CC_BLOB_DATA_H_
#define CC_MAX_BLOB_NAME_LEN 50
#include <stdint.h>
#include <functional>
#include <string>
namespace tongxing{
    
    class BlobData{
        public:
            uint32_t u32NameLen;
            char     vcBlobName[CC_MAX_BLOB_NAME_LEN];
            uint8_t* pu8VirAddr;
            void* u64PsyAddr;
            uint32_t u32Size;
            uint8_t* pu8Reserve;
        public:
            BlobData(std::function<int (uint32_t,BlobData*)> create_memory=std::function<int (uint32_t,BlobData*)>(),std::function<void(void*)> release_memory=std::function<void(void*)>());
            int init(uint32_t size,const std::string& blob_name="",uint8_t* data_vir_addr=NULL,uint64_t data_psy_addr=0, void* data_handle=NULL);
            int loadFromFile(const std::string& fileName);
            int deinit();
            ~BlobData();
            void* operator new(size_t size);
            void operator delete(void* ptr);
        private:
            bool flag_release;
            std::function<int (uint32_t,BlobData*)> create_memory_function_;
            std::function<void(void*)> release_memory_function_;
            
    };
}


#endif
