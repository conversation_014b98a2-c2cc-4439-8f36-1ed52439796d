#ifndef __CC_MESSAGE_QUEUE_H__
#define __CC_MESSAGE_QUEUE_H__
#include <queue>
#include <mutex>
#include <condition_variable>
namespace tongxing
{
    template <class MessageType>
    class MessageQueue
    {
    private:
        std::mutex mutex_;
        std::condition_variable push_condition_variable_;
        std::condition_variable pop_condition_variable_;
        std::queue<MessageType> queue_;
        size_t max_size_;
        int status_;

    public:
        MessageQueue(size_t max_size)
        {
            max_size_ = max_size;
            status_ = 0;
        }
        int push(const MessageType &message, bool flag_wait = true)
        {
            std::unique_lock<std::mutex> lock(mutex_);
            if (status_ != 0)
            {
                return status_;
            }
            if (flag_wait)
            {
                while ((queue_.size() >= max_size_))
                {
                    // pop_condition_variable_.wait(lock);
                    queue_.pop();
                    if (status_ != 0)
                    {
                        return status_;
                    }
                };
                queue_.push(message);
                push_condition_variable_.notify_one();
            }else{
                if((queue_.size() < max_size_)){
                    queue_.push(message);
                    push_condition_variable_.notify_one();
                }
                else{
                    return -1;
                }
            }
            return 0;
        }
        int pop(MessageType &message)
        {
            std::unique_lock<std::mutex> lock(mutex_);
            if (status_ != 0)
            {
                return status_;
            }
            while (queue_.size() <= 0)
            {
                push_condition_variable_.wait(lock);
                if (status_ != 0)
                {
                    return status_;
                }
            };
            message = queue_.front();
            queue_.pop();
            pop_condition_variable_.notify_one();
            return 0;
        }
        void set_status(int status)
        {
            mutex_.lock();
            status_ = status;
            pop_condition_variable_.notify_all();
            push_condition_variable_.notify_all();
            mutex_.unlock();
        }
        size_t size()
        {
            std::unique_lock<std::mutex> m(mutex_);
            return queue_.size();
        }
    };

}

#endif
