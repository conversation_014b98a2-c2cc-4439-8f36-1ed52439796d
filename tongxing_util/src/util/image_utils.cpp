#include "image_utils.h"

namespace tongxing {
bool unsigned_char_to_cv_mat(
    unsigned char* src_image, uint32_t width, uint32_t height, int channel, cv::Mat& dst_image) {
    bool status = false;
    cc_assert(src_image);
    cc_assert(width);
    cc_assert(height);

    if (channel == 3) {  //RGB BGR
        unsigned char* pData = (unsigned char*)dst_image.data;
        for (int i = 0; i < height * width; i++) {
            memcpy(&pData[i * 3], &src_image[i * 3], sizeof(unsigned char));
            memcpy(&pData[i * 3 + 1], &src_image[i * 3 + 1], sizeof(unsigned char));
            memcpy(&pData[i * 3 + 2], &src_image[i * 3 + 2], sizeof(unsigned char));
        }
        status = true;
    } else if (channel == 1) {  //GRAY
        unsigned char* pData = (unsigned char*)dst_image.data;
        for (int i = 0; i < height * width; i++) {
            memcpy(&pData[i], &src_image[i], sizeof(unsigned char));
        }
        status = true;
    } else {
        std::cout << "The number of channels is not supported! " << __FILE__ << __LINE__
                  << std::endl;
    }

    return status;
}

bool cv_mat_to_unsigned_char(
    cv::Mat& src_image, uint32_t width, uint32_t height, int channel, unsigned char* dst_image) {
    bool status = false;
    unsigned char* tmp_image = (unsigned char*)src_image.data;

    if (channel == 3) {  //RGB BGR
        for (int i = 0; i < height * width; i++) {
            memcpy(&dst_image[i * 3], &tmp_image[i * 3], sizeof(unsigned char));
            memcpy(&dst_image[i * 3 + 1], &tmp_image[i * 3 + 1], sizeof(unsigned char));
            memcpy(&dst_image[i * 3 + 2], &tmp_image[i * 3 + 2], sizeof(unsigned char));
        }
        status = true;
    } else if (channel == 1) {  //GRAY
        for (int i = 0; i < height * width; i++) {
            memcpy(&dst_image[i], &tmp_image[i], sizeof(unsigned char));
        }
        status = true;
    } else {
        std::cout << "The number of channels is not supported! " << __FILE__ << __LINE__
                  << std::endl;
    }
    return status;
}

void crop_img(cv::Mat& img, cv::Mat& crop_img, std::vector<int>& area) {
    int crop_x1 = std::max(0, area[0]);
    int crop_y1 = std::max(0, area[1]);
    int crop_x2 = std::min(img.cols - 1, area[0] + area[2] - 1);
    int crop_y2 = std::min(img.rows - 1, area[1] + area[3] - 1);

    crop_img = img(cv::Range(crop_y1, crop_y2 + 1), cv::Range(crop_x1, crop_x2 + 1));
}

void uchar2float_gray(const cv::Mat tyuchar, cv::Mat& tyfloat) {
    for (int i = 0; i < tyuchar.rows; i++) {
        const uchar* ty1 = tyuchar.ptr<uchar>(i);
        float* ty2 = tyfloat.ptr<float>(i);
        for (int j = 0; j < tyuchar.cols; j++) {
            ty2[j] = ty1[j];
        }
    }
}
void uchar2float_rgb(const cv::Mat tyuchar, cv::Mat& tyfloat) {
    for (int i = 0; i < tyuchar.rows; i++) {
        const uchar* ty1 = tyuchar.ptr<uchar>(i);
        float* ty2 = tyfloat.ptr<float>(i);
        for (int j = 0; j < tyuchar.cols; j++) {
            //ty2[j] = ty1[j];
            const uchar* dataWarpCol1 = ty1 + j * tyuchar.channels();
            float* dataWarpCol2 = ty2 + j * tyfloat.channels();
            dataWarpCol2[0] = dataWarpCol1[0];
            dataWarpCol2[1] = dataWarpCol1[1];
            dataWarpCol2[2] = dataWarpCol1[2];
        }
    }
}

void float2uchar_gray(const cv::Mat tyfloat, cv::Mat& tyuchar) {
    for (int i = 0; i < tyfloat.rows; i++) {
        const float* ty1 = tyfloat.ptr<float>(i);
        uchar* ty2 = tyuchar.ptr<uchar>(i);
        for (int j = 0; j < tyfloat.cols; j++) {
            if (ty1[j] < 0) {
                ty2[j] = 0;
            } else if (ty1[j] > 255) {
                ty2[j] = 255;
            } else {
                ty2[j] = ty1[j];
            }
        }
    }
}

void float2uchar_rgb(const cv::Mat tyfloat, cv::Mat& tyuchar) {
    for (int i = 0; i < tyfloat.rows; i++) {
        const float* ty1 = tyfloat.ptr<float>(i);
        uchar* ty2 = tyuchar.ptr<uchar>(i);
        for (int j = 0; j < tyfloat.cols; j++) {
            uchar* dataWarpCol2 = ty2 + j * tyuchar.channels();
            const float* dataWarpCol1 = ty1 + j * tyfloat.channels();

            if (dataWarpCol1[0] < 0)
                dataWarpCol2[0] = 0;
            else if (dataWarpCol1[0] > 255)
                dataWarpCol2[0] = 255;
            else
                dataWarpCol2[0] = dataWarpCol1[0];

            if (dataWarpCol1[1] < 0)
                dataWarpCol2[1] = 0;
            else if (dataWarpCol1[1] > 255)
                dataWarpCol2[1] = 255;
            else
                dataWarpCol2[1] = dataWarpCol1[1];

            if (dataWarpCol1[2] < 0)
                dataWarpCol2[2] = 0;
            else if (dataWarpCol1[2] > 255)
                dataWarpCol2[2] = 255;
            else
                dataWarpCol2[2] = dataWarpCol1[2];
        }
    }
}

cv::Rect resize_keep_aspectratio(const cv::Mat& image_src,
                                 cv::Mat& image_dst,
                                 const cv::Size dst_size) {
    int src_h = image_src.rows;
    int src_w = image_src.cols;

    int dst_w = dst_size.width;
    int dst_h = dst_size.height;

    //判断应该按哪个边做等比缩放
    float h = dst_w * (float(src_h) / src_w);  //按照ｗ做等比缩放
    float w = dst_h * (float(src_w) / src_h);  //按照h做等比缩放

    if (h <= dst_h) {
        cv::resize(image_src, image_dst, cv::Size(dst_w, (int)h));
    } else {
        cv::resize(image_src, image_dst, cv::Size((int)w, dst_h));
    }

    cv::Rect pad_rect;

    // int top = int((dst_h - image_src.rows) / 2);
    int top = 0;
    int down = int((dst_h - image_src.rows + 1) / 2 + (dst_h - image_src.rows) / 2);
    int left = int((dst_w - image_src.cols) / 2);
    int right = int((dst_w - image_src.cols + 1) / 2);

    pad_rect.y = top;
    pad_rect.x = left;
    pad_rect.width = image_dst.cols;
    pad_rect.height = image_dst.rows;
    // std::cout << pad_rect.x << "," << pad_rect.y << "," << pad_rect.width << "," << pad_rect.height
    //           << std::endl;

    // cv::copyMakeBorder(image_dst, image_dst, top, down, left, right, cv::BORDER_CONSTANT,
    //                    cv::Scalar(0, 0, 0));
    return pad_rect;
}
}  // namespace tongxing