#include "line_simplification.h"
void LineSimplification(const std::vector<cv::Point> &input,
                        std::vector<cv::Point> &output) {
  output.clear();
  std::vector<cv::Point> tmpLanes = input;
  std::vector<cv::Point> simpleLane;
  std::sort(tmpLanes.begin(), tmpLanes.end(),
            [](cv::Point &a, cv::Point &b) { return (a.y > b.y); });
  unsigned int count_point = 0;
  unsigned int current_y = 0;
  unsigned int sum_x = 0;
  for (int i = 0; i < tmpLanes.size(); i++) {

    if (current_y == tmpLanes[i].y) {

      count_point++;
      sum_x += tmpLanes[i].x;
    } else {

      if (count_point != 0) {
        simpleLane.push_back(cv::Point(sum_x / count_point, current_y));
      }
      count_point = 1;
      sum_x = tmpLanes[i].x;
      current_y = tmpLanes[i].y;
    }
  }
  if (count_point != 0) {
    simpleLane.push_back(cv::Point(sum_x / count_point, current_y));
  }
  if (simpleLane.size() > 1) {
    cv::Point last_point = simpleLane[0];
    output.push_back(last_point);
    for (int j = 1; j < simpleLane.size(); j++) {
      if ((powf(simpleLane[j].y - last_point.y, 2) +
           powf(simpleLane[j].x - last_point.x, 2)) > 25) {
        last_point = simpleLane[j];
        output.push_back(last_point);
      }else if(j==(simpleLane.size()-1)){
          last_point = simpleLane[j];
          output.push_back(last_point);
      }
    }
  }
}