#include <stdio.h>
#include <dlfcn.h>
#include "tx_media_server_interface.h"
#if ANDROID
#include "android_log.h"
#endif
static void* handel=NULL;
int (* tx_media_server_get_status)()=NULL;
int (* tx_media_server_push_image_and_message)(const TXMediaServerImageInfo*,char*,int,int)=NULL;
void __attribute__((constructor)) load_tx_media_server_so(){
    handel=dlopen("./libtx_media_server.so",RTLD_LAZY);
    if(handel){
        tx_media_server_get_status=dlsym(handel,"TXMediaServerGetConnectStatus");
        tx_media_server_push_image_and_message=dlsym(handel,"TXMediaServerPushImageAndMessage");
        printf("load libtx_media_server.so success tx_media_server_get_status=0x%x tx_media_server_push_image_and_message=0x%x\n",tx_media_server_get_status,tx_media_server_push_image_and_message);
        #if ANDROID
        DLOGE("load libtx_media_server.so success tx_media_server_get_status=0x%x tx_media_server_push_image_and_message=0x%x\n",tx_media_server_get_status,tx_media_server_push_image_and_message);
        #endif
    }
    else{
        printf("%s\n", dlerror());
        #if ANDROID
        DLOGE("%s\n", dlerror());
        #endif //ANDROID
    }
}

void __attribute__((destructor)) unload_tx_media_server_so(){
    if(handel){
        if(dlclose(handel)!=0){
            printf("%s\n", dlerror());
            #if ANDROID
            DLOGE("%s\n", dlerror());
            #endif //ANDROID
        }
        else{

            printf("unload libtx_media_server.so success\n");
            #if ANDROID
            DLOGE("unload libtx_media_server.so success \n");
            #endif //ANDROID
        }
    }

}
