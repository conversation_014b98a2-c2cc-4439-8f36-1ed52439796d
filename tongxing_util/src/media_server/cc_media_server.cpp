#include "cc_media_server.h"
#include <unistd.h>
#include "CalmCarLog.h"
#include <fstream>
#include <iostream>
#include "cc_resource_register.h"
#include <string.h>
#include <vector>
#include <string>
#include <opencv2/opencv.hpp>
#include "tongxing_dms.pb.h"
#include "cc_timestamp.h"
#include <chrono>
#include <stdlib.h>
namespace tongxing
{
    inline std::vector<std::string> vStringSplit(const std::string &s,
                                                 const std::string &delim = "/")
    {
        std::vector<std::string> elems;
        size_t pos = 0;
        size_t len = s.length();
        size_t delim_len = delim.length();
        if (delim_len == 0)
            return elems;
        while (pos < len)
        {
            int find_pos = s.find(delim, pos);
            if (find_pos < 0)
            {
                elems.push_back(s.substr(pos, len - pos));
                break;
            }
            elems.push_back(s.substr(pos, find_pos - pos));
            pos = find_pos + delim_len;
        }
        return elems;
    }
    void on_mk_http_request(const mk_parser parser,
                            const mk_http_response_invoker invoker, int *consumed,
                            const mk_sock_info sender)
    {
        *consumed = 0;
        const char *url = mk_parser_get_url(parser);
        const char *method = mk_parser_get_method(parser);

        if (strcmp(url, "/") == 0)
        {
            url = "/index.html";
        }

        if (false)
        {
        }
        else
        {
            std::string filename = "www" + std::string(url);
            CcResourcData file_data;
            if (CcResourcDataRegister::instance().find_function(filename, file_data))
            {
                std::vector<std::string> filename_split = vStringSplit(url, ".");
                std::string file_exname;
                if (filename_split.size() >= 2)
                {
                    file_exname = filename_split[filename_split.size() - 1];
                }
                const char *content_type = "text/plain";
                if (file_exname == "html")
                {
                    content_type = "text/html";
                }
                else if (file_exname == "js")
                {
                    content_type = "application/x-javascript";
                }
                else if (file_exname == "json")
                {
                    content_type = "application/json";
                }
                else if (file_exname == "jpg")
                {
                    content_type = "image/jpg";
                }
                else if (file_exname == "png")
                {
                    content_type = "image/png";
                }
                else if (file_exname == "gif")
                {
                    content_type = "image/gif";
                }
                else if (file_exname == "css")
                {
                    content_type = "text/css";
                }
                const char *response_header[] = {"Content-Type", content_type, NULL};
                mk_http_body body = mk_http_body_from_string((const char *)file_data.second, file_data.first);
                mk_http_response_invoker_do(invoker, 200, response_header, body);
                mk_http_body_release(body);
                *consumed = 1;
            }
            else
            {
                std::string requires_body;
                if (strcmp(method, "POST") == 0 || strcmp(method, "post") == 0)
                {
                    size_t length = 0;
                    requires_body = std::string(mk_parser_get_content(parser, &length), length);
                }
                Json::Value response_body;
                Json::Value prarm;
                std::vector<std::string> params_str =
                    vStringSplit(API_CALL mk_parser_get_url_params(parser), "&");
                for (int i = 0; i < params_str.size(); i++)
                {
                    std::vector<std::string> kv_str = vStringSplit(params_str[i], "=");
                    if (kv_str.size() >= 2)
                    {
                        prarm[kv_str[0]] = kv_str[1];
                    }
                }
                if (CcMediaServer::instance().onHttpResful(url, prarm, method, requires_body, response_body))
                {
                    *consumed = 1;
                    std::string response_body_str = Json::FastWriter().write(response_body);
                    const char *response_header[] = {"Content-Type", "application/json", NULL};
                    mk_http_body body = mk_http_body_from_string(response_body_str.c_str(), 0);
                    mk_http_response_invoker_do(invoker, 200, response_header, body);
                    mk_http_body_release(body);
                }
            }
        }
    }

    void on_mk_tcp_session_create(uint16_t server_port, mk_tcp_session session)
    {
        CcMediaSession *media_session = new CcMediaSession(session);
        media_session->onConnect();
        CcMediaServer::instance().add_session(media_session);
    }
    void on_mk_tcp_session_data(uint16_t server_port, mk_tcp_session session, mk_buffer buffer)
    {
        CcMediaSession *media_session = (CcMediaSession *)mk_tcp_session_get_user_data(session);
        mk_buffer buffer_ref = mk_buffer_ref(buffer);
        std::shared_ptr<BlobData> blob_data(new BlobData(std::function<int(uint32_t, BlobData *)>(), (void (*)(void *))mk_buffer_unref));
        blob_data->init(mk_buffer_get_size(buffer_ref), "", (uint8_t *)mk_buffer_get_data(buffer_ref), NULL, buffer_ref);
        media_session->onData(blob_data);
    }
    void on_mk_tcp_session_manager(uint16_t server_port, mk_tcp_session session)
    {
        CcMediaSession *media_session = (CcMediaSession *)mk_tcp_session_get_user_data(session);
        media_session->onManager();
    }
    void on_mk_tcp_session_disconnect(uint16_t server_port, mk_tcp_session session, int code, const char *msg)
    {
        CcMediaSession *media_session = (CcMediaSession *)mk_tcp_session_get_user_data(session);
        CcMediaServer::instance().del_session(media_session);
        media_session->onDisconnect(code, msg);
    }

    inline bool exists_file(const std::string &file_name)
    {
        std::ifstream file(file_name.c_str());
        return file.good();
    }
    CcMediaServer::CcMediaServer() : message_queue_(10)
    {
        run_status_ = true;
        run_thread_ = std::thread(&CcMediaServer::run, this);
    }
    CcMediaServer::~CcMediaServer()
    {
        run_status_ = false;
    }
    void CcMediaServer::init(const char *cache_path)
    {
        Json::Reader json_reader;
        Json::Value root;
        if (cache_path)
        {
            cache_path_ = std::string(cache_path);
            cache_param_filename_ = cache_path_ + "/" + "media_server_param.json";
            std::ifstream infile(cache_param_filename_, std::ios::binary);
            config_param_["use_fake_speed"] = use_fake_speed_status_;
             config_param_["flag_save_data"] = flag_save_data_;
              config_param_["fake_speed"] = fake_speed;
            if (!infile.is_open())
            {
                TX_LOG_INFO("MediaServer", "Open %s file failed!", cache_param_filename_.c_str());
                // return -1;
            }

            else if (!json_reader.parse(infile, config_param_))
            {
                TX_LOG_INFO("MediaServer", "Parse %s file failed!", cache_param_filename_.c_str());
                // return -1;
            }
            else
            {
                if (config_param_.isMember("use_fake_speed"))
                {
                    use_fake_speed_status_ = config_param_["use_fake_speed"].asBool();
                }
                else
                {
                    config_param_["use_fake_speed"] = use_fake_speed_status_;
                }
                if (config_param_.isMember("flag_save_data"))
                {
                    flag_save_data_ = config_param_["flag_save_data"].asBool();
                }
                else
                {
                    config_param_["flag_save_data"] = flag_save_data_;
                }
                if (config_param_.isMember("fake_speed"))
                {
                    fake_speed = config_param_["fake_speed"].asInt();
                }
                else
                {
                    config_param_["fake_speed"] = fake_speed;
                }
                // std::cout<<enable_fatigue_<<" "<<enable_distraction_<<std::endl;
            }
        }

        // std::cout<<"register_resful start"<<std::endl;
        register_resful("/api/SetMediaServerStatus", std::bind(&CcMediaServer::set_media_server_status, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
        // std::cout<<"register_resful end"<<std::endl;
    }
    int CcMediaServer::mk_server_init()
    {
        mk_events events_http = {0};
        events_http.on_mk_http_request = on_mk_http_request;
        mk_events_listen(&events_http);
        mk_http_server_start(1180, 0);

        mk_tcp_session_events events_ws = {0};
        events_ws.on_mk_tcp_session_create = on_mk_tcp_session_create;
        events_ws.on_mk_tcp_session_data = on_mk_tcp_session_data;
        events_ws.on_mk_tcp_session_disconnect = on_mk_tcp_session_disconnect;
        events_ws.on_mk_tcp_session_manager = on_mk_tcp_session_manager;
        mk_tcp_server_events_listen(&events_ws);
        mk_tcp_server_start(1181, mk_type_ws);
        return 0;
    }
    int CcMediaServer::mk_server_deinit()
    {
        mk_stop_all_server();
        return 0;
    }
    int CcMediaServer::process()
    {
        if (message_queue_.size() > 0)
        {
            std::shared_ptr<Message> message;
            message_queue_.pop(message);

            tongxing::proto::DMS_Frame dms_frame;

            std::shared_ptr<std::vector<uint8_t>> data(new std::vector<uint8_t>);

            dms_frame.set_image_type("JPG");
            dms_frame.set_image_width(message->image.width);
            dms_frame.set_image_height(message->image.height);
            // std::cout<<message->image.width<<" "<<message->image.height<<std::endl;
            std::vector<uint8_t> image_data;
            image_data.reserve(1024*1024);
            cv::imencode(".jpg",
                         cv::Mat(message->image.height, message->image.width,
                                 CV_8UC1,
                                 (void *)message->image.data),
                         image_data);
            
            dms_frame.set_image_data(image_data.data(), image_data.size());
            // dms_frame.set_image_data(message->image.data,message->image.dataLen);


            // std::string message_str = Json::FastWriter().write(message->message);
            dms_frame.set_message_data(message->message);
            data->resize((size_t)dms_frame.ByteSize());
            dms_frame.SerializeToArray(data->data(), dms_frame.ByteSize());
            broadcast_message(data);
        }
        return 0;
    }

    void CcMediaServer::run()
    {
        bool last_test_mode = false;
        do
        {
            bool now_test_mode = true;
            if (last_test_mode == false && now_test_mode == true)
            {
                int ret = mk_server_init();
                if (ret != 0)
                {
                    TX_LOG_FATAL("CcMediaServer", "mk_server_init fatal %d", ret);
                    run_status_ = false;
                }
                // use_fake_speed_status_ = true;
                init_status_ = true;
            }
            else if (last_test_mode == true && now_test_mode == false)
            {
                int ret = mk_server_deinit();
                if (ret != 0)
                {
                    TX_LOG_FATAL("CcMediaServer", "mk_server_deinit fatal %d", ret);
                    run_status_ = false;
                }
                // use_fake_speed_status_ = false;
                init_status_ = false;
            }
            last_test_mode = now_test_mode;
            if (now_test_mode == true)
            {
                int ret = process();
                if (ret != 0)
                {
                    TX_LOG_FATAL("CcMediaServer", "mk_server_process fatal %d", ret);
                    run_status_ = false;
                }
            }
            else
            {
                usleep(1000000);
            }
        } while (run_status_);
    }
    bool CcMediaServer::onHttpResful(const std::string &url, const Json::Value &params, const std::string &method, const std::string &requires_body, Json::Value &response_body)
    {
        std::unique_lock<std::mutex> m(resful_map_lock_);
        //  std::cout << url << std::endl;
        auto item = resful_map.find(url);
        if (item == resful_map.end())
        {
            return false;
        }
        item->second(params, method, requires_body, response_body);
        return true;
    }
    void CcMediaServer::register_resful(const std::string &url, ResfulApi api)
    {
        std::unique_lock<std::mutex> m(resful_map_lock_);
        resful_map[url] = api;
    }
    CcMediaServer &CcMediaServer::instance()
    {
        static CcMediaServer media_server_;
        return media_server_;
    }
    bool CcMediaServer::get_use_fake_speed_status()
    {
        std::unique_lock<std::mutex> m(status_lock_);
        return use_fake_speed_status_ && init_status_;
    }
    int CcMediaServer::get_speed()
    {
        return fake_speed;
    }
    bool CcMediaServer::get_push_data_status()
    {
        std::unique_lock<std::mutex> m(status_lock_);
        return init_status_ && ( (session_map.size() > 0));
    }
    int CcMediaServer::push_image_and_message(const ImageInfo *image,const std::string &message,int scale)
    {
        std::shared_ptr<Message> message_(new Message);
        message_->message = message;
        if(scale!=1){
            size_t data_size = image->height/scale * image->width/scale;
            // data_size=data_size>image->dataLen?data_size:image->dataLen;
            std::shared_ptr<BlobData> bolb_data(new BlobData);
            bolb_data->init(data_size);
            // memcpy(bolb_data->pu8VirAddr, image->data, data_size);
            cv::Mat mat(cv::Size(image->width,image->height),CV_8UC1,image->data);
            cv::Mat resize_mat(cv::Size(image->width/scale,image->height/scale),CV_8UC1,bolb_data->pu8VirAddr);
            cv::resize(mat,resize_mat,cv::Size(image->width/scale,image->height/scale));
            message_->image_bolb = bolb_data;
            message_->image.data = (char *)bolb_data->pu8VirAddr;
            message_->image.dataLen = image->dataLen/(scale*scale);
            message_->image.dataType = image->dataType;
            message_->image.height = image->height/scale;
            message_->image.stride = image->stride/scale;
            message_->image.width = image->width/scale;
            message_queue_.push(message_, false);
        }
        else{
            size_t data_size = image->height * image->width;
            // data_size=data_size>image->dataLen?data_size:image->dataLen;
            std::shared_ptr<BlobData> bolb_data(new BlobData);
            bolb_data->init(data_size);
            memcpy(bolb_data->pu8VirAddr,image->data,data_size);
            message_->image_bolb = bolb_data;
            message_->image.data = (char *)bolb_data->pu8VirAddr;
            message_->image.dataLen = image->dataLen;
            message_->image.dataType = image->dataType;
            message_->image.height = image->height;
            message_->image.stride = image->stride;
            message_->image.width = image->width;
            message_queue_.push(message_, false);
        }
        return 0;
    }

    void CcMediaServer::add_session(void *session)
    {
        std::unique_lock<std::mutex> m(session_map_lock_);
        session_map[session] = (CcMediaSession *)session;
    }
    void CcMediaServer::del_session(void *session)
    {
        std::unique_lock<std::mutex> m(session_map_lock_);
        auto item = session_map.find(session);
        if (item != session_map.end())
        {
            session_map.erase(item);
        }
    }
    void CcMediaServer::broadcast_message(const std::shared_ptr<std::vector<uint8_t>> &data)
    {
        std::unique_lock<std::mutex> m(session_map_lock_);
        for (auto &session : session_map)
        {
            session.second->send(data);
        }
    }
    void CcMediaServer::broadcast_message(const std::shared_ptr<BlobData> &data)
    {
        std::unique_lock<std::mutex> m(session_map_lock_);
        for (auto &session : session_map)
        {
            session.second->send(data);
        }
    }
    void CcMediaServer::set_media_server_status(const Json::Value &params, const std::string &method, const std::string &requires_body, Json::Value &response_body)
    {
        std::string value_str;
        if (params.isMember("use_fake_speed"))
        {
            value_str = params["use_fake_speed"].asString();
            if (value_str == "true" || value_str == "True")
            {
                use_fake_speed_status_ = true;
            }
            else
            {
                use_fake_speed_status_ = false;
            }
            config_param_["use_fake_speed"] = use_fake_speed_status_;
        }

        if (params.isMember("fake_speed"))
        {
            value_str = params["fake_speed"].asString();
            std::cout << value_str << std::endl;
            fake_speed = std::atoi(value_str.c_str());
            config_param_["fake_speed"] = fake_speed;
        }
        if (params.isMember("flag_save_data"))
        {
            value_str = params["flag_save_data"].asString();
            if (value_str == "true" || value_str == "True")
            {
                flag_save_data_ = true;
            }
            else
            {
                flag_save_data_ = false;
            }
            config_param_["flag_save_data"] = flag_save_data_;
        }
        if (!cache_param_filename_.empty())
        {
            std::ofstream outfile(cache_param_filename_, std::ios::binary);
            if (!outfile.is_open())
            {
                TX_LOG_INFO("MediaServer", "Open %s file failed!", cache_param_filename_.c_str());
                // return -1;
            }
            else
            {
                std::string save_data = config_param_.toStyledString();
                outfile.write(save_data.c_str(), save_data.size());
                outfile.close();
            }
        }
        response_body["status"] = 0;
        response_body["message"] = "success";
        double ts = get_timestamp();
        response_body["ts"] = ts;
        response_body["playload"] = config_param_;
    }

    void delete_media_session(void *ptr)
    {
        // std::cout<<"delete_media_session"<<std::endl;
        delete (CcMediaSession *)ptr;
    }
    void delete_shared_ptr_BlobData(void *user_data, void *data)
    {
        delete (std::shared_ptr<BlobData> *)user_data;
    }
    void delete_shared_ptr_vector_uchar(void *user_data, void *data)
    {
        // std::cout<<"delete_shared_ptr_vector_uchar"<<std::endl;
        delete (std::shared_ptr<std::vector<uint8_t>> *)user_data;
    }
    CcMediaSession::CcMediaSession(mk_tcp_session session)
    {
        session_ = session;
        mk_tcp_session_set_user_data2(session, (void *)this, delete_media_session);
    }
    CcMediaSession::~CcMediaSession()
    {
        // std::cout << "~CcMediaSession" << std::endl;
    }
    void CcMediaSession::disconnect()
    {
    }
    void CcMediaSession::send(const std::shared_ptr<BlobData> &data)
    {
        std::shared_ptr<BlobData> *data_ptr = new std::shared_ptr<BlobData>(data);
        mk_buffer buffer = mk_buffer_from_char((const char *)data->pu8VirAddr, data->u32Size, delete_shared_ptr_BlobData, (void *)data_ptr);
        mk_tcp_session_send_buffer_safe(session_, buffer);
        mk_buffer_unref(buffer);
    }
    void CcMediaSession::send(const std::shared_ptr<std::vector<uint8_t>> &data)
    {
        std::shared_ptr<std::vector<uint8_t>> *data_ptr = new std::shared_ptr<std::vector<uint8_t>>(data);
        mk_buffer buffer = mk_buffer_from_char((const char *)data->data(), data->size(), delete_shared_ptr_vector_uchar, (void *)data_ptr);
        mk_tcp_session_send_buffer_safe(session_, buffer);
        mk_buffer_unref(buffer);
    }
    void CcMediaSession::onConnect()
    {
        // std::shared_ptr<std::vector<uint8_t>> data(new std::vector<uint8_t>);
        // data->push_back('s');
        // data->push_back('t');
        // data->push_back('a');
        // data->push_back('r');
        // data->push_back('t');
        // send(data);
        // std::cout<<"onConnect"<<std::endl;
    }
    void CcMediaSession::onDisconnect(int code, const char *msg)
    {
        // std::cout<<"onDisconnect code="<<code<<" msg="<<msg<<std::endl;
    }
    void CcMediaSession::onData(std::shared_ptr<BlobData> data)
    {
        // std::cout<<"onData data="<<std::string((char*)data->pu8VirAddr,data->u32Size)<<std::endl;
    }
    void CcMediaSession::onManager()
    {
    }

}