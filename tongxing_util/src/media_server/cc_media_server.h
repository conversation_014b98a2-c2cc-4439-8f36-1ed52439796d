#ifndef __CC_MEDIA_SERVER_H__
#define __CC_MEDIA_SERVER_H__
#include "mk_mediakit.h"
#include <string>
#include <mutex>
#include <thread>
#include "cc_message_queue.h"
#include <memory>
#include "cc_blob_data.h"
#include "json.h"
#include <map>
#include "tx_media_server_interface.h"
namespace tongxing
{
    class CcMediaSession;
    class CcMediaServer
    {
    public:
        typedef  TXMediaServerImageFormat ImageFormat;
        typedef TXMediaServerImageInfo ImageInfo;
        typedef struct Message_
        {
            ImageInfo image;
            std::shared_ptr<BlobData> image_bolb; //
            std::string message;
        } Message;
        typedef std::function<void(const Json::Value &, const std::string &, const std::string &, Json::Value &)> ResfulApi;

    private:
        std::mutex queue_lock_;
        MessageQueue<std::shared_ptr<Message>> message_queue_;
        std::mutex status_lock_;
        std::thread run_thread_;
        std::mutex session_map_lock_;
        std::map<void *, CcMediaSession *> session_map;
        std::mutex resful_map_lock_;
        std::map<std::string, ResfulApi> resful_map;
        bool init_status_ = false;
        bool run_status_ = false;
    private:
        bool use_fake_speed_status_ = false;
        bool flag_save_data_ =false;
        int fake_speed = 71;

        Json::Value config_param_;
        std::string cache_path_;
        std::string cache_param_filename_;
        long long now_save_image_path_ts=0;
        std::string now_save_image_path;
    public:
        CcMediaServer();
        ~CcMediaServer();
        void init(const char* cache_path);
        static CcMediaServer &instance();
        bool get_use_fake_speed_status();
        int get_speed();
        bool get_push_data_status();
        int push_image_and_message(const ImageInfo *image,const std::string &message,int scale=2);

        void add_session(void *session);
        void del_session(void *session);
        void broadcast_message(const std::shared_ptr<std::vector<uint8_t>> &data);
        void broadcast_message(const std::shared_ptr<BlobData> &data);
        bool onHttpResful(const std::string &url, const Json::Value &params, const std::string &method, const std::string &requires_body, Json::Value &response_body);
        void register_resful(const std::string &url, ResfulApi api);

    private:
        int mk_server_init();
        int mk_server_deinit();
        int process();
        void run();
    //resful api
    public:
        void set_media_server_status(const Json::Value &params, const std::string &method, const std::string &requires_body, Json::Value &response_body);
    };
    class CcMediaSession
    {
    public:
    private:
        mk_tcp_session session_;

    public:
        CcMediaSession(mk_tcp_session session);
        ~CcMediaSession();
        void disconnect();
        void send(const std::shared_ptr<BlobData> &data);
        void send(const std::shared_ptr<std::vector<uint8_t>> &data);

    public: // callback
        void onConnect();
        void onDisconnect(int code, const char *msg);
        void onData(std::shared_ptr<BlobData> data);
        void onManager();
    };
}

#endif
