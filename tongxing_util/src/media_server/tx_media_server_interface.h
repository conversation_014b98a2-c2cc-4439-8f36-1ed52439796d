#ifndef __TX_MEDIA_SERVER_INTERFACE_H__
#define __TX_MEDIA_SERVER_INTERFACE_H__

#ifdef __cplusplus
extern "C" {
#endif
        typedef enum TXMediaServerImageFormat_
        {
            TX_NV21 = 0,
            TX_YV12 = 1,
            TX_YUV422 = 4,
            TX_NV12 = 5
        } TXMediaServerImageFormat;
        typedef struct TxMediaServerImageInfo_
        {
            TXMediaServerImageFormat dataType; // 图像类型
            int height;           // 图像高度
            int width;            // 图像宽度
            int stride;           // 图像每行像素的跨度
                                  // 以RBG,BGR为例stride=width*3
                                  // 以NV12,YV12为例stride=width
            int dataLen;          // 像素数据的数据字节数
            char *data;           // 像素数据的缓冲区指针
        } TXMediaServerImageInfo;
        extern int (* tx_media_server_get_status)();
        extern int (* tx_media_server_push_image_and_message)(const TXMediaServerImageInfo*,char*,int,int);
#ifdef __cplusplus
}
#endif
#endif