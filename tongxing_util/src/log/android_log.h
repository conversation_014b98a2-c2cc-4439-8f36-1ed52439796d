#ifndef ANDROID_LOG_H__
#define ANDROID_LOG_H__

#if ANDROID
#include <android/log.h>




#define DLOGI(...) \
  ((void)__android_log_print(ANDROID_LOG_INFO, "dmslog", __VA_ARGS__))
#define DLOGW(...) \
  ((void)__android_log_print(ANDROID_LOG_WARN, "dmslog", __VA_ARGS__))
#define DLOGE(...) \
  ((void)__android_log_print(ANDROID_LOG_ERROR, "dmslog", __VA_ARGS__))

#define FLOGI(...) \
  ((void)__android_log_print(ANDROID_LOG_INFO, "face_info", __VA_ARGS__))
#define FLOGW(...) \
  ((void)__android_log_print(ANDROID_LOG_WARN, "face_warning", __VA_ARGS__))
#define FLOGE(...) \
  ((void)__android_log_print(ANDROID_LOG_ERROR, "face_error", __VA_ARGS__))

#endif // 0

#endif  // ANDROID_LOG_H__
