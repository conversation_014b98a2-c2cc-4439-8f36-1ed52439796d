/*
 * @Author: wei.yu <EMAIL>
 * @Date: 2022-12-20 11:45:37
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-04-11 16:18:48
 * @FilePath: /log_system/src/CalmCarLog.h
 * @Description: Calmcar日志类头文件
 */
#ifndef _CALMCARLOG_H_
#define _CALMCARLOG_H_

#include <stdarg.h>
#include <sys/time.h>
#include <time.h>
#include <memory>
#include <string>
#include <mutex>
namespace tongxing {
typedef enum LogType {
    TERMINAL_OUTPUT = 0,  //表示命令行输出
    FILE_OUTPUT = 1,      //表示文件输出
    REDIRECT_OUTPUT = 2,  //表示Log重定向
} LogType;

typedef enum LogLevel {  //日志等级
    DEBUG = 0,
    INFO,
    WARN,
    ERROR,
    FATAL,
} LogLevel;

typedef void (*CalmcarLogCallback)(char* data, int data_size);

class CalmCarLog {
  public:
    static std::shared_ptr<CalmCarLog> instance;
    static std::shared_ptr<CalmCarLog> getInstance();
    void set_calmcar_log_type(LogType type,
                              const char* log_filepath,
                              const char* log_filename,
                              int log_line_num,
                              CalmcarLogCallback log_callback);
    void CALMCAR_LOG(
        LogLevel level, const char* file, int line, const char* label, const char* fmt, ...);
    void set_calmcar_log_level(LogLevel log_level);
    ~CalmCarLog();
  private:
    LogType log_type_ = LogType::TERMINAL_OUTPUT;  //日志输出类型，默认命令行输出
    LogLevel log_level_ = LogLevel::DEBUG;         //日志输出等级，默认级别debug
    std::string log_file_name_ = "tongxing";        //日志输出文件名前缀，默认calmcar
    std::string log_file_path_ = "./";  //日志输出路径，默认当前目录，路径以/结尾
    int log_line_num_ = 1000;           //日志输出单文件最大行数,默认1000行
    CalmcarLogCallback log_callback_;  //日志重定向回调函数
  private:
  FILE* fp_=NULL;
  int now_line_num_=0;
  FILE* Get_FileFp();
  private:
    std::mutex calmcar_log_mutex;
};

//日志打印请用以下宏定义
#define TX_LOG_DEBUG(tag, ...)                                                                        \
    CalmCarLog::getInstance()->CALMCAR_LOG(DEBUG, __FILE__, __LINE__, tag, __VA_ARGS__)
#define TX_LOG_INFO(tag, ...)                                                                         \
    CalmCarLog::getInstance()->CALMCAR_LOG(INFO, __FILE__, __LINE__, tag, __VA_ARGS__)
#define TX_LOG_WARN(tag, ...)                                                                         \
    CalmCarLog::getInstance()->CALMCAR_LOG(WARN, __FILE__, __LINE__, tag, __VA_ARGS__)
#define TX_LOG_ERROR(tag, ...)                                                                        \
    CalmCarLog::getInstance()->CALMCAR_LOG(ERROR, __FILE__, __LINE__, tag, __VA_ARGS__)
#define TX_LOG_FATAL(tag, ...)                                                                        \
    CalmCarLog::getInstance()->CALMCAR_LOG(FATAL, __FILE__, __LINE__, tag, __VA_ARGS__)

}  // namespace tongxing

#endif