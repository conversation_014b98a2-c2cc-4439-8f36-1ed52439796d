/*
 * @Author: wei.yu <EMAIL>
 * @Date: 2022-12-20 11:45:37
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-04-11 16:18:48
 * @FilePath: /log_system/src/CalmCarLog.h
 * @Description: Calmcar日志类头文件
 */
#ifndef _DEBUG_INFO_H_
#define _DEBUG_INFO_H_

#include <stdarg.h>
#include <sys/time.h>
#include <time.h>
#include <memory>
#include <mutex>
#include <string>
#include "json.h"
namespace tongxing {

typedef enum INFO_OUT {
    JSON_OUTPUT = 0,  //表示命令行输出
    FILE_OUTPUT1 = 1
} InfoOut;

class DebugInfo {
  public:
    static std::shared_ptr<DebugInfo> instance;
    static std::shared_ptr<DebugInfo> getInstance();
    void set_calmcar_log_type(INFO_OUT type,
                              std::string log_filepath,
                              std::string log_filename,
                              int log_line_num);
    // 重载函数声明
    void CALMCAR_Info(std::string parent, std::string label, int value);
    //   void CALMCAR_Info(std::string parent, std::string label, long value);
    void CALMCAR_Info(std::string parent, std::string label, float value);
    void CALMCAR_Info(std::string parent, std::string label, const char* value);
    void CALMCAR_Info(std::string parent, std::string label, const std::string& value);
    const std::map<std::string, std::unique_ptr<Json::Value>>& getInfoMap() const {
        return info_map;
    }

    Json::Value getInfoJson(std::string parent);

    void clear_Info(std::string parent);
    ~DebugInfo();

  private:
    // 模板实现声明
    template <typename T>
    void CALMCAR_Info_Impl(std::string parent, std::string label, T value);

  private:
    InfoOut out_type_ = InfoOut::JSON_OUTPUT;  //日志输出类型，默认命令行输出
    std::string log_file_name_ = "tongxing";   //日志输出文件名前缀，默认calmcar
    std::string log_file_path_ = "./";  //日志输出路径，默认当前目录，路径以/结尾
    int log_line_num_ = 1000;           //日志输出单文件最大行数,默认1000行

  private:
    FILE* fp_ = NULL;
    int now_line_num_ = 0;
    FILE* Get_FileFp();
    std::map<std::string, std::unique_ptr<Json::Value>> info_map;

    // Json::Value info_json;
  private:
    std::mutex calmcar_log_mutex;
};

#define TX_INFO_IN(parent, key, value) DebugInfo::getInstance()->CALMCAR_Info(parent, key, value)

#define TX_GET_INFO(parent) DebugInfo::getInstance()->getInfoJson(parent)

#define TX_GET_ALL_INFO() DebugInfo::getInstance()->getInfoMap()

#define TX_CLEAR_INFO(parent) DebugInfo::getInstance()->clear_Info(parent)

}  // namespace tongxing

#endif