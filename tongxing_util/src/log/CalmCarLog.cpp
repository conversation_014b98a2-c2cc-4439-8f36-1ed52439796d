/*
 * @Author: wei.yu <EMAIL>
 * @Date: 2022-12-20 11:50:15
 * @LastEditors: wei.yu <EMAIL>
 * @LastEditTime: 2022-12-21 17:03:53
 * @FilePath: /log_system/src/CalmCarLog.cpp
 * @Description: Calmcar日志类实现原函数
 */

#include "CalmCarLog.h"
#include <dirent.h>
#include <fstream>
#include <iostream>
#include <regex>
#include <vector>
#include <string>
#include <string.h>
namespace tongxing
{
    std::shared_ptr<CalmCarLog> CalmCarLog::instance = std::make_shared<CalmCarLog>();

    std::shared_ptr<CalmCarLog> CalmCarLog::getInstance()
    {
        if (instance.get() == nullptr)
        {
            instance.reset(new CalmCarLog);
        }
        return instance;
    }

    void CalmCarLog::set_calmcar_log_type(LogType type,
                                          const char *log_filepath,
                                          const char *log_filename,
                                          int log_line_num,
                                          CalmcarLogCallback log_callback)
    {
        std::unique_lock<std::mutex> m(calmcar_log_mutex);
        log_type_ = type;
        if (log_type_ == LogType::FILE_OUTPUT)
        {
            log_file_path_ = log_filepath;
            log_file_name_ = log_filename;
            log_line_num_ = log_line_num;
        }

        if (log_type_ == LogType::REDIRECT_OUTPUT)
        {
            log_callback_ = log_callback;
        }

        if (fp_ != 0)
        {
            fclose(fp_);
            fp_ = NULL;
        }
    }
    CalmCarLog::~CalmCarLog()
    {
        if (fp_ != 0)
        {
            fclose(fp_);
            fp_ = NULL;
        }
    }
    void CalmCarLog::set_calmcar_log_level(LogLevel log_level)
    {
        std::unique_lock<std::mutex> m(calmcar_log_mutex);
        log_level_ = log_level;
    }

    static const char *level_strings[] = {"DEBUG", "INFO", "WARN", "ERROR", "FATAL"};

#ifdef LOG_USE_COLOR
    static const char *level_colors[] = {"\x1b[36m", "\x1b[32m", "\x1b[33m", "\x1b[31m", "\x1b[35m"};
#endif

    static bool log_is_find(std::string file_path, std::string file_prefix, std::string &obj_file)
    {
        DIR *pDir;
        struct dirent *pDirent;
        bool res = false;
        std::vector<std::string> file_vec;

        pDir = opendir(file_path.c_str());
        if (pDir == NULL)
        {
            std::cout << file_path << " is not existed" << std::endl;
            return false;
        }

        while ((pDirent = readdir(pDir)) != NULL)
        {
            std::string s(pDirent->d_name);
            std::string::size_type idx = s.find(file_prefix);
            if (idx != std::string::npos)
            {
                file_vec.push_back(s);
            }
        }

        closedir(pDir);

        if (file_vec.size() == 0)
        {
            // std::cout << "empty" << std::endl;
            res = false;
        }
        else
        {
            res = true;
            std::sort(file_vec.begin(), file_vec.end());
            obj_file = file_vec.back();
        }

        return res;
    }
    // 方式一统计文件行
    static int log_file_line(std::string filePath)
    {
        std::ifstream ReadFile;
        int n = 0;
        std::string tmp;
        ReadFile.open(filePath, std::ios::in); // ios::in 表示以只读的方式读取文件
        if (ReadFile.fail())                   // 文件打开失败:返回0
            return 0;
        else // 文件存在
        {
            while (getline(ReadFile, tmp))
            {
                if (tmp == "")
                    continue; // 不统计空行
                n++;
            }
        }
        ReadFile.close();

        return n;
    }

    static int cover(char *str)
    {
        int index = strchr((const char *)str, ' ') - str;
        str[index] = '\0';
        return atoi(str);
    }
    // 方式二统计文件行
    static int getFileAttr(const char *fileName, char C, char *buff)
    {
        FILE *fstream = NULL;
        sprintf(buff, "wc -%c %s", C, fileName);

        if (fstream = popen(buff, "r"))
        {
            memset(buff, 0x00, sizeof(buff));
            if (fgets(buff, sizeof(buff), fstream))
            {
                pclose(fstream);
                return cover(buff);
            }
        }

        if (fstream)
            pclose(fstream);

        return -1;
    }

    static std::string getCurrentTime()
    {
        struct timeval tv;
        gettimeofday(&tv, NULL);

        static const int MAX_BUFFER_SIZE = 128;
        char timestamp_str[MAX_BUFFER_SIZE];
        time_t sec = static_cast<time_t>(tv.tv_sec);
        int ms = static_cast<int>(tv.tv_usec) / 1000;

        struct tm tm_time;
        localtime_r(&sec, &tm_time);
        static const char *formater = "%02d%02d-%02d-%02d-%02d";
        int wsize = snprintf(timestamp_str, MAX_BUFFER_SIZE, formater,tm_time.tm_mon + 1, tm_time.tm_mday, tm_time.tm_hour, tm_time.tm_min,
                             tm_time.tm_sec);

        timestamp_str[std::min(wsize, MAX_BUFFER_SIZE - 1)] = '\0';
        return std::string(timestamp_str);
    }
    FILE *CalmCarLog::Get_FileFp()
    {
        if (now_line_num_ >= log_line_num_ || fp_ == 0)
        {
            FILE *fp;
            std::string log_file;
            if (fp_ != 0)
            {
                fclose(fp_);
            }
            log_file = log_file_path_ + log_file_name_ + getCurrentTime() + ".log";
            fp = fopen(log_file.c_str(), "a+");
            now_line_num_ = 0;
            fp_ = fp;
        }
        return fp_;
    }
    void CalmCarLog::CALMCAR_LOG(
        LogLevel level, const char *file, int line, const char *label, const char *fmt, ...)
    {
        std::unique_lock<std::mutex> m(calmcar_log_mutex);
        if (level < log_level_)
        {
            return;
        }

        if (log_type_ == LogType::TERMINAL_OUTPUT)
        { // 终端输出打印
            char buf[100];
            FILE *udata = stderr;
            va_list ap;
            struct tm *time_now;
            int milTime;
            // getTime(time, milTime);
            time_t t = time(NULL);
            time_now = localtime(&t);

            struct timeval tv;
            gettimeofday(&tv, NULL);
            milTime = tv.tv_usec / 1000;
            sprintf(buf, "[%s] [%d-%02d-%02d %02d:%02d:%02d.%03d]", label, time_now->tm_year + 1900,
                    time_now->tm_mon + 1, time_now->tm_mday, time_now->tm_hour, time_now->tm_min,
                    time_now->tm_sec, milTime);
            va_start(ap, fmt);

#ifdef LOG_USE_COLOR
            fprintf(udata, "%s [%s%-5s] \x1b[0m \x1b[90m[%s:%d]:\x1b[0m ", buf, level_colors[level],
                    level_strings[level], file, line);
#else
            fprintf(udata, "%s %-5s : %d: ", buf, level_strings[level],  line);
#endif
            vfprintf(udata, fmt, ap);
            fprintf(udata, "\n");
            fflush(udata);

            va_end(ap);
        }
        else if (log_type_ == LogType::FILE_OUTPUT)
        { // 输出文件
            FILE *fp;

            fp = Get_FileFp();
            // std::cout<<fp<<std::endl;
            if (fp)
            {
                char buf[100];
                va_list ap;
                struct tm *time_now;
                int milTime;
                time_t t = time(NULL);
                time_now = localtime(&t);

                struct timeval tv;
                gettimeofday(&tv, NULL);
                milTime = tv.tv_usec / 1000;
                va_start(ap, fmt);
                sprintf(buf, "[%s] [%d-%02d-%02d %02d:%02d:%02d.%03d]", label, time_now->tm_year + 1900,
                        time_now->tm_mon + 1, time_now->tm_mday, time_now->tm_hour, time_now->tm_min,
                        time_now->tm_sec, milTime);

                fprintf(fp, "%s [%-5s] [%s:%d]: ", buf, level_strings[level], file, line);
                vfprintf(fp, fmt, ap);
                fprintf(fp, "\n");
                va_end(ap);

                now_line_num_++;
            }
            else
            {
                char buf[100];
                FILE *udata = stderr;
                va_list ap;
                struct tm *time_now;
                int milTime;
                // getTime(time, milTime);
                time_t t = time(NULL);
                time_now = localtime(&t);

                struct timeval tv;
                gettimeofday(&tv, NULL);
                milTime = tv.tv_usec / 1000;
                sprintf(buf, "[%s] [%d-%02d-%02d %02d:%02d:%02d.%03d]", label, time_now->tm_year + 1900,
                        time_now->tm_mon + 1, time_now->tm_mday, time_now->tm_hour, time_now->tm_min,
                        time_now->tm_sec, milTime);
                va_start(ap, fmt);

#ifdef LOG_USE_COLOR
                fprintf(udata, "%s [%s%-5s] \x1b[0m \x1b[90m[%s:%d]:\x1b[0m ", buf, level_colors[level],
                        level_strings[level], file, line);
#else
                fprintf(udata, "%s %-5s %s:%d: ", buf, level_strings[level], file, line);
#endif
                vfprintf(udata, fmt, ap);
                fprintf(udata, "\n");
                fflush(udata);

                va_end(ap);
            }
        }
        else
        { // 重定向输出
            if (log_callback_)
            {
                char buf[100];
                char result_buf[1024] = {'\0'};

                va_list ap;
                struct tm *time_now;
                int milTime;
                time_t t = time(NULL);
                time_now = localtime(&t);

                struct timeval tv;
                gettimeofday(&tv, NULL);
                milTime = tv.tv_usec / 1000;
                va_start(ap, fmt);
                sprintf(buf, "[%s] [%d-%02d-%02d %02d:%02d:%02d.%03d]", label, time_now->tm_year + 1900,
                        time_now->tm_mon + 1, time_now->tm_mday, time_now->tm_hour, time_now->tm_min,
                        time_now->tm_sec, milTime);

                sprintf(result_buf, "%s [%-5s] [%d]: ", buf, level_strings[level], line);
                int len = strlen(result_buf);
                vsprintf(result_buf + len, fmt, ap);
                va_end(ap);

                // std::cout << "result_buf :" << result_buf << " ,result_buf size: " << strlen(result_buf)
                //           << std::endl;

                log_callback_(result_buf, strlen(result_buf));
            }
        }
    }

} // namespace tongxing