/*
 * @Author: wei.yu <EMAIL>
 * @Date: 2022-12-20 11:50:15
 * @LastEditors: wei.yu <EMAIL>
 * @LastEditTime: 2022-12-21 17:03:53
 * @FilePath: /log_system/src/DebugInfo.cpp
 * @Description: Calmcar日志类实现原函数
 */

 #include "debug_info.h"
 #include <dirent.h>
 #include <string.h>
 #include <fstream>
 #include <iostream>
 #include <regex>
 #include <string>
 #include <vector>
 namespace tongxing {
 std::shared_ptr<DebugInfo> DebugInfo::instance = std::make_shared<DebugInfo>();
 
 std::shared_ptr<DebugInfo> DebugInfo::getInstance() {
     if (instance.get() == nullptr) {
         instance.reset(new DebugInfo);
     }
     return instance;
 }
 
 void DebugInfo::set_calmcar_log_type(INFO_OUT type,
                                      std::string log_filepath,
                                      std::string log_filename,
                                      int log_line_num) {
     std::unique_lock<std::mutex> m(calmcar_log_mutex);
     out_type_ = type;
     if (out_type_ == INFO_OUT::FILE_OUTPUT1) {
         log_file_path_ = log_filepath;
         log_file_name_ = log_filename;
         log_line_num_ = log_line_num;
     }
 
     if (fp_ != 0) {
         fclose(fp_);
         fp_ = NULL;
     }
 }
 DebugInfo::~DebugInfo() {
     if (fp_ != 0) {
         fclose(fp_);
         fp_ = NULL;
     }
 }

 Json::Value  DebugInfo::getInfoJson(std::string parent){
     return *info_map[parent];
 }
 
 // 基础模板声明
 template<typename T>
 void DebugInfo::CALMCAR_Info_Impl(std::string parent, std::string label, T value) {
    auto& jsonPtr = info_map[parent];
    if(!jsonPtr){
        jsonPtr.reset(new Json::Value()); //使用 reset 方法并传入 new 分配的对象
    }
    (*jsonPtr)[label] = value;
 }
 
 // 显式实例化支持的类型
 template void DebugInfo::CALMCAR_Info_Impl<int>(std::string parent, std::string, int);
//  template void DebugInfo::CALMCAR_Info_Impl<long>(std::string parent, std::string, long);
 template void DebugInfo::CALMCAR_Info_Impl<float>(std::string parent, std::string, float);
 template void DebugInfo::CALMCAR_Info_Impl<double>(std::string parent, std::string, double);
 template void DebugInfo::CALMCAR_Info_Impl<const char*>(std::string parent, std::string, const char*);
 template void DebugInfo::CALMCAR_Info_Impl<std::string>(std::string parent, std::string, std::string);


 void DebugInfo::clear_Info(std::string parent) {
    std::unique_lock<std::mutex> m(calmcar_log_mutex);
    if (parent == "all")
    {
        info_map.clear();
    }else
    {
        info_map[parent].reset();
    }
 }
 
 // 对外接口保持名称重载
 void DebugInfo::CALMCAR_Info(std::string parent, std::string label, int value) {
     std::unique_lock<std::mutex> m(calmcar_log_mutex);
     CALMCAR_Info_Impl(parent, label, value);
 }

//  void DebugInfo::CALMCAR_Info(std::string parent, std::string label, long value) {
//     std::unique_lock<std::mutex> m(calmcar_log_mutex);
//     CALMCAR_Info_Impl(parent, label, value);
// }
 
 void DebugInfo::CALMCAR_Info(std::string parent, std::string label, float value) {
     std::unique_lock<std::mutex> m(calmcar_log_mutex);
     CALMCAR_Info_Impl(parent, label, value);
 }
 
 void DebugInfo::CALMCAR_Info(std::string parent, std::string label, const char* value) {
     std::unique_lock<std::mutex> m(calmcar_log_mutex);
     CALMCAR_Info_Impl(parent, label, std::string(value));
 }
 
 void DebugInfo::CALMCAR_Info(std::string parent, std::string label, const std::string& value) {
     std::unique_lock<std::mutex> m(calmcar_log_mutex);
     CALMCAR_Info_Impl(parent, label, value);
 }
 
 }  // namespace tongxing
 