#ifndef __CC_ANDROID_CAMERA2_H__
#define __CC_ANDROID_CAMERA2_H__
#include <camera/NdkCaptureRequest.h>
#include <camera/NdkCameraCaptureSession.h>
#include <camera/NdkCameraDevice.h>
#include <camera/NdkCameraError.h>
#include <camera/NdkCameraManager.h>
#include <media/NdkImageReader.h>
#include <android/native_window_jni.h>
#include <jni.h>
#include <stdio.h>
#include "json.h"
#include "cc_data_source.h"
namespace tongxing {
class CcAndroidCamera2 :public CcDataSource{
    public:
        int init(const Json::Value& config);
        ~CcAndroidCamera2();
        void openSource();
        void closeSource();
        int getData(std::shared_ptr<NumArray>& data);
    private:
         ANativeWindow *theNativeWindow=NULL;
        ACameraDevice *cameraDevice=NULL;
        ACaptureRequest *captureRequest=NULL;
        ACameraOutputTarget *cameraOutputTarget=NULL;
        ACaptureSessionOutput *sessionOutput=NULL;
        ACaptureSessionOutputContainer *captureSessionOutputContainer=NULL;
        ACameraCaptureSession *captureSession=NULL;
        ACameraDevice_StateCallbacks deviceStateCallbacks;
        ACameraCaptureSession_stateCallbacks captureSessionStateCallbacks;
        AImageReader* imageReader=NULL; 
};
}
#endif
