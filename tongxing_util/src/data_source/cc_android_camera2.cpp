#include "cc_android_camera2.h"
#include "cc_resource_register.h"
#include "cc_assert.h"
#include <string.h>
#include "CalmCarLog.h"

namespace tongxing
{
    static void camera_device_on_disconnected(void *context, ACameraDevice *device) {
        printf("Camera(id: %s) is diconnected.\n", ACameraDevice_getId(device));
    }

    static void camera_device_on_error(void *context, ACameraDevice *device, int error) {
        printf("Error(code: %d) on Camera(id: %s).\n", error, ACameraDevice_getId(device));
    }

    static void capture_session_on_ready(void *context, ACameraCaptureSession *session) {
        printf("Session is ready. %p\n", session);
    }

    static void capture_session_on_active(void *context, ACameraCaptureSession *session) {
        printf("Session is activated. %p\n", session);
    }

    static void capture_session_on_closed(void *context, ACameraCaptureSession *session) {
        printf("Session is closed. %p\n", session);
    }
    int CcAndroidCamera2::init(const Json::Value &config)
    {

        return 0;
    }
   
    CcAndroidCamera2::~CcAndroidCamera2()
    {
        closeSource();
    }

    void CcAndroidCamera2::openSource()
    {
        ACameraIdList *cameraIdList = NULL;
        ACameraMetadata *cameraMetadata = NULL;

        const char *selectedCameraId = NULL;
        camera_status_t camera_status = ACAMERA_OK;
        ACameraManager *cameraManager = ACameraManager_create();

        camera_status = ACameraManager_getCameraIdList(cameraManager, &cameraIdList);
        if (camera_status != ACAMERA_OK)
        {
            printf("Failed to get camera id list (reason: %d)\n", camera_status);
            return;
        }

        if (cameraIdList->numCameras < 1)
        {
            printf("No camera device detected.\n");
            return;
        }

        selectedCameraId = cameraIdList->cameraIds[0];

        printf("Trying to open Camera2 (id: %s, num of camera : %d)\n", selectedCameraId,
               cameraIdList->numCameras);

        camera_status = ACameraManager_getCameraCharacteristics(cameraManager, selectedCameraId,
                                                                &cameraMetadata);

        if (camera_status != ACAMERA_OK)
        {
            printf("Failed to get camera meta data of ID:%s\n", selectedCameraId);
        }

        deviceStateCallbacks.onDisconnected = camera_device_on_disconnected;
        deviceStateCallbacks.onError = camera_device_on_error;

        camera_status = ACameraManager_openCamera(cameraManager, selectedCameraId,
                                                  &deviceStateCallbacks, &cameraDevice);

        if (camera_status != ACAMERA_OK)
        {
            printf("Failed to open camera device (id: %s)\n", selectedCameraId);
        }

        camera_status = ACameraDevice_createCaptureRequest(cameraDevice, TEMPLATE_VIDEO_SNAPSHOT,
                                                           &captureRequest);

        if (camera_status != ACAMERA_OK)
        {
            printf("Failed to create preview capture request (id: %s)\n", selectedCameraId);
        }

        ACaptureSessionOutputContainer_create(&captureSessionOutputContainer);

        captureSessionStateCallbacks.onReady  = capture_session_on_ready;
        captureSessionStateCallbacks.onActive = capture_session_on_active;
        captureSessionStateCallbacks.onClosed = capture_session_on_closed;
        media_status_t media_status=AImageReader_new(1920,1080,AIMAGE_FORMAT_YUV_420_888,3,&imageReader);
        if (media_status != AMEDIA_OK)
        {
            printf("Failed to create media (code: %d)\n", media_status);
        }
        media_status=AImageReader_getWindow(imageReader, &theNativeWindow);
        if (media_status != AMEDIA_OK)
        {
            printf("Failed to getWindow (code: %d)\n", media_status);
        }
        camera_status=ACameraOutputTarget_create(theNativeWindow, &cameraOutputTarget);
        if (camera_status != ACAMERA_OK)
        {
            printf("Failed to create output target (id: %s)\n", camera_status);
        }
        ACaptureRequest_addTarget(captureRequest, cameraOutputTarget);
        ACaptureSessionOutput_create(theNativeWindow, &sessionOutput);
        ACaptureSessionOutputContainer_add(captureSessionOutputContainer, sessionOutput);

        ACameraDevice_createCaptureSession(cameraDevice, captureSessionOutputContainer,
                                        &captureSessionStateCallbacks, &captureSession);

        ACameraCaptureSession_setRepeatingRequest(captureSession, NULL, 1, &captureRequest, NULL);

        ACameraMetadata_free(cameraMetadata);
        ACameraManager_deleteCameraIdList(cameraIdList);
        ACameraManager_delete(cameraManager);
    }

    void CcAndroidCamera2::closeSource(void)
    {
        camera_status_t camera_status = ACAMERA_OK;
        if (cameraDevice != NULL)
        {
            camera_status = ACameraDevice_close(cameraDevice);

            if (camera_status != ACAMERA_OK)
            {
                printf("Failed to close CameraDevice.\n");
            }
            cameraDevice = NULL;
        }
        printf("cameraDevice free\n");
        
        if (captureRequest != NULL)
        {
            ACaptureRequest_free(captureRequest);
            captureRequest = NULL;
        }
        printf("captureRequest free\n");
        if (sessionOutput != NULL)
        {
            ACaptureSessionOutput_free(sessionOutput);
            sessionOutput = NULL;
        }
        printf("sessionOutput free\n");
        if (sessionOutput != NULL)
        {
            ACaptureSessionOutput_free(sessionOutput);
            sessionOutput = NULL;
        }
        printf("sessionOutput free\n");

        if (cameraOutputTarget != NULL)
        {
            ACameraOutputTarget_free(cameraOutputTarget);
            cameraOutputTarget = NULL;
        }
        printf("cameraOutputTarget free\n");


        if (captureSessionOutputContainer != NULL)
        {
            ACaptureSessionOutputContainer_free(captureSessionOutputContainer);
            captureSessionOutputContainer = NULL;
        }
        printf("captureSessionOutputContainer free\n");

        // if (theNativeWindow != NULL)
        // {
        //     ANativeWindow_release(theNativeWindow);
        //     theNativeWindow = NULL;
        // }
        // printf("theNativeWindow free\n");

        if (imageReader != NULL)
        {
            AImageReader_delete(imageReader);
            imageReader = NULL;
            theNativeWindow = NULL;
        }
        printf("imageReader free\n");









        if (sessionOutput != NULL)
        {
            ACaptureSessionOutput_free(sessionOutput);
            sessionOutput = NULL;
        }
        printf("sessionOutput free\n");

        if (cameraOutputTarget != NULL)
        {
            ACameraOutputTarget_free(cameraOutputTarget);
            cameraOutputTarget = NULL;
        }
        printf("cameraOutputTarget free\n");


        if (captureSessionOutputContainer != NULL)
        {
            ACaptureSessionOutputContainer_free(captureSessionOutputContainer);
            captureSessionOutputContainer = NULL;
        }
        printf("captureSessionOutputContainer free\n");


        printf("Close Camera\n");
    }

    int CcAndroidCamera2::getData(std::shared_ptr<NumArray>& data)
    {
        AImage* image;
        media_status_t media_status=AImageReader_acquireLatestImage(imageReader,&image);
        if (media_status != AMEDIA_OK)
        {
            printf("Failed to get image (code: %d)\n", media_status);
            return -1;
        }
        else{
            printf("get image success\n");
            AImage_delete(image);
            
        }
        return 0;
    }

}