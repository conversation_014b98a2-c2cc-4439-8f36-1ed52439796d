# byd_ddaw_addw
## byd_ddaw_addw 介绍
byd_ddaw_addw用于比亚迪元UP、海鸥系列addw ddaw算法代码仓库。
主要是用于算法报警逻辑。其中读取模型获取结果数据，进行过滤，计数，时间窗，等等动作进行处理。和硬件无关代码。

#### 依赖
1. thirdparty       第三方库 opencv , curl , 芯片平台库
2. tongxing_util    子仓库 ，核心框架
   


## 编译命令
```
编译命令，如果不清楚，直接执行
./bulid.sh

build.sh 平台类型 芯片架构/项目名称
./build.sh linux x86_64
./build.sh linux oax4600
```
## 宏定义变量说明：

| 名称                  | 说明                                                        |
| --------------------- | ----------------------------------------------------------- |
| INTEGRATION_TEST_MODE | ON：编译时会带传图模块，python端可以接收；默认是OFF         |
| CAR_BUILD_TYPE           | SC3E: 编译SC3E版本；EQ: 编译EQ版本；HA6: 编译HA6版本； 默认是编译的是SC3E版本         |
| MODEL_TEST_MODE       | ON: 编译时使用内部测试模型，用于内部测试精度使用；默认是OFF |
| MEM_ISSUE_DEBUG       | ON: 编译时开启内存问题调试，能更容易排查出内存使用的问题，默认是OFF                     |

### 备忘录
现在EQ_R的头部分心判断已经从相对值改成绝对值，后续车型再发版本也记得同步修改这部分







