{"class_name": "Functional", "config": {"name": "dms", "layers": [{"class_name": "InputLayer", "config": {"dtype": "uint8_t", "sparse": false, "ragged": false, "name": "input_1"}, "name": "input_1", "inbound_nodes": []}, {"class_name": "g2d_preprocessor", "config": {"blob_nume": "image_1", "output_width": 608, "output_height": 608, "color": true, "flag_crop": true, "flag_input_rwm": true, "resize": true}, "name": "faceRoi_rsize", "inbound_nodes": [["input_1", 0, 0, {}], ["input_1", 1, 0, {}]]}, {"class_name": "g2d_preprocessor", "config": {"blob_nume": "image_2_face", "output_width": 256, "output_height": 256, "color": true, "resize": true, "flag_static_mode": true}, "name": "faceDet_pre_normal", "inbound_nodes": [["faceRoi_rsize", 0, 0, {}]]}, {"class_name": "tvm_inference", "config": {"type": "file", "inputname": "image", "filename": "f_d_mod.so"}, "name": "faceDet_infer", "inbound_nodes": [["faceDet_pre_normal", 0, 0, {}]]}, {"class_name": "FaceBboxDecoder", "config": {"image_width": 256, "image_height": 256, "class_num": 1, "scales_num": 1, "featmap_strides": [8], "align": 1, "score_th": 0.5, "iou_th": 0.1, "feat_width": 32, "feat_height": 32, "stride": 8, "has_cube": false}, "name": "faceDet_post", "inbound_nodes": [["faceDet_infer", 0, 0, {}]]}, {"class_name": "xcCoordinateScaling2D", "config": {"width_resize": 256, "height_resize": 256, "width_ram": 608, "height_ram": 608, "keep_ratio": true}, "name": "faceBbox_scaling", "inbound_nodes": [["faceDet_post", 0, 0, {}], ["faceDet_post", 1, 0, {}]]}, {"class_name": "XcSquareRoi", "config": {"width_resize": 128, "height_resize": 128, "width_ram": 608, "height_ram": 608, "roi_type": 1}, "name": "facekeypoint_roi", "inbound_nodes": [["faceBbox_scaling", 0, 0, {}]]}, {"class_name": "g2d_preprocessor", "config": {"blob_nume": "image_3_key", "output_width": 128, "output_height": 128, "color": true, "flag_crop": true, "resize": true}, "name": "faceFeature_pre_normal", "inbound_nodes": [["faceRoi_rsize", 0, 0, {}], ["facekeypoint_roi", 0, 0, {}]]}, {"class_name": "tvm_inference", "config": {"type": "file", "inputname": "input", "filename": "f_k_p_mod.so"}, "name": "faceFeature_infer", "inbound_nodes": [["faceFeature_pre_normal", 0, 0, {}]]}, {"class_name": "HeatmapKeypointsDecoder", "config": {"stride": 8}, "name": "facekeypoint_post", "inbound_nodes": [["faceFeature_infer", 0, 0, {}]]}, {"class_name": "xcCoordinateScaling2D", "config": {"width_resize": 64, "height_resize": 64, "width_ram": 608, "height_ram": 608, "keep_ratio": false}, "name": "faceKeypoints_scaling", "inbound_nodes": [["facekeypoint_post", 0, 0, {}], ["facekeypoint_roi", 0, 0, {}]]}, {"class_name": "XcSquareRoi", "config": {"width_resize": 64, "height_resize": 64, "width_ram": 608, "height_ram": 608, "roi_type": 2}, "name": "faceeye_roi_left", "inbound_nodes": [["faceKeypoints_scaling", 0, 0, {}]]}, {"class_name": "g2d_preprocessor", "config": {"blob_nume": "image_4_eye_l", "output_width": 64, "output_height": 64, "color": true, "flag_crop": true, "resize": true}, "name": "left_eye_pre_normal", "inbound_nodes": [["faceRoi_rsize", 0, 0, {}], ["faceeye_roi_left", 0, 0, {}]]}, {"class_name": "tvm_inference", "config": {"type": "file", "inputname": "input", "eye_inf": "left_eye_infer", "filename": "e_c_mod.so"}, "name": "lefteye_infer", "inbound_nodes": [["left_eye_pre_normal", 0, 0, {}]]}, {"class_name": "XcSquareRoi", "config": {"width_resize": 64, "height_resize": 64, "width_ram": 608, "height_ram": 608, "roi_type": 3}, "name": "faceeye_roi_right", "inbound_nodes": [["faceKeypoints_scaling", 0, 0, {}]]}, {"class_name": "g2d_preprocessor", "config": {"blob_nume": "image_5_eye_r", "output_width": 64, "output_height": 64, "color": true, "flag_crop": true, "resize": true}, "name": "right_eye_pre_normal", "inbound_nodes": [["faceRoi_rsize", 0, 0, {}], ["faceeye_roi_right", 0, 0, {}]]}, {"class_name": "tvm_inference", "config": {"type": "file", "inputname": "input", "eye_inf": "right_eye_infer", "filename": "e_c_mod.so"}, "name": "righteye_infer", "inbound_nodes": [["right_eye_pre_normal", 0, 0, {}]]}], "input_layers": [["input_1", 0, 0]], "output_layers": [["faceBbox_scaling", 0, 0], ["faceKeypoints_scaling", 0, 0], ["lefteye_infer", 0, 0], ["righteye_infer", 0, 0]]}}