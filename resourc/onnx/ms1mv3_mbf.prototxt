layer {
  name: "input_0"
  type: "Input"
  top: "input_0"
  input_param {
    shape {
      dim: 1
      dim: 3
      dim: 112
      dim: 112
    }
  }
}
layer {
  name: "ConvLayer_1"
  type: "Convolution"
  bottom: "input_0"
  top: "Layer_1"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 1
    stride: 2
    dilation: 1
  }
}
layer {
  name: "BNLayer_1"
  type: "BatchNorm"
  bottom: "Layer_1"
  top: "Layer_1"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_1"
  type: "Scale"
  bottom: "Layer_1"
  top: "Layer_1"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_1"
  type: "ReLU"
  bottom: "Layer_1"
  top: "Layer_1"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_2"
  type: "Convolution"
  bottom: "Layer_1"
  top: "Layer_2"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 64
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_2"
  type: "BatchNorm"
  bottom: "Layer_2"
  top: "Layer_2"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_2"
  type: "Scale"
  bottom: "Layer_2"
  top: "Layer_2"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_2"
  type: "ReLU"
  bottom: "Layer_2"
  top: "Layer_2"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_3"
  type: "Convolution"
  bottom: "Layer_2"
  top: "Layer_3"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_3"
  type: "BatchNorm"
  bottom: "Layer_3"
  top: "Layer_3"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_3"
  type: "Scale"
  bottom: "Layer_3"
  top: "Layer_3"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_3"
  type: "ReLU"
  bottom: "Layer_3"
  top: "Layer_3"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_4"
  type: "Convolution"
  bottom: "Layer_3"
  top: "Layer_4"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 128
    stride: 2
    dilation: 1
  }
}
layer {
  name: "BNLayer_4"
  type: "BatchNorm"
  bottom: "Layer_4"
  top: "Layer_4"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_4"
  type: "Scale"
  bottom: "Layer_4"
  top: "Layer_4"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_4"
  type: "ReLU"
  bottom: "Layer_4"
  top: "Layer_4"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_5"
  type: "Convolution"
  bottom: "Layer_4"
  top: "Layer_5"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_5"
  type: "BatchNorm"
  bottom: "Layer_5"
  top: "Layer_5"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_5"
  type: "Scale"
  bottom: "Layer_5"
  top: "Layer_5"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "ConvLayer_6"
  type: "Convolution"
  bottom: "Layer_5"
  top: "Layer_6"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_6"
  type: "BatchNorm"
  bottom: "Layer_6"
  top: "Layer_6"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_6"
  type: "Scale"
  bottom: "Layer_6"
  top: "Layer_6"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_6"
  type: "ReLU"
  bottom: "Layer_6"
  top: "Layer_6"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_7"
  type: "Convolution"
  bottom: "Layer_6"
  top: "Layer_7"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 128
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_7"
  type: "BatchNorm"
  bottom: "Layer_7"
  top: "Layer_7"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_7"
  type: "Scale"
  bottom: "Layer_7"
  top: "Layer_7"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_7"
  type: "ReLU"
  bottom: "Layer_7"
  top: "Layer_7"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_8"
  type: "Convolution"
  bottom: "Layer_7"
  top: "Layer_8"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_8"
  type: "BatchNorm"
  bottom: "Layer_8"
  top: "Layer_8"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_8"
  type: "Scale"
  bottom: "Layer_8"
  top: "Layer_8"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_9"
  type: "Eltwise"
  bottom: "Layer_5"
  bottom: "Layer_8"
  top: "Layer_9"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_10"
  type: "Convolution"
  bottom: "Layer_9"
  top: "Layer_10"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_10"
  type: "BatchNorm"
  bottom: "Layer_10"
  top: "Layer_10"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_10"
  type: "Scale"
  bottom: "Layer_10"
  top: "Layer_10"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_10"
  type: "ReLU"
  bottom: "Layer_10"
  top: "Layer_10"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_11"
  type: "Convolution"
  bottom: "Layer_10"
  top: "Layer_11"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 128
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_11"
  type: "BatchNorm"
  bottom: "Layer_11"
  top: "Layer_11"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_11"
  type: "Scale"
  bottom: "Layer_11"
  top: "Layer_11"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_11"
  type: "ReLU"
  bottom: "Layer_11"
  top: "Layer_11"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_12"
  type: "Convolution"
  bottom: "Layer_11"
  top: "Layer_12"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_12"
  type: "BatchNorm"
  bottom: "Layer_12"
  top: "Layer_12"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_12"
  type: "Scale"
  bottom: "Layer_12"
  top: "Layer_12"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_13"
  type: "Eltwise"
  bottom: "Layer_9"
  bottom: "Layer_12"
  top: "Layer_13"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_14"
  type: "Convolution"
  bottom: "Layer_13"
  top: "Layer_14"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_14"
  type: "BatchNorm"
  bottom: "Layer_14"
  top: "Layer_14"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_14"
  type: "Scale"
  bottom: "Layer_14"
  top: "Layer_14"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_14"
  type: "ReLU"
  bottom: "Layer_14"
  top: "Layer_14"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_15"
  type: "Convolution"
  bottom: "Layer_14"
  top: "Layer_15"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 128
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_15"
  type: "BatchNorm"
  bottom: "Layer_15"
  top: "Layer_15"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_15"
  type: "Scale"
  bottom: "Layer_15"
  top: "Layer_15"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_15"
  type: "ReLU"
  bottom: "Layer_15"
  top: "Layer_15"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_16"
  type: "Convolution"
  bottom: "Layer_15"
  top: "Layer_16"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_16"
  type: "BatchNorm"
  bottom: "Layer_16"
  top: "Layer_16"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_16"
  type: "Scale"
  bottom: "Layer_16"
  top: "Layer_16"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_17"
  type: "Eltwise"
  bottom: "Layer_13"
  bottom: "Layer_16"
  top: "Layer_17"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_18"
  type: "Convolution"
  bottom: "Layer_17"
  top: "Layer_18"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_18"
  type: "BatchNorm"
  bottom: "Layer_18"
  top: "Layer_18"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_18"
  type: "Scale"
  bottom: "Layer_18"
  top: "Layer_18"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_18"
  type: "ReLU"
  bottom: "Layer_18"
  top: "Layer_18"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_19"
  type: "Convolution"
  bottom: "Layer_18"
  top: "Layer_19"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 128
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_19"
  type: "BatchNorm"
  bottom: "Layer_19"
  top: "Layer_19"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_19"
  type: "Scale"
  bottom: "Layer_19"
  top: "Layer_19"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_19"
  type: "ReLU"
  bottom: "Layer_19"
  top: "Layer_19"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_20"
  type: "Convolution"
  bottom: "Layer_19"
  top: "Layer_20"
  convolution_param {
    num_output: 128
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_20"
  type: "BatchNorm"
  bottom: "Layer_20"
  top: "Layer_20"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_20"
  type: "Scale"
  bottom: "Layer_20"
  top: "Layer_20"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_21"
  type: "Eltwise"
  bottom: "Layer_17"
  bottom: "Layer_20"
  top: "Layer_21"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_22"
  type: "Convolution"
  bottom: "Layer_21"
  top: "Layer_22"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_22"
  type: "BatchNorm"
  bottom: "Layer_22"
  top: "Layer_22"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_22"
  type: "Scale"
  bottom: "Layer_22"
  top: "Layer_22"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_22"
  type: "ReLU"
  bottom: "Layer_22"
  top: "Layer_22"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_23"
  type: "Convolution"
  bottom: "Layer_22"
  top: "Layer_23"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 256
    stride: 2
    dilation: 1
  }
}
layer {
  name: "BNLayer_23"
  type: "BatchNorm"
  bottom: "Layer_23"
  top: "Layer_23"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_23"
  type: "Scale"
  bottom: "Layer_23"
  top: "Layer_23"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_23"
  type: "ReLU"
  bottom: "Layer_23"
  top: "Layer_23"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_24"
  type: "Convolution"
  bottom: "Layer_23"
  top: "Layer_24"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_24"
  type: "BatchNorm"
  bottom: "Layer_24"
  top: "Layer_24"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_24"
  type: "Scale"
  bottom: "Layer_24"
  top: "Layer_24"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "ConvLayer_25"
  type: "Convolution"
  bottom: "Layer_24"
  top: "Layer_25"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_25"
  type: "BatchNorm"
  bottom: "Layer_25"
  top: "Layer_25"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_25"
  type: "Scale"
  bottom: "Layer_25"
  top: "Layer_25"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_25"
  type: "ReLU"
  bottom: "Layer_25"
  top: "Layer_25"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_26"
  type: "Convolution"
  bottom: "Layer_25"
  top: "Layer_26"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 256
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_26"
  type: "BatchNorm"
  bottom: "Layer_26"
  top: "Layer_26"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_26"
  type: "Scale"
  bottom: "Layer_26"
  top: "Layer_26"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_26"
  type: "ReLU"
  bottom: "Layer_26"
  top: "Layer_26"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_27"
  type: "Convolution"
  bottom: "Layer_26"
  top: "Layer_27"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_27"
  type: "BatchNorm"
  bottom: "Layer_27"
  top: "Layer_27"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_27"
  type: "Scale"
  bottom: "Layer_27"
  top: "Layer_27"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_28"
  type: "Eltwise"
  bottom: "Layer_24"
  bottom: "Layer_27"
  top: "Layer_28"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_29"
  type: "Convolution"
  bottom: "Layer_28"
  top: "Layer_29"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_29"
  type: "BatchNorm"
  bottom: "Layer_29"
  top: "Layer_29"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_29"
  type: "Scale"
  bottom: "Layer_29"
  top: "Layer_29"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_29"
  type: "ReLU"
  bottom: "Layer_29"
  top: "Layer_29"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_30"
  type: "Convolution"
  bottom: "Layer_29"
  top: "Layer_30"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 256
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_30"
  type: "BatchNorm"
  bottom: "Layer_30"
  top: "Layer_30"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_30"
  type: "Scale"
  bottom: "Layer_30"
  top: "Layer_30"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_30"
  type: "ReLU"
  bottom: "Layer_30"
  top: "Layer_30"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_31"
  type: "Convolution"
  bottom: "Layer_30"
  top: "Layer_31"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_31"
  type: "BatchNorm"
  bottom: "Layer_31"
  top: "Layer_31"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_31"
  type: "Scale"
  bottom: "Layer_31"
  top: "Layer_31"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_32"
  type: "Eltwise"
  bottom: "Layer_28"
  bottom: "Layer_31"
  top: "Layer_32"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_33"
  type: "Convolution"
  bottom: "Layer_32"
  top: "Layer_33"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_33"
  type: "BatchNorm"
  bottom: "Layer_33"
  top: "Layer_33"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_33"
  type: "Scale"
  bottom: "Layer_33"
  top: "Layer_33"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_33"
  type: "ReLU"
  bottom: "Layer_33"
  top: "Layer_33"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_34"
  type: "Convolution"
  bottom: "Layer_33"
  top: "Layer_34"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 256
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_34"
  type: "BatchNorm"
  bottom: "Layer_34"
  top: "Layer_34"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_34"
  type: "Scale"
  bottom: "Layer_34"
  top: "Layer_34"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_34"
  type: "ReLU"
  bottom: "Layer_34"
  top: "Layer_34"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_35"
  type: "Convolution"
  bottom: "Layer_34"
  top: "Layer_35"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_35"
  type: "BatchNorm"
  bottom: "Layer_35"
  top: "Layer_35"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_35"
  type: "Scale"
  bottom: "Layer_35"
  top: "Layer_35"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_36"
  type: "Eltwise"
  bottom: "Layer_32"
  bottom: "Layer_35"
  top: "Layer_36"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_37"
  type: "Convolution"
  bottom: "Layer_36"
  top: "Layer_37"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_37"
  type: "BatchNorm"
  bottom: "Layer_37"
  top: "Layer_37"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_37"
  type: "Scale"
  bottom: "Layer_37"
  top: "Layer_37"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_37"
  type: "ReLU"
  bottom: "Layer_37"
  top: "Layer_37"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_38"
  type: "Convolution"
  bottom: "Layer_37"
  top: "Layer_38"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 256
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_38"
  type: "BatchNorm"
  bottom: "Layer_38"
  top: "Layer_38"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_38"
  type: "Scale"
  bottom: "Layer_38"
  top: "Layer_38"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_38"
  type: "ReLU"
  bottom: "Layer_38"
  top: "Layer_38"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_39"
  type: "Convolution"
  bottom: "Layer_38"
  top: "Layer_39"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_39"
  type: "BatchNorm"
  bottom: "Layer_39"
  top: "Layer_39"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_39"
  type: "Scale"
  bottom: "Layer_39"
  top: "Layer_39"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_40"
  type: "Eltwise"
  bottom: "Layer_36"
  bottom: "Layer_39"
  top: "Layer_40"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_41"
  type: "Convolution"
  bottom: "Layer_40"
  top: "Layer_41"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_41"
  type: "BatchNorm"
  bottom: "Layer_41"
  top: "Layer_41"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_41"
  type: "Scale"
  bottom: "Layer_41"
  top: "Layer_41"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_41"
  type: "ReLU"
  bottom: "Layer_41"
  top: "Layer_41"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_42"
  type: "Convolution"
  bottom: "Layer_41"
  top: "Layer_42"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 256
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_42"
  type: "BatchNorm"
  bottom: "Layer_42"
  top: "Layer_42"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_42"
  type: "Scale"
  bottom: "Layer_42"
  top: "Layer_42"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_42"
  type: "ReLU"
  bottom: "Layer_42"
  top: "Layer_42"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_43"
  type: "Convolution"
  bottom: "Layer_42"
  top: "Layer_43"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_43"
  type: "BatchNorm"
  bottom: "Layer_43"
  top: "Layer_43"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_43"
  type: "Scale"
  bottom: "Layer_43"
  top: "Layer_43"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_44"
  type: "Eltwise"
  bottom: "Layer_40"
  bottom: "Layer_43"
  top: "Layer_44"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_45"
  type: "Convolution"
  bottom: "Layer_44"
  top: "Layer_45"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_45"
  type: "BatchNorm"
  bottom: "Layer_45"
  top: "Layer_45"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_45"
  type: "Scale"
  bottom: "Layer_45"
  top: "Layer_45"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_45"
  type: "ReLU"
  bottom: "Layer_45"
  top: "Layer_45"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_46"
  type: "Convolution"
  bottom: "Layer_45"
  top: "Layer_46"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 256
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_46"
  type: "BatchNorm"
  bottom: "Layer_46"
  top: "Layer_46"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_46"
  type: "Scale"
  bottom: "Layer_46"
  top: "Layer_46"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_46"
  type: "ReLU"
  bottom: "Layer_46"
  top: "Layer_46"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_47"
  type: "Convolution"
  bottom: "Layer_46"
  top: "Layer_47"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_47"
  type: "BatchNorm"
  bottom: "Layer_47"
  top: "Layer_47"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_47"
  type: "Scale"
  bottom: "Layer_47"
  top: "Layer_47"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_48"
  type: "Eltwise"
  bottom: "Layer_44"
  bottom: "Layer_47"
  top: "Layer_48"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_49"
  type: "Convolution"
  bottom: "Layer_48"
  top: "Layer_49"
  convolution_param {
    num_output: 512
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_49"
  type: "BatchNorm"
  bottom: "Layer_49"
  top: "Layer_49"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_49"
  type: "Scale"
  bottom: "Layer_49"
  top: "Layer_49"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_49"
  type: "ReLU"
  bottom: "Layer_49"
  top: "Layer_49"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_50"
  type: "Convolution"
  bottom: "Layer_49"
  top: "Layer_50"
  convolution_param {
    num_output: 512
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 512
    stride: 2
    dilation: 1
  }
}
layer {
  name: "BNLayer_50"
  type: "BatchNorm"
  bottom: "Layer_50"
  top: "Layer_50"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_50"
  type: "Scale"
  bottom: "Layer_50"
  top: "Layer_50"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_50"
  type: "ReLU"
  bottom: "Layer_50"
  top: "Layer_50"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_51"
  type: "Convolution"
  bottom: "Layer_50"
  top: "Layer_51"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_51"
  type: "BatchNorm"
  bottom: "Layer_51"
  top: "Layer_51"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_51"
  type: "Scale"
  bottom: "Layer_51"
  top: "Layer_51"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "ConvLayer_52"
  type: "Convolution"
  bottom: "Layer_51"
  top: "Layer_52"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_52"
  type: "BatchNorm"
  bottom: "Layer_52"
  top: "Layer_52"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_52"
  type: "Scale"
  bottom: "Layer_52"
  top: "Layer_52"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_52"
  type: "ReLU"
  bottom: "Layer_52"
  top: "Layer_52"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_53"
  type: "Convolution"
  bottom: "Layer_52"
  top: "Layer_53"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 256
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_53"
  type: "BatchNorm"
  bottom: "Layer_53"
  top: "Layer_53"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_53"
  type: "Scale"
  bottom: "Layer_53"
  top: "Layer_53"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_53"
  type: "ReLU"
  bottom: "Layer_53"
  top: "Layer_53"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_54"
  type: "Convolution"
  bottom: "Layer_53"
  top: "Layer_54"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_54"
  type: "BatchNorm"
  bottom: "Layer_54"
  top: "Layer_54"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_54"
  type: "Scale"
  bottom: "Layer_54"
  top: "Layer_54"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_55"
  type: "Eltwise"
  bottom: "Layer_51"
  bottom: "Layer_54"
  top: "Layer_55"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_56"
  type: "Convolution"
  bottom: "Layer_55"
  top: "Layer_56"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_56"
  type: "BatchNorm"
  bottom: "Layer_56"
  top: "Layer_56"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_56"
  type: "Scale"
  bottom: "Layer_56"
  top: "Layer_56"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_56"
  type: "ReLU"
  bottom: "Layer_56"
  top: "Layer_56"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_57"
  type: "Convolution"
  bottom: "Layer_56"
  top: "Layer_57"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 1
    kernel_size: 3
    group: 256
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_57"
  type: "BatchNorm"
  bottom: "Layer_57"
  top: "Layer_57"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_57"
  type: "Scale"
  bottom: "Layer_57"
  top: "Layer_57"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_57"
  type: "ReLU"
  bottom: "Layer_57"
  top: "Layer_57"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_58"
  type: "Convolution"
  bottom: "Layer_57"
  top: "Layer_58"
  convolution_param {
    num_output: 256
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_58"
  type: "BatchNorm"
  bottom: "Layer_58"
  top: "Layer_58"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_58"
  type: "Scale"
  bottom: "Layer_58"
  top: "Layer_58"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "AddLayer_59"
  type: "Eltwise"
  bottom: "Layer_55"
  bottom: "Layer_58"
  top: "Layer_59"
  eltwise_param {
    operation: SUM
  }
}
layer {
  name: "ConvLayer_60"
  type: "Convolution"
  bottom: "Layer_59"
  top: "Layer_60"
  convolution_param {
    num_output: 512
    bias_term: false
    pad: 0
    kernel_size: 1
    group: 1
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_60"
  type: "BatchNorm"
  bottom: "Layer_60"
  top: "Layer_60"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_60"
  type: "Scale"
  bottom: "Layer_60"
  top: "Layer_60"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "RELULayer_60"
  type: "ReLU"
  bottom: "Layer_60"
  top: "Layer_60"
  relu_param {
    negative_slope: 0.0
  }
}
layer {
  name: "ConvLayer_61"
  type: "Convolution"
  bottom: "Layer_60"
  top: "Layer_61"
  convolution_param {
    num_output: 512
    bias_term: false
    pad: 0
    kernel_size: 7
    group: 512
    stride: 1
    dilation: 1
  }
}
layer {
  name: "BNLayer_61"
  type: "BatchNorm"
  bottom: "Layer_61"
  top: "Layer_61"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_61"
  type: "Scale"
  bottom: "Layer_61"
  top: "Layer_61"
  scale_param {
    bias_term: true
  }
}
layer {
  name: "ViewLayer_61"
  type: "Reshape"
  bottom: "Layer_61"
  top: "Layer_62"
  reshape_param {
    shape {
      dim: 0
      dim: -1
      dim: 1
      dim: 1
    }
  }
}
layer {
  name: "LinearLayer_63"
  type: "InnerProduct"
  bottom: "Layer_62"
  top: "face_recognition"
  inner_product_param {
    num_output: 512
    bias_term: false
  }
}
layer {
  name: "BNLayer_63"
  type: "BatchNorm"
  bottom: "face_recognition"
  top: "face_recognition"
  batch_norm_param {
    use_global_stats: true
  }
}
layer {
  name: "ScaleLayer_63"
  type: "Scale"
  bottom: "face_recognition"
  top: "face_recognition"
  scale_param {
    bias_term: true
  }
}
