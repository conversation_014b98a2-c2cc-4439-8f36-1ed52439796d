{"class_name": "Functional", "config": {"name": "dms", "layers": [{"class_name": "InputLayer", "config": {"dtype": "uint8_t", "sparse": false, "ragged": false, "name": "input_1"}, "name": "input_1", "inbound_nodes": []}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 160, "output_height": 96, "keep_ratio": true, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": true, "mean": [0], "std": [255.0]}, "name": "faceDet_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}]]}, {"class_name": "oax4600_inference", "config": {"type": "inside", "filename": "model/FaceDetection.ovm", "norm": 255.0, "mean": [0], "std": [1.0]}, "name": "faceDet_infer", "inbound_nodes": [["faceDet_pre_normal", 0, 0, {}]]}, {"class_name": "FaceBboxDecoder", "config": {"feat_width": 20, "feat_height": 12, "stride": 8, "offset": 6, "scale": 20, "score_th": 0.5, "iou_th": 0.7, "flag_chw": false}, "name": "faceDet_post", "inbound_nodes": [["faceDet_infer", 0, 0, {}]]}, {"class_name": "shape", "config": {}, "name": "input_1_shape", "inbound_nodes": [["input_1", 0, 0, {}]]}, {"class_name": "CoordinateScaling2D", "config": {"x_index": [2, 4], "y_index": [3, 5], "input_max_width": 160, "input_max_height": 96, "keep_ratio": true, "flag_nchw": true}, "name": "faceBbox_scaling", "inbound_nodes": [["faceDet_post", 0, 0, {}], ["input_1_shape", 0, 0, {}]]}, {"class_name": "RoiNms", "config": {"roi": {"isConst": true, "x": 0, "y": 0, "w": 0, "h": 0}, "min_iou": 0.7}, "name": "driver_roi", "inbound_nodes": [["faceBbox_scaling", 0, 0, {}], ["input_1", 1, 0, {}], ["input_1", 2, 0, {}]]}, {"class_name": "SquareRoi", "config": {"isBbox": true, "extend": 0.5, "x_index": 1, "y_index": 2}, "name": "faceBbox_roi", "inbound_nodes": [["driver_roi", 0, 0, {}]]}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 256, "output_height": 256, "keep_ratio": true, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": false, "mean": [0], "std": [255.0]}, "name": "facekeypoint_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}], ["faceBbox_roi", 0, 0, {}]]}, {"class_name": "oax4600_inference", "config": {"type": "inside", "filename": "model/FaceKeypoints.ovm", "norm": 255.0, "mean": [0], "std": [1.0]}, "name": "facekeypoint_infer", "inbound_nodes": [["facekeypoint_pre_normal", 0, 0, {}]]}, {"class_name": "HeatmapKeypointsDecoder", "config": {"stride": 16, "offset": 8, "scale": 3}, "name": "facekeypoint_post", "inbound_nodes": [["facekeypoint_infer", 0, 0, {}]]}, {"class_name": "CoordinateScalingOffset2D", "config": {"x_index": [1], "y_index": [2], "input_max_width": 256, "input_max_height": 256, "keep_ratio": true, "flag_nchw": true}, "name": "faceKeypoints_scaling", "inbound_nodes": [["facekeypoint_post", 0, 0, {}], ["faceBbox_roi", 0, 0, {}]]}, {"class_name": "Sampling", "config": {"indexs": [2, 3]}, "name": "right_eye_point", "inbound_nodes": [["faceKeypoints_scaling", 0, 0, {}]]}, {"class_name": "KeypointsQualityEvaluator", "config": {"num_keypoints": 38, "window_size": 10, "std_threshold": 2.0, "velocity_threshold": 20.0, "acceleration_threshold": 10.0, "confidence_threshold": 0.6, "instability_ratio_threshold": 0.3, "std_velocity_disable_threshold": 35.0, "enable_logging": false, "log_file_path": "./tmp/face_keypoints_quality_evaluator.csv"}, "name": "face_keypoints_quality_evaluator", "inbound_nodes": [["faceKeypoints_scaling", 0, 0, {}]]}, {"class_name": "KeypointsQualityEvaluator", "config": {"num_keypoints": 17, "window_size": 10, "std_threshold": 2.0, "velocity_threshold": 20.0, "acceleration_threshold": 10.0, "confidence_threshold": 0.6, "instability_ratio_threshold": 0.3, "std_velocity_disable_threshold": 35.0, "enable_logging": false, "log_file_path": "./tmp/left_eye_keypoints_quality_evaluator.csv"}, "name": "left_eye_keypoints_quality_evaluator", "inbound_nodes": [["left_eye_Keypoints_scaling", 0, 0, {}]]}, {"class_name": "KeypointsQualityEvaluator", "config": {"num_keypoints": 17, "window_size": 10, "std_threshold": 2.0, "velocity_threshold": 20.0, "acceleration_threshold": 10.0, "confidence_threshold": 0.6, "instability_ratio_threshold": 0.3, "std_velocity_disable_threshold": 35.0, "enable_logging": false, "log_file_path": "./tmp/right_eye_keypoints_quality_evaluator.csv"}, "name": "right_eye_keypoints_quality_evaluator", "inbound_nodes": [["right_eye_Keypoints_scaling", 0, 0, {}]]}, {"class_name": "Sampling", "config": {"indexs": [0, 1]}, "name": "left_eye_point", "inbound_nodes": [["faceKeypoints_scaling", 0, 0, {}]]}, {"class_name": "SquareRoi", "config": {"isBbox": false, "extend": 0.5, "x_index": 1, "y_index": 2}, "name": "right_eye_roi", "inbound_nodes": [["right_eye_point", 0, 0, {}]]}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 96, "output_height": 96, "keep_ratio": false, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": false, "mean": [0], "std": [255.0]}, "name": "right_eye_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}], ["right_eye_roi", 0, 0, {}]]}, {"class_name": "oax4600_inference", "config": {"type": "inside", "filename": "model/eye.ovm", "norm": 255.0, "mean": [0], "std": [1.0]}, "name": "right_eye_infer", "inbound_nodes": [["right_linesight_pre_normal", 0, 0, {}]]}, {"class_name": "SquareRoi", "config": {"isBbox": false, "extend": 0.5, "x_index": 1, "y_index": 2}, "name": "left_eye_roi", "inbound_nodes": [["left_eye_point", 0, 0, {}]]}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 96, "output_height": 96, "keep_ratio": false, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": false, "mean": [0], "std": [255.0]}, "name": "left_eye_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}], ["left_eye_roi", 0, 0, {}]]}, {"class_name": "oax4600_inference", "config": {"type": "inside", "filename": "model/eye.ovm", "norm": 255.0, "mean": [0], "std": [1.0]}, "name": "left_eye_infer", "inbound_nodes": [["left_linesight_pre_normal", 0, 0, {}]]}, {"class_name": "Sampling", "config": {"indexs": [0, 3, 4, 9, 10, 5, 7]}, "name": "driver_angle_point", "inbound_nodes": [["faceKeypoints_scaling", 0, 0, {}]]}, {"class_name": "SolvePnpAngle", "config": {"img_x_index": 1, "img_y_index": 2, "three_d_points": [[-225.0, 170.0, -135.0], [225.0, 170.0, -135.0], [0.0, 0.0, 0.0], [-75.0, -100.0, -80.0], [75.0, -100.0, -80.0], [-150.0, -150.0, -125.0], [150.0, -150.0, -125.0]]}, "name": "driver_face_angle", "inbound_nodes": [["driver_angle_point", 0, 0, {}], ["input_1_shape", 0, 0, {}]]}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 256, "output_height": 256, "keep_ratio": false, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": true, "mean": [0], "std": [255.0]}, "name": "OcclusionDetector_img", "inbound_nodes": [["input_1", 0, 0, {}]]}, {"class_name": "CcCheckImage", "config": {"flag_input_nchw": true, "hist_window_size": 19, "percent_threshold": 0.11, "blurstd_threshold": 4.0, "overexposure_threshold": 130, "underexpose_threshold": 50, "cornerneeded": 1000}, "name": "OcclusionDetector_res", "inbound_nodes": [["input_1", 0, 0, {}]]}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 96, "output_height": 96, "keep_ratio": false, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": false, "flag_calc_histogram": true, "mean": [0], "std": [255.0]}, "name": "right_linesight_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}], ["right_eye_roi", 0, 0, {}]]}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 96, "output_height": 96, "keep_ratio": false, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": false, "flag_calc_histogram": true, "mean": [0], "std": [255.0]}, "name": "left_linesight_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}], ["left_eye_roi", 0, 0, {}]]}, {"class_name": "HeatmapKeypointsDecoder", "config": {"stride": 16, "offset": 8, "scale": 3}, "name": "left_eye_keypoint_post", "inbound_nodes": [["left_eye_infer", 1, 0, {}]]}, {"class_name": "CoordinateScalingOffset2D", "config": {"x_index": [1], "y_index": [2], "input_max_width": 96, "input_max_height": 96, "keep_ratio": true, "flag_nchw": true}, "name": "left_eye_Keypoints_scaling", "inbound_nodes": [["left_eye_keypoint_post", 0, 0, {}], ["left_eye_roi", 0, 0, {}]]}, {"class_name": "HeatmapKeypointsDecoder", "config": {"stride": 16, "offset": 8, "scale": 3}, "name": "right_eye_keypoint_post", "inbound_nodes": [["right_eye_infer", 1, 0, {}]]}, {"class_name": "CoordinateScalingOffset2D", "config": {"x_index": [1], "y_index": [2], "input_max_width": 96, "input_max_height": 96, "keep_ratio": true, "flag_nchw": true}, "name": "right_eye_Keypoints_scaling", "inbound_nodes": [["right_eye_keypoint_post", 0, 0, {}], ["right_eye_roi", 0, 0, {}]]}, {"class_name": "CcROIGenerator", "config": {"indexs": [0, 4], "upper_offset": 15, "lower_offset": 15}, "name": "left_eye_roi_generator", "inbound_nodes": [["left_eye_Keypoints_scaling", 0, 0, {}]]}, {"class_name": "CcROIGenerator", "config": {"indexs": [0, 4], "upper_offset": 15, "lower_offset": 15}, "name": "right_eye_roi_generator", "inbound_nodes": [["right_eye_Keypoints_scaling", 0, 0, {}]]}, {"class_name": "CcROIGenerator", "config": {"indexs": [5, 7], "upper_offset": 20, "lower_offset": 20}, "name": "mouth_roi_generator", "inbound_nodes": [["faceKeypoints_scaling", 0, 0, {}]]}, {"class_name": "CcLuminanceAnalyzer", "config": {"flag_nchw": true}, "name": "face_exact_rois", "inbound_nodes": [["input_1", 0, 0, {}], ["left_eye_roi_generator", 0, 0, {}], ["right_eye_roi_generator", 0, 0, {}], ["mouth_roi_generator", 0, 0, {}]]}], "input_layers": [["input_1", 0, 0]], "output_layers": [["driver_roi", 0, 0], ["faceKeypoints_scaling", 0, 0], ["right_eye_infer", 0, 0], ["left_eye_infer", 0, 0], ["driver_face_angle", 0, 0], ["OcclusionDetector_res", 0, 0], ["facekeypoint_infer", 1, 0], ["right_eye_Keypoints_scaling", 0, 0], ["right_linesight_pre_normal", 1, 0], ["left_eye_Keypoints_scaling", 0, 0], ["left_linesight_pre_normal", 1, 0], ["face_exact_rois", 0, 0], ["face_keypoints_quality_evaluator", 0, 0], ["left_eye_keypoints_quality_evaluator", 0, 0], ["right_eye_keypoints_quality_evaluator", 0, 0]]}}