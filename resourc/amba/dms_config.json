{"class_name": "Functional", "config": {"name": "dms", "layers": [{"class_name": "InputLayer", "config": {"dtype": "uint8_t", "sparse": false, "ragged": false, "name": "input_1"}, "name": "input_1", "inbound_nodes": []}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 256, "output_height": 256, "keep_ratio": true, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": true, "mean": [0], "std": [255.0]}, "name": "faceDet_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}]]}, {"class_name": "amba_inference", "config": {"type": "inside", "flag_crop": false, "m_name": "m_face", "filename": "model/cv22_cavalry2.2.8.4_facedetection.bin"}, "name": "faceDet_infer", "inbound_nodes": [["input_1", 0, 0, {}]]}, {"class_name": "FaceBboxDecoder", "config": {"feat_width": 32, "feat_height": 32, "stride": 8, "score_th": 0.5, "iou_th": 0.1}, "name": "faceDet_post", "inbound_nodes": [["faceDet_infer", 0, 0, {}]]}, {"class_name": "shape", "config": {}, "name": "input_1_shape", "inbound_nodes": [["input_1", 0, 0, {}]]}, {"class_name": "CoordinateScaling2D", "config": {"x_index": [2, 4], "y_index": [3, 5], "input_max_width": 256, "input_max_height": 256, "keep_ratio": true, "flag_nchw": true}, "name": "faceBbox_scaling", "inbound_nodes": [["faceDet_post", 0, 0, {}], ["input_1_shape", 0, 0, {}]]}, {"class_name": "RoiNms", "config": {"roi": {"isConst": true, "x": 0, "y": 0, "w": 0, "h": 0}, "min_iou": 0.7}, "name": "driver_roi", "inbound_nodes": [["faceBbox_scaling", 0, 0, {}], ["input_1", 1, 0, {}]]}, {"class_name": "SquareRoi", "config": {"isBbox": true, "extend": 0.2, "x_index": 1, "y_index": 2}, "name": "faceBbox_roi", "inbound_nodes": [["driver_roi", 0, 0, {}]]}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 128, "output_height": 128, "keep_ratio": true, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": false, "mean": [0], "std": [255.0]}, "name": "facekeypoint_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}], ["faceBbox_roi", 0, 0, {}]]}, {"class_name": "amba_inference", "config": {"type": "inside", "m_name": "m_fkey", "filename": "model/cv22_cavalry2.2.8.4_facekeypoints.bin"}, "name": "facekeypoint_infer", "inbound_nodes": [["input_1", 0, 0, {}], ["faceBbox_roi", 0, 0, {}]]}, {"class_name": "HeatmapKeypointsDecoder", "config": {"stride": 16}, "name": "facekeypoint_post", "inbound_nodes": [["facekeypoint_infer", 0, 0, {}]]}, {"class_name": "CoordinateScalingOffset2D", "config": {"x_index": [1], "y_index": [2], "input_max_width": 128, "input_max_height": 128, "keep_ratio": true, "flag_nchw": true}, "name": "faceKeypoints_scaling", "inbound_nodes": [["facekeypoint_post", 0, 0, {}], ["faceBbox_roi", 0, 0, {}]]}, {"class_name": "Sampling", "config": {"indexs": [2, 3]}, "name": "right_eye_point", "inbound_nodes": [["faceKeypoints_scaling", 0, 0, {}]]}, {"class_name": "Sampling", "config": {"indexs": [0, 1]}, "name": "left_eye_point", "inbound_nodes": [["faceKeypoints_scaling", 0, 0, {}]]}, {"class_name": "SquareRoi", "config": {"isBbox": false, "extend": 1, "x_index": 1, "y_index": 2}, "name": "right_eye_roi", "inbound_nodes": [["right_eye_point", 0, 0, {}]]}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 64, "output_height": 64, "keep_ratio": false, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": false, "mean": [0], "std": [255.0]}, "name": "right_eye_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}], ["right_eye_roi", 0, 0, {}]]}, {"class_name": "amba_inference", "config": {"type": "inside", "m_name": "m_reye", "filename": "model/cv22_cavalry2.2.8.4_eye.bin"}, "name": "right_eye_infer", "inbound_nodes": [["input_1", 0, 0, {}], ["right_eye_roi", 0, 0, {}]]}, {"class_name": "SquareRoi", "config": {"isBbox": false, "extend": 1, "x_index": 1, "y_index": 2}, "name": "left_eye_roi", "inbound_nodes": [["left_eye_point", 0, 0, {}]]}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 64, "output_height": 64, "keep_ratio": false, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": false, "mean": [0], "std": [255.0]}, "name": "left_eye_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}], ["left_eye_roi", 0, 0, {}]]}, {"class_name": "amba_inference", "config": {"type": "inside", "m_name": "m_leye", "filename": "model/cv22_cavalry2.2.8.4_eye.bin"}, "name": "left_eye_infer", "inbound_nodes": [["input_1", 0, 0, {}], ["left_eye_roi", 0, 0, {}]]}, {"class_name": "Sampling", "config": {"indexs": [0, 3, 4, 5, 7, 9]}, "name": "driver_angle_point", "inbound_nodes": [["faceKeypoints_scaling", 0, 0, {}]]}, {"class_name": "Sampling", "config": {"indexs": [0, 3, 9]}, "name": "driver_face_align_point", "inbound_nodes": [["faceKeypoints_scaling", 0, 0, {}]]}, {"class_name": "SolvePnpAngle", "config": {"img_x_index": 1, "img_y_index": 2, "three_d_points": [[-46.04729, 37.471411, -7.037989], [45.170805, 38.086515, -4.894163], [0.55592, -6.5629, 25.944448], [-28.916267, -28.612716, 2.24031], [28.794412, -28.079924, 3.217393], [0.098749, -77.061286, 0.881698]]}, "name": "driver_face_angle", "inbound_nodes": [["driver_angle_point", 0, 0, {}], ["input_1_shape", 0, 0, {}]]}, {"class_name": "SquareRoi", "config": {"isBbox": true, "extend": 0.5, "x_index": 1, "y_index": 2}, "name": "phone_roi", "inbound_nodes": [["driver_roi", 0, 0, {}]]}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 256, "output_height": 256, "keep_ratio": true, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": false, "mean": [0], "std": [255.0]}, "name": "phone_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}], ["phone_roi", 0, 0, {}]]}, {"class_name": "Extend", "config": {"dim": 1, "extend_num": 2}, "name": "phone_pre_normal_rgb", "inbound_nodes": [["phone_pre_normal", 0, 0, {}]]}, {"class_name": "amba_inference", "config": {"type": "inside", "m_name": "phone", "flag_crop": false, "filename": "model/cv22_cavalry2.2.8.4_phoneseg.bin"}, "name": "phone_infer", "inbound_nodes": [["input_1", 0, 0, {}]]}, {"class_name": "SegDecoder", "config": {"flag_nchw": true, "flag_argmax": false, "keep_ratio": true, "flag_image_shape_nchw": true}, "name": "phone_decoder", "inbound_nodes": [["phone_infer", 0, 0, {}], ["input_1_shape", 0, 0, {}]]}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 256, "output_height": 256, "keep_ratio": false, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": true, "mean": [0], "std": [255.0]}, "name": "OcclusionDetector_img", "inbound_nodes": [["input_1", 0, 0, {}]]}, {"class_name": "CcCheckImage", "config": {"flag_input_nchw": true}, "name": "OcclusionDetector_res", "inbound_nodes": [["input_1", 0, 0, {}]]}, {"class_name": "Const", "config": {"dim": [1, 5], "data": [0, 0, 0, 0, 0]}, "name": "mask_glass_infer0", "inbound_nodes": [[]]}, {"class_name": "Const", "config": {"dim": [1, 5], "data": [0, 0, 0, 0, 0]}, "name": "mask_glass_infer1", "inbound_nodes": [[]]}, {"class_name": "Const", "config": {"dim": [1, 512], "data": [0, 0, 0, 0, 0]}, "name": "faceid_infer", "inbound_nodes": [[]]}], "input_layers": [["input_1", 0, 0]], "output_layers": [["driver_roi", 0, 0], ["faceKeypoints_scaling", 0, 0], ["right_eye_infer", 0, 0], ["left_eye_infer", 0, 0], ["driver_face_angle", 0, 0], ["phone_decoder", 1, 0], ["phone_decoder", 0, 0], ["faceid_infer", 0, 0], ["OcclusionDetector_res", 0, 0], ["mask_glass_infer0", 0, 0], ["mask_glass_infer1", 0, 0]]}}