import os;
import sys

def find_file(dir):
    file_list=[]
    for dirpath,dirname,files in os.walk(dir):
        for file in files:
            # path=dirpath[len(dir)+1:]
            path=dirpath
            file_list.append(path+"/"+file)
    return file_list

def generate_array_c_code(name,buf):
    head="static const unsigned char {}[] ".format(name)+"{"
    data=""
    i=0
    for b in buf:
        if(i%16==0):
            data+="\n"
        data+=hex(b)+","
        i+=1
        
    end="};\n"
    return head+data+end

path=sys.argv[1]

file_list=find_file(path)
dst_file = open("resourc.cpp",'w')
dst_file.write('#include "cc_resource_register.h"\n')
for filename in file_list:
    array_name=filename[len(path)+1:].replace("/", "_").replace("\\", "_").replace(".", "_")
    src_file = open(filename,'rb')
    src_data = src_file.read()
    array_c_code=generate_array_c_code(array_name,src_data)
    dst_file.write(array_c_code)
    dst_file.write('tongxing::CcResourcData {}(sizeof({}),{});\n '.format(array_name+"_data",array_name,array_name))
    dst_file.write('tongxing::CcResourcDataRegister& {}= tongxing::CcResourcDataRegister::instance().register_function(std::string("{}"),{});\n'.format(array_name+"_register",filename[len(path)+1:],array_name+"_data"))