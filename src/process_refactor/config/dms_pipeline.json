{"pipeline": {"name": "DMS_Processing_Pipeline", "version": "1.0.0", "description": "DMS系统模块化处理流水线配置", "layers": [{"name": "ai_inference", "type": "CcModuleGrop", "description": "AI推理模块组（原有tongxing模块）", "config": {"model_path": "models/", "device": "cpu"}}, {"name": "face_analysis", "type": "FaceAnalysisModule", "description": "人脸分析模块", "inbound_nodes": [["ai_inference", 0]], "config": {"face_bbox_threshold": 0.5, "face_angle_threshold": 0.35, "face_attr_threshold": 0.7, "min_face_height_px": 230}}, {"name": "camera_status", "type": "CameraStatusModule", "description": "摄像头状态检测模块", "inbound_nodes": [["ai_inference", 1]], "config": {"occlusion_threshold": 0.5, "brightness_threshold": [50, 200], "blur_threshold": 0.3}}, {"name": "fatigue_detection", "type": "FatigueDetectionModule", "description": "疲劳检测模块", "inbound_nodes": [["face_analysis", 0], ["ai_inference", 2]], "config": {"eye_closure_threshold": 0.1, "yawn_threshold": 0.6, "fatigue_speed_threshold": 10, "min_eye_opening": 0.1, "min_eye_x_px": 32}}, {"name": "distraction_detection", "type": "DistractionDetectionModule", "description": "分心检测模块", "inbound_nodes": [["face_analysis", 0], ["ai_inference", 3]], "config": {"head_pose_threshold": 35.0, "gaze_threshold": 30.0, "distraction_speed_threshold": 20, "pitch_threshold": 25.0, "yaw_threshold": 35.0, "roll_threshold": 20.0}}, {"name": "behavior_detection", "type": "BehaviorDetectionModule", "description": "行为检测模块（打电话、抽烟等）", "inbound_nodes": [["ai_inference", 4]], "config": {"phone_detection_enabled": true, "smoking_detection_enabled": true, "detection_threshold": 0.7}}, {"name": "result_integration", "type": "ResultIntegrationModule", "description": "结果整合模块", "inbound_nodes": [["face_analysis", 0], ["camera_status", 0], ["fatigue_detection", 0], ["distraction_detection", 0], ["behavior_detection", 0]], "config": {"integration_strategy": "weighted_fusion", "weights": {"fatigue": 0.4, "distraction": 0.4, "behavior": 0.2}}}], "output_layers": [["result_integration", 0]]}, "face_analysis": {"face_bbox": {"threshold": 0.5, "min_width": 80, "min_height": 80, "max_width": 800, "max_height": 600}, "keypoints": {"threshold": 0.35, "min_points": 68, "validation_enabled": true}, "attributes": {"mask_threshold": 0.7, "glasses_threshold": 0.7, "ir_block_threshold": 0.7}}, "fatigue_detection": {"eye_analysis": {"closure_threshold": 0.1, "min_opening": 0.1, "min_x_pixels": 32, "temporal_smoothing": true, "smoothing_window": 5}, "yawn_detection": {"mouth_opening_threshold": 0.6, "min_duration_ms": 1000, "max_duration_ms": 5000}, "fatigue_patterns": {"eye_closure_weight": 0.6, "yawn_weight": 0.4, "speed_threshold": 10}}, "distraction_detection": {"head_pose": {"pitch_threshold": 25.0, "yaw_threshold": 35.0, "roll_threshold": 20.0, "temporal_smoothing": true, "smoothing_window": 3}, "gaze_analysis": {"pitch_threshold": 20.0, "yaw_threshold": 30.0, "tracking_enabled": true, "calibration_enabled": true}, "distraction_patterns": {"head_pose_weight": 0.7, "gaze_weight": 0.3, "speed_threshold": 20}}, "warning_system": {"fatigue_warning": {"time_window_ms": 3000, "trigger_threshold": 0.7, "speed_threshold": 10, "gear_requirement": "forward"}, "distraction_warning": {"time_window_ms": 2000, "trigger_threshold": 0.8, "speed_threshold": 20, "gear_requirement": "forward"}, "behavior_warning": {"phone_time_window_ms": 1000, "smoking_time_window_ms": 2000, "speed_threshold": 5}}, "state_manager": {"activation": {"license_check_enabled": true, "timeout_ms": 300000, "retry_interval_ms": 60000}, "hardware_monitoring": {"camera_fault_threshold": 3, "can_fault_threshold": 5, "monitoring_interval_ms": 1000}, "calibration": {"auto_calibration_enabled": true, "calibration_timeout_ms": 30000, "min_samples": 100}}, "data_adapter": {"image_preprocessing": {"resize_enabled": false, "normalization_enabled": false, "roi_enabled": true}, "roi_settings": {"default_left_top": [100, 100], "default_right_bottom": [540, 380], "auto_adjust_enabled": true}}, "vehicle_types": {"BYD_SA2": {"fatigue_speed_threshold": 10, "distraction_speed_threshold": 20, "warning_sensitivity": "normal"}, "BYD_HKH_L": {"fatigue_speed_threshold": 15, "distraction_speed_threshold": 25, "warning_sensitivity": "high"}, "BYD_SC2EDE": {"fatigue_speed_threshold": 10, "distraction_speed_threshold": 20, "warning_sensitivity": "normal"}}, "debug": {"logging_enabled": true, "log_level": "INFO", "performance_monitoring": true, "result_saving": {"enabled": false, "save_path": "/tmp/dms_results/", "save_images": false, "save_json": true}}}