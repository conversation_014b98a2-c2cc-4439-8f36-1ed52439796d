#include "face_analysis_module.h"
#include "cc_numarray_tool.h"
#include "CalmCarLog.h"
#include <cstring>
#include <algorithm>
#include <cmath>

namespace tongxing {

FaceAnalysisModule::FaceAnalysisModule() 
    : face_bbox_threshold_(0.5f)
    , face_angle_threshold_(0.35f)
    , face_attr_threshold_(0.7f)
    , min_face_height_px_(230) {
    TX_LOG_INFO("FaceAnalysisModule", "FaceAnalysisModule constructed");
}

FaceAnalysisModule::~FaceAnalysisModule() {
    TX_LOG_INFO("FaceAnalysisModule", "FaceAnalysisModule destructed");
}

int FaceAnalysisModule::init(const Json::Value& config) {
    // 从配置中读取参数
    if (config.isMember("face_bbox_threshold")) {
        face_bbox_threshold_ = config["face_bbox_threshold"].asFloat();
    }
    
    if (config.isMember("face_angle_threshold")) {
        face_angle_threshold_ = config["face_angle_threshold"].asFloat();
    }
    
    if (config.isMember("face_attr_threshold")) {
        face_attr_threshold_ = config["face_attr_threshold"].asFloat();
    }
    
    if (config.isMember("min_face_height_px")) {
        min_face_height_px_ = config["min_face_height_px"].asInt();
    }
    
    TX_LOG_INFO("FaceAnalysisModule", "FaceAnalysisModule initialized with thresholds: bbox=%.2f, angle=%.2f, attr=%.2f, min_height=%d",
               face_bbox_threshold_, face_angle_threshold_, face_attr_threshold_, min_face_height_px_);
    
    return 0;
}

int FaceAnalysisModule::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}

int FaceAnalysisModule::execute() {
    if (input_.empty()) {
        TX_LOG_ERROR("FaceAnalysisModule", "No input data provided");
        return -1;
    }
    
    // 假设输入数据的格式：
    // input_[0]: 人脸框数据 (来自AI推理)
    // input_[1]: 人脸关键点数据 (来自AI推理)
    // input_[2]: 人脸角度数据 (来自AI推理)
    // input_[3]: 人脸属性数据 (来自AI推理)
    
    // 1. 解析人脸框信息
    if (input_.size() > 0) {
        TXDmsFaceInfo face_info = ParseFaceBbox(input_[0]);
        
        // 验证人脸有效性
        if (ValidateFace(face_info)) {
            // 创建人脸框输出
            face_bbox_output_ = CreateOutput(&face_info, sizeof(TXDmsFaceInfo));
            TX_LOG_DEBUG("FaceAnalysisModule", "Face bbox parsed: score=%.2f, bbox=(%d,%d,%d,%d)",
                        face_info.score, face_info.xmin, face_info.ymin, face_info.xmax, face_info.ymax);
        } else {
            // 创建空的人脸框输出
            TXDmsFaceInfo empty_face = {};
            face_bbox_output_ = CreateOutput(&empty_face, sizeof(TXDmsFaceInfo));
            TX_LOG_DEBUG("FaceAnalysisModule", "Invalid face detected, using empty face info");
        }
    }
    
    // 2. 解析人脸关键点
    if (input_.size() > 1) {
        std::vector<Point2f> keypoints = ParseFaceKeypoints(input_[1]);
        
        if (ValidateKeypoints(keypoints)) {
            // 创建关键点输出
            face_keypoints_output_ = CreateOutput(keypoints.data(), keypoints.size() * sizeof(Point2f));
            TX_LOG_DEBUG("FaceAnalysisModule", "Face keypoints parsed: %zu points", keypoints.size());
        } else {
            // 创建空的关键点输出
            std::vector<Point2f> empty_keypoints(TX_MAX_FLD_SIZE);
            face_keypoints_output_ = CreateOutput(empty_keypoints.data(), empty_keypoints.size() * sizeof(Point2f));
            TX_LOG_DEBUG("FaceAnalysisModule", "Invalid keypoints detected, using empty keypoints");
        }
    }
    
    // 3. 解析人脸角度
    if (input_.size() > 2) {
        float face_angle_score = 0.0f;
        float mouth_score = 0.0f;
        ParseFaceAngle(input_[2], face_angle_score, mouth_score);
        
        // 创建角度输出
        float angle_data[2] = {face_angle_score, mouth_score};
        face_angle_output_ = CreateOutput(angle_data, sizeof(angle_data));
        TX_LOG_DEBUG("FaceAnalysisModule", "Face angle parsed: angle_score=%.2f, mouth_score=%.2f",
                    face_angle_score, mouth_score);
    }
    
    // 4. 解析人脸属性
    if (input_.size() > 3) {
        TXDmsFaceInfo attr_info = ParseFaceAttributes(input_[3]);
        
        // 创建属性输出
        face_attr_output_ = CreateOutput(&attr_info, sizeof(TXDmsFaceInfo));
        TX_LOG_DEBUG("FaceAnalysisModule", "Face attributes parsed: mask=%d, glass=%d, ir_block=%d",
                    attr_info.isMask, attr_info.isGlass, attr_info.isIRBlock);
    }
    
    // 5. 创建人脸有效性输出
    bool face_valid = (face_bbox_output_ != nullptr);
    face_valid_output_ = CreateOutput(&face_valid, sizeof(bool));
    
    TX_LOG_DEBUG("FaceAnalysisModule", "Face analysis completed, face_valid=%s", face_valid ? "true" : "false");
    return 0;
}

size_t FaceAnalysisModule::getOutputNum() {
    return 5; // 人脸框、关键点、角度、属性、有效性
}

std::shared_ptr<NumArray> FaceAnalysisModule::getOutput(int index) {
    switch (index) {
        case 0: return face_bbox_output_;
        case 1: return face_keypoints_output_;
        case 2: return face_angle_output_;
        case 3: return face_attr_output_;
        case 4: return face_valid_output_;
        default:
            TX_LOG_ERROR("FaceAnalysisModule", "Invalid output index: %d", index);
            return nullptr;
    }
}

TXDmsFaceInfo FaceAnalysisModule::ParseFaceBbox(const std::shared_ptr<NumArray>& face_bbox_data) {
    TXDmsFaceInfo face_info = {};
    
    if (!face_bbox_data || face_bbox_data->shape.empty()) {
        TX_LOG_WARN("FaceAnalysisModule", "Empty face bbox data");
        return face_info;
    }
    
    // 假设人脸框数据格式：[score, label, x, y, width, height]
    if (face_bbox_data->shape.size() >= 2 && face_bbox_data->shape[1] >= 6) {
        float* bbox_data = reinterpret_cast<float*>(face_bbox_data->data);
        
        face_info.score = bbox_data[0];
        // bbox_data[1] 是label，暂时不使用
        face_info.xmin = static_cast<int>(bbox_data[2]);
        face_info.ymin = static_cast<int>(bbox_data[3]);
        face_info.xmax = static_cast<int>(bbox_data[2] + bbox_data[4]); // x + width
        face_info.ymax = static_cast<int>(bbox_data[3] + bbox_data[5]); // y + height
        
        TX_LOG_DEBUG("FaceAnalysisModule", "Parsed face bbox: score=%.2f, bbox=(%d,%d,%d,%d)",
                    face_info.score, face_info.xmin, face_info.ymin, face_info.xmax, face_info.ymax);
    }
    
    return face_info;
}

std::vector<Point2f> FaceAnalysisModule::ParseFaceKeypoints(const std::shared_ptr<NumArray>& keypoints_data) {
    std::vector<Point2f> keypoints;
    
    if (!keypoints_data || keypoints_data->shape.empty()) {
        TX_LOG_WARN("FaceAnalysisModule", "Empty keypoints data");
        return keypoints;
    }
    
    // 假设关键点数据格式：[score1, x1, y1, score2, x2, y2, ...]
    int point_num = keypoints_data->shape[1] / 3; // 每个点3个值：score, x, y
    keypoints.resize(point_num);
    
    float* kp_data = reinterpret_cast<float*>(keypoints_data->data);
    
    for (int i = 0; i < point_num; ++i) {
        float score = kp_data[i * 3 + 0];
        float x = kp_data[i * 3 + 1];
        float y = kp_data[i * 3 + 2];
        
        // 只有置信度足够高的关键点才使用
        if (score >= face_angle_threshold_) {
            keypoints[i] = Point2f(x, y);
        } else {
            keypoints[i] = Point2f(-1, -1); // 无效点
        }
    }
    
    TX_LOG_DEBUG("FaceAnalysisModule", "Parsed %d keypoints", point_num);
    return keypoints;
}

void FaceAnalysisModule::ParseFaceAngle(const std::shared_ptr<NumArray>& angle_data,
                                       float& face_angle_score, float& mouth_score) {
    face_angle_score = 0.0f;
    mouth_score = 0.0f;
    
    if (!angle_data || angle_data->shape.empty()) {
        TX_LOG_WARN("FaceAnalysisModule", "Empty angle data");
        return;
    }
    
    // 假设角度数据格式：[yaw, pitch, roll, mouth_opening, ...]
    float* angle_ptr = reinterpret_cast<float*>(angle_data->data);
    
    if (angle_data->shape[1] >= 4) {
        float yaw = angle_ptr[0];
        float pitch = angle_ptr[1];
        float roll = angle_ptr[2];
        float mouth_opening = angle_ptr[3];
        
        // 计算综合角度分数（简化计算）
        face_angle_score = std::sqrt(yaw * yaw + pitch * pitch + roll * roll) / 90.0f; // 归一化到0-1
        mouth_score = mouth_opening;
        
        TX_LOG_DEBUG("FaceAnalysisModule", "Parsed face angle: yaw=%.2f, pitch=%.2f, roll=%.2f, mouth=%.2f",
                    yaw, pitch, roll, mouth_opening);
    }
}

TXDmsFaceInfo FaceAnalysisModule::ParseFaceAttributes(const std::shared_ptr<NumArray>& attr_data) {
    TXDmsFaceInfo attr_info = {};
    
    if (!attr_data || attr_data->shape.empty()) {
        TX_LOG_WARN("FaceAnalysisModule", "Empty attribute data");
        return attr_info;
    }
    
    // 假设属性数据格式：[mask_score, glass_score, ir_block_score, ...]
    float* attr_ptr = reinterpret_cast<float*>(attr_data->data);
    
    if (attr_data->shape[1] >= 3) {
        float mask_score = attr_ptr[0];
        float glass_score = attr_ptr[1];
        float ir_block_score = attr_ptr[2];
        
        // 根据阈值判断属性
        attr_info.isMask = (mask_score >= face_attr_threshold_) ? 1 : 0;
        attr_info.isGlass = (glass_score >= face_attr_threshold_) ? 1 : 0;
        attr_info.isIRBlock = (ir_block_score >= face_attr_threshold_) ? 1 : 0;
        
        TX_LOG_DEBUG("FaceAnalysisModule", "Parsed face attributes: mask=%.2f->%d, glass=%.2f->%d, ir_block=%.2f->%d",
                    mask_score, attr_info.isMask, glass_score, attr_info.isGlass, ir_block_score, attr_info.isIRBlock);
    }
    
    return attr_info;
}

bool FaceAnalysisModule::ValidateFace(const TXDmsFaceInfo& face_info) {
    // 检查置信度
    if (face_info.score < face_bbox_threshold_) {
        TX_LOG_DEBUG("FaceAnalysisModule", "Face score too low: %.2f < %.2f", face_info.score, face_bbox_threshold_);
        return false;
    }
    
    // 检查人脸尺寸
    int face_height = face_info.ymax - face_info.ymin;
    if (face_height < min_face_height_px_) {
        TX_LOG_DEBUG("FaceAnalysisModule", "Face height too small: %d < %d", face_height, min_face_height_px_);
        return false;
    }
    
    // 检查边界框有效性
    if (face_info.xmin >= face_info.xmax || face_info.ymin >= face_info.ymax) {
        TX_LOG_DEBUG("FaceAnalysisModule", "Invalid face bbox: (%d,%d,%d,%d)",
                    face_info.xmin, face_info.ymin, face_info.xmax, face_info.ymax);
        return false;
    }
    
    return true;
}

bool FaceAnalysisModule::ValidateKeypoints(const std::vector<Point2f>& keypoints) {
    if (keypoints.empty()) {
        return false;
    }
    
    // 检查有效关键点的数量
    int valid_count = 0;
    for (const auto& point : keypoints) {
        if (point.x >= 0 && point.y >= 0) {
            valid_count++;
        }
    }
    
    // 至少需要一半的关键点有效
    bool is_valid = (valid_count >= static_cast<int>(keypoints.size()) / 2);
    
    TX_LOG_DEBUG("FaceAnalysisModule", "Keypoints validation: %d/%zu valid points, result=%s",
                valid_count, keypoints.size(), is_valid ? "valid" : "invalid");
    
    return is_valid;
}

std::shared_ptr<NumArray> FaceAnalysisModule::CreateOutput(void* data, size_t size) {
    if (!data || size == 0) {
        TX_LOG_ERROR("FaceAnalysisModule", "Invalid output data");
        return nullptr;
    }
    
    // 创建一维数组来存储数据
    std::vector<int> shape = {1, static_cast<int>(size)};
    std::shared_ptr<NumArray> output = creat_numarray(shape, NumArray::UINT8);
    
    if (output) {
        std::memcpy(output->data, data, size);
    }
    
    return output;
}

} // namespace tongxing
