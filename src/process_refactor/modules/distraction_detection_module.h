#ifndef __DISTRACTION_DETECTION_MODULE_H__
#define __DISTRACTION_DETECTION_MODULE_H__

#include "cc_module.h"
#include "tx_dms_sdk.h"

namespace tongxing {

/**
 * @brief 分心检测模块
 * 
 * 基于tongxing框架的分心检测模块，专门负责分心相关的算法处理
 * 包括头部姿态分析、视线方向分析、分心行为识别等
 */
class DistractionDetectionModule : public CcModule {
public:
    DistractionDetectionModule();
    ~DistractionDetectionModule() override;

    /**
     * @brief 初始化模块
     * @param config JSON配置
     * @return 0成功，非0失败
     */
    int init(const Json::Value& config) override;

    /**
     * @brief 设置输入数据
     * @param in 输入数据数组 [0]:人脸信息 [1]:眼部信息 [2]:车辆信息
     * @return 0成功，非0失败
     */
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in) override;

    /**
     * @brief 执行分心检测
     * @return 0成功，非0失败
     */
    int execute() override;

    /**
     * @brief 获取输出数量
     * @return 输出数量
     */
    size_t getOutputNum() override;

    /**
     * @brief 获取指定输出
     * @param index 输出索引 [0]:分心检测结果 [1]:头部姿态 [2]:视线方向
     * @return 输出数据
     */
    std::shared_ptr<NumArray> getOutput(int index) override;

private:
    // 配置参数
    float head_pose_threshold_;      // 头部姿态阈值
    float gaze_threshold_;          // 视线方向阈值
    int distraction_speed_threshold_; // 分心检测速度阈值
    float pitch_threshold_;         // pitch角度阈值
    float yaw_threshold_;           // yaw角度阈值
    float roll_threshold_;          // roll角度阈值

    // 输出数据
    std::shared_ptr<NumArray> distraction_result_output_;  // 分心检测结果
    std::shared_ptr<NumArray> head_pose_output_;           // 头部姿态输出
    std::shared_ptr<NumArray> gaze_direction_output_;      // 视线方向输出

    // 内部状态
    struct HeadPoseInfo {
        float pitch;
        float yaw;
        float roll;
        bool valid;
    } current_head_pose_;

    struct GazeInfo {
        float pitch;
        float yaw;
        bool valid;
    } current_gaze_;

    /**
     * @brief 分析头部姿态
     * @param face_info 人脸信息
     * @param face_angle 人脸角度数据
     * @return 头部姿态分析结果
     */
    HeadPoseInfo AnalyzeHeadPose(const TXDmsFaceInfo& face_info,
                                const std::vector<float>& face_angle);

    /**
     * @brief 分析视线方向
     * @param eye_landmarks 眼部关键点
     * @param head_pose 头部姿态
     * @return 视线方向分析结果
     */
    GazeInfo AnalyzeGazeDirection(const std::vector<cv::Point2f>& eye_landmarks,
                                 const HeadPoseInfo& head_pose);

    /**
     * @brief 检测分心行为
     * @param head_pose 头部姿态
     * @param gaze_info 视线信息
     * @param car_info 车辆信息
     * @return 分心检测结果
     */
    TXDistractionType DetectDistraction(const HeadPoseInfo& head_pose,
                                       const GazeInfo& gaze_info,
                                       const TXCarInfo& car_info);

    /**
     * @brief 验证头部姿态有效性
     * @param head_pose 头部姿态
     * @return true有效，false无效
     */
    bool ValidateHeadPose(const HeadPoseInfo& head_pose);

    /**
     * @brief 验证视线信息有效性
     * @param gaze_info 视线信息
     * @return true有效，false无效
     */
    bool ValidateGazeInfo(const GazeInfo& gaze_info);

    /**
     * @brief 计算角度偏差
     * @param current_angle 当前角度
     * @param reference_angle 参考角度
     * @return 角度偏差
     */
    float CalculateAngleDeviation(float current_angle, float reference_angle);

    /**
     * @brief 判断是否超出正常范围
     * @param head_pose 头部姿态
     * @param gaze_info 视线信息
     * @return true超出范围，false在正常范围内
     */
    bool IsOutOfNormalRange(const HeadPoseInfo& head_pose, const GazeInfo& gaze_info);

    /**
     * @brief 分析分心类型
     * @param head_pose 头部姿态
     * @param gaze_info 视线信息
     * @return 具体的分心类型
     */
    TXDistractionType AnalyzeDistractionType(const HeadPoseInfo& head_pose,
                                            const GazeInfo& gaze_info);

    /**
     * @brief 创建输出数据
     * @param data 数据指针
     * @param size 数据大小
     * @return NumArray智能指针
     */
    std::shared_ptr<NumArray> CreateOutput(void* data, size_t size);
};

} // namespace tongxing

// 注册模块到tongxing系统
// 模块注册 - 暂时注释掉，避免编译错误
// REGISTER_CC_MODULE(DistractionDetectionModule, DistractionDetectionModule);

#endif // __DISTRACTION_DETECTION_MODULE_H__
