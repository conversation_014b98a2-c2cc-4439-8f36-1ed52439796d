#ifndef __FATIGUE_DETECTION_MODULE_H__
#define __FATIGUE_DETECTION_MODULE_H__

#include "cc_module.h"
#include "tx_dms_sdk.h"

namespace tongxing {

/**
 * @brief 疲劳检测模块
 * 
 * 基于tongxing框架的疲劳检测模块，专门负责疲劳相关的算法处理
 * 包括眼部疲劳检测、打哈欠检测、疲劳模式识别等
 */
class FatigueDetectionModule : public CcModule {
public:
    FatigueDetectionModule();
    ~FatigueDetectionModule() override;

    /**
     * @brief 初始化模块
     * @param config JSON配置
     * @return 0成功，非0失败
     */
    int init(const Json::Value& config) override;

    /**
     * @brief 设置输入数据
     * @param in 输入数据数组 [0]:人脸信息 [1]:眼部信息 [2]:车辆信息
     * @return 0成功，非0失败
     */
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in) override;

    /**
     * @brief 执行疲劳检测
     * @return 0成功，非0失败
     */
    int execute() override;

    /**
     * @brief 获取输出数量
     * @return 输出数量
     */
    size_t getOutputNum() override;

    /**
     * @brief 获取指定输出
     * @param index 输出索引 [0]:疲劳检测结果 [1]:眼部状态 [2]:嘴部状态
     * @return 输出数据
     */
    std::shared_ptr<NumArray> getOutput(int index) override;

private:
    // 配置参数
    float eye_closure_threshold_;    // 闭眼阈值
    float yawn_threshold_;          // 打哈欠阈值
    int fatigue_speed_threshold_;   // 疲劳检测速度阈值
    float min_eye_opening_;         // 最小睁眼阈值
    int min_eye_x_px_;             // 有效眼睛横坐标最小像素数

    // 输入输出数据
    std::vector<std::shared_ptr<NumArray>> input_;      // 输入数据
    std::shared_ptr<NumArray> fatigue_result_output_;   // 疲劳检测结果
    std::shared_ptr<NumArray> eye_status_output_;       // 眼部状态输出
    std::shared_ptr<NumArray> mouth_status_output_;     // 嘴部状态输出

    // 内部状态
    TXEyeLandmark left_eye_landmark_;
    TXEyeLandmark right_eye_landmark_;

    /**
     * @brief 检测眼部疲劳
     * @param face_info 人脸信息
     * @param eye_landmarks 眼部关键点
     * @param fatigue_score 疲劳分数输出
     * @return 疲劳检测结果
     */
    TXDrowsinessType DetectEyeFatigue(const TXDmsFaceInfo& face_info,
                                     const std::vector<TXPoint2f>& eye_landmarks,
                                     float& fatigue_score);

    /**
     * @brief 检测打哈欠
     * @param mouth_points 嘴部关键点
     * @param mouth_opening_score 嘴部张开分数
     * @return 是否在打哈欠
     */
    bool DetectYawning(const std::vector<cv::Point>& mouth_points,
                      float& mouth_opening_score);

    /**
     * @brief 分析疲劳模式
     * @param eye_fatigue_score 眼部疲劳分数
     * @param yawn_detected 是否检测到打哈欠
     * @param car_info 车辆信息
     * @return 综合疲劳检测结果
     */
    TXDrowsinessType AnalyzeFatiguePattern(float eye_fatigue_score,
                                          bool yawn_detected,
                                          const TXCarInfo& car_info);

    /**
     * @brief 检查眼部有效性
     * @param eye_landmarks 眼部关键点
     * @param is_left_eye 是否为左眼
     * @return true有效，false无效
     */
    bool ValidateEyeLandmarks(const std::vector<cv::Point2f>& eye_landmarks,
                             bool is_left_eye);

    /**
     * @brief 计算眼部开合度
     * @param eye_landmarks 眼部关键点
     * @return 眼部开合度分数
     */
    float CalculateEyeOpenness(const std::vector<cv::Point2f>& eye_landmarks);

    /**
     * @brief 计算嘴部开合度
     * @param mouth_points 嘴部关键点
     * @return 嘴部开合度分数
     */
    float CalculateMouthOpenness(const std::vector<cv::Point>& mouth_points);

    /**
     * @brief 更新眼部关键点信息
     * @param eye_result 眼部检测结果
     * @param fatigue_score 疲劳分数
     */
    void UpdateEyeLandmarks(TXEyeLandmark& eye_result, float fatigue_score);

    /**
     * @brief 创建输出数据
     * @param data 数据指针
     * @param size 数据大小
     * @return NumArray智能指针
     */
    std::shared_ptr<NumArray> CreateOutput(void* data, size_t size);
};

} // namespace tongxing

// 注册模块到tongxing系统
// 模块注册 - 暂时注释掉，避免编译错误
// REGISTER_CC_MODULE(FatigueDetectionModule, FatigueDetectionModule);

#endif // __FATIGUE_DETECTION_MODULE_H__
