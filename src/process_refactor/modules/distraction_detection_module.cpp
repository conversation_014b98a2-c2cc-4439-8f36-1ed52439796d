#include "distraction_detection_module.h"
#include "cc_numarray_tool.h"
#include "CalmCarLog.h"
#include <cstring>
#include <cmath>

namespace tongxing {

DistractionDetectionModule::DistractionDetectionModule() 
    : head_pose_threshold_(25.0f)
    , gaze_threshold_(30.0f)
    , phone_threshold_(0.7f)
    , smoking_threshold_(0.7f) {
    TX_LOG_INFO("DistractionDetectionModule", "DistractionDetectionModule constructed");
}

DistractionDetectionModule::~DistractionDetectionModule() {
    TX_LOG_INFO("DistractionDetectionModule", "DistractionDetectionModule destructed");
}

int DistractionDetectionModule::init(const Json::Value& config) {
    // 从配置中读取参数
    if (config.isMember("head_pose_threshold")) {
        head_pose_threshold_ = config["head_pose_threshold"].asFloat();
    }
    
    if (config.isMember("gaze_threshold")) {
        gaze_threshold_ = config["gaze_threshold"].asFloat();
    }
    
    if (config.isMember("phone_threshold")) {
        phone_threshold_ = config["phone_threshold"].asFloat();
    }
    
    if (config.isMember("smoking_threshold")) {
        smoking_threshold_ = config["smoking_threshold"].asFloat();
    }
    
    TX_LOG_INFO("DistractionDetectionModule", "DistractionDetectionModule initialized with thresholds: pose=%.2f, gaze=%.2f, phone=%.2f, smoking=%.2f",
               head_pose_threshold_, gaze_threshold_, phone_threshold_, smoking_threshold_);
    
    return 0;
}

int DistractionDetectionModule::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}

int DistractionDetectionModule::execute() {
    if (input_.empty()) {
        TX_LOG_ERROR("DistractionDetectionModule", "No input data provided");
        return -1;
    }
    
    // 假设输入数据的格式：
    // input_[0]: 头部姿态数据 (yaw, pitch, roll)
    // input_[1]: 视线方向数据 (gaze_x, gaze_y)
    // input_[2]: 打电话检测数据
    // input_[3]: 抽烟检测数据
    
    // 1. 头部姿态分析
    bool head_distracted = false;
    if (input_.size() > 0) {
        head_distracted = AnalyzeHeadPose(input_[0]);
        head_pose_output_ = CreateOutput(&head_distracted, sizeof(bool));
        TX_LOG_DEBUG("DistractionDetectionModule", "Head pose analyzed: %s", head_distracted ? "distracted" : "normal");
    }
    
    // 2. 视线分析
    bool gaze_distracted = false;
    if (input_.size() > 1) {
        gaze_distracted = AnalyzeGazeDirection(input_[1]);
        gaze_output_ = CreateOutput(&gaze_distracted, sizeof(bool));
        TX_LOG_DEBUG("DistractionDetectionModule", "Gaze analyzed: %s", gaze_distracted ? "distracted" : "normal");
    }
    
    // 3. 打电话检测
    bool phone_detected = false;
    if (input_.size() > 2) {
        phone_detected = AnalyzePhoneUsage(input_[2]);
        phone_output_ = CreateOutput(&phone_detected, sizeof(bool));
        TX_LOG_DEBUG("DistractionDetectionModule", "Phone usage analyzed: %s", phone_detected ? "detected" : "normal");
    }
    
    // 4. 抽烟检测
    bool smoking_detected = false;
    if (input_.size() > 3) {
        smoking_detected = AnalyzeSmokingBehavior(input_[3]);
        smoking_output_ = CreateOutput(&smoking_detected, sizeof(bool));
        TX_LOG_DEBUG("DistractionDetectionModule", "Smoking analyzed: %s", smoking_detected ? "detected" : "normal");
    }
    
    // 5. 综合分心状态判断
    bool distraction_detected = head_distracted || gaze_distracted || phone_detected || smoking_detected;
    distraction_status_output_ = CreateOutput(&distraction_detected, sizeof(bool));
    
    TX_LOG_DEBUG("DistractionDetectionModule", "Distraction detection completed: distraction=%s", 
                distraction_detected ? "detected" : "normal");
    
    return 0;
}

size_t DistractionDetectionModule::getOutputNum() {
    return 5; // 头部姿态、视线、打电话、抽烟、分心状态
}

std::shared_ptr<NumArray> DistractionDetectionModule::getOutput(int index) {
    switch (index) {
        case 0: return head_pose_output_;
        case 1: return gaze_output_;
        case 2: return phone_output_;
        case 3: return smoking_output_;
        case 4: return distraction_status_output_;
        default:
            TX_LOG_ERROR("DistractionDetectionModule", "Invalid output index: %d", index);
            return nullptr;
    }
}

bool DistractionDetectionModule::AnalyzeHeadPose(const std::shared_ptr<NumArray>& pose_data) {
    if (!pose_data || pose_data->shape.empty()) {
        TX_LOG_WARN("DistractionDetectionModule", "Empty pose data");
        return false;
    }
    
    // 假设头部姿态数据格式：[yaw, pitch, roll]
    float* pose_ptr = reinterpret_cast<float*>(pose_data->data);
    
    if (pose_data->shape[1] >= 3) {
        float yaw = pose_ptr[0];
        float pitch = pose_ptr[1];
        float roll = pose_ptr[2];
        
        // 计算头部偏转角度
        float total_angle = std::sqrt(yaw * yaw + pitch * pitch + roll * roll);
        
        // 如果头部偏转角度超过阈值，认为是分心
        bool distracted = (total_angle > head_pose_threshold_);
        
        TX_LOG_DEBUG("DistractionDetectionModule", "Head pose analysis: yaw=%.2f, pitch=%.2f, roll=%.2f, total=%.2f, distracted=%s",
                    yaw, pitch, roll, total_angle, distracted ? "true" : "false");
        
        return distracted;
    }
    
    return false;
}

bool DistractionDetectionModule::AnalyzeGazeDirection(const std::shared_ptr<NumArray>& gaze_data) {
    if (!gaze_data || gaze_data->shape.empty()) {
        TX_LOG_WARN("DistractionDetectionModule", "Empty gaze data");
        return false;
    }
    
    // 假设视线数据格式：[gaze_x, gaze_y]
    float* gaze_ptr = reinterpret_cast<float*>(gaze_data->data);
    
    if (gaze_data->shape[1] >= 2) {
        float gaze_x = gaze_ptr[0];
        float gaze_y = gaze_ptr[1];
        
        // 计算视线偏离角度
        float gaze_angle = std::sqrt(gaze_x * gaze_x + gaze_y * gaze_y);
        
        // 如果视线偏离角度超过阈值，认为是分心
        bool distracted = (gaze_angle > gaze_threshold_);
        
        TX_LOG_DEBUG("DistractionDetectionModule", "Gaze analysis: x=%.2f, y=%.2f, angle=%.2f, distracted=%s",
                    gaze_x, gaze_y, gaze_angle, distracted ? "true" : "false");
        
        return distracted;
    }
    
    return false;
}

bool DistractionDetectionModule::AnalyzePhoneUsage(const std::shared_ptr<NumArray>& phone_data) {
    if (!phone_data || phone_data->shape.empty()) {
        TX_LOG_WARN("DistractionDetectionModule", "Empty phone data");
        return false;
    }
    
    // 假设打电话数据格式：[phone_confidence]
    float* phone_ptr = reinterpret_cast<float*>(phone_data->data);
    
    if (phone_data->shape[1] >= 1) {
        float phone_confidence = phone_ptr[0];
        
        // 如果打电话置信度超过阈值，认为是打电话
        bool phone_detected = (phone_confidence > phone_threshold_);
        
        TX_LOG_DEBUG("DistractionDetectionModule", "Phone analysis: confidence=%.2f, detected=%s",
                    phone_confidence, phone_detected ? "true" : "false");
        
        return phone_detected;
    }
    
    return false;
}

bool DistractionDetectionModule::AnalyzeSmokingBehavior(const std::shared_ptr<NumArray>& smoking_data) {
    if (!smoking_data || smoking_data->shape.empty()) {
        TX_LOG_WARN("DistractionDetectionModule", "Empty smoking data");
        return false;
    }
    
    // 假设抽烟数据格式：[smoking_confidence]
    float* smoking_ptr = reinterpret_cast<float*>(smoking_data->data);
    
    if (smoking_data->shape[1] >= 1) {
        float smoking_confidence = smoking_ptr[0];
        
        // 如果抽烟置信度超过阈值，认为是抽烟
        bool smoking_detected = (smoking_confidence > smoking_threshold_);
        
        TX_LOG_DEBUG("DistractionDetectionModule", "Smoking analysis: confidence=%.2f, detected=%s",
                    smoking_confidence, smoking_detected ? "true" : "false");
        
        return smoking_detected;
    }
    
    return false;
}

std::shared_ptr<NumArray> DistractionDetectionModule::CreateOutput(void* data, size_t size) {
    if (!data || size == 0) {
        TX_LOG_ERROR("DistractionDetectionModule", "Invalid output data");
        return nullptr;
    }
    
    // 创建一维数组来存储数据
    std::vector<int> shape = {1, static_cast<int>(size)};
    std::shared_ptr<NumArray> output = creat_numarray(shape, NumArray::UINT8);
    
    if (output) {
        std::memcpy(output->data, data, size);
    }
    
    return output;
}

} // namespace tongxing
