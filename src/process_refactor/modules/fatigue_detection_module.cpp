#include "fatigue_detection_module.h"
#include "cc_numarray_tool.h"
#include "CalmCarLog.h"
#include <cstring>
#include <algorithm>

namespace tongxing {

FatigueDetectionModule::FatigueDetectionModule()
    : eye_closure_threshold_(0.1f)
    , yawn_threshold_(0.6f)
    , fatigue_speed_threshold_(3)
    , min_eye_opening_(0.2f)
    , min_eye_x_px_(10) {
    TX_LOG_INFO("FatigueDetectionModule", "FatigueDetectionModule constructed");
}

FatigueDetectionModule::~FatigueDetectionModule() {
    TX_LOG_INFO("FatigueDetectionModule", "FatigueDetectionModule destructed");
}

int FatigueDetectionModule::init(const Json::Value& config) {
    // 从配置中读取参数
    if (config.isMember("eye_closure_threshold")) {
        eye_closure_threshold_ = config["eye_closure_threshold"].asFloat();
    }

    if (config.isMember("yawn_threshold")) {
        yawn_threshold_ = config["yawn_threshold"].asFloat();
    }

    if (config.isMember("fatigue_speed_threshold")) {
        fatigue_speed_threshold_ = config["fatigue_speed_threshold"].asInt();
    }

    if (config.isMember("min_eye_opening")) {
        min_eye_opening_ = config["min_eye_opening"].asFloat();
    }

    if (config.isMember("min_eye_x_px")) {
        min_eye_x_px_ = config["min_eye_x_px"].asInt();
    }

    TX_LOG_INFO("FatigueDetectionModule", "FatigueDetectionModule initialized with thresholds: eye=%.2f, yawn=%.2f, speed=%d, min_opening=%.2f",
               eye_closure_threshold_, yawn_threshold_, fatigue_speed_threshold_, min_eye_opening_);

    return 0;
}

int FatigueDetectionModule::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}

int FatigueDetectionModule::execute() {
    if (input_.empty()) {
        TX_LOG_ERROR("FatigueDetectionModule", "No input data provided");
        return -1;
    }

    // 假设输入数据的格式：
    // input_[0]: 眼部状态数据 (来自AI推理)
    // input_[1]: 嘴部状态数据 (来自AI推理)

    // 1. 眼部状态分析
    bool eye_closed = false;
    if (input_.size() > 0) {
        eye_closed = AnalyzeEyeStatus(input_[0]);
        eye_status_output_ = CreateOutput(&eye_closed, sizeof(bool));
        TX_LOG_DEBUG("FatigueDetectionModule", "Eye status analyzed: %s", eye_closed ? "closed" : "open");
    }

    // 2. 打哈欠检测
    bool yawning = false;
    if (input_.size() > 1) {
        yawning = AnalyzeYawnStatus(input_[1]);
        mouth_status_output_ = CreateOutput(&yawning, sizeof(bool));
        TX_LOG_DEBUG("FatigueDetectionModule", "Yawn status analyzed: %s", yawning ? "yawning" : "normal");
    }

    // 3. 综合疲劳状态判断
    bool fatigue_detected = eye_closed || yawning;
    fatigue_result_output_ = CreateOutput(&fatigue_detected, sizeof(bool));

    TX_LOG_DEBUG("FatigueDetectionModule", "Fatigue detection completed: fatigue=%s",
                fatigue_detected ? "detected" : "normal");

    return 0;
}

size_t FatigueDetectionModule::getOutputNum() {
    return 3; // 疲劳结果、眼部状态、嘴部状态
}

std::shared_ptr<NumArray> FatigueDetectionModule::getOutput(int index) {
    switch (index) {
        case 0: return fatigue_result_output_;
        case 1: return eye_status_output_;
        case 2: return mouth_status_output_;
        default:
            TX_LOG_ERROR("FatigueDetectionModule", "Invalid output index: %d", index);
            return nullptr;
    }
}

bool FatigueDetectionModule::AnalyzeEyeStatus(const std::shared_ptr<NumArray>& eye_data) {
    if (!eye_data || eye_data->shape.empty()) {
        TX_LOG_WARN("FatigueDetectionModule", "Empty eye data");
        return false;
    }
    
    // 假设眼部数据格式：[left_eye_openness, right_eye_openness]
    float* eye_ptr = reinterpret_cast<float*>(eye_data->data);
    
    if (eye_data->shape[1] >= 2) {
        float left_eye = eye_ptr[0];
        float right_eye = eye_ptr[1];
        
        // 如果任一眼睛的开合度低于阈值，认为是闭眼
        bool eye_closed = (left_eye < eye_closure_threshold_) || (right_eye < eye_closure_threshold_);
        
        TX_LOG_DEBUG("FatigueDetectionModule", "Eye analysis: left=%.2f, right=%.2f, closed=%s",
                    left_eye, right_eye, eye_closed ? "true" : "false");
        
        return eye_closed;
    }
    
    return false;
}

bool FatigueDetectionModule::AnalyzeYawnStatus(const std::shared_ptr<NumArray>& mouth_data) {
    if (!mouth_data || mouth_data->shape.empty()) {
        TX_LOG_WARN("FatigueDetectionModule", "Empty mouth data");
        return false;
    }
    
    // 假设嘴部数据格式：[mouth_opening_ratio]
    float* mouth_ptr = reinterpret_cast<float*>(mouth_data->data);
    
    if (mouth_data->shape[1] >= 1) {
        float mouth_opening = mouth_ptr[0];
        
        // 如果嘴部开合度超过阈值，认为是打哈欠
        bool yawning = (mouth_opening > yawn_threshold_);
        
        TX_LOG_DEBUG("FatigueDetectionModule", "Yawn analysis: mouth_opening=%.2f, yawning=%s",
                    mouth_opening, yawning ? "true" : "false");
        
        return yawning;
    }
    
    return false;
}



std::shared_ptr<NumArray> FatigueDetectionModule::CreateOutput(void* data, size_t size) {
    if (!data || size == 0) {
        TX_LOG_ERROR("FatigueDetectionModule", "Invalid output data");
        return nullptr;
    }
    
    // 创建一维数组来存储数据
    std::vector<int> shape = {1, static_cast<int>(size)};
    std::shared_ptr<NumArray> output = creat_numarray(shape, NumArray::UINT8);
    
    if (output) {
        std::memcpy(output->data, data, size);
    }
    
    return output;
}

} // namespace tongxing
