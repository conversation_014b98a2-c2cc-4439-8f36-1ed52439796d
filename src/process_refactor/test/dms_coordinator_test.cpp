#include <gtest/gtest.h>
#include <memory>
#include <fstream>
#include "../coordinators/dms_coordinator.h"

using namespace tongxing;

class DmsCoordinatorTest : public ::testing::Test {
protected:
    void SetUp() override {
        coordinator_ = std::make_unique<DmsCoordinator>();
        
        // 创建测试配置文件
        test_config_file_ = "test_dms_config.json";
        CreateTestConfigFile();
        
        // 创建测试图像数据
        test_image_width_ = 640;
        test_image_height_ = 480;
        test_image_size_ = test_image_width_ * test_image_height_;
        test_image_data_ = new char[test_image_size_];
        
        // 填充测试数据
        for (int i = 0; i < test_image_size_; ++i) {
            test_image_data_[i] = static_cast<char>(i % 256);
        }
        
        // 创建测试图像信息
        memset(&test_image_info_, 0, sizeof(test_image_info_));
        test_image_info_.data = test_image_data_;
        test_image_info_.width = test_image_width_;
        test_image_info_.height = test_image_height_;
        test_image_info_.dataLen = test_image_size_;
        test_image_info_.dataType = GRAY;
        
        // 创建测试车辆信息
        memset(&test_car_info_, 0, sizeof(test_car_info_));
        test_car_info_.speed = 60;
        test_car_info_.gear = FORWARD;
    }

    void TearDown() override {
        coordinator_.reset();
        delete[] test_image_data_;
        std::remove(test_config_file_.c_str());
    }

    void CreateTestConfigFile() {
        std::ofstream file(test_config_file_);
        file << R"({
    "pipeline": {
        "name": "DMS_Test_Pipeline",
        "version": "1.0.0"
    },
    "face_analysis": {
        "face_bbox": {
            "threshold": 0.5
        },
        "keypoints": {
            "threshold": 0.35
        }
    },
    "fatigue_detection": {
        "enabled": true,
        "eye_closure_threshold": 0.1,
        "yawn_threshold": 0.6,
        "analysis_window_ms": 30000
    },
    "distraction_detection": {
        "enabled": true,
        "head_pose": {
            "pitch_threshold": 25.0,
            "yaw_threshold": 35.0,
            "roll_threshold": 20.0
        }
    },
    "state_manager": {
        "activation": {
            "timeout_ms": 300000
        }
    },
    "data_adapter": {
        "roi_settings": {
            "default_left_top": [100, 100],
            "default_right_bottom": [540, 380]
        }
    }
})";
        file.close();
    }

    std::unique_ptr<DmsCoordinator> coordinator_;
    std::string test_config_file_;
    
    char* test_image_data_;
    int test_image_width_;
    int test_image_height_;
    int test_image_size_;
    TXImageInfo test_image_info_;
    TXCarInfo test_car_info_;
};

// 测试DmsCoordinator初始化
TEST_F(DmsCoordinatorTest, InitializationTest) {
    EXPECT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
}

// 测试无效配置文件路径
TEST_F(DmsCoordinatorTest, InvalidConfigFileTest) {
    EXPECT_NE(coordinator_->Init("non_existent_config.json"), 0);
}

// 测试空配置文件路径
TEST_F(DmsCoordinatorTest, NullConfigFileTest) {
    EXPECT_NE(coordinator_->Init(nullptr), 0);
}

// 测试正常帧处理
TEST_F(DmsCoordinatorTest, NormalFrameProcessingTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    TXDmsResult result;
    EXPECT_EQ(coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, result), 0);
    
    // 验证结果基本字段
    // EXPECT_GT(result.timestamp, 0);  // TXDmsResult没有timestamp字段
    // EXPECT_EQ(result.speed, test_car_info_.speed);  // TXDmsResult没有speed字段
    // EXPECT_EQ(result.gear, test_car_info_.gear);    // TXDmsResult没有gear字段

    // 验证实际存在的字段
    EXPECT_NE(result.drowsiness_status, -1);
    EXPECT_NE(result.distraction_status, -1);
}

// 测试无效输入处理
TEST_F(DmsCoordinatorTest, InvalidInputTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    TXDmsResult result;
    
    // 测试空图像信息
    EXPECT_NE(coordinator_->ProcessFrame(nullptr, &test_car_info_, result), 0);
    
    // 测试空车辆信息
    EXPECT_NE(coordinator_->ProcessFrame(&test_image_info_, nullptr, result), 0);
    
    // 测试无效图像数据
    TXImageInfo invalid_image = test_image_info_;
    invalid_image.data = nullptr;
    EXPECT_NE(coordinator_->ProcessFrame(&invalid_image, &test_car_info_, result), 0);
}

// 测试疲劳检测使能控制
TEST_F(DmsCoordinatorTest, FatigueEnabledControlTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    // 测试默认状态
    EXPECT_TRUE(coordinator_->GetFatigueEnabled());
    
    // 测试禁用疲劳检测
    EXPECT_EQ(coordinator_->SetFatigueEnabled(false), 0);
    EXPECT_FALSE(coordinator_->GetFatigueEnabled());
    
    // 测试启用疲劳检测
    EXPECT_EQ(coordinator_->SetFatigueEnabled(true), 0);
    EXPECT_TRUE(coordinator_->GetFatigueEnabled());
}

// 测试分心检测使能控制
TEST_F(DmsCoordinatorTest, DistractionEnabledControlTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    // 测试默认状态
    EXPECT_TRUE(coordinator_->GetDistractionEnabled());
    
    // 测试禁用分心检测
    EXPECT_EQ(coordinator_->SetDistractionEnabled(false), 0);
    EXPECT_FALSE(coordinator_->GetDistractionEnabled());
    
    // 测试启用分心检测
    EXPECT_EQ(coordinator_->SetDistractionEnabled(true), 0);
    EXPECT_TRUE(coordinator_->GetDistractionEnabled());
}

// 测试报警状态重置
TEST_F(DmsCoordinatorTest, AlarmStateResetTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    EXPECT_EQ(coordinator_->ResetAlarmState(), 0);
}

// 测试报警确认
TEST_F(DmsCoordinatorTest, AlarmAcknowledgedTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    EXPECT_EQ(coordinator_->SetAlarmAcknowledged(), 0);
}

// 测试疲劳信息获取
TEST_F(DmsCoordinatorTest, GetFatigueInfoTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    // 先处理一帧数据
    TXDmsResult result;
    EXPECT_EQ(coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, result), 0);
    
    // 获取疲劳信息
    tx_tired tired_info;
    EXPECT_NO_THROW(coordinator_->GetFatigueInfo(tired_info));
    
    // 验证信息有效性
    EXPECT_GE(tired_info.total_eye_count, 0);
    EXPECT_GE(tired_info.total_mouth_count, 0);
}

// 测试分心信息获取
TEST_F(DmsCoordinatorTest, GetDistractionInfoTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    // 先处理一帧数据
    TXDmsResult result;
    EXPECT_EQ(coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, result), 0);
    
    // 获取分心信息
    internal_analysis_distraction_info distraction_info;
    EXPECT_NO_THROW(coordinator_->GetDistractionInfo(distraction_info));
}

// 测试分心原因获取
TEST_F(DmsCoordinatorTest, GetDistractionReasonTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    std::string reason = coordinator_->GetDistractionReason();
    EXPECT_FALSE(reason.empty());
}

// 测试分心参数获取
TEST_F(DmsCoordinatorTest, GetDistractionParametersTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    std::string params = coordinator_->GetDistractionParameters();
    EXPECT_FALSE(params.empty());
    EXPECT_NE(params.find("头部姿态阈值"), std::string::npos);
}

// 测试未初始化状态下的操作
TEST_F(DmsCoordinatorTest, UninitializedOperationsTest) {
    TXDmsResult result;
    
    // 未初始化时处理帧应该失败
    EXPECT_NE(coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, result), 0);
    
    // 未初始化时重置报警状态应该失败
    EXPECT_NE(coordinator_->ResetAlarmState(), 0);
    
    // 未初始化时设置报警确认应该失败
    EXPECT_NE(coordinator_->SetAlarmAcknowledged(), 0);
}

// 测试多帧连续处理
TEST_F(DmsCoordinatorTest, MultipleFrameProcessingTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    // 连续处理多帧
    for (int i = 0; i < 5; ++i) {
        TXDmsResult result;
        EXPECT_EQ(coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, result), 0);
        
        // 验证时间戳递增
        if (i > 0) {
            // 时间戳应该在合理范围内
            // EXPECT_GT(result.timestamp, 0);  // TXDmsResult没有timestamp字段
        }
    }
}

// 测试不同车速下的处理
TEST_F(DmsCoordinatorTest, DifferentSpeedProcessingTest) {
    ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
    
    // 测试低速
    test_car_info_.speed = 10;
    TXDmsResult low_speed_result;
    EXPECT_EQ(coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, low_speed_result), 0);
    // EXPECT_EQ(low_speed_result.speed, 10);  // TXDmsResult没有speed字段
    
    // 测试高速
    test_car_info_.speed = 120;
    TXDmsResult high_speed_result;
    EXPECT_EQ(coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, high_speed_result), 0);
    // EXPECT_EQ(high_speed_result.speed, 120);  // TXDmsResult没有speed字段
}
