#include <gtest/gtest.h>
#include <memory>
#include <vector>
#include <fstream>
#include <cstdio>
#include <cstring>
#include "../coordinators/dms_coordinator.h"
#include "CalmCarLog.h"

using namespace tongxing;

/**
 * @brief 端到端集成测试类
 * 测试完整的DMS处理流水线
 */
class IntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建测试配置文件
        CreateTestConfig();
        
        // 初始化DmsCoordinator
        coordinator_ = std::make_unique<DmsCoordinator>();
        ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
        
        // 创建测试图像数据
        CreateTestImageData();
        
        // 创建测试车辆信息
        CreateTestCarInfo();
    }

    void TearDown() override {
        coordinator_.reset();
        // 清理测试文件
        if (!test_config_file_.empty()) {
            std::remove(test_config_file_.c_str());
        }
    }

    void CreateTestConfig() {
        // 创建临时配置文件
        test_config_file_ = "/tmp/test_dms_config.json";
        
        Json::Value config;
        config["pipeline"]["enabled"] = true;
        config["pipeline"]["max_fps"] = 30;
        
        // 状态管理器配置
        config["state_manager"]["algorithm_timeout_ms"] = 5000;
        config["state_manager"]["system_check_interval_ms"] = 1000;
        
        // 数据适配器配置
        config["data_adapter"]["input_width"] = 640;
        config["data_adapter"]["input_height"] = 480;
        config["data_adapter"]["roi_enabled"] = true;
        
        // 人脸分析配置
        config["face_analysis"]["face_detection_threshold"] = 0.5f;
        config["face_analysis"]["keypoints_threshold"] = 0.3f;
        
        // 疲劳检测配置
        config["fatigue_detection"]["eye_closure_threshold"] = 0.1f;
        config["fatigue_detection"]["yawn_threshold"] = 0.6f;
        config["fatigue_detection"]["analysis_window_ms"] = 30000;
        
        // 分心检测配置
        config["distraction_detection"]["head_pose_threshold"] = 30.0f;
        config["distraction_detection"]["gaze_threshold"] = 25.0f;
        config["distraction_detection"]["analysis_window_ms"] = 10000;
        
        // 写入文件
        std::ofstream file(test_config_file_);
        Json::StreamWriterBuilder builder;
        std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
        writer->write(config, &file);
    }

    void CreateTestImageData() {
        // 创建640x480的测试图像（不使用OpenCV）
        const int width = 640;
        const int height = 480;

        test_image_info_.dataType = GRAY;
        test_image_info_.width = width;
        test_image_info_.height = height;
        test_image_info_.stride = width;
        test_image_info_.dataLen = width * height;

        // 分配内存并创建简单的测试图像
        test_image_data_.resize(test_image_info_.dataLen);

        // 填充背景为64，中间区域为128（模拟人脸）
        memset(test_image_data_.data(), 64, test_image_info_.dataLen);

        // 在中间绘制一个矩形区域（模拟人脸）
        for (int y = 150; y < 430; ++y) {
            for (int x = 200; x < 440; ++x) {
                if (y >= 0 && y < height && x >= 0 && x < width) {
                    test_image_data_[y * width + x] = 128;
                }
            }
        }

        test_image_info_.data = reinterpret_cast<char*>(test_image_data_.data());
    }

    void CreateTestCarInfo() {
        memset(&test_car_info_, 0, sizeof(test_car_info_));
        test_car_info_.speed = 60;  // 60 km/h
        test_car_info_.gear = FORWARD;
        test_car_info_.turn_light = TURN_OFF;
        test_car_info_.mask = TX_CAR_INFO_MASK_SPEED | TX_CAR_INFO_MASK_GEARPOSITION;
    }

protected:
    std::unique_ptr<DmsCoordinator> coordinator_;
    std::string test_config_file_;
    TXImageInfo test_image_info_;
    std::vector<uint8_t> test_image_data_;
    TXCarInfo test_car_info_;
};

// 测试基本的处理流水线
TEST_F(IntegrationTest, BasicProcessingPipelineTest) {
    TXDmsResult result;
    long timestamp = 1000000;  // 1秒
    
    // 执行处理
    int ret = coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, result, timestamp);
    EXPECT_EQ(ret, 0);
    
    // 验证基本结果
    EXPECT_NE(result.drowsiness_status, -1);
    EXPECT_NE(result.distraction_status, -1);
    EXPECT_EQ(result.system_status, SYSTEM_NORMAL);
    EXPECT_EQ(result.camera_status, Camera_Normal);
    EXPECT_EQ(result.algorithm_status, ALGORITHM_NORMAL);
    
    // 验证人脸信息
    EXPECT_GE(result.face_info.score, 0.0f);
    EXPECT_LE(result.face_info.score, 1.0f);
}

// 测试多帧连续处理
TEST_F(IntegrationTest, MultiFrameProcessingTest) {
    const int frame_count = 10;
    std::vector<TXDmsResult> results(frame_count);
    
    for (int i = 0; i < frame_count; ++i) {
        long timestamp = 1000000 + i * 33333;  // 30fps间隔
        
        int ret = coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, results[i], timestamp);
        EXPECT_EQ(ret, 0);
        
        // 验证每帧结果的基本有效性
        EXPECT_NE(results[i].drowsiness_status, -1);
        EXPECT_NE(results[i].distraction_status, -1);
    }
    
    // 验证结果的连续性（不应该有剧烈变化）
    for (int i = 1; i < frame_count; ++i) {
        // 系统状态应该保持稳定
        EXPECT_EQ(results[i].system_status, results[i-1].system_status);
        EXPECT_EQ(results[i].camera_status, results[i-1].camera_status);
    }
}

// 测试不同车速下的处理
TEST_F(IntegrationTest, DifferentSpeedProcessingTest) {
    TXDmsResult low_speed_result, high_speed_result;
    long timestamp = 1000000;
    
    // 低速测试 (10 km/h)
    test_car_info_.speed = 10;
    int ret1 = coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, low_speed_result, timestamp);
    EXPECT_EQ(ret1, 0);
    
    // 高速测试 (120 km/h)
    test_car_info_.speed = 120;
    timestamp += 1000000;  // 1秒后
    int ret2 = coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, high_speed_result, timestamp);
    EXPECT_EQ(ret2, 0);
    
    // 验证速度对算法的影响
    // 在实际实现中，低速时某些报警可能被抑制
    EXPECT_NE(low_speed_result.drowsiness_status, -1);
    EXPECT_NE(high_speed_result.drowsiness_status, -1);
}

// 测试状态管理功能
TEST_F(IntegrationTest, StateManagementTest) {
    // 测试疲劳检测使能/禁用
    EXPECT_TRUE(coordinator_->GetFatigueEnabled());
    
    EXPECT_EQ(coordinator_->SetFatigueEnabled(false), 0);
    EXPECT_FALSE(coordinator_->GetFatigueEnabled());
    
    EXPECT_EQ(coordinator_->SetFatigueEnabled(true), 0);
    EXPECT_TRUE(coordinator_->GetFatigueEnabled());
    
    // 测试分心检测使能/禁用
    EXPECT_TRUE(coordinator_->GetDistractionEnabled());
    
    EXPECT_EQ(coordinator_->SetDistractionEnabled(false), 0);
    EXPECT_FALSE(coordinator_->GetDistractionEnabled());
    
    EXPECT_EQ(coordinator_->SetDistractionEnabled(true), 0);
    EXPECT_TRUE(coordinator_->GetDistractionEnabled());
}

// 测试报警状态重置
TEST_F(IntegrationTest, AlarmResetTest) {
    TXDmsResult result;
    long timestamp = 1000000;
    
    // 先处理一帧
    int ret = coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, result, timestamp);
    EXPECT_EQ(ret, 0);
    
    // 重置报警状态
    EXPECT_EQ(coordinator_->ResetAlarmState(), 0);
    
    // 设置报警确认
    EXPECT_EQ(coordinator_->SetAlarmAcknowledged(), 0);
    
    // 再次处理应该正常
    timestamp += 1000000;
    ret = coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, result, timestamp);
    EXPECT_EQ(ret, 0);
}

// 测试统计信息获取
TEST_F(IntegrationTest, StatisticsRetrievalTest) {
    // 先处理几帧以产生统计数据
    for (int i = 0; i < 5; ++i) {
        TXDmsResult result;
        long timestamp = 1000000 + i * 33333;
        coordinator_->ProcessFrame(&test_image_info_, &test_car_info_, result, timestamp);
    }
    
    // 获取疲劳统计信息
    tx_tired tired_info;
    coordinator_->GetFatigueInfo(tired_info);
    
    // 验证统计信息的基本有效性
    EXPECT_GE(tired_info.total_eye_count, 0);
    EXPECT_GE(tired_info.total_mouth_count, 0);
    
    // 获取分心统计信息
    internal_analysis_distraction_info distraction_info;
    coordinator_->GetDistractionInfo(distraction_info);
    
    // 验证分心统计信息
    EXPECT_GE(distraction_info.time_gap, 0);
    EXPECT_GE(distraction_info.distraction_continue_time, 0);
}

// 测试错误处理
TEST_F(IntegrationTest, ErrorHandlingTest) {
    TXDmsResult result;
    long timestamp = 1000000;
    
    // 测试空指针输入
    int ret = coordinator_->ProcessFrame(nullptr, &test_car_info_, result, timestamp);
    EXPECT_NE(ret, 0);  // 应该返回错误
    
    ret = coordinator_->ProcessFrame(&test_image_info_, nullptr, result, timestamp);
    EXPECT_NE(ret, 0);  // 应该返回错误
    
    // 测试无效图像数据
    TXImageInfo invalid_image = test_image_info_;
    invalid_image.data = nullptr;
    ret = coordinator_->ProcessFrame(&invalid_image, &test_car_info_, result, timestamp);
    EXPECT_NE(ret, 0);  // 应该返回错误
}
