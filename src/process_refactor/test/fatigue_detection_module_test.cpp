#include <gtest/gtest.h>
#include <memory>
#include "../modules/fatigue_detection_module.h"
#include "cc_numarray_tool.h"

using namespace tongxing;

class FatigueDetectionModuleTest : public ::testing::Test {
protected:
    void SetUp() override {
        module_ = std::make_unique<FatigueDetectionModule>();
        
        // 创建测试配置
        test_config_["eye_closure_threshold"] = 0.1f;
        test_config_["mouth_opening_threshold"] = 0.3f;
        test_config_["yawn_threshold"] = 0.6f;
        test_config_["fatigue_blink_duration_ms"] = 500;
        test_config_["long_closure_duration_ms"] = 2000;
        test_config_["yawn_duration_ms"] = 1000;
        test_config_["fatigue_blink_count_threshold"] = 2;
        test_config_["long_closure_count_threshold"] = 1;
        test_config_["yawn_count_threshold"] = 2;
        test_config_["analysis_window_ms"] = 30000;
        test_config_["max_history_frames"] = 300;
    }

    void TearDown() override {
        module_.reset();
    }

    std::shared_ptr<NumArray> CreateEyeData(float left_openness, float right_openness, 
                                           float left_score = 1.0f, float right_score = 1.0f) {
        std::vector<int> shape = {1, 4};
        std::shared_ptr<NumArray> eye_data = creat_numarray(shape, NumArray::FLOAT32);
        
        float* data = reinterpret_cast<float*>(eye_data->data);
        data[0] = left_openness;
        data[1] = right_openness;
        data[2] = left_score;
        data[3] = right_score;
        
        return eye_data;
    }

    std::shared_ptr<NumArray> CreateMouthData(float opening_ratio, float mouth_score = 1.0f) {
        std::vector<int> shape = {1, 2};
        std::shared_ptr<NumArray> mouth_data = creat_numarray(shape, NumArray::FLOAT32);
        
        float* data = reinterpret_cast<float*>(mouth_data->data);
        data[0] = opening_ratio;
        data[1] = mouth_score;
        
        return mouth_data;
    }

    std::shared_ptr<NumArray> CreateTimestampData(long timestamp) {
        std::vector<int> shape = {1, 1};
        std::shared_ptr<NumArray> timestamp_data = creat_numarray(shape, NumArray::INT64);
        
        long* data = reinterpret_cast<long*>(timestamp_data->data);
        data[0] = timestamp;
        
        return timestamp_data;
    }

    std::unique_ptr<FatigueDetectionModule> module_;
    Json::Value test_config_;
};

// 测试模块初始化
TEST_F(FatigueDetectionModuleTest, InitializationTest) {
    EXPECT_EQ(module_->init(test_config_), 0);
}

// 测试空配置初始化
TEST_F(FatigueDetectionModuleTest, EmptyConfigInitTest) {
    Json::Value empty_config;
    EXPECT_EQ(module_->init(empty_config), 0);
}

// 测试输入设置
TEST_F(FatigueDetectionModuleTest, SetInputTest) {
    ASSERT_EQ(module_->init(test_config_), 0);
    
    std::vector<std::shared_ptr<NumArray>> input;
    input.push_back(nullptr);  // 人脸信息
    input.push_back(CreateEyeData(0.8f, 0.7f));  // 眼部数据
    input.push_back(CreateMouthData(0.2f));      // 嘴部数据
    input.push_back(CreateTimestampData(1000));  // 时间戳
    
    EXPECT_EQ(module_->setInput(input), 0);
}

// 测试正常状态检测
TEST_F(FatigueDetectionModuleTest, NormalStateDetectionTest) {
    ASSERT_EQ(module_->init(test_config_), 0);
    
    std::vector<std::shared_ptr<NumArray>> input;
    input.push_back(nullptr);  // 人脸信息
    input.push_back(CreateEyeData(0.8f, 0.7f));  // 眼睛睁开
    input.push_back(CreateMouthData(0.2f));      // 嘴巴闭合
    input.push_back(CreateTimestampData(1000));  // 时间戳
    
    EXPECT_EQ(module_->setInput(input), 0);
    EXPECT_EQ(module_->execute(), 0);
    
    // 验证输出数量
    EXPECT_EQ(module_->getOutputNum(), 3);
    
    // 验证输出不为空
    EXPECT_NE(module_->getOutput(0), nullptr);  // 疲劳结果
    EXPECT_NE(module_->getOutput(1), nullptr);  // 眼部状态
    EXPECT_NE(module_->getOutput(2), nullptr);  // 嘴部状态
}

// 测试闭眼检测
TEST_F(FatigueDetectionModuleTest, EyeClosureDetectionTest) {
    ASSERT_EQ(module_->init(test_config_), 0);
    
    std::vector<std::shared_ptr<NumArray>> input;
    input.push_back(nullptr);  // 人脸信息
    input.push_back(CreateEyeData(0.05f, 0.08f));  // 眼睛闭合
    input.push_back(CreateMouthData(0.2f));        // 嘴巴闭合
    input.push_back(CreateTimestampData(1000));    // 时间戳
    
    EXPECT_EQ(module_->setInput(input), 0);
    EXPECT_EQ(module_->execute(), 0);
    
    // 获取眼部状态输出
    std::shared_ptr<NumArray> eye_output = module_->getOutput(1);
    ASSERT_NE(eye_output, nullptr);
    
    EyeState* eye_state = reinterpret_cast<EyeState*>(eye_output->data);
    EXPECT_TRUE(eye_state->is_closed);
    EXPECT_TRUE(eye_state->is_valid);
}

// 测试打哈欠检测
TEST_F(FatigueDetectionModuleTest, YawnDetectionTest) {
    ASSERT_EQ(module_->init(test_config_), 0);
    
    std::vector<std::shared_ptr<NumArray>> input;
    input.push_back(nullptr);  // 人脸信息
    input.push_back(CreateEyeData(0.8f, 0.7f));  // 眼睛睁开
    input.push_back(CreateMouthData(0.7f));      // 嘴巴大张（打哈欠）
    input.push_back(CreateTimestampData(1000));  // 时间戳
    
    EXPECT_EQ(module_->setInput(input), 0);
    EXPECT_EQ(module_->execute(), 0);
    
    // 获取嘴部状态输出
    std::shared_ptr<NumArray> mouth_output = module_->getOutput(2);
    ASSERT_NE(mouth_output, nullptr);
    
    MouthState* mouth_state = reinterpret_cast<MouthState*>(mouth_output->data);
    EXPECT_TRUE(mouth_state->is_yawning);
    EXPECT_TRUE(mouth_state->is_valid);
}

// 测试无效输入处理
TEST_F(FatigueDetectionModuleTest, InvalidInputTest) {
    ASSERT_EQ(module_->init(test_config_), 0);
    
    // 测试空输入
    std::vector<std::shared_ptr<NumArray>> empty_input;
    EXPECT_EQ(module_->setInput(empty_input), 0);
    EXPECT_NE(module_->execute(), 0);  // 应该返回错误
}

// 测试无效输出索引
TEST_F(FatigueDetectionModuleTest, InvalidOutputIndexTest) {
    ASSERT_EQ(module_->init(test_config_), 0);
    
    EXPECT_EQ(module_->getOutput(-1), nullptr);
    EXPECT_EQ(module_->getOutput(3), nullptr);
    EXPECT_EQ(module_->getOutput(100), nullptr);
}

// 测试疲劳状态重置
TEST_F(FatigueDetectionModuleTest, ResetFatigueStateTest) {
    ASSERT_EQ(module_->init(test_config_), 0);
    
    // 先执行一些检测
    std::vector<std::shared_ptr<NumArray>> input;
    input.push_back(nullptr);
    input.push_back(CreateEyeData(0.05f, 0.08f));  // 闭眼
    input.push_back(CreateMouthData(0.7f));        // 打哈欠
    input.push_back(CreateTimestampData(1000));
    
    EXPECT_EQ(module_->setInput(input), 0);
    EXPECT_EQ(module_->execute(), 0);
    
    // 重置状态
    EXPECT_NO_THROW(module_->ResetFatigueState());
}

// 测试疲劳信息获取
TEST_F(FatigueDetectionModuleTest, GetFatigueInfoTest) {
    ASSERT_EQ(module_->init(test_config_), 0);
    
    // 执行一些检测
    std::vector<std::shared_ptr<NumArray>> input;
    input.push_back(nullptr);
    input.push_back(CreateEyeData(0.05f, 0.08f));  // 闭眼
    input.push_back(CreateMouthData(0.7f));        // 打哈欠
    input.push_back(CreateTimestampData(1000));
    
    EXPECT_EQ(module_->setInput(input), 0);
    EXPECT_EQ(module_->execute(), 0);
    
    // 获取疲劳信息
    tx_tired tired_info;
    EXPECT_NO_THROW(module_->GetFatigueInfo(tired_info));
    
    // 验证信息有效性
    EXPECT_GE(tired_info.total_eye_count, 0);
    EXPECT_GE(tired_info.total_mouth_count, 0);
}

// 测试配置参数读取
TEST_F(FatigueDetectionModuleTest, ConfigParametersTest) {
    // 修改配置参数
    test_config_["eye_closure_threshold"] = 0.2f;
    test_config_["yawn_threshold"] = 0.8f;
    test_config_["analysis_window_ms"] = 60000;
    
    ASSERT_EQ(module_->init(test_config_), 0);
    
    // 验证参数被正确读取（通过行为验证）
    std::vector<std::shared_ptr<NumArray>> input;
    input.push_back(nullptr);
    input.push_back(CreateEyeData(0.25f, 0.28f));  // 在新阈值0.2下应该不算闭眼
    input.push_back(CreateMouthData(0.7f));        // 在新阈值下应该不算打哈欠
    input.push_back(CreateTimestampData(1000));
    
    EXPECT_EQ(module_->setInput(input), 0);
    EXPECT_EQ(module_->execute(), 0);
    
    // 验证检测结果符合新阈值
    std::shared_ptr<NumArray> eye_output = module_->getOutput(1);
    ASSERT_NE(eye_output, nullptr);
    
    EyeState* eye_state = reinterpret_cast<EyeState*>(eye_output->data);
    EXPECT_FALSE(eye_state->is_closed);  // 在新阈值下不应该算闭眼
    
    std::shared_ptr<NumArray> mouth_output = module_->getOutput(2);
    ASSERT_NE(mouth_output, nullptr);
    
    MouthState* mouth_state = reinterpret_cast<MouthState*>(mouth_output->data);
    EXPECT_FALSE(mouth_state->is_yawning);  // 在新阈值下不应该算打哈欠
}
