#ifndef __SIMPLE_NUMARRAY_TOOL_H__
#define __SIMPLE_NUMARRAY_TOOL_H__

#include <memory>
#include <vector>
#include <cstring>
#include <cstdint>

namespace tongxing {

/**
 * @brief 简化的BlobData类，用于内存管理
 */
class SimpleBlobData {
public:
    SimpleBlobData() : pu8VirAddr(nullptr), u32Size(0) {}
    
    ~SimpleBlobData() {
        if (pu8VirAddr) {
            delete[] pu8VirAddr;
            pu8VirAddr = nullptr;
        }
    }
    
    int init(size_t size) {
        if (size == 0) return -1;
        
        try {
            pu8VirAddr = new uint8_t[size];
            u32Size = size;
            std::memset(pu8VirAddr, 0, size);
            return 0;
        } catch (const std::bad_alloc& e) {
            return -1;
        }
    }
    
    uint8_t* pu8VirAddr;
    uint32_t u32Size;
};

/**
 * @brief 简化的NumArray类
 */
class NumArray {
public:
    enum DataType {
        FLOAT32 = 0,
        FLOAT64,
        INT8,
        INT16,
        INT32,
        INT64,
        UINT8,
        UINT16,
        UINT32,
        UINT64,
        BOOL,
    };
    
    NumArray() : data(nullptr), word_size(0), type(UINT8) {}
    
    ~NumArray() = default;
    
    std::vector<int> shape;                         // 数据形状
    void* data;                                     // 数据指针
    std::shared_ptr<SimpleBlobData> data_blob_ptr;  // 内存管理
    size_t word_size;                               // 单个元素大小
    DataType type;                                  // 数据类型
};

/**
 * @brief 获取数据类型的字节大小
 */
inline size_t GetNumArrayTypeWordSize(NumArray::DataType type) {
    static const size_t type_size_map[] = {4, 8, 1, 2, 4, 8, 1, 2, 4, 8, 1};
    return type_size_map[static_cast<int>(type)];
}

/**
 * @brief 创建NumArray对象
 * @param shape 数据形状
 * @param type 数据类型
 * @return NumArray智能指针
 */
std::shared_ptr<NumArray> creat_numarray(const std::vector<int>& shape, NumArray::DataType type);

} // namespace tongxing

#endif // __SIMPLE_NUMARRAY_TOOL_H__
