#include <gtest/gtest.h>
#include <chrono>
#include <memory>
#include <vector>
#include <fstream>
#include <cstring>
#include <thread>
#include <atomic>
#include <sys/resource.h>
#include <unistd.h>
#include "../coordinators/dms_coordinator.h"
#include "CalmCarLog.h"

using namespace tongxing;
using namespace std::chrono;

/**
 * @brief 性能统计结构
 */
struct PerformanceStats {
    double avg_processing_time_ms;
    double min_processing_time_ms;
    double max_processing_time_ms;
    double fps;
    size_t peak_memory_kb;
    size_t avg_memory_kb;
    double cpu_usage_percent;
    
    PerformanceStats() : avg_processing_time_ms(0), min_processing_time_ms(0), 
                        max_processing_time_ms(0), fps(0), peak_memory_kb(0), 
                        avg_memory_kb(0), cpu_usage_percent(0) {}
};

/**
 * @brief 性能基准测试类
 */
class PerformanceBenchmark : public ::testing::Test {
protected:
    struct TestImageData {
        TXImageInfo info;
        std::vector<uint8_t> data;
    };

    void SetUp() override {
        // 创建测试配置
        CreateTestConfig();
        
        // 初始化DmsCoordinator
        coordinator_ = std::make_unique<DmsCoordinator>();
        ASSERT_EQ(coordinator_->Init(test_config_file_.c_str()), 0);
        
        // 创建测试数据
        CreateTestData();
    }

    void TearDown() override {
        coordinator_.reset();
        if (!test_config_file_.empty()) {
            std::remove(test_config_file_.c_str());
        }
    }

    void CreateTestConfig() {
        test_config_file_ = "/tmp/perf_test_config.json";
        
        Json::Value config;
        config["pipeline"]["enabled"] = true;
        config["pipeline"]["max_fps"] = 30;
        config["state_manager"]["algorithm_timeout_ms"] = 5000;
        config["data_adapter"]["input_width"] = 640;
        config["data_adapter"]["input_height"] = 480;
        config["face_analysis"]["face_detection_threshold"] = 0.5f;
        config["fatigue_detection"]["eye_closure_threshold"] = 0.1f;
        config["fatigue_detection"]["analysis_window_ms"] = 30000;
        config["distraction_detection"]["head_pose_threshold"] = 30.0f;
        config["distraction_detection"]["analysis_window_ms"] = 10000;
        
        std::ofstream file(test_config_file_);
        Json::StreamWriterBuilder builder;
        std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
        writer->write(config, &file);
    }

    void CreateTestData() {
        // 创建多种分辨率的测试图像
        CreateImageData(640, 480, test_image_640x480_);
        CreateImageData(1280, 720, test_image_1280x720_);
        CreateImageData(1920, 1080, test_image_1920x1080_);
        
        // 创建测试车辆信息
        memset(&test_car_info_, 0, sizeof(test_car_info_));
        test_car_info_.speed = 60;
        test_car_info_.gear = FORWARD;
        test_car_info_.mask = TX_CAR_INFO_MASK_SPEED | TX_CAR_INFO_MASK_GEARPOSITION;
    }

    void CreateImageData(int width, int height, TestImageData& image_data) {
        image_data.info.dataType = GRAY;
        image_data.info.width = width;
        image_data.info.height = height;
        image_data.info.stride = width;
        image_data.info.dataLen = width * height;
        
        image_data.data.resize(image_data.info.dataLen);
        
        // 创建有梯度的测试图像
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                uint8_t value = static_cast<uint8_t>((x + y) % 256);
                image_data.data[y * width + x] = value;
            }
        }
        
        image_data.info.data = reinterpret_cast<char*>(image_data.data.data());
    }

    size_t GetCurrentMemoryUsage() {
        struct rusage usage;
        getrusage(RUSAGE_SELF, &usage);
        return usage.ru_maxrss; // KB on Linux
    }

    double GetCpuUsage() {
        static clock_t last_cpu = 0;
        static clock_t last_sys_cpu = 0;
        static clock_t last_user_cpu = 0;
        
        FILE* file = fopen("/proc/stat", "r");
        if (!file) return 0.0;
        
        clock_t cpu, sys_cpu, user_cpu;
        fscanf(file, "cpu %ld %ld %ld", &user_cpu, &sys_cpu, &cpu);
        fclose(file);
        
        if (last_cpu == 0) {
            last_cpu = cpu;
            last_sys_cpu = sys_cpu;
            last_user_cpu = user_cpu;
            return 0.0;
        }
        
        double percent = (double)(cpu - last_cpu) / (double)(cpu - last_cpu) * 100.0;
        last_cpu = cpu;
        last_sys_cpu = sys_cpu;
        last_user_cpu = user_cpu;
        
        return percent;
    }

    PerformanceStats RunBenchmark(const TestImageData& image_data, int frame_count) {
        PerformanceStats stats;
        std::vector<double> processing_times;
        std::vector<size_t> memory_usage;
        
        auto start_time = high_resolution_clock::now();
        
        for (int i = 0; i < frame_count; ++i) {
            auto frame_start = high_resolution_clock::now();
            
            TXDmsResult result;
            long timestamp = duration_cast<microseconds>(frame_start.time_since_epoch()).count();
            
            // 执行处理
            int ret = coordinator_->ProcessFrame(&image_data.info, &test_car_info_, result, timestamp);
            EXPECT_EQ(ret, 0);
            
            auto frame_end = high_resolution_clock::now();
            double frame_time = duration_cast<microseconds>(frame_end - frame_start).count() / 1000.0;
            processing_times.push_back(frame_time);
            
            // 记录内存使用
            memory_usage.push_back(GetCurrentMemoryUsage());
            
            // 模拟30fps间隔
            std::this_thread::sleep_for(milliseconds(1));
        }
        
        auto end_time = high_resolution_clock::now();
        double total_time = duration_cast<milliseconds>(end_time - start_time).count();
        
        // 计算统计数据
        stats.avg_processing_time_ms = 0;
        stats.min_processing_time_ms = processing_times[0];
        stats.max_processing_time_ms = processing_times[0];
        
        for (double time : processing_times) {
            stats.avg_processing_time_ms += time;
            stats.min_processing_time_ms = std::min(stats.min_processing_time_ms, time);
            stats.max_processing_time_ms = std::max(stats.max_processing_time_ms, time);
        }
        stats.avg_processing_time_ms /= frame_count;
        
        stats.fps = (frame_count * 1000.0) / total_time;
        
        // 内存统计
        stats.peak_memory_kb = *std::max_element(memory_usage.begin(), memory_usage.end());
        stats.avg_memory_kb = 0;
        for (size_t mem : memory_usage) {
            stats.avg_memory_kb += mem;
        }
        stats.avg_memory_kb /= memory_usage.size();
        
        stats.cpu_usage_percent = GetCpuUsage();
        
        return stats;
    }

    void PrintStats(const std::string& test_name, const PerformanceStats& stats) {
        printf("\n=== %s ===\n", test_name.c_str());
        printf("平均处理时间: %.2f ms\n", stats.avg_processing_time_ms);
        printf("最小处理时间: %.2f ms\n", stats.min_processing_time_ms);
        printf("最大处理时间: %.2f ms\n", stats.max_processing_time_ms);
        printf("实际FPS: %.2f\n", stats.fps);
        printf("峰值内存: %zu KB (%.2f MB)\n", stats.peak_memory_kb, stats.peak_memory_kb / 1024.0);
        printf("平均内存: %zu KB (%.2f MB)\n", stats.avg_memory_kb, stats.avg_memory_kb / 1024.0);
        printf("CPU使用率: %.2f%%\n", stats.cpu_usage_percent);
        printf("========================================\n");
    }

protected:
    std::unique_ptr<DmsCoordinator> coordinator_;
    std::string test_config_file_;
    TestImageData test_image_640x480_;
    TestImageData test_image_1280x720_;
    TestImageData test_image_1920x1080_;
    TXCarInfo test_car_info_;
};

// 基础性能测试 - 640x480分辨率
TEST_F(PerformanceBenchmark, BasicPerformanceTest_640x480) {
    const int frame_count = 100;
    PerformanceStats stats = RunBenchmark(test_image_640x480_, frame_count);
    
    PrintStats("640x480 基础性能测试", stats);
    
    // 性能断言
    EXPECT_LT(stats.avg_processing_time_ms, 50.0);  // 平均处理时间应小于50ms
    EXPECT_GT(stats.fps, 15.0);                     // FPS应大于15
    EXPECT_LT(stats.peak_memory_kb, 100000);        // 峰值内存应小于100MB
}

// 高分辨率性能测试 - 1280x720
TEST_F(PerformanceBenchmark, HighResolutionTest_1280x720) {
    const int frame_count = 50;
    PerformanceStats stats = RunBenchmark(test_image_1280x720_, frame_count);
    
    PrintStats("1280x720 高分辨率测试", stats);
    
    // 高分辨率性能断言
    EXPECT_LT(stats.avg_processing_time_ms, 100.0); // 平均处理时间应小于100ms
    EXPECT_GT(stats.fps, 8.0);                      // FPS应大于8
}

// 超高分辨率性能测试 - 1920x1080
TEST_F(PerformanceBenchmark, UltraHighResolutionTest_1920x1080) {
    const int frame_count = 30;
    PerformanceStats stats = RunBenchmark(test_image_1920x1080_, frame_count);
    
    PrintStats("1920x1080 超高分辨率测试", stats);
    
    // 超高分辨率性能断言
    EXPECT_LT(stats.avg_processing_time_ms, 200.0); // 平均处理时间应小于200ms
    EXPECT_GT(stats.fps, 4.0);                      // FPS应大于4
}

// 长时间稳定性测试
TEST_F(PerformanceBenchmark, LongTermStabilityTest) {
    const int frame_count = 300;  // 10秒@30fps
    PerformanceStats stats = RunBenchmark(test_image_640x480_, frame_count);
    
    PrintStats("长时间稳定性测试", stats);
    
    // 稳定性断言
    double time_variance = stats.max_processing_time_ms - stats.min_processing_time_ms;
    EXPECT_LT(time_variance, 100.0);  // 处理时间方差应小于100ms
    EXPECT_LT(stats.peak_memory_kb - stats.avg_memory_kb, 10000); // 内存波动应小于10MB
}

// 内存泄漏测试
TEST_F(PerformanceBenchmark, MemoryLeakTest) {
    const int iterations = 5;
    const int frames_per_iteration = 50;
    std::vector<size_t> peak_memories;
    
    for (int i = 0; i < iterations; ++i) {
        PerformanceStats stats = RunBenchmark(test_image_640x480_, frames_per_iteration);
        peak_memories.push_back(stats.peak_memory_kb);
        
        // 强制垃圾回收（如果有的话）
        std::this_thread::sleep_for(milliseconds(100));
    }
    
    // 检查内存是否持续增长
    bool memory_leak = false;
    for (size_t i = 1; i < peak_memories.size(); ++i) {
        if (peak_memories[i] > peak_memories[i-1] + 5000) { // 增长超过5MB认为可能有泄漏
            memory_leak = true;
            break;
        }
    }
    
    EXPECT_FALSE(memory_leak) << "检测到可能的内存泄漏";
    
    printf("\n=== 内存泄漏测试 ===\n");
    for (size_t i = 0; i < peak_memories.size(); ++i) {
        printf("迭代 %zu: %zu KB\n", i+1, peak_memories[i]);
    }
    printf("====================\n");
}
