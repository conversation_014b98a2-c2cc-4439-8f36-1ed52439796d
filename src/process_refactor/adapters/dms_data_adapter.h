#ifndef __DMS_DATA_ADAPTER_H__
#define __DMS_DATA_ADAPTER_H__

#include <memory>
#include <vector>
#include "cc_tensor.h"
#include "tx_dms_sdk.h"
#include "json.h"

namespace tongxing {

/**
 * @brief DMS数据适配器
 * 
 * 负责将外部输入数据转换为tongxing模块系统可处理的格式
 * 消除原有的回调函数依赖，提供统一的数据接口
 */
class DmsDataAdapter {
public:
    DmsDataAdapter();
    ~DmsDataAdapter();

    /**
     * @brief 初始化适配器
     * @param config 配置参数
     * @return 0成功，非0失败
     */
    int Init(const Json::Value& config);

    /**
     * @brief 适配输入图像数据
     * @param image 原始图像信息
     * @return 适配后的数据数组，供tongxing模块使用
     */
    std::vector<std::shared_ptr<NumArray>> AdaptImageInput(const TXImageInfo* image);

    /**
     * @brief 设置ROI区域
     * @param left_top 左上角坐标
     * @param right_bottom 右下角坐标
     */
    void SetDriverRoi(const TXPoint2i& left_top, const TXPoint2i& right_bottom);

    /**
     * @brief 获取当前ROI设置
     * @param left_top 左上角坐标
     * @param right_bottom 右下角坐标
     */
    void GetDriverRoi(TXPoint2i& left_top, TXPoint2i& right_bottom);

    /**
     * @brief 预处理图像数据
     * @param image_data 原始图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @return 预处理后的NumArray
     */
    std::shared_ptr<NumArray> PreprocessImage(const unsigned char* image_data, 
                                             int width, int height);

    /**
     * @brief 生成人脸ROI数据
     * @return 人脸ROI的NumArray
     */
    std::shared_ptr<NumArray> GenerateFaceRoi();

    /**
     * @brief 生成精确人脸ROI数据
     * @return 精确人脸ROI的NumArray
     */
    std::shared_ptr<NumArray> GenerateExactFaceRoi();

private:
    // ROI设置
    TXPoint2i driver_roi_left_top_;
    TXPoint2i driver_roi_right_bottom_;
    
    // 配置参数
    Json::Value config_;
    
    // 缓存数据
    std::shared_ptr<NumArray> face_roi_cache_;
    std::shared_ptr<NumArray> exact_face_roi_cache_;

    /**
     * @brief 创建NumArray对象
     * @param data 数据指针
     * @param shape 数据形状
     * @param type 数据类型
     * @return NumArray智能指针
     */
    std::shared_ptr<NumArray> CreateNumArray(void* data, 
                                            const std::vector<int>& shape,
                                            NumArray::DataType type = NumArray::UINT8);

    /**
     * @brief 验证图像数据有效性
     * @param image 图像信息
     * @return true有效，false无效
     */
    bool ValidateImageData(const TXImageInfo* image);
};

} // namespace tongxing

#endif // __DMS_DATA_ADAPTER_H__
