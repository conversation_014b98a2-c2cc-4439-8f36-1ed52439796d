#include "dms_data_adapter.h"
#include "cc_numarray_tool.h"
#include "CalmCarLog.h"
#include <cstring>

namespace tongxing {

DmsDataAdapter::DmsDataAdapter() {
    // 初始化默认ROI
    driver_roi_left_top_.x = 100;
    driver_roi_left_top_.y = 100;
    driver_roi_right_bottom_.x = 540;
    driver_roi_right_bottom_.y = 380;
    
    TX_LOG_INFO("DmsDataAdapter", "DmsDataAdapter constructed");
}

DmsDataAdapter::~DmsDataAdapter() {
    TX_LOG_INFO("DmsDataAdapter", "DmsDataAdapter destructed");
}

int DmsDataAdapter::Init(const Json::Value& config) {
    config_ = config;
    
    // 从配置中读取ROI设置
    if (config.isMember("roi_settings")) {
        const Json::Value& roi_config = config["roi_settings"];
        
        if (roi_config.isMember("default_left_top") && roi_config["default_left_top"].isArray()) {
            driver_roi_left_top_.x = roi_config["default_left_top"][0].asInt();
            driver_roi_left_top_.y = roi_config["default_left_top"][1].asInt();
        }
        
        if (roi_config.isMember("default_right_bottom") && roi_config["default_right_bottom"].isArray()) {
            driver_roi_right_bottom_.x = roi_config["default_right_bottom"][0].asInt();
            driver_roi_right_bottom_.y = roi_config["default_right_bottom"][1].asInt();
        }
    }
    
    // 预生成ROI数据
    face_roi_cache_ = GenerateFaceRoi();
    exact_face_roi_cache_ = GenerateExactFaceRoi();
    
    TX_LOG_INFO("DmsDataAdapter", "DmsDataAdapter initialized successfully");
    TX_LOG_INFO("DmsDataAdapter", "ROI: (%d,%d) to (%d,%d)", 
               driver_roi_left_top_.x, driver_roi_left_top_.y,
               driver_roi_right_bottom_.x, driver_roi_right_bottom_.y);
    
    return 0;
}

std::vector<std::shared_ptr<NumArray>> DmsDataAdapter::AdaptImageInput(const TXImageInfo* image) {
    std::vector<std::shared_ptr<NumArray>> result;
    
    if (!ValidateImageData(image)) {
        TX_LOG_ERROR("DmsDataAdapter", "Invalid image data");
        return result;
    }
    
    // 1. 预处理图像数据
    std::shared_ptr<NumArray> processed_image = PreprocessImage(
        reinterpret_cast<const unsigned char*>(image->data), 
        image->width, 
        image->height
    );
    
    if (processed_image) {
        result.push_back(processed_image);
        TX_LOG_DEBUG("DmsDataAdapter", "Image data adapted: %dx%d", image->width, image->height);
    }
    
    // 2. 添加人脸ROI数据
    if (face_roi_cache_) {
        result.push_back(face_roi_cache_);
        TX_LOG_DEBUG("DmsDataAdapter", "Face ROI data added");
    }
    
    // 3. 添加精确人脸ROI数据
    if (exact_face_roi_cache_) {
        result.push_back(exact_face_roi_cache_);
        TX_LOG_DEBUG("DmsDataAdapter", "Exact face ROI data added");
    }
    
    return result;
}

void DmsDataAdapter::SetDriverRoi(const TXPoint2i& left_top, const TXPoint2i& right_bottom) {
    driver_roi_left_top_ = left_top;
    driver_roi_right_bottom_ = right_bottom;
    
    // 重新生成ROI缓存
    face_roi_cache_ = GenerateFaceRoi();
    exact_face_roi_cache_ = GenerateExactFaceRoi();
    
    TX_LOG_INFO("DmsDataAdapter", "Driver ROI updated: (%d,%d) to (%d,%d)", 
               left_top.x, left_top.y, right_bottom.x, right_bottom.y);
}

void DmsDataAdapter::GetDriverRoi(TXPoint2i& left_top, TXPoint2i& right_bottom) {
    left_top = driver_roi_left_top_;
    right_bottom = driver_roi_right_bottom_;
}

std::shared_ptr<NumArray> DmsDataAdapter::PreprocessImage(const unsigned char* image_data, 
                                                         int width, int height) {
    if (image_data == nullptr || width <= 0 || height <= 0) {
        TX_LOG_ERROR("DmsDataAdapter", "Invalid image parameters");
        return nullptr;
    }
    
    // 创建图像NumArray (假设输入是单通道灰度图像)
    std::vector<int> shape = {1, height, width, 1}; // NHWC格式
    std::shared_ptr<NumArray> image_array = creat_numarray(shape, NumArray::UINT8);
    
    if (!image_array) {
        TX_LOG_ERROR("DmsDataAdapter", "Failed to create image NumArray");
        return nullptr;
    }
    
    // 复制图像数据
    size_t data_size = width * height;
    std::memcpy(image_array->data, image_data, data_size);
    
    TX_LOG_DEBUG("DmsDataAdapter", "Image preprocessed: %dx%d", width, height);
    return image_array;
}

std::shared_ptr<NumArray> DmsDataAdapter::GenerateFaceRoi() {
    // 创建人脸ROI数据 [x1, y1, x2, y2]
    std::vector<int> shape = {1, 4}; // 1个ROI，4个坐标值
    std::shared_ptr<NumArray> roi_array = creat_numarray(shape, NumArray::FLOAT32);
    
    if (!roi_array) {
        TX_LOG_ERROR("DmsDataAdapter", "Failed to create face ROI NumArray");
        return nullptr;
    }
    
    float* roi_data = reinterpret_cast<float*>(roi_array->data);
    roi_data[0] = static_cast<float>(driver_roi_left_top_.x);     // x1
    roi_data[1] = static_cast<float>(driver_roi_left_top_.y);     // y1
    roi_data[2] = static_cast<float>(driver_roi_right_bottom_.x); // x2
    roi_data[3] = static_cast<float>(driver_roi_right_bottom_.y); // y2
    
    TX_LOG_DEBUG("DmsDataAdapter", "Face ROI generated: [%.1f, %.1f, %.1f, %.1f]", 
                roi_data[0], roi_data[1], roi_data[2], roi_data[3]);
    
    return roi_array;
}

std::shared_ptr<NumArray> DmsDataAdapter::GenerateExactFaceRoi() {
    // 创建精确人脸ROI数据，格式与普通ROI相同但可能有不同的用途
    std::vector<int> shape = {1, 4}; // 1个ROI，4个坐标值
    std::shared_ptr<NumArray> exact_roi_array = creat_numarray(shape, NumArray::FLOAT32);
    
    if (!exact_roi_array) {
        TX_LOG_ERROR("DmsDataAdapter", "Failed to create exact face ROI NumArray");
        return nullptr;
    }
    
    float* roi_data = reinterpret_cast<float*>(exact_roi_array->data);
    
    // 精确ROI可能比普通ROI稍微小一些，用于更精确的检测
    int margin = 10; // 内缩10像素
    roi_data[0] = static_cast<float>(driver_roi_left_top_.x + margin);     // x1
    roi_data[1] = static_cast<float>(driver_roi_left_top_.y + margin);     // y1
    roi_data[2] = static_cast<float>(driver_roi_right_bottom_.x - margin); // x2
    roi_data[3] = static_cast<float>(driver_roi_right_bottom_.y - margin); // y2
    
    TX_LOG_DEBUG("DmsDataAdapter", "Exact face ROI generated: [%.1f, %.1f, %.1f, %.1f]", 
                roi_data[0], roi_data[1], roi_data[2], roi_data[3]);
    
    return exact_roi_array;
}

std::shared_ptr<NumArray> DmsDataAdapter::CreateNumArray(void* data, 
                                                        const std::vector<int>& shape,
                                                        NumArray::DataType type) {
    std::shared_ptr<NumArray> array = creat_numarray(shape, type);
    
    if (array && data) {
        size_t data_size = array->word_size;
        for (int dim : shape) {
            data_size *= dim;
        }
        std::memcpy(array->data, data, data_size);
    }
    
    return array;
}

bool DmsDataAdapter::ValidateImageData(const TXImageInfo* image) {
    if (image == nullptr) {
        TX_LOG_ERROR("DmsDataAdapter", "Image pointer is null");
        return false;
    }
    
    if (image->data == nullptr) {
        TX_LOG_ERROR("DmsDataAdapter", "Image data pointer is null");
        return false;
    }
    
    if (image->width <= 0 || image->height <= 0) {
        TX_LOG_ERROR("DmsDataAdapter", "Invalid image dimensions: %dx%d", 
                    image->width, image->height);
        return false;
    }
    
    if (image->dataLen <= 0) {
        TX_LOG_ERROR("DmsDataAdapter", "Invalid image data length: %d", image->dataLen);
        return false;
    }
    
    return true;
}

} // namespace tongxing
