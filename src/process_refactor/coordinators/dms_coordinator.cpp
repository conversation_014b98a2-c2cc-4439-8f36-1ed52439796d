#include "dms_coordinator.h"
#include "../managers/config_manager.h"
#include "../managers/state_manager.h"
#include "../adapters/dms_data_adapter.h"
#include "../modules/face_analysis_module.h"
#include "../modules/fatigue_detection_module.h"
#include "../modules/distraction_detection_module.h"
#include "CalmCarLog.h"
#include "cc_numarray_tool.h"
#include <chrono>
#include <cstring>

namespace tongxing {

DmsCoordinator::DmsCoordinator()
    : fatigue_enabled_(true)
    , distraction_enabled_(true)
    , initialized_(false)
    , last_process_time_(0) {
    TX_LOG_INFO("DmsCoordinator", "DmsCoordinator constructed");
}

DmsCoordinator::~DmsCoordinator() {
    TX_LOG_INFO("DmsCoordinator", "DmsCoordinator destructed");
}

int DmsCoordinator::Init(const char* config_file) {
    if (!config_file) {
        TX_LOG_ERROR("DmsCoordinator", "Config file path is null");
        return -1;
    }

    try {
        // 1. 初始化配置管理器
        config_manager_ = std::make_unique<ConfigManager>();
        if (config_manager_->Init(config_file) != 0) {
            TX_LOG_ERROR("DmsCoordinator", "Failed to initialize ConfigManager");
            return -1;
        }
        // 直接使用参数获取方式
        // config_ = config_manager_->GetConfig();

        // 2. 初始化状态管理器
        state_manager_ = std::make_unique<StateManager>();
        Json::Value state_config;  // 使用默认配置
        if (state_manager_->Init(state_config) != 0) {
            TX_LOG_ERROR("DmsCoordinator", "Failed to initialize StateManager");
            return -1;
        }

        // 3. 初始化数据适配器
        data_adapter_ = std::make_unique<DmsDataAdapter>();
        Json::Value adapter_config;  // 使用默认配置
        if (data_adapter_->Init(adapter_config) != 0) {
            TX_LOG_ERROR("DmsCoordinator", "Failed to initialize DmsDataAdapter");
            return -1;
        }

        // 4. 初始化检测模块
        if (InitializeComponents() != 0) {
            TX_LOG_ERROR("DmsCoordinator", "Failed to initialize detection modules");
            return -1;
        }

        // 5. 读取使能配置
        fatigue_enabled_ = config_manager_->GetParameter<bool>("fatigue_detection.enabled", true);
        distraction_enabled_ = config_manager_->GetParameter<bool>("distraction_detection.enabled", true);

        // 6. 启用DMS系统
        state_manager_->SetDmsEnabled(true);

        initialized_ = true;
        TX_LOG_INFO("DmsCoordinator", "DmsCoordinator initialized successfully");
        return 0;

    } catch (const std::exception& e) {
        TX_LOG_ERROR("DmsCoordinator", "Exception during initialization: %s", e.what());
        return -1;
    }
}

int DmsCoordinator::InitializeComponents() {
    // 1. 初始化人脸分析模块
    face_module_ = std::make_unique<FaceAnalysisModule>();
    Json::Value face_config;  // 使用默认配置
    if (face_module_->init(face_config) != 0) {
        TX_LOG_ERROR("DmsCoordinator", "Failed to initialize FaceAnalysisModule");
        return -1;
    }

    // 2. 初始化疲劳检测模块
    fatigue_module_ = std::make_unique<FatigueDetectionModule>();
    Json::Value fatigue_config;  // 使用默认配置
    if (fatigue_module_->init(fatigue_config) != 0) {
        TX_LOG_ERROR("DmsCoordinator", "Failed to initialize FatigueDetectionModule");
        return -1;
    }

    // 3. 初始化分心检测模块
    distraction_module_ = std::make_unique<DistractionDetectionModule>();
    Json::Value distraction_config;  // 使用默认配置
    if (distraction_module_->init(distraction_config) != 0) {
        TX_LOG_ERROR("DmsCoordinator", "Failed to initialize DistractionDetectionModule");
        return -1;
    }

    TX_LOG_INFO("DmsCoordinator", "All detection modules initialized successfully");
    return 0;
}

int DmsCoordinator::ProcessFrame(const TXImageInfo* image_info,
                                const TXCarInfo* car_info,
                                TXDmsResult& result_out,
                                long timestamp) {
    if (!initialized_) {
        TX_LOG_ERROR("DmsCoordinator", "DmsCoordinator not initialized");
        return -1;
    }

    // 验证输入数据
    if (!ValidateInputs(image_info, car_info)) {
        TX_LOG_ERROR("DmsCoordinator", "Invalid input data");
        return -1;
    }

    // 获取时间戳
    if (timestamp == 0) {
        timestamp = GetCurrentTimestamp();
    }

    // 清空输出结果
    memset(&result_out, 0, sizeof(result_out));

    try {
        // 1. 更新系统状态
        UpdateSystemState(car_info, timestamp);

        // 2. 检查算法激活状态
        if (!CheckAlgorithmActivation(car_info, timestamp)) {
            result_out.algorithm_status = state_manager_->GetAlgorithmStatus();
            result_out.system_status = state_manager_->GetSystemStatus();
            TX_LOG_DEBUG("DmsCoordinator", "Algorithm not activated, skipping processing");
            return 0;
        }

        // 3. 处理摄像头状态
        result_out.camera_status = ProcessCameraStatus(image_info, car_info, timestamp);
        if (result_out.camera_status != Camera_Normal) {
            TX_LOG_DEBUG("DmsCoordinator", "Camera status abnormal: %d", result_out.camera_status);
            // 摄像头异常时仍需要设置基本状态
            result_out.algorithm_status = state_manager_->GetAlgorithmStatus();
            result_out.system_status = state_manager_->GetSystemStatus();
            return 0;
        }

        // 4. 数据适配
        std::vector<std::shared_ptr<NumArray>> adapted_inputs = data_adapter_->AdaptImageInput(image_info);
        if (adapted_inputs.empty()) {
            TX_LOG_ERROR("DmsCoordinator", "Failed to adapt image input");
            return -1;
        }

        // 5. 人脸分析
        TXDmsFaceInfo face_info = ProcessFaceAnalysis(adapted_inputs, timestamp);
        result_out.face_info = face_info;

        // 6. 疲劳检测
        TXDrowsinessType fatigue_type = Drowsiness_Normal;
        if (fatigue_enabled_) {
            fatigue_type = ProcessFatigueDetection(face_info, timestamp);
        }

        // 7. 分心检测
        TXDistractionType distraction_type = Distraction_Normal;
        if (distraction_enabled_) {
            distraction_type = ProcessDistractionDetection(face_info, car_info, timestamp);
        }

        // 8. 应用速度阈值限制
        ApplySpeedThresholds(fatigue_type, distraction_type, car_info);

        // 9. 整合结果
        IntegrateResults(face_info, fatigue_type, distraction_type, car_info, timestamp, result_out);

        last_process_time_ = timestamp;
        TX_LOG_DEBUG("DmsCoordinator", "Frame processed successfully: fatigue=%d, distraction=%d",
                    fatigue_type, distraction_type);

        return 0;

    } catch (const std::exception& e) {
        TX_LOG_ERROR("DmsCoordinator", "Exception during frame processing: %s", e.what());
        return -1;
    }
}

TXDmsFaceInfo DmsCoordinator::ProcessFaceAnalysis(const std::vector<std::shared_ptr<NumArray>>& adapted_inputs,
                                                 long timestamp) {
    TXDmsFaceInfo face_info = {};
    
    // 设置人脸分析模块输入
    if (face_module_->setInput(adapted_inputs) != 0) {
        TX_LOG_ERROR("DmsCoordinator", "Failed to set face analysis input");
        return face_info;
    }

    // 执行人脸分析
    if (face_module_->execute() != 0) {
        TX_LOG_ERROR("DmsCoordinator", "Failed to execute face analysis");
        return face_info;
    }

    // 获取人脸分析结果
    // 这里需要从face_module_的输出中解析TXDmsFaceInfo
    // 简化处理，实际应该从NumArray中解析
    // face_info.timestamp = timestamp;  // TXDmsFaceInfo没有timestamp字段
    // face_info.face_score = 0.8f;      // TXDmsFaceInfo没有face_score字段
    // face_info.face_valid = true;      // TXDmsFaceInfo没有face_valid字段

    // 设置实际存在的字段
    face_info.score = 0.8f;
    face_info.xmin = 100;
    face_info.ymin = 100;
    face_info.xmax = 300;
    face_info.ymax = 300;
    face_info.head_yaw = 0.0f;
    face_info.head_pitch = 0.0f;
    face_info.head_roll = 0.0f;

    TX_LOG_DEBUG("DmsCoordinator", "Face analysis completed: bbox=(%d,%d,%d,%d), score=%.2f",
                face_info.xmin, face_info.ymin, face_info.xmax, face_info.ymax, face_info.score);

    return face_info;
}

TXDrowsinessType DmsCoordinator::ProcessFatigueDetection(const TXDmsFaceInfo& face_info, long timestamp) {
    // 准备疲劳检测输入数据
    std::vector<std::shared_ptr<NumArray>> fatigue_inputs;
    
    // 添加人脸信息
    fatigue_inputs.push_back(CreateNumArray((void*)&face_info, sizeof(face_info), NumArray::UINT8));
    
    // 添加眼部信息（简化处理）
    float eye_data[4] = {0.8f, 0.7f, 1.0f, 1.0f};  // left_openness, right_openness, left_score, right_score
    fatigue_inputs.push_back(CreateNumArray(eye_data, sizeof(eye_data), NumArray::FLOAT32));
    
    // 添加嘴部信息（简化处理）
    float mouth_data[2] = {0.2f, 1.0f};  // mouth_opening, mouth_score
    fatigue_inputs.push_back(CreateNumArray(mouth_data, sizeof(mouth_data), NumArray::FLOAT32));
    
    // 添加时间戳
    fatigue_inputs.push_back(CreateNumArray(&timestamp, sizeof(timestamp), NumArray::INT64));

    // 设置疲劳检测模块输入
    if (fatigue_module_->setInput(fatigue_inputs) != 0) {
        TX_LOG_ERROR("DmsCoordinator", "Failed to set fatigue detection input");
        return Drowsiness_Normal;
    }

    // 执行疲劳检测
    if (fatigue_module_->execute() != 0) {
        TX_LOG_ERROR("DmsCoordinator", "Failed to execute fatigue detection");
        return Drowsiness_Normal;
    }

    // 获取疲劳检测结果
    std::shared_ptr<NumArray> fatigue_output = fatigue_module_->getOutput(0);
    if (!fatigue_output) {
        TX_LOG_ERROR("DmsCoordinator", "Failed to get fatigue detection output");
        return Drowsiness_Normal;
    }

    // 解析疲劳检测结果（简化处理）
    // 实际应该从FatigueResult结构中解析
    TXDrowsinessType fatigue_type = Drowsiness_Normal;
    
    TX_LOG_DEBUG("DmsCoordinator", "Fatigue detection completed: type=%d", fatigue_type);
    return fatigue_type;
}

TXDistractionType DmsCoordinator::ProcessDistractionDetection(const TXDmsFaceInfo& face_info,
                                                             const TXCarInfo* car_info,
                                                             long timestamp) {
    // 准备分心检测输入数据
    std::vector<std::shared_ptr<NumArray>> distraction_inputs;

    // 添加人脸信息
    distraction_inputs.push_back(CreateNumArray((void*)&face_info, sizeof(face_info), NumArray::UINT8));

    // 添加头部姿态信息（简化处理）
    float head_pose_data[3] = {face_info.head_pitch, face_info.head_yaw, face_info.head_roll};
    distraction_inputs.push_back(CreateNumArray(head_pose_data, sizeof(head_pose_data), NumArray::FLOAT32));

    // 添加视线信息（简化处理）
    float gaze_data[2] = {0.0f, 0.0f};  // gaze_pitch, gaze_yaw
    distraction_inputs.push_back(CreateNumArray(gaze_data, sizeof(gaze_data), NumArray::FLOAT32));

    // 添加行为信息（简化处理）
    float behavior_data[4] = {0.0f, 0.0f, 0.5f, 0.5f};  // phone_detected, smoking_detected, phone_score, smoking_score
    distraction_inputs.push_back(CreateNumArray(behavior_data, sizeof(behavior_data), NumArray::FLOAT32));

    // 添加车辆信息
    distraction_inputs.push_back(CreateNumArray((void*)car_info, sizeof(TXCarInfo), NumArray::UINT8));

    // 添加时间戳
    distraction_inputs.push_back(CreateNumArray(&timestamp, sizeof(timestamp), NumArray::INT64));

    // 设置分心检测模块输入
    if (distraction_module_->setInput(distraction_inputs) != 0) {
        TX_LOG_ERROR("DmsCoordinator", "Failed to set distraction detection input");
        return Distraction_Normal;
    }

    // 执行分心检测
    if (distraction_module_->execute() != 0) {
        TX_LOG_ERROR("DmsCoordinator", "Failed to execute distraction detection");
        return Distraction_Normal;
    }

    // 获取分心检测结果
    std::shared_ptr<NumArray> distraction_output = distraction_module_->getOutput(0);
    if (!distraction_output) {
        TX_LOG_ERROR("DmsCoordinator", "Failed to get distraction detection output");
        return Distraction_Normal;
    }

    // 解析分心检测结果（简化处理）
    TXDistractionType distraction_type = Distraction_Normal;

    TX_LOG_DEBUG("DmsCoordinator", "Distraction detection completed: type=%d", distraction_type);
    return distraction_type;
}

void DmsCoordinator::IntegrateResults(const TXDmsFaceInfo& face_info,
                                     TXDrowsinessType fatigue_type,
                                     TXDistractionType distraction_type,
                                     const TXCarInfo* car_info,
                                     long timestamp,
                                     TXDmsResult& result_out) {
    // 设置基本信息
    result_out.face_info = face_info;
    result_out.drowsiness_status = fatigue_type;
    result_out.distraction_status = distraction_type;
    // result_out.timestamp = timestamp;  // TXDmsResult没有timestamp字段

    // 设置系统状态
    result_out.algorithm_status = state_manager_->GetAlgorithmStatus();
    result_out.system_status = state_manager_->GetSystemStatus();
    result_out.calibrate_status = state_manager_->GetCalibrateStatus();

    // 设置车辆信息（TXDmsResult中没有speed和gear字段，这些信息在car_info中）
    // if (car_info) {
    //     result_out.speed = car_info->speed;
    //     result_out.gear = car_info->gear;
    // }

    TX_LOG_DEBUG("DmsCoordinator", "Results integrated: fatigue=%d, distraction=%d, algorithm_status=%d",
                fatigue_type, distraction_type, result_out.algorithm_status);
}

bool DmsCoordinator::ValidateInputs(const TXImageInfo* image_info, const TXCarInfo* car_info) {
    if (!image_info) {
        TX_LOG_ERROR("DmsCoordinator", "Image info is null");
        return false;
    }

    if (!image_info->data || image_info->width <= 0 || image_info->height <= 0 || image_info->dataLen <= 0) {
        TX_LOG_ERROR("DmsCoordinator", "Invalid image data: data=%p, width=%d, height=%d, dataLen=%d",
                    image_info->data, image_info->width, image_info->height, image_info->dataLen);
        return false;
    }

    if (!car_info) {
        TX_LOG_ERROR("DmsCoordinator", "Car info is null");
        return false;
    }

    return true;
}

long DmsCoordinator::GetCurrentTimestamp() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

void DmsCoordinator::UpdateSystemState(const TXCarInfo* car_info, long timestamp) {
    // 检查硬件错误
    TXDmsResult temp_result = {};
    bool has_error = state_manager_->CheckHardwareError(car_info, temp_result);
    if (has_error) {
        TX_LOG_WARN("DmsCoordinator", "Hardware error detected");
    }

    // 更新摄像头状态（简化处理）
    uint8_t camera_state = 0x01;  // 正常状态
    TXCameraStatus camera_status = state_manager_->GetCameraStatus(car_info, timestamp, camera_state);

    TX_LOG_DEBUG("DmsCoordinator", "System state updated: camera_status=%d", camera_status);
}

bool DmsCoordinator::CheckAlgorithmActivation(const TXCarInfo* car_info, long timestamp) {
    TXDmsResult temp_result = {};
    bool activated = state_manager_->IsAlgorithmActivated(temp_result, timestamp);

    if (!activated) {
        TX_LOG_DEBUG("DmsCoordinator", "Algorithm not activated: status=%d", temp_result.algorithm_status);
    }

    return activated;
}

void DmsCoordinator::ApplySpeedThresholds(TXDrowsinessType& fatigue_type,
                                         TXDistractionType& distraction_type,
                                         const TXCarInfo* car_info) {
    if (!car_info) return;

    // 根据标准类型设置速度阈值
    int fatigue_speed_threshold = 60;    // 默认非欧标
    int distraction_speed_threshold = 60;

    // 这里应该根据实际的标准类型来设置阈值
    // if (TXDmsGetStandardType() == TX_STANDARD_EU) {
    //     fatigue_speed_threshold = 10;
    //     distraction_speed_threshold = 20;
    // }

    // 应用速度阈值
    if (car_info->speed < fatigue_speed_threshold) {
        fatigue_type = Drowsiness_Normal;
        TX_LOG_DEBUG("DmsCoordinator", "Fatigue detection disabled due to low speed: %d < %d",
                    car_info->speed, fatigue_speed_threshold);
    }

    if (car_info->speed < distraction_speed_threshold) {
        distraction_type = Distraction_Normal;
        TX_LOG_DEBUG("DmsCoordinator", "Distraction detection disabled due to low speed: %d < %d",
                    car_info->speed, distraction_speed_threshold);
    }
}

TXCameraStatus DmsCoordinator::ProcessCameraStatus(const TXImageInfo* image_info,
                                                  const TXCarInfo* car_info,
                                                  long timestamp) {
    // 简化的摄像头状态检测
    // 实际应该包括遮挡检测、图像质量检测等

    if (!image_info || !image_info->data) {
        return Camera_Invalid;
    }

    // 检查图像尺寸
    if (image_info->width < 320 || image_info->height < 240) {
        TX_LOG_WARN("DmsCoordinator", "Image size too small: %dx%d", image_info->width, image_info->height);
        return Camera_Invalid;
    }

    // 这里可以添加更多的图像质量检测逻辑
    // 例如亮度检测、模糊检测、遮挡检测等

    return Camera_Normal;
}

std::shared_ptr<NumArray> DmsCoordinator::CreateNumArray(void* data, size_t size, NumArray::DataType type) {
    if (!data || size == 0) {
        TX_LOG_ERROR("DmsCoordinator", "Invalid data for NumArray creation");
        return nullptr;
    }

    std::vector<int> shape = {1, static_cast<int>(size)};
    std::shared_ptr<NumArray> array = creat_numarray(shape, type);

    if (array && array->data) {
        std::memcpy(array->data, data, size);
    }

    return array;
}

// 设置和获取使能状态的方法
int DmsCoordinator::SetFatigueEnabled(bool enabled) {
    fatigue_enabled_ = enabled;
    TX_LOG_INFO("DmsCoordinator", "Fatigue detection %s", enabled ? "enabled" : "disabled");
    return 0;
}

bool DmsCoordinator::GetFatigueEnabled() const {
    return fatigue_enabled_;
}

int DmsCoordinator::SetDistractionEnabled(bool enabled) {
    distraction_enabled_ = enabled;
    TX_LOG_INFO("DmsCoordinator", "Distraction detection %s", enabled ? "enabled" : "disabled");
    return 0;
}

bool DmsCoordinator::GetDistractionEnabled() const {
    return distraction_enabled_;
}

// 报警状态管理方法
int DmsCoordinator::ResetAlarmState() {
    if (!initialized_ || !state_manager_) {
        TX_LOG_ERROR("DmsCoordinator", "DmsCoordinator not initialized");
        return -1;
    }

    state_manager_->ResetAlarmState();

    if (fatigue_module_) {
        fatigue_module_->ResetFatigueState();
    }

    if (distraction_module_) {
        distraction_module_->ResetDistractionState();
    }

    TX_LOG_INFO("DmsCoordinator", "Alarm state reset");
    return 0;
}

int DmsCoordinator::SetAlarmAcknowledged() {
    if (!initialized_ || !state_manager_) {
        TX_LOG_ERROR("DmsCoordinator", "DmsCoordinator not initialized");
        return -1;
    }

    state_manager_->SetAlarmAcknowledged();
    TX_LOG_INFO("DmsCoordinator", "Alarm acknowledged");
    return 0;
}

// 统计信息获取方法
void DmsCoordinator::GetFatigueInfo(tx_tired& tired_info) {
    if (!initialized_ || !fatigue_module_) {
        TX_LOG_ERROR("DmsCoordinator", "DmsCoordinator not initialized or fatigue module not available");
        memset(&tired_info, 0, sizeof(tired_info));
        return;
    }

    fatigue_module_->GetFatigueInfo(tired_info);
    TX_LOG_DEBUG("DmsCoordinator", "Fatigue info retrieved: eye_count=%d, yawn_count=%d",
                tired_info.total_eye_count, tired_info.yawn_count);
}

void DmsCoordinator::GetDistractionInfo(internal_analysis_distraction_info& distraction_info) {
    if (!initialized_ || !distraction_module_) {
        TX_LOG_ERROR("DmsCoordinator", "DmsCoordinator not initialized or distraction module not available");
        memset(&distraction_info, 0, sizeof(distraction_info));
        return;
    }

    distraction_module_->GetDistractionInfo(distraction_info);
    TX_LOG_DEBUG("DmsCoordinator", "Distraction info retrieved");
}

std::string DmsCoordinator::GetDistractionReason() {
    if (!initialized_ || !distraction_module_) {
        TX_LOG_ERROR("DmsCoordinator", "DmsCoordinator not initialized or distraction module not available");
        return "未初始化";
    }

    // 这里应该从distraction_module_获取分心原因
    // 简化处理，返回默认值
    return "正常";
}

std::string DmsCoordinator::GetDistractionParameters() {
    if (!initialized_ || !config_manager_) {
        TX_LOG_ERROR("DmsCoordinator", "DmsCoordinator not initialized");
        return "未初始化";
    }

    // 构建分心参数字符串
    std::string params = "";

    float head_pitch_threshold = config_manager_->GetParameter<float>("distraction_detection.head_pose.pitch_threshold", 25.0f);
    float head_yaw_threshold = config_manager_->GetParameter<float>("distraction_detection.head_pose.yaw_threshold", 35.0f);
    float head_roll_threshold = config_manager_->GetParameter<float>("distraction_detection.head_pose.roll_threshold", 20.0f);

    params += "头部姿态阈值: pitch=" + std::to_string(head_pitch_threshold) +
              ", yaw=" + std::to_string(head_yaw_threshold) +
              ", roll=" + std::to_string(head_roll_threshold);

    return params;
}

} // namespace tongxing
