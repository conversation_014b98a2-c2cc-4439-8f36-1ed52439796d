cmake_minimum_required(VERSION 3.10)
project(DmsProcessRefactor)

# 设置C++标准
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -g")
set(CMAKE_CXX_FLAGS_DEBUG "-O0 -g")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# 添加编译定义
add_definitions(-DJSON_IS_AMALGAMATION)

# 设置包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/managers
    ${CMAKE_CURRENT_SOURCE_DIR}/adapters
    ${CMAKE_CURRENT_SOURCE_DIR}/modules
    ${CMAKE_CURRENT_SOURCE_DIR}/coordinators
    ${CMAKE_CURRENT_SOURCE_DIR}/../interface
    ${CMAKE_CURRENT_SOURCE_DIR}/../../tongxing_util/src/util
    ${CMAKE_CURRENT_SOURCE_DIR}/../../tongxing_util/src/log
    ${CMAKE_CURRENT_SOURCE_DIR}/../../tongxing_util/src/module
    ${CMAKE_CURRENT_SOURCE_DIR}/../../tongxing_util/src/memory_manager
)

# 查找OpenCV
find_package(OpenCV QUIET)
if(OpenCV_FOUND)
    message(STATUS "Found OpenCV: ${OpenCV_VERSION}")
    add_definitions(-DWITH_OPENCV)
    include_directories(${OpenCV_INCLUDE_DIRS})
else()
    message(WARNING "OpenCV not found, some features will be disabled")
endif()

# 查找jsoncpp
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(JSONCPP jsoncpp)
endif()

# 源文件列表
set(TONGXING_UTIL_SOURCES
    ../../tongxing_util/src/log/CalmCarLog.cpp
    ../../tongxing_util/src/util/cc_blob_data.cpp
    ../../tongxing_util/src/util/cc_tensor.cpp
    ../../tongxing_util/src/memory_manager/cc_memory_pool.c
    ../../tongxing_util/src/memory_manager/cc_small_memory_pool.c
    ../../tongxing_util/src/memory_manager/cc_medium_memory_pool.c
    ../../tongxing_util/src/memory_manager/cc_big_memory_pool.c
    ../../tongxing_util/src/memory_manager/cc_tiny_mempool.cpp
    ../../tongxing_util/src/memory_manager/cc_memory_manager.cpp
    ../../tongxing_util/src/module/cc_module.cpp
)

# 如果找到OpenCV，添加相关源文件
if(OpenCV_FOUND)
    list(APPEND TONGXING_UTIL_SOURCES
        ../../tongxing_util/src/util/cc_numarray_tool.cpp
    )
endif()

# 重构模块源文件
set(REFACTOR_SOURCES
    managers/config_manager.cpp
    managers/state_manager.cpp
    modules/face_analysis_module.cpp
    modules/fatigue_detection_module.cpp
    modules/distraction_detection_module.cpp
)

# 如果找到OpenCV，添加数据适配器
if(OpenCV_FOUND)
    list(APPEND REFACTOR_SOURCES
        adapters/dms_data_adapter.cpp
        coordinators/dms_coordinator.cpp
    )
endif()

# 创建静态库
add_library(dms_process_refactor STATIC
    ${TONGXING_UTIL_SOURCES}
    ${REFACTOR_SOURCES}
)

# 链接库
target_link_libraries(dms_process_refactor
    ${CMAKE_DL_LIBS}
    pthread
)

if(OpenCV_FOUND)
    target_link_libraries(dms_process_refactor ${OpenCV_LIBS})
endif()

if(JSONCPP_FOUND)
    target_link_libraries(dms_process_refactor ${JSONCPP_LIBRARIES})
    target_include_directories(dms_process_refactor PRIVATE ${JSONCPP_INCLUDE_DIRS})
else()
    # 使用内置的jsoncpp
    target_sources(dms_process_refactor PRIVATE
        ../../tongxing_util/src/util/jsoncpp.cpp
    )
endif()

# 创建测试可执行文件（可选）
option(BUILD_TESTS "Build unit tests" OFF)
if(BUILD_TESTS)
    enable_testing()
    find_package(GTest REQUIRED)
    
    # 测试源文件
    set(TEST_SOURCES
        test/config_manager_test.cpp
        test/state_manager_test.cpp
        test/fatigue_detection_module_test.cpp
    )

    if(OpenCV_FOUND)
        list(APPEND TEST_SOURCES
            test/dms_data_adapter_test.cpp
            test/dms_coordinator_test.cpp
            test/integration_test.cpp
            test/performance_benchmark.cpp
        )
    endif()
    
    # 为每个测试创建可执行文件
    foreach(test_source ${TEST_SOURCES})
        get_filename_component(test_name ${test_source} NAME_WE)
        add_executable(${test_name} ${test_source})
        target_link_libraries(${test_name}
            dms_process_refactor
            GTest::GTest
            GTest::Main
        )
        add_test(NAME ${test_name} COMMAND ${test_name})
    endforeach()
endif()

# 安装规则
install(TARGETS dms_process_refactor
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# 安装头文件
install(DIRECTORY managers/ adapters/ modules/ coordinators/
    DESTINATION include/dms_process_refactor
    FILES_MATCHING PATTERN "*.h"
)

# 创建配置文件
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/dms_process_refactor_config.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/dms_process_refactor_config.cmake"
    @ONLY
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/dms_process_refactor_config.cmake"
    DESTINATION lib/cmake/dms_process_refactor
)
