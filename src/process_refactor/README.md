# DMS Process 模块化重构

## 📁 目录结构

```
src/process_refactor/
├── coordinator/           # 协调器层
│   └── dms_coordinator.h     # 统一协调器（替代CcDmsProcess）
├── modules/              # 基于tongxing的模块
│   ├── face_analysis_module.h        # 人脸分析模块
│   ├── fatigue_detection_module.h    # 疲劳检测模块
│   └── distraction_detection_module.h # 分心检测模块
├── adapters/             # 数据适配器
│   └── dms_data_adapter.h           # 数据格式转换（消除回调函数）
├── managers/             # 状态和配置管理
│   ├── state_manager.h              # 状态管理器
│   └── config_manager.h             # 配置管理器
├── utils/                # 工具类（待实现）
├── config/               # 配置文件
│   └── dms_pipeline.json            # 模块流水线配置
├── test/                 # 测试代码
│   └── modules_test.cpp             # 单元测试框架
└── README.md             # 本文件
```

## 🎯 设计目标

### 解决的问题
1. **职责边界模糊**：原有CcDmsProcess和DmsProcess职责重叠
2. **数据流混乱**：tongxing模块组 + 12个回调函数的双重数据流
3. **缺乏清晰分层**：业务逻辑、数据处理、状态管理混在一起

### 设计原则
1. **单一职责**：每个模块只负责一个明确的功能域
2. **依赖倒置**：依赖抽象接口而不是具体实现
3. **配置驱动**：通过JSON配置灵活组合模块
4. **充分利用tongxing框架**：所有模块基于CcModule实现

## 🏗️ 架构设计

### 分层架构
```
┌─────────────────────────────────────┐
│           Interface Layer          │  tx_dms_sdk.cpp
├─────────────────────────────────────┤
│         Coordinator Layer          │  DmsCoordinator
├─────────────────────────────────────┤
│          Adapter Layer             │  DmsDataAdapter
├─────────────────────────────────────┤
│          Module Layer              │  tongxing modules
├─────────────────────────────────────┤
│         Warning Layer              │  existing warning system
├─────────────────────────────────────┤
│         Manager Layer              │  StateManager, ConfigManager
└─────────────────────────────────────┘
```

### 数据流
```
TXImageInfo → DmsDataAdapter → ModulePipeline → WarningSystem → TXDmsResult
                ↓                    ↓              ↓
           NumArray[]         ProcessingResults  AlarmResults
```

## 🔧 核心组件

### 1. DmsCoordinator (协调器)
- **职责**：统一协调整个DMS处理流程
- **替代**：原有的CcDmsProcess
- **特点**：清晰的分层调用，消除职责混乱

### 2. DmsDataAdapter (数据适配器)
- **职责**：数据格式转换和预处理
- **替代**：原有的12个回调函数
- **特点**：统一的数据接口，消除回调依赖

### 3. 模块系统 (基于tongxing)
- **FaceAnalysisModule**：人脸分析（框、关键点、角度、属性）
- **FatigueDetectionModule**：疲劳检测（眼部、打哈欠）
- **DistractionDetectionModule**：分心检测（头部姿态、视线）

### 4. 管理器系统
- **StateManager**：状态管理（激活、硬件错误、标定）
- **ConfigManager**：配置管理（参数、阈值、车型配置）

## 📋 实施状态

### ✅ 已完成
- [x] 目录结构创建
- [x] 所有头文件骨架
- [x] 接口定义完成
- [x] JSON配置文件
- [x] 测试框架骨架

### 🚧 待实施
- [ ] 各模块的具体实现
- [ ] 数据适配器实现
- [ ] 配置管理器实现
- [ ] 状态管理器实现
- [ ] 协调器实现
- [ ] 单元测试实现

## 🚀 开始实施

### 1. 查看骨架结构
```bash
cd src/process_refactor
find . -name "*.h" -o -name "*.cpp" -o -name "*.json" | sort
```

### 2. 编译测试（需要先实现基本功能）
```bash
# 在项目根目录
mkdir -p build_refactor
cd build_refactor
cmake .. -DREFACTOR_BUILD=ON
make
```

### 3. 运行测试
```bash
./modules_test
```

## 📚 参考文档

- **原始分析**：`mydoc/src_process_模块化重构计划.md`
- **tongxing框架**：`tongxing_util/src/module/`
- **现有接口**：`src/interface/tx_dms_sdk.h`
- **配置示例**：`config/dms_pipeline.json`

## 🔄 迁移策略

1. **渐进式重构**：保持现有接口兼容性
2. **并行开发**：新旧代码并存，逐步切换
3. **充分测试**：每个组件都有对应的单元测试
4. **性能验证**：确保重构后性能不下降

---

**创建时间**：2025-01-26  
**状态**：骨架完成，等待实施  
**维护者**：My Lord
