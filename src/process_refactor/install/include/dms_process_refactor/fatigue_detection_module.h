#ifndef __FATIGUE_DETECTION_MODULE_H__
#define __FATIGUE_DETECTION_MODULE_H__

#include "cc_module.h"
#include "tx_dms_sdk.h"
#include "tx_dms_warning.h"
#include <deque>
#include <chrono>

namespace tongxing {

/**
 * @brief 眼睛状态信息
 */
struct EyeState {
    long timestamp;         // 时间戳
    bool is_closed;         // 是否闭眼
    float closure_score;    // 闭眼分数
    float opening_value;    // 眼睛开度值
    bool is_valid;          // 是否有效

    EyeState() : timestamp(0), is_closed(false), closure_score(0.0f),
                 opening_value(0.0f), is_valid(false) {}
};

/**
 * @brief 嘴部状态信息
 */
struct MouthState {
    long timestamp;         // 时间戳
    bool is_open;           // 是否张嘴
    float opening_score;    // 张嘴分数
    float opening_value;    // 嘴部开度值
    bool is_yawning;        // 是否打哈欠
    bool is_valid;          // 是否有效

    MouthState() : timestamp(0), is_open(false), opening_score(0.0f),
                   opening_value(0.0f), is_yawning(false), is_valid(false) {}
};

/**
 * @brief 疲劳检测结果
 */
struct FatigueResult {
    // 疲劳等级
    TXDrowsinessType fatigue_level;     // 疲劳等级

    // 眼部疲劳信息
    bool has_eye_fatigue;               // 是否存在眼部疲劳
    int fatigue_blink_count;            // 疲劳眨眼次数
    int long_closure_count;             // 长时间闭眼次数
    float eye_closure_ratio;            // 闭眼比例
    long eye_closure_duration_ms;       // 闭眼持续时间

    // 嘴部疲劳信息
    bool has_mouth_fatigue;             // 是否存在嘴部疲劳
    int yawn_count;                     // 打哈欠次数
    long mouth_open_duration_ms;        // 张嘴持续时间

    // 综合疲劳信息
    std::string fatigue_reason;         // 疲劳原因描述
    float fatigue_confidence;           // 疲劳置信度

    FatigueResult() : fatigue_level(Drowsiness_Normal), has_eye_fatigue(false),
                      fatigue_blink_count(0), long_closure_count(0),
                      eye_closure_ratio(0.0f), eye_closure_duration_ms(0),
                      has_mouth_fatigue(false), yawn_count(0),
                      mouth_open_duration_ms(0), fatigue_confidence(0.0f) {}
};

/**
 * @brief 疲劳检测模块
 * 
 * 基于tongxing框架的疲劳检测模块，专门负责疲劳相关的算法处理
 * 包括眼部疲劳检测、打哈欠检测、疲劳模式识别等
 */
class FatigueDetectionModule : public CcModule {
public:
    FatigueDetectionModule();
    ~FatigueDetectionModule() override;

    /**
     * @brief 初始化模块
     * @param config JSON配置
     * @return 0成功，非0失败
     */
    int init(const Json::Value& config) override;

    /**
     * @brief 设置输入数据
     * @param in 输入数据数组 [0]:人脸信息 [1]:眼部信息 [2]:车辆信息 [3]:时间戳
     * @return 0成功，非0失败
     */
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in) override;

    /**
     * @brief 执行疲劳检测
     * @return 0成功，非0失败
     */
    int execute() override;

    /**
     * @brief 获取输出数量
     * @return 输出数量
     */
    size_t getOutputNum() override;

    /**
     * @brief 获取指定输出
     * @param index 输出索引 [0]:疲劳检测结果 [1]:眼部状态 [2]:嘴部状态
     * @return 输出数据
     */
    std::shared_ptr<NumArray> getOutput(int index) override;

    /**
     * @brief 重置疲劳状态
     */
    void ResetFatigueState();

    /**
     * @brief 获取疲劳统计信息
     * @param tired_info 疲劳信息结构
     */
    void GetFatigueInfo(tx_tired& tired_info);

private:
    // 配置参数
    float eye_closure_threshold_;           // 闭眼阈值
    float mouth_opening_threshold_;         // 张嘴阈值
    float yawn_threshold_;                  // 打哈欠阈值

    // 时间窗口配置
    long fatigue_blink_duration_ms_;        // 疲劳眨眼持续时间阈值
    long long_closure_duration_ms_;         // 长时间闭眼持续时间阈值
    long yawn_duration_ms_;                 // 打哈欠持续时间阈值

    // 计数阈值配置
    int fatigue_blink_count_threshold_;     // 疲劳眨眼次数阈值
    int long_closure_count_threshold_;      // 长时间闭眼次数阈值
    int yawn_count_threshold_;              // 打哈欠次数阈值

    // 时间窗口大小
    long analysis_window_ms_;               // 分析时间窗口大小
    int max_history_frames_;                // 最大历史帧数

    // 输入输出数据
    std::vector<std::shared_ptr<NumArray>> input_;      // 输入数据
    std::shared_ptr<NumArray> fatigue_result_output_;   // 疲劳检测结果输出
    std::shared_ptr<NumArray> eye_state_output_;        // 眼部状态输出
    std::shared_ptr<NumArray> mouth_state_output_;      // 嘴部状态输出

    // 历史状态数据
    std::deque<EyeState> eye_history_;      // 眼部状态历史
    std::deque<MouthState> mouth_history_;  // 嘴部状态历史

    // 当前状态
    FatigueResult current_result_;          // 当前疲劳检测结果
    long last_analysis_time_;               // 上次分析时间

    /**
     * @brief 分析眼部状态
     * @param eye_data 眼部数据
     * @param face_info 人脸信息
     * @param timestamp 时间戳
     * @return 眼部状态
     */
    EyeState AnalyzeEyeState(const std::shared_ptr<NumArray>& eye_data,
                            const TXDmsFaceInfo& face_info, long timestamp);

    /**
     * @brief 分析嘴部状态
     * @param mouth_data 嘴部数据
     * @param face_info 人脸信息
     * @param timestamp 时间戳
     * @return 嘴部状态
     */
    MouthState AnalyzeMouthState(const std::shared_ptr<NumArray>& mouth_data,
                                const TXDmsFaceInfo& face_info, long timestamp);

    /**
     * @brief 检测疲劳眨眼
     * @param window_ms 时间窗口
     * @return 疲劳眨眼次数
     */
    int DetectFatigueBlinks(long window_ms);

    /**
     * @brief 检测长时间闭眼
     * @param window_ms 时间窗口
     * @param duration_threshold_ms 持续时间阈值
     * @return 长时间闭眼次数
     */
    int DetectLongEyeClosure(long window_ms, long duration_threshold_ms);

    /**
     * @brief 检测打哈欠
     * @param window_ms 时间窗口
     * @return 打哈欠次数
     */
    int DetectYawning(long window_ms);

    /**
     * @brief 计算眼部闭合比例
     * @param window_ms 时间窗口
     * @return 闭眼比例
     */
    float CalculateEyeClosureRatio(long window_ms);

    /**
     * @brief 评估疲劳等级
     * @param eye_fatigue 眼部疲劳信息
     * @param mouth_fatigue 嘴部疲劳信息
     * @return 疲劳等级
     */
    TXDrowsinessType EvaluateFatigueLevel(bool eye_fatigue, bool mouth_fatigue);

    /**
     * @brief 清理过期历史数据
     * @param current_time 当前时间
     */
    void CleanupExpiredHistory(long current_time);

    /**
     * @brief 验证眼部数据有效性
     * @param eye_landmark 眼部关键点
     * @return true有效，false无效
     */
    bool ValidateEyeData(const TXEyeLandmark& eye_landmark);

    /**
     * @brief 验证嘴部数据有效性
     * @param mouth_score 嘴部分数
     * @return true有效，false无效
     */
    bool ValidateMouthData(float mouth_score);

    /**
     * @brief 执行疲劳分析
     * @param current_timestamp 当前时间戳
     */
    void PerformFatigueAnalysis(long current_timestamp);

    /**
     * @brief 生成疲劳原因描述
     */
    void GenerateFatigueReason();

    /**
     * @brief 计算疲劳置信度
     * @return 疲劳置信度
     */
    float CalculateFatigueConfidence();

    /**
     * @brief 创建输出数据
     * @param data 数据指针
     * @param size 数据大小
     * @return NumArray智能指针
     */
    std::shared_ptr<NumArray> CreateOutput(void* data, size_t size);
};

} // namespace tongxing

// 注册模块到tongxing系统
// 模块注册 - 暂时注释掉，避免编译错误
// REGISTER_CC_MODULE(FatigueDetectionModule, FatigueDetectionModule);

#endif // __FATIGUE_DETECTION_MODULE_H__
