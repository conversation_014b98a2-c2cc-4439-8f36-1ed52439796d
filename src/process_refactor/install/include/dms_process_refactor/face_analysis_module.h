#ifndef __FACE_ANALYSIS_MODULE_H__
#define __FACE_ANALYSIS_MODULE_H__

#include "cc_module.h"
#include "tx_dms_sdk.h"

namespace tongxing {

// 简单的2D点结构，替代opencv依赖
struct Point2f {
    float x, y;
    Point2f() : x(0), y(0) {}
    Point2f(float x_, float y_) : x(x_), y(y_) {}
};

/**
 * @brief 人脸分析模块
 * 
 * 基于tongxing框架的标准模块，专门负责人脸相关的分析处理
 * 包括人脸框解析、关键点解析、角度解析、属性解析等
 */
class FaceAnalysisModule : public CcModule {
public:
    FaceAnalysisModule();
    ~FaceAnalysisModule() override;

    /**
     * @brief 初始化模块
     * @param config JSON配置
     * @return 0成功，非0失败
     */
    int init(const Json::Value& config) override;

    /**
     * @brief 设置输入数据
     * @param in 输入数据数组
     * @return 0成功，非0失败
     */
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in) override;

    /**
     * @brief 执行人脸分析
     * @return 0成功，非0失败
     */
    int execute() override;

    /**
     * @brief 获取输出数量
     * @return 输出数量
     */
    size_t getOutputNum() override;

    /**
     * @brief 获取指定输出
     * @param index 输出索引
     * @return 输出数据
     */
    std::shared_ptr<NumArray> getOutput(int index) override;

private:
    // 配置参数
    float face_bbox_threshold_;      // 人脸框阈值
    float face_angle_threshold_;     // 人脸角度阈值
    float face_attr_threshold_;      // 人脸属性阈值
    int min_face_height_px_;         // 最小人脸高度像素

    // 输入输出数据
    std::vector<std::shared_ptr<NumArray>> input_;      // 输入数据
    std::shared_ptr<NumArray> face_bbox_output_;        // 人脸框输出
    std::shared_ptr<NumArray> face_keypoints_output_;   // 人脸关键点输出
    std::shared_ptr<NumArray> face_angle_output_;       // 人脸角度输出
    std::shared_ptr<NumArray> face_attr_output_;        // 人脸属性输出
    std::shared_ptr<NumArray> face_valid_output_;       // 人脸有效性输出

    /**
     * @brief 解析人脸框信息
     * @param face_bbox_data 人脸框原始数据
     * @return 解析后的人脸框信息
     */
    TXDmsFaceInfo ParseFaceBbox(const std::shared_ptr<NumArray>& face_bbox_data);

    /**
     * @brief 解析人脸关键点
     * @param keypoints_data 关键点原始数据
     * @return 解析后的关键点信息
     */
    std::vector<Point2f> ParseFaceKeypoints(const std::shared_ptr<NumArray>& keypoints_data);

    /**
     * @brief 解析人脸角度
     * @param angle_data 角度原始数据
     * @param face_angle_score 人脸角度分数
     * @param mouth_score 嘴部分数
     */
    void ParseFaceAngle(const std::shared_ptr<NumArray>& angle_data,
                       float& face_angle_score, float& mouth_score);

    /**
     * @brief 解析人脸属性
     * @param attr_data 属性原始数据
     * @return 人脸属性信息
     */
    TXDmsFaceInfo ParseFaceAttributes(const std::shared_ptr<NumArray>& attr_data);

    /**
     * @brief 验证人脸有效性
     * @param face_info 人脸信息
     * @return true有效，false无效
     */
    bool ValidateFace(const TXDmsFaceInfo& face_info);

    /**
     * @brief 验证关键点有效性
     * @param keypoints 关键点数据
     * @return true有效，false无效
     */
    bool ValidateKeypoints(const std::vector<Point2f>& keypoints);

    /**
     * @brief 创建输出数据
     * @param data 数据指针
     * @param size 数据大小
     * @return NumArray智能指针
     */
    std::shared_ptr<NumArray> CreateOutput(void* data, size_t size);
};

} // namespace tongxing

// 注册模块到tongxing系统
// 模块注册 - 暂时注释掉，避免编译错误
// REGISTER_CC_MODULE(FaceAnalysisModule, FaceAnalysisModule);

#endif // __FACE_ANALYSIS_MODULE_H__
