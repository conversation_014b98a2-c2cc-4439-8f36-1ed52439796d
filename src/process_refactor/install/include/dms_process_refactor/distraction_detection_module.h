#ifndef __DISTRACTION_DETECTION_MODULE_H__
#define __DISTRACTION_DETECTION_MODULE_H__

#include "cc_module.h"
#include "tx_dms_sdk.h"
#include "tx_dms_warning.h"
#include <deque>
#include <chrono>

namespace tongxing {

/**
 * @brief 头部姿态信息
 */
struct HeadPoseState {
    long timestamp;         // 时间戳
    float pitch;           // 俯仰角
    float yaw;             // 偏航角
    float roll;            // 翻滚角
    bool is_valid;         // 是否有效
    bool is_distracted;    // 是否分心

    HeadPoseState() : timestamp(0), pitch(0.0f), yaw(0.0f), roll(0.0f),
                      is_valid(false), is_distracted(false) {}
};

/**
 * @brief 视线信息
 */
struct GazeState {
    long timestamp;         // 时间戳
    float gaze_pitch;      // 视线俯仰角
    float gaze_yaw;        // 视线偏航角
    bool is_valid;         // 是否有效
    bool is_distracted;    // 是否分心

    GazeState() : timestamp(0), gaze_pitch(0.0f), gaze_yaw(0.0f),
                  is_valid(false), is_distracted(false) {}
};

/**
 * @brief 行为检测状态
 */
struct BehaviorState {
    long timestamp;         // 时间戳
    bool phone_detected;    // 是否检测到打电话
    bool smoking_detected;  // 是否检测到抽烟
    float phone_score;      // 打电话置信度
    float smoking_score;    // 抽烟置信度
    bool is_valid;          // 是否有效

    BehaviorState() : timestamp(0), phone_detected(false), smoking_detected(false),
                      phone_score(0.0f), smoking_score(0.0f), is_valid(false) {}
};

/**
 * @brief 分心检测结果
 */
struct DistractionResult {
    // 分心等级
    TXDistractionType distraction_level;    // 分心等级

    // 头部姿态分心信息
    bool has_head_distraction;              // 是否存在头部姿态分心
    int head_distraction_count;             // 头部分心次数
    long head_distraction_duration_ms;      // 头部分心持续时间

    // 视线分心信息
    bool has_gaze_distraction;              // 是否存在视线分心
    int gaze_distraction_count;             // 视线分心次数
    long gaze_distraction_duration_ms;      // 视线分心持续时间

    // 行为分心信息
    bool has_behavior_distraction;          // 是否存在行为分心
    int phone_count;                        // 打电话次数
    int smoking_count;                      // 抽烟次数

    // 综合分心信息
    std::string distraction_reason;         // 分心原因描述
    float distraction_confidence;           // 分心置信度

    DistractionResult() : distraction_level(Distraction_Normal), has_head_distraction(false),
                         head_distraction_count(0), head_distraction_duration_ms(0),
                         has_gaze_distraction(false), gaze_distraction_count(0),
                         gaze_distraction_duration_ms(0), has_behavior_distraction(false),
                         phone_count(0), smoking_count(0), distraction_confidence(0.0f) {}
};

/**
 * @brief 分心检测模块
 * 
 * 基于tongxing框架的分心检测模块，专门负责分心相关的算法处理
 * 包括头部姿态分析、视线方向分析、分心行为识别等
 */
class DistractionDetectionModule : public CcModule {
public:
    DistractionDetectionModule();
    ~DistractionDetectionModule() override;

    /**
     * @brief 初始化模块
     * @param config JSON配置
     * @return 0成功，非0失败
     */
    int init(const Json::Value& config) override;

    /**
     * @brief 设置输入数据
     * @param in 输入数据数组 [0]:人脸信息 [1]:头部姿态 [2]:视线数据 [3]:行为数据 [4]:车辆信息 [5]:时间戳
     * @return 0成功，非0失败
     */
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in) override;

    /**
     * @brief 执行分心检测
     * @return 0成功，非0失败
     */
    int execute() override;

    /**
     * @brief 获取输出数量
     * @return 输出数量
     */
    size_t getOutputNum() override;

    /**
     * @brief 获取指定输出
     * @param index 输出索引 [0]:分心检测结果 [1]:头部姿态状态 [2]:视线状态 [3]:行为状态
     * @return 输出数据
     */
    std::shared_ptr<NumArray> getOutput(int index) override;

    /**
     * @brief 重置分心状态
     */
    void ResetDistractionState();

    /**
     * @brief 获取分心统计信息
     * @param distraction_info 分心信息结构
     */
    void GetDistractionInfo(internal_analysis_distraction_info& distraction_info);

private:
    // 配置参数
    float head_pose_pitch_threshold_;       // 头部姿态pitch阈值
    float head_pose_yaw_threshold_;         // 头部姿态yaw阈值
    float head_pose_roll_threshold_;        // 头部姿态roll阈值
    float gaze_pitch_threshold_;            // 视线pitch阈值
    float gaze_yaw_threshold_;              // 视线yaw阈值
    float phone_threshold_;                 // 打电话检测阈值
    float smoking_threshold_;               // 抽烟检测阈值
    float head_pose_threshold_;             // 头部姿态阈值（简化版本）
    float gaze_threshold_;                  // 视线阈值（简化版本）

    // 时间窗口配置
    long distraction_duration_threshold_ms_;    // 分心持续时间阈值
    long analysis_window_ms_;                   // 分析时间窗口大小
    int max_history_frames_;                    // 最大历史帧数

    // 计数阈值配置
    int head_distraction_count_threshold_;      // 头部分心次数阈值
    int gaze_distraction_count_threshold_;      // 视线分心次数阈值
    int behavior_count_threshold_;              // 行为分心次数阈值

    // 输入输出数据
    std::vector<std::shared_ptr<NumArray>> input_;          // 输入数据
    std::shared_ptr<NumArray> distraction_result_output_;   // 分心检测结果输出
    std::shared_ptr<NumArray> head_pose_state_output_;      // 头部姿态状态输出
    std::shared_ptr<NumArray> gaze_state_output_;           // 视线状态输出
    std::shared_ptr<NumArray> behavior_state_output_;       // 行为状态输出

    // 历史状态数据
    std::deque<HeadPoseState> head_pose_history_;   // 头部姿态历史
    std::deque<GazeState> gaze_history_;            // 视线状态历史
    std::deque<BehaviorState> behavior_history_;    // 行为状态历史

    // 当前状态
    DistractionResult current_result_;      // 当前分心检测结果
    long last_analysis_time_;               // 上次分析时间

    /**
     * @brief 分析头部姿态状态
     * @param head_pose_data 头部姿态数据
     * @param face_info 人脸信息
     * @param timestamp 时间戳
     * @return 头部姿态状态
     */
    HeadPoseState AnalyzeHeadPoseState(const std::shared_ptr<NumArray>& head_pose_data,
                                      const TXDmsFaceInfo& face_info, long timestamp);

    /**
     * @brief 分析视线状态
     * @param gaze_data 视线数据
     * @param face_info 人脸信息
     * @param timestamp 时间戳
     * @return 视线状态
     */
    GazeState AnalyzeGazeState(const std::shared_ptr<NumArray>& gaze_data,
                              const TXDmsFaceInfo& face_info, long timestamp);

    /**
     * @brief 分析行为状态
     * @param behavior_data 行为数据
     * @param timestamp 时间戳
     * @return 行为状态
     */
    BehaviorState AnalyzeBehaviorState(const std::shared_ptr<NumArray>& behavior_data,
                                      long timestamp);

    /**
     * @brief 检测头部姿态分心
     * @param window_ms 时间窗口
     * @return 头部分心次数
     */
    int DetectHeadPoseDistraction(long window_ms);

    /**
     * @brief 检测视线分心
     * @param window_ms 时间窗口
     * @return 视线分心次数
     */
    int DetectGazeDistraction(long window_ms);

    /**
     * @brief 检测行为分心
     * @param window_ms 时间窗口
     * @return 行为分心次数
     */
    int DetectBehaviorDistraction(long window_ms);

    /**
     * @brief 评估分心等级
     * @param head_distraction 头部分心信息
     * @param gaze_distraction 视线分心信息
     * @param behavior_distraction 行为分心信息
     * @return 分心等级
     */
    TXDistractionType EvaluateDistractionLevel(bool head_distraction,
                                               bool gaze_distraction,
                                               bool behavior_distraction);

    /**
     * @brief 清理过期历史数据
     * @param current_time 当前时间
     */
    void CleanupExpiredHistory(long current_time);

    /**
     * @brief 验证头部姿态数据有效性
     * @param head_pose_state 头部姿态状态
     * @return true有效，false无效
     */
    bool ValidateHeadPoseData(const HeadPoseState& head_pose_state);

    /**
     * @brief 验证视线数据有效性
     * @param gaze_state 视线状态
     * @return true有效，false无效
     */
    bool ValidateGazeData(const GazeState& gaze_state);

    /**
     * @brief 验证行为数据有效性
     * @param behavior_state 行为状态
     * @return true有效，false无效
     */
    bool ValidateBehaviorData(const BehaviorState& behavior_state);

    /**
     * @brief 执行分心分析
     * @param current_timestamp 当前时间戳
     */
    void PerformDistractionAnalysis(long current_timestamp);

    /**
     * @brief 生成分心原因描述
     */
    void GenerateDistractionReason();

    /**
     * @brief 计算分心置信度
     * @return 分心置信度
     */
    float CalculateDistractionConfidence();

    /**
     * @brief 分析头部姿态（简化版本）
     * @param pose_data 头部姿态数据
     * @return 是否分心
     */
    bool AnalyzeHeadPose(const std::shared_ptr<NumArray>& pose_data);

    /**
     * @brief 分析视线方向（简化版本）
     * @param gaze_data 视线数据
     * @return 是否分心
     */
    bool AnalyzeGazeDirection(const std::shared_ptr<NumArray>& gaze_data);

    /**
     * @brief 分析打电话行为
     * @param phone_data 打电话数据
     * @return 是否检测到打电话
     */
    bool AnalyzePhoneUsage(const std::shared_ptr<NumArray>& phone_data);

    /**
     * @brief 分析抽烟行为
     * @param smoking_data 抽烟数据
     * @return 是否检测到抽烟
     */
    bool AnalyzeSmokingBehavior(const std::shared_ptr<NumArray>& smoking_data);



    /**
     * @brief 创建输出数据
     * @param data 数据指针
     * @param size 数据大小
     * @return NumArray智能指针
     */
    std::shared_ptr<NumArray> CreateOutput(void* data, size_t size);
};

} // namespace tongxing

// 注册模块到tongxing系统
// 模块注册 - 暂时注释掉，避免编译错误
// REGISTER_CC_MODULE(DistractionDetectionModule, DistractionDetectionModule);

#endif // __DISTRACTION_DETECTION_MODULE_H__
