#ifndef __DMS_COORDINATOR_H__
#define __DMS_COORDINATOR_H__

#include "tx_dms_sdk.h"
#include "tx_dms_warning.h"
#include "json.h"
#include "cc_numarray_tool.h"
#include <memory>
#include <vector>

// 前向声明
namespace tongxing {
    class ConfigManager;
    class StateManager;
    class DmsDataAdapter;
    class FaceAnalysisModule;
    class FatigueDetectionModule;
    class DistractionDetectionModule;
}

namespace tongxing {

/**
 * @brief DMS系统协调器
 * 
 * 统一协调器，替代原有的CcDmsProcess，集成所有下层组件
 * 实现完整的DMS处理流水线，包括数据流转、模块协调、结果整合
 */
class DmsCoordinator {
public:
    DmsCoordinator();
    ~DmsCoordinator();

    /**
     * @brief 初始化DMS协调器
     * @param config_file 配置文件路径
     * @return 0成功，非0失败
     */
    int Init(const char* config_file);

    /**
     * @brief 处理单帧DMS数据
     * @param image_info 图像信息
     * @param car_info 车辆信息
     * @param result_out 输出结果
     * @param timestamp 时间戳（可选，默认使用当前时间）
     * @return 0成功，非0失败
     */
    int ProcessFrame(const TXImageInfo* image_info,
                    const TXCarInfo* car_info,
                    TXDmsResult& result_out,
                    long timestamp = 0);

    /**
     * @brief 设置疲劳检测使能状态
     * @param enabled 是否启用疲劳检测
     * @return 0成功，非0失败
     */
    int SetFatigueEnabled(bool enabled);

    /**
     * @brief 获取疲劳检测使能状态
     * @return true启用，false禁用
     */
    bool GetFatigueEnabled() const;

    /**
     * @brief 设置分心检测使能状态
     * @param enabled 是否启用分心检测
     * @return 0成功，非0失败
     */
    int SetDistractionEnabled(bool enabled);

    /**
     * @brief 获取分心检测使能状态
     * @return true启用，false禁用
     */
    bool GetDistractionEnabled() const;

    /**
     * @brief 重置报警状态
     * @return 0成功，非0失败
     */
    int ResetAlarmState();

    /**
     * @brief 设置报警确认
     * @return 0成功，非0失败
     */
    int SetAlarmAcknowledged();

    /**
     * @brief 获取疲劳统计信息
     * @param tired_info 疲劳信息输出
     */
    void GetFatigueInfo(tx_tired& tired_info);

    /**
     * @brief 获取分心统计信息
     * @param distraction_info 分心信息输出
     */
    void GetDistractionInfo(internal_analysis_distraction_info& distraction_info);

    /**
     * @brief 获取分心原因描述
     * @return 分心原因字符串
     */
    std::string GetDistractionReason();

    /**
     * @brief 获取分心参数信息
     * @return 分心参数字符串
     */
    std::string GetDistractionParameters();

private:
    // 核心组件
    std::unique_ptr<ConfigManager> config_manager_;         // 配置管理器
    std::unique_ptr<StateManager> state_manager_;           // 状态管理器
    std::unique_ptr<DmsDataAdapter> data_adapter_;          // 数据适配器
    
    // 检测模块
    std::unique_ptr<FaceAnalysisModule> face_module_;       // 人脸分析模块
    std::unique_ptr<FatigueDetectionModule> fatigue_module_; // 疲劳检测模块
    std::unique_ptr<DistractionDetectionModule> distraction_module_; // 分心检测模块

    // 配置参数
    Json::Value config_;                    // 完整配置
    bool fatigue_enabled_;                  // 疲劳检测使能
    bool distraction_enabled_;              // 分心检测使能
    
    // 处理状态
    bool initialized_;                      // 是否已初始化
    long last_process_time_;                // 上次处理时间
    
    /**
     * @brief 初始化所有组件
     * @return 0成功，非0失败
     */
    int InitializeComponents();

    /**
     * @brief 处理人脸分析
     * @param adapted_inputs 适配后的输入数据
     * @param timestamp 时间戳
     * @return 人脸分析结果
     */
    TXDmsFaceInfo ProcessFaceAnalysis(const std::vector<std::shared_ptr<NumArray>>& adapted_inputs,
                                     long timestamp);

    /**
     * @brief 处理疲劳检测
     * @param face_info 人脸信息
     * @param timestamp 时间戳
     * @return 疲劳检测结果
     */
    TXDrowsinessType ProcessFatigueDetection(const TXDmsFaceInfo& face_info, long timestamp);

    /**
     * @brief 处理分心检测
     * @param face_info 人脸信息
     * @param car_info 车辆信息
     * @param timestamp 时间戳
     * @return 分心检测结果
     */
    TXDistractionType ProcessDistractionDetection(const TXDmsFaceInfo& face_info,
                                                  const TXCarInfo* car_info,
                                                  long timestamp);

    /**
     * @brief 整合检测结果
     * @param face_info 人脸信息
     * @param fatigue_type 疲劳类型
     * @param distraction_type 分心类型
     * @param car_info 车辆信息
     * @param timestamp 时间戳
     * @param result_out 输出结果
     */
    void IntegrateResults(const TXDmsFaceInfo& face_info,
                         TXDrowsinessType fatigue_type,
                         TXDistractionType distraction_type,
                         const TXCarInfo* car_info,
                         long timestamp,
                         TXDmsResult& result_out);

    /**
     * @brief 验证输入数据
     * @param image_info 图像信息
     * @param car_info 车辆信息
     * @return true有效，false无效
     */
    bool ValidateInputs(const TXImageInfo* image_info, const TXCarInfo* car_info);

    /**
     * @brief 获取当前时间戳
     * @return 当前时间戳（毫秒）
     */
    long GetCurrentTimestamp();

    /**
     * @brief 更新系统状态
     * @param car_info 车辆信息
     * @param timestamp 时间戳
     */
    void UpdateSystemState(const TXCarInfo* car_info, long timestamp);

    /**
     * @brief 检查算法激活状态
     * @param car_info 车辆信息
     * @param timestamp 时间戳
     * @return true激活，false未激活
     */
    bool CheckAlgorithmActivation(const TXCarInfo* car_info, long timestamp);

    /**
     * @brief 应用速度阈值限制
     * @param fatigue_type 疲劳类型（输入输出）
     * @param distraction_type 分心类型（输入输出）
     * @param car_info 车辆信息
     */
    void ApplySpeedThresholds(TXDrowsinessType& fatigue_type,
                             TXDistractionType& distraction_type,
                             const TXCarInfo* car_info);

    /**
     * @brief 处理摄像头状态
     * @param image_info 图像信息
     * @param car_info 车辆信息
     * @param timestamp 时间戳
     * @return 摄像头状态
     */
    TXCameraStatus ProcessCameraStatus(const TXImageInfo* image_info,
                                      const TXCarInfo* car_info,
                                      long timestamp);

    /**
     * @brief 创建NumArray数据
     * @param data 数据指针
     * @param size 数据大小
     * @param type 数据类型
     * @return NumArray智能指针
     */
    std::shared_ptr<NumArray> CreateNumArray(void* data, size_t size, NumArray::DataType type);
};

} // namespace tongxing

#endif // __DMS_COORDINATOR_H__
