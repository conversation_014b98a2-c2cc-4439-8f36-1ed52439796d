# DMS Process Refactor Configuration File

# 版本信息
set(DMS_PROCESS_REFACTOR_VERSION_MAJOR 1)
set(DMS_PROCESS_REFACTOR_VERSION_MINOR 0)
set(DMS_PROCESS_REFACTOR_VERSION_PATCH 0)
set(DMS_PROCESS_REFACTOR_VERSION "${DMS_PROCESS_REFACTOR_VERSION_MAJOR}.${DMS_PROCESS_REFACTOR_VERSION_MINOR}.${DMS_PROCESS_REFACTOR_VERSION_PATCH}")

# 安装路径
set(DMS_PROCESS_REFACTOR_INSTALL_PREFIX "/home/<USER>/byd_ddaw_addw_optimised/byd_ddaw_addw/src/process_refactor/install")
set(DMS_PROCESS_REFACTOR_INCLUDE_DIRS "${DMS_PROCESS_REFACTOR_INSTALL_PREFIX}/include/dms_process_refactor")
set(DMS_PROCESS_REFACTOR_LIBRARIES "${DMS_PROCESS_REFACTOR_INSTALL_PREFIX}/lib/libdms_process_refactor.a")

# 依赖项
set(DMS_PROCESS_REFACTOR_REQUIRES_OPENCV 1)
if(DMS_PROCESS_REFACTOR_REQUIRES_OPENCV)
    find_package(OpenCV REQUIRED)
    list(APPEND DMS_PROCESS_REFACTOR_LIBRARIES ${OpenCV_LIBS})
    list(APPEND DMS_PROCESS_REFACTOR_INCLUDE_DIRS ${OpenCV_INCLUDE_DIRS})
endif()

# 编译定义
set(DMS_PROCESS_REFACTOR_DEFINITIONS
    -DJSON_IS_AMALGAMATION
)

if(DMS_PROCESS_REFACTOR_REQUIRES_OPENCV)
    list(APPEND DMS_PROCESS_REFACTOR_DEFINITIONS -DWITH_OPENCV)
endif()

# 使用示例
# find_package(dms_process_refactor REQUIRED)
# target_link_libraries(your_target ${DMS_PROCESS_REFACTOR_LIBRARIES})
# target_include_directories(your_target PRIVATE ${DMS_PROCESS_REFACTOR_INCLUDE_DIRS})
# target_compile_definitions(your_target PRIVATE ${DMS_PROCESS_REFACTOR_DEFINITIONS})
