#ifndef __DMS_COORDINATOR_H__
#define __DMS_COORDINATOR_H__

#include <memory>
#include <string>
#include "tx_dms_sdk.h"
#include "cc_module_grop.h"

namespace tongxing {

// 前向声明
class DmsDataAdapter;
class StateManager;
class ConfigManager;
class ResultFormatter;

/**
 * @brief DMS系统统一协调器
 * 
 * 负责协调整个DMS处理流程，替代原有的CcDmsProcess
 * 采用清晰的分层架构，消除职责混乱问题
 */
class DmsCoordinator {
public:
    DmsCoordinator();
    ~DmsCoordinator();

    /**
     * @brief 初始化协调器
     * @param config_file 配置文件路径
     * @param cache_path 缓存路径
     * @return 0成功，非0失败
     */
    int Init(const char* config_file = nullptr, const char* cache_path = nullptr);

    /**
     * @brief 设置输入图像并处理
     * @param image 输入图像信息
     * @param result 输出结果
     * @return 0成功，非0失败
     */
    int SetInput(const TXImageInfo* image, TXDmsResult* result);

    /**
     * @brief 设置输入图像并处理（带时间戳）
     * @param image 输入图像信息
     * @param result 输出结果
     * @param ts 时间戳
     * @return 0成功，非0失败
     */
    int SetInput(const TXImageInfo* image, TXDmsResult* result, long ts);

    /**
     * @brief 更新车辆信息
     * @param carInfo 车辆信息
     * @return 0成功，非0失败
     */
    int UpdateCarInfo(const TXCarInfo* carInfo);

    /**
     * @brief 设置DMS状态
     * @param state 状态
     * @return 0成功，非0失败
     */
    int SetDmsState(bool state);

    /**
     * @brief 获取DMS状态
     * @return DMS状态
     */
    bool GetDmsState();

    /**
     * @brief 复位报警
     * @return 0成功，非0失败
     */
    int RestAlarm();

    /**
     * @brief 复位分心报警
     * @return 0成功，非0失败
     */
    int RestDistractAlarm();

    /**
     * @brief 报警确认
     * @return 0成功，非0失败
     */
    int AlarmSetOk();

    /**
     * @brief 获取版本信息
     * @param version 版本字符串
     * @return 0成功，非0失败
     */
    int GetVersion(std::string& version);

private:
    // 核心组件
    std::shared_ptr<CcModuleGrop> module_pipeline_;     // tongxing模块流水线
    std::unique_ptr<DmsDataAdapter> data_adapter_;      // 数据适配器
    std::unique_ptr<StateManager> state_manager_;       // 状态管理器
    std::unique_ptr<ConfigManager> config_manager_;     // 配置管理器
    std::unique_ptr<ResultFormatter> result_formatter_; // 结果格式化器

    // 状态变量
    bool dms_enabled_;
    long long frame_id_;
    TXCarInfo car_info_;
    std::string cache_path_;

    /**
     * @brief 执行模块流水线处理
     * @param adapted_input 适配后的输入数据
     * @return 处理结果
     */
    int ExecutePipeline(const std::vector<std::shared_ptr<NumArray>>& adapted_input);

    /**
     * @brief 处理结果整合
     * @param pipeline_results 流水线处理结果
     * @param result 最终输出结果
     * @param ts 时间戳
     * @return 0成功，非0失败
     */
    int IntegrateResults(const std::vector<std::shared_ptr<NumArray>>& pipeline_results,
                        TXDmsResult& result, long ts);

    /**
     * @brief 验证输入参数
     * @param image 输入图像
     * @param result 结果指针
     * @return true有效，false无效
     */
    bool ValidateInput(const TXImageInfo* image, TXDmsResult* result);
};

} // namespace tongxing

#endif // __DMS_COORDINATOR_H__
