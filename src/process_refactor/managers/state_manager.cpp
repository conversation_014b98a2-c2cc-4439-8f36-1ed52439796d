#include "state_manager.h"
#include "CalmCarLog.h"
#include <ctime>

namespace tongxing {

StateManager::StateManager()
    : dms_enabled_(false)
    , calibrate_status_(CALIBRATE_CONDITION_UNDONE)
    , system_status_(SYSTEM_NORMAL)
    , algorithm_status_(ALGORITHM_UNACTIVATED_NO_WORK)
    , camera_status_(Camera_Normal)
    , last_activation_check_time_(0)
    , algorithm_start_time_(0)
    , use_activation_check_(true)
    , activation_timeout_(300000)  // 5分钟默认超时
    , camera_fault_threshold_(3)
    , can_fault_threshold_(5) {
    TX_LOG_INFO("StateManager", "StateManager constructed");
}

StateManager::~StateManager() {
    TX_LOG_INFO("StateManager", "StateManager destructed");
}

int StateManager::Init(const Json::Value& config) {
    // 从配置中读取参数
    if (config.isMember("activation")) {
        const Json::Value& activation_config = config["activation"];
        use_activation_check_ = activation_config.get("license_check_enabled", true).asBool();
        activation_timeout_ = activation_config.get("timeout_ms", 300000).asInt64();
    }

    if (config.isMember("hardware_monitoring")) {
        const Json::Value& hw_config = config["hardware_monitoring"];
        camera_fault_threshold_ = hw_config.get("camera_fault_threshold", 3).asInt();
        can_fault_threshold_ = hw_config.get("can_fault_threshold", 5).asInt();
    }

    // 初始化状态
    dms_enabled_ = false;
    calibrate_status_ = CALIBRATE_CONDITION_UNDONE;
    system_status_ = SYSTEM_NORMAL;
    algorithm_status_ = ALGORITHM_UNACTIVATED_NO_WORK;
    camera_status_ = Camera_Normal;

    TX_LOG_INFO("StateManager", "StateManager initialized successfully");
    return 0;
}

bool StateManager::IsAlgorithmActivated(TXDmsResult& result, long timestamp) {
    // 检查DMS是否使能
    if (!dms_enabled_) {
        algorithm_status_ = ALGORITHM_UNACTIVATED_NO_WORK;
        result.algorithm_status = algorithm_status_;
        return false;
    }

    // 检查激活许可
    if (use_activation_check_ && !CheckActivationLicense(timestamp)) {
        algorithm_status_ = ALGORITHM_UNACTIVATED_EXPERIENCE_TIME;
        result.algorithm_status = algorithm_status_;
        TX_LOG_WARN("StateManager", "Algorithm activation license expired");
        return false;
    }

    // 更新算法状态
    UpdateAlgorithmStatus(timestamp);
    result.algorithm_status = algorithm_status_;

    return (algorithm_status_ == ALGORITHM_NORMAL);
}

bool StateManager::CheckHardwareError(const TXCarInfo* car_info, TXDmsResult& result) {
    if (car_info == nullptr) {
        TX_LOG_ERROR("StateManager", "Car info is null");
        return true;
    }

    bool has_error = false;

    // 检查CAN总线状态
    if (!CheckCanBusStatus(car_info)) {
        system_status_ = SYSTEM_CAN_FAULT;
        has_error = true;
        TX_LOG_ERROR("StateManager", "CAN bus error detected");
    }

    // 检查摄像头硬件状态
    if (!CheckCameraHardwareStatus(car_info)) {
        system_status_ = SYSTEM_CAMERA_FAULT;
        has_error = true;
        TX_LOG_ERROR("StateManager", "Camera hardware error detected");
    }

    // 更新系统状态
    if (!has_error) {
        system_status_ = SYSTEM_NORMAL;
    }

    UpdateSystemStatus(car_info);
    result.system_status = system_status_;

    return has_error;
}

TXCameraStatus StateManager::GetCameraStatus(const TXCarInfo* car_info,
                                            long timestamp,
                                            uint8_t camera_states) {
    if (car_info == nullptr) {
        camera_status_ = Camera_Invalid;
        return camera_status_;
    }

    // 检查摄像头状态位
    if (CheckCameraState(camera_states, 0x01)) {  // 假设0x01表示正常状态
        camera_status_ = Camera_Normal;
    } else if (CheckCameraState(camera_states, 0x02)) {  // 假设0x02表示遮挡
        camera_status_ = Camera_Occlusion;
    } else if (CheckCameraState(camera_states, 0x04)) {  // 假设0x04表示故障
        camera_status_ = Camera_Invalid;
    } else {
        camera_status_ = Camera_Invalid;
    }

    TX_LOG_DEBUG("StateManager", "Camera status updated: %d", camera_status_);
    return camera_status_;
}

void StateManager::SetDmsEnabled(bool enabled) {
    dms_enabled_ = enabled;
    if (enabled) {
        algorithm_start_time_ = time(nullptr) * 1000;  // 转换为毫秒
        TX_LOG_INFO("StateManager", "DMS enabled");
    } else {
        algorithm_status_ = ALGORITHM_UNACTIVATED_NO_WORK;
        TX_LOG_INFO("StateManager", "DMS disabled");
    }
}

bool StateManager::IsDmsEnabled() const {
    return dms_enabled_;
}

void StateManager::UpdateCalibrateStatus(TXDistracCaliStatus calibrate_status) {
    calibrate_status_ = calibrate_status;
    TX_LOG_DEBUG("StateManager", "Calibrate status updated: %d", calibrate_status);
}

TXDistracCaliStatus StateManager::GetCalibrateStatus() const {
    return calibrate_status_;
}

void StateManager::ResetAlarmState() {
    // 重置所有报警状态
    TX_LOG_INFO("StateManager", "All alarm states reset");
}

void StateManager::ResetDistractionAlarmState() {
    // 重置分心报警状态
    TX_LOG_INFO("StateManager", "Distraction alarm state reset");
}

void StateManager::SetAlarmAcknowledged() {
    // 设置报警已确认
    TX_LOG_INFO("StateManager", "Alarm acknowledged");
}

TXSystemStatus StateManager::GetSystemStatus() const {
    return system_status_;
}

TXAlgorithmStatus StateManager::GetAlgorithmStatus() const {
    return algorithm_status_;
}

bool StateManager::CheckActivationLicense(long timestamp) {
    // 简化的激活检查逻辑
    if (!use_activation_check_) {
        return true;
    }

    // 检查是否超时
    if (algorithm_start_time_ > 0) {
        long elapsed = timestamp - algorithm_start_time_;
        if (elapsed > activation_timeout_) {
            TX_LOG_WARN("StateManager", "Activation timeout: %ld ms", elapsed);
            return false;
        }
    }

    last_activation_check_time_ = timestamp;
    return true;
}

bool StateManager::CheckCanBusStatus(const TXCarInfo* car_info) {
    // 检查CAN总线状态
    // 这里可以根据实际的CAN状态字段进行检查
    return true;  // 简化实现，假设CAN总线正常
}

bool StateManager::CheckCameraHardwareStatus(const TXCarInfo* car_info) {
    // 检查摄像头硬件状态
    // 这里可以根据实际的硬件状态字段进行检查
    return true;  // 简化实现，假设摄像头硬件正常
}

void StateManager::UpdateSystemStatus(const TXCarInfo* car_info) {
    // 根据车辆信息更新系统状态
    // 这里可以添加更复杂的状态更新逻辑
    TX_LOG_DEBUG("StateManager", "System status updated");
}

void StateManager::UpdateAlgorithmStatus(long timestamp) {
    if (dms_enabled_) {
        algorithm_status_ = ALGORITHM_NORMAL;
    } else {
        algorithm_status_ = ALGORITHM_UNACTIVATED_NO_WORK;
    }
    TX_LOG_DEBUG("StateManager", "Algorithm status updated: %d", algorithm_status_);
}

bool StateManager::CheckCameraState(uint8_t camera_states, uint8_t target_state) {
    return (camera_states & target_state) != 0;
}

} // namespace tongxing
