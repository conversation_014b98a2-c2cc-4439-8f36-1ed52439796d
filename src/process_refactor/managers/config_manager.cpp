#include "config_manager.h"
#include <fstream>
#include <iostream>
#include <sstream>
#include <algorithm>
#include "CalmCarLog.h"

namespace tongxing {

ConfigManager::ConfigManager() {
    InitDefaultConfig();
}

ConfigManager::~ConfigManager() {
    // 析构函数
}

int ConfigManager::Init(const char* config_file) {
    if (config_file == nullptr) {
        TX_LOG_ERROR("ConfigManager", "Config file path is null");
        return -1;
    }

    config_file_path_ = std::string(config_file);
    
    int ret = LoadConfig(config_file);
    if (ret != 0) {
        TX_LOG_ERROR("ConfigManager", "Failed to load config file: %s", config_file);
        return ret;
    }

    if (!ValidateConfig()) {
        TX_LOG_ERROR("ConfigManager", "Config validation failed");
        return -2;
    }

    CacheConfigNodes();
    TX_LOG_INFO("ConfigManager", "Config manager initialized successfully");
    return 0;
}

int ConfigManager::LoadConfig(const char* config_file) {
    if (config_file == nullptr) {
        TX_LOG_ERROR("ConfigManager", "Config file path is null");
        return -1;
    }

    std::ifstream infile(config_file, std::ios::binary);
    if (!infile.is_open()) {
        TX_LOG_ERROR("ConfigManager", "Failed to open config file: %s", config_file);
        return -1;
    }

    std::stringstream buffer;
    buffer << infile.rdbuf();
    std::string config_content = buffer.str();
    infile.close();

    return ParseConfig(config_content);
}

Json::Value ConfigManager::GetPipelineConfig() const {
    return pipeline_config_;
}

Json::Value ConfigManager::GetFaceAnalysisConfig() const {
    return face_analysis_config_;
}

Json::Value ConfigManager::GetFatigueDetectionConfig() const {
    return fatigue_detection_config_;
}

Json::Value ConfigManager::GetDistractionDetectionConfig() const {
    return distraction_detection_config_;
}

Json::Value ConfigManager::GetWarningSystemConfig() const {
    return warning_system_config_;
}

Json::Value ConfigManager::GetStateManagerConfig() const {
    return state_manager_config_;
}

int ConfigManager::SaveConfig(const char* config_file) {
    const char* file_path = config_file ? config_file : config_file_path_.c_str();
    
    std::ofstream outfile(file_path, std::ios::binary);
    if (!outfile.is_open()) {
        TX_LOG_ERROR("ConfigManager", "Failed to open config file for writing: %s", file_path);
        return -1;
    }

    Json::StreamWriterBuilder builder;
    builder["indentation"] = "  ";
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    
    writer->write(root_config_, &outfile);
    outfile.close();

    TX_LOG_INFO("ConfigManager", "Config saved to file: %s", file_path);
    return 0;
}

bool ConfigManager::ValidateConfig() const {
    // 验证根配置存在
    if (root_config_.isNull()) {
        TX_LOG_ERROR("ConfigManager", "Root config is null");
        return false;
    }

    // 验证必要的配置节点
    if (!root_config_.isMember("pipeline")) {
        TX_LOG_ERROR("ConfigManager", "Missing 'pipeline' configuration");
        return false;
    }

    // 验证模块配置
    if (!ValidateModuleConfig(root_config_["face_analysis"], "face_analysis")) {
        return false;
    }

    if (!ValidateModuleConfig(root_config_["fatigue_detection"], "fatigue_detection")) {
        return false;
    }

    if (!ValidateModuleConfig(root_config_["distraction_detection"], "distraction_detection")) {
        return false;
    }

    return true;
}

int ConfigManager::ParseConfig(const std::string& config_content) {
    Json::Reader json_reader;
    
    if (!json_reader.parse(config_content, root_config_)) {
        TX_LOG_ERROR("ConfigManager", "Failed to parse JSON config: %s", 
                    json_reader.getFormattedErrorMessages().c_str());
        return -1;
    }

    TX_LOG_INFO("ConfigManager", "Config parsed successfully");
    return 0;
}

void ConfigManager::InitDefaultConfig() {
    // 初始化默认配置
    root_config_ = Json::Value(Json::objectValue);
    
    // 默认流水线配置
    Json::Value default_pipeline;
    default_pipeline["name"] = "DMS_Default_Pipeline";
    default_pipeline["version"] = "1.0.0";
    root_config_["pipeline"] = default_pipeline;

    // 默认人脸分析配置
    Json::Value default_face_analysis;
    default_face_analysis["face_bbox"]["threshold"] = 0.5;
    default_face_analysis["keypoints"]["threshold"] = 0.35;
    root_config_["face_analysis"] = default_face_analysis;

    // 默认疲劳检测配置
    Json::Value default_fatigue;
    default_fatigue["eye_analysis"]["closure_threshold"] = 0.1;
    default_fatigue["yawn_detection"]["mouth_opening_threshold"] = 0.6;
    root_config_["fatigue_detection"] = default_fatigue;

    // 默认分心检测配置
    Json::Value default_distraction;
    default_distraction["head_pose"]["pitch_threshold"] = 25.0;
    default_distraction["head_pose"]["yaw_threshold"] = 35.0;
    default_distraction["head_pose"]["roll_threshold"] = 20.0;
    root_config_["distraction_detection"] = default_distraction;

    // 默认报警系统配置
    Json::Value default_warning;
    default_warning["fatigue_warning"]["time_window_ms"] = 3000;
    default_warning["distraction_warning"]["time_window_ms"] = 2000;
    root_config_["warning_system"] = default_warning;

    // 默认状态管理配置
    Json::Value default_state;
    default_state["activation"]["timeout_ms"] = 300000;
    default_state["hardware_monitoring"]["monitoring_interval_ms"] = 1000;
    root_config_["state_manager"] = default_state;

    TX_LOG_INFO("ConfigManager", "Default config initialized");
}

void ConfigManager::CacheConfigNodes() {
    pipeline_config_ = root_config_["pipeline"];
    face_analysis_config_ = root_config_["face_analysis"];
    fatigue_detection_config_ = root_config_["fatigue_detection"];
    distraction_detection_config_ = root_config_["distraction_detection"];
    warning_system_config_ = root_config_["warning_system"];
    state_manager_config_ = root_config_["state_manager"];
    
    TX_LOG_DEBUG("ConfigManager", "Config nodes cached");
}

bool ConfigManager::ValidateModuleConfig(const Json::Value& module_config,
                                        const std::string& module_name) const {
    if (module_config.isNull()) {
        TX_LOG_WARN("ConfigManager", "Module config '%s' is null, using defaults",
                   module_name.c_str());
        return true; // 允许使用默认配置
    }

    // 这里可以添加更具体的模块配置验证逻辑
    TX_LOG_DEBUG("ConfigManager", "Module config '%s' validated", module_name.c_str());
    return true;
}

Json::Value ConfigManager::GetValueByPath(const std::string& key) const {
    std::vector<std::string> keys;
    std::stringstream ss(key);
    std::string item;

    // 分割键路径（例如："fatigue.eye_threshold" -> ["fatigue", "eye_threshold"]）
    while (std::getline(ss, item, '.')) {
        keys.push_back(item);
    }

    Json::Value current = root_config_;
    for (const auto& k : keys) {
        if (!current.isMember(k)) {
            TX_LOG_WARN("ConfigManager", "Parameter key '%s' not found", key.c_str());
            return Json::Value(); // 返回null值
        }
        current = current[k];
    }

    return current;
}

int ConfigManager::SetValueByPath(const std::string& key, const Json::Value& value) {
    std::vector<std::string> keys;
    std::stringstream ss(key);
    std::string item;

    while (std::getline(ss, item, '.')) {
        keys.push_back(item);
    }

    if (keys.empty()) {
        TX_LOG_ERROR("ConfigManager", "Invalid parameter key: %s", key.c_str());
        return -1;
    }

    Json::Value* current = &root_config_;
    for (size_t i = 0; i < keys.size() - 1; ++i) {
        if (!current->isMember(keys[i])) {
            (*current)[keys[i]] = Json::Value(Json::objectValue);
        }
        current = &((*current)[keys[i]]);
    }

    (*current)[keys.back()] = value;
    TX_LOG_DEBUG("ConfigManager", "Parameter '%s' set successfully", key.c_str());
    return 0;
}

// 模板方法的显式实例化
template<>
float ConfigManager::GetParameter<float>(const std::string& key, const float& default_value) const {
    std::vector<std::string> keys;
    std::stringstream ss(key);
    std::string item;

    // 分割键路径（例如："fatigue.eye_threshold" -> ["fatigue", "eye_threshold"]）
    while (std::getline(ss, item, '.')) {
        keys.push_back(item);
    }

    Json::Value current = root_config_;
    for (const auto& k : keys) {
        if (!current.isMember(k)) {
            TX_LOG_WARN("ConfigManager", "Parameter key '%s' not found, using default value", key.c_str());
            return default_value;
        }
        current = current[k];
    }

    if (current.isDouble() || current.isNumeric()) {
        return current.asFloat();
    }

    TX_LOG_WARN("ConfigManager", "Parameter '%s' is not a float, using default value", key.c_str());
    return default_value;
}

template<>
int ConfigManager::GetParameter<int>(const std::string& key, const int& default_value) const {
    std::vector<std::string> keys;
    std::stringstream ss(key);
    std::string item;

    while (std::getline(ss, item, '.')) {
        keys.push_back(item);
    }

    Json::Value current = root_config_;
    for (const auto& k : keys) {
        if (!current.isMember(k)) {
            TX_LOG_WARN("ConfigManager", "Parameter key '%s' not found, using default value", key.c_str());
            return default_value;
        }
        current = current[k];
    }

    if (current.isInt() || current.isNumeric()) {
        return current.asInt();
    }

    TX_LOG_WARN("ConfigManager", "Parameter '%s' is not an int, using default value", key.c_str());
    return default_value;
}

template<>
bool ConfigManager::GetParameter<bool>(const std::string& key, const bool& default_value) const {
    std::vector<std::string> keys;
    std::stringstream ss(key);
    std::string item;

    while (std::getline(ss, item, '.')) {
        keys.push_back(item);
    }

    Json::Value current = root_config_;
    for (const auto& k : keys) {
        if (!current.isMember(k)) {
            TX_LOG_WARN("ConfigManager", "Parameter key '%s' not found, using default value", key.c_str());
            return default_value;
        }
        current = current[k];
    }

    if (current.isBool()) {
        return current.asBool();
    }

    TX_LOG_WARN("ConfigManager", "Parameter '%s' is not a bool, using default value", key.c_str());
    return default_value;
}

template<>
std::string ConfigManager::GetParameter<std::string>(const std::string& key, const std::string& default_value) const {
    std::vector<std::string> keys;
    std::stringstream ss(key);
    std::string item;

    while (std::getline(ss, item, '.')) {
        keys.push_back(item);
    }

    Json::Value current = root_config_;
    for (const auto& k : keys) {
        if (!current.isMember(k)) {
            TX_LOG_WARN("ConfigManager", "Parameter key '%s' not found, using default value", key.c_str());
            return default_value;
        }
        current = current[k];
    }

    if (current.isString()) {
        return current.asString();
    }

    TX_LOG_WARN("ConfigManager", "Parameter '%s' is not a string, using default value", key.c_str());
    return default_value;
}

template<>
int ConfigManager::SetParameter<float>(const std::string& key, const float& value) {
    std::vector<std::string> keys;
    std::stringstream ss(key);
    std::string item;

    while (std::getline(ss, item, '.')) {
        keys.push_back(item);
    }

    if (keys.empty()) {
        TX_LOG_ERROR("ConfigManager", "Invalid parameter key: %s", key.c_str());
        return -1;
    }

    Json::Value* current = &root_config_;
    for (size_t i = 0; i < keys.size() - 1; ++i) {
        if (!current->isMember(keys[i])) {
            (*current)[keys[i]] = Json::Value(Json::objectValue);
        }
        current = &((*current)[keys[i]]);
    }

    (*current)[keys.back()] = value;
    TX_LOG_DEBUG("ConfigManager", "Parameter '%s' set to %f", key.c_str(), value);
    return 0;
}

template<>
int ConfigManager::SetParameter<int>(const std::string& key, const int& value) {
    std::vector<std::string> keys;
    std::stringstream ss(key);
    std::string item;

    while (std::getline(ss, item, '.')) {
        keys.push_back(item);
    }

    if (keys.empty()) {
        TX_LOG_ERROR("ConfigManager", "Invalid parameter key: %s", key.c_str());
        return -1;
    }

    Json::Value* current = &root_config_;
    for (size_t i = 0; i < keys.size() - 1; ++i) {
        if (!current->isMember(keys[i])) {
            (*current)[keys[i]] = Json::Value(Json::objectValue);
        }
        current = &((*current)[keys[i]]);
    }

    (*current)[keys.back()] = value;
    TX_LOG_DEBUG("ConfigManager", "Parameter '%s' set to %d", key.c_str(), value);
    return 0;
}

template<>
int ConfigManager::SetParameter<bool>(const std::string& key, const bool& value) {
    std::vector<std::string> keys;
    std::stringstream ss(key);
    std::string item;

    while (std::getline(ss, item, '.')) {
        keys.push_back(item);
    }

    if (keys.empty()) {
        TX_LOG_ERROR("ConfigManager", "Invalid parameter key: %s", key.c_str());
        return -1;
    }

    Json::Value* current = &root_config_;
    for (size_t i = 0; i < keys.size() - 1; ++i) {
        if (!current->isMember(keys[i])) {
            (*current)[keys[i]] = Json::Value(Json::objectValue);
        }
        current = &((*current)[keys[i]]);
    }

    (*current)[keys.back()] = value;
    TX_LOG_DEBUG("ConfigManager", "Parameter '%s' set to %s", key.c_str(), value ? "true" : "false");
    return 0;
}

template<>
int ConfigManager::SetParameter<std::string>(const std::string& key, const std::string& value) {
    std::vector<std::string> keys;
    std::stringstream ss(key);
    std::string item;

    while (std::getline(ss, item, '.')) {
        keys.push_back(item);
    }

    if (keys.empty()) {
        TX_LOG_ERROR("ConfigManager", "Invalid parameter key: %s", key.c_str());
        return -1;
    }

    Json::Value* current = &root_config_;
    for (size_t i = 0; i < keys.size() - 1; ++i) {
        if (!current->isMember(keys[i])) {
            (*current)[keys[i]] = Json::Value(Json::objectValue);
        }
        current = &((*current)[keys[i]]);
    }

    (*current)[keys.back()] = value;
    TX_LOG_DEBUG("ConfigManager", "Parameter '%s' set to '%s'", key.c_str(), value.c_str());
    return 0;
}

} // namespace tongxing
