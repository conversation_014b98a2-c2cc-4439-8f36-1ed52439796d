#ifndef __CONFIG_MANAGER_H__
#define __CONFIG_MANAGER_H__

#include <string>
#include <memory>
#include "json.h"

namespace tongxing {

/**
 * @brief 配置管理器
 * 
 * 统一管理DMS系统的所有配置参数，包括模块配置、阈值配置、车型配置等
 * 支持配置文件热加载和参数验证
 */
class ConfigManager {
public:
    ConfigManager();
    ~ConfigManager();

    /**
     * @brief 初始化配置管理器
     * @param config_file 配置文件路径
     * @return 0成功，非0失败
     */
    int Init(const char* config_file);

    /**
     * @brief 加载配置文件
     * @param config_file 配置文件路径
     * @return 0成功，非0失败
     */
    int LoadConfig(const char* config_file);

    /**
     * @brief 获取模块流水线配置
     * @return 模块流水线配置
     */
    Json::Value GetPipelineConfig() const;

    /**
     * @brief 获取人脸分析配置
     * @return 人脸分析配置
     */
    Json::Value GetFaceAnalysisConfig() const;

    /**
     * @brief 获取疲劳检测配置
     * @return 疲劳检测配置
     */
    Json::Value GetFatigueDetectionConfig() const;

    /**
     * @brief 获取分心检测配置
     * @return 分心检测配置
     */
    Json::Value GetDistractionDetectionConfig() const;

    /**
     * @brief 获取报警系统配置
     * @return 报警系统配置
     */
    Json::Value GetWarningSystemConfig() const;

    /**
     * @brief 获取状态管理配置
     * @return 状态管理配置
     */
    Json::Value GetStateManagerConfig() const;

    /**
     * @brief 获取指定参数值
     * @param key 参数键名（支持点分隔的路径，如"fatigue.eye_threshold"）
     * @param default_value 默认值
     * @return 参数值
     */
    template<typename T>
    T GetParameter(const std::string& key, const T& default_value) const;

    /**
     * @brief 设置参数值
     * @param key 参数键名
     * @param value 参数值
     * @return 0成功，非0失败
     */
    template<typename T>
    int SetParameter(const std::string& key, const T& value);

    /**
     * @brief 保存配置到文件
     * @param config_file 配置文件路径（可选，默认使用原文件）
     * @return 0成功，非0失败
     */
    int SaveConfig(const char* config_file = nullptr);

    /**
     * @brief 验证配置有效性
     * @return true有效，false无效
     */
    bool ValidateConfig() const;

    /**
     * @brief 重新加载配置文件
     * @return 0成功，非0失败
     */
    int ReloadConfig();

    /**
     * @brief 获取配置文件路径
     * @return 配置文件路径
     */
    std::string GetConfigFilePath() const;

private:
    Json::Value root_config_;           // 根配置对象
    std::string config_file_path_;      // 配置文件路径
    
    // 缓存的配置节点
    Json::Value pipeline_config_;
    Json::Value face_analysis_config_;
    Json::Value fatigue_detection_config_;
    Json::Value distraction_detection_config_;
    Json::Value warning_system_config_;
    Json::Value state_manager_config_;

    /**
     * @brief 解析配置文件
     * @param config_content 配置文件内容
     * @return 0成功，非0失败
     */
    int ParseConfig(const std::string& config_content);

    /**
     * @brief 初始化默认配置
     */
    void InitDefaultConfig();

    /**
     * @brief 缓存配置节点
     */
    void CacheConfigNodes();

    /**
     * @brief 验证模块配置
     * @param module_config 模块配置
     * @param module_name 模块名称
     * @return true有效，false无效
     */
    bool ValidateModuleConfig(const Json::Value& module_config,
                             const std::string& module_name) const;

    /**
     * @brief 根据路径获取配置值
     * @param key 配置键路径
     * @return 配置值
     */
    Json::Value GetValueByPath(const std::string& key) const;

    /**
     * @brief 根据路径设置配置值
     * @param key 配置键路径
     * @param value 配置值
     * @return 0成功，非0失败
     */
    int SetValueByPath(const std::string& key, const Json::Value& value);


};

// 模板函数声明（实现在.cpp文件中）
// 支持的类型：bool, int, float, double, std::string

} // namespace tongxing

#endif // __CONFIG_MANAGER_H__
