#ifndef __STATE_MANAGER_H__
#define __STATE_MANAGER_H__

#include "tx_dms_sdk.h"
#include "json.h"
#include <memory>
#include <string>

namespace tongxing {

/**
 * @brief 状态管理器
 * 
 * 统一管理DMS系统的各种状态，包括算法激活状态、硬件错误状态、标定状态等
 * 从原有的DmsProcess中提取状态管理逻辑，实现集中化管理
 */
class StateManager {
public:
    StateManager();
    ~StateManager();

    /**
     * @brief 初始化状态管理器
     * @param config 配置参数
     * @return 0成功，非0失败
     */
    int Init(const Json::Value& config);

    /**
     * @brief 检查算法是否激活
     * @param result DMS结果（用于设置状态）
     * @param timestamp 当前时间戳
     * @return true激活，false未激活
     */
    bool IsAlgorithmActivated(TXDmsResult& result, long timestamp);

    /**
     * @brief 检查硬件错误
     * @param car_info 车辆信息
     * @param result DMS结果（用于设置错误状态）
     * @return true有错误，false无错误
     */
    bool CheckHardwareError(const TXCarInfo* car_info, TXDmsResult& result);

    /**
     * @brief 获取摄像头状态
     * @param car_info 车辆信息
     * @param timestamp 当前时间戳
     * @param camera_states 摄像头状态位掩码
     * @return 摄像头状态
     */
    TXCameraStatus GetCameraStatus(const TXCarInfo* car_info, 
                                  long timestamp, 
                                  uint8_t camera_states);

    /**
     * @brief 设置DMS使能状态
     * @param enabled 是否使能
     */
    void SetDmsEnabled(bool enabled);

    /**
     * @brief 获取DMS使能状态
     * @return true使能，false禁用
     */
    bool IsDmsEnabled() const;

    /**
     * @brief 更新标定状态
     * @param calibrate_status 标定状态
     */
    void UpdateCalibrateStatus(TXDistracCaliStatus calibrate_status);

    /**
     * @brief 获取标定状态
     * @return 当前标定状态
     */
    TXDistracCaliStatus GetCalibrateStatus() const;

    /**
     * @brief 复位报警状态
     */
    void ResetAlarmState();

    /**
     * @brief 复位分心报警状态
     */
    void ResetDistractionAlarmState();

    /**
     * @brief 设置报警确认
     */
    void SetAlarmAcknowledged();

    /**
     * @brief 获取系统状态
     * @return 系统状态
     */
    TXSystemStatus GetSystemStatus() const;

    /**
     * @brief 获取算法状态
     * @return 算法状态
     */
    TXAlgorithmStatus GetAlgorithmStatus() const;

private:
    // 状态变量
    bool dms_enabled_;                          // DMS使能状态
    TXDistracCaliStatus calibrate_status_;      // 标定状态
    TXSystemStatus system_status_;              // 系统状态
    TXAlgorithmStatus algorithm_status_;        // 算法状态
    TXCameraStatus camera_status_;              // 摄像头状态

    // 时间相关
    long last_activation_check_time_;           // 上次激活检查时间
    long algorithm_start_time_;                 // 算法启动时间

    // 配置参数
    bool use_activation_check_;                 // 是否使用激活检查
    long activation_timeout_;                   // 激活超时时间
    int camera_fault_threshold_;                // 摄像头故障阈值
    int can_fault_threshold_;                   // CAN故障阈值

    /**
     * @brief 检查激活许可
     * @param timestamp 当前时间戳
     * @return true有许可，false无许可
     */
    bool CheckActivationLicense(long timestamp);

    /**
     * @brief 检查CAN总线状态
     * @param car_info 车辆信息
     * @return true正常，false故障
     */
    bool CheckCanBusStatus(const TXCarInfo* car_info);

    /**
     * @brief 检查摄像头硬件状态
     * @param car_info 车辆信息
     * @return true正常，false故障
     */
    bool CheckCameraHardwareStatus(const TXCarInfo* car_info);

    /**
     * @brief 更新系统状态
     * @param car_info 车辆信息
     */
    void UpdateSystemStatus(const TXCarInfo* car_info);

    /**
     * @brief 更新算法状态
     * @param timestamp 当前时间戳
     */
    void UpdateAlgorithmStatus(long timestamp);

    /**
     * @brief 检查摄像头状态位
     * @param camera_states 状态位掩码
     * @param target_state 目标状态
     * @return true匹配，false不匹配
     */
    bool CheckCameraState(uint8_t camera_states, uint8_t target_state);
};

} // namespace tongxing

#endif // __STATE_MANAGER_H__
