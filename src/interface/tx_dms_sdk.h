#ifndef __TXZL_DMS_N50_H__
#define __TXZL_DMS_N50_H__
#define TX_MAX_FLD_SIZE 19
// #define WITH_PHONE_SMOKING_DET
#include <stddef.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// 枚举类型
typedef enum {
    TX_STANDARD_EU = 0,     // 欧标
    TX_STANDARD_NON_EU = 1  // 非欧标

} TXStandardType;

//  图像像素格式
typedef enum TXInputFormat_ { NV21 = 0, YV12 = 1, YUV422 = 4, NV12 = 5, GRAY = 6 } TXInputFormat;
//档位枚举
typedef enum TXGearPition_ {
    BACK = 0,     //倒档
    NEUTRAL = 1,  //N档
    FORWARD = 2,  //前进档
    PARK = 3      //P档
} TXGearPition;
//转向灯枚举
typedef enum TXTurnSignal_ {
    TURN_OFF = 0,       //未开启转向灯
    TURN_ON_LEFT = 1,   //开启左转向灯
    TURN_ON_RIGHT = 2,  //开启右转向灯
    TURN_INVALID = 3    //故障状态
} TXTurnSignal;

//左前门状态枚举
typedef enum TXDriverDoorStatus_ {
    DOOR_INVAID = 0,  //无效
    DOOR_OPEN = 1,    //打开
    DOOR_CLOSE = 2,   //关闭
} TXDriverDoorStatus;

//主驾座椅状态枚举
typedef enum TXDriverSeatStatus_ {
    SEAT_INVALID = 0,            //无效
    SEAT_STATIC = 1,             //静止状态
    SEAT_ELECTRIC_ADJUST = 2,    //电动调节状态
    SEAT_MEMORY_WAKEUP = 3,      //记忆唤醒状态
    SEAT_WELCOME_FORWARD = 4,    //迎宾向前状态
    SEAT_WELCOME_BACK = 5,       //迎宾后退状态
    SEAT_ANTIPINCH = 6,          //防夹状态
    SEAT_LEGACY_DETECTION = 7,   //遗留物检测状态
    SEAT_ZERO_GRAVITY = 8,       //零重力状态
    SEAT_ZERO_GRAVITY_EXIT = 9,  //零重力退出中
} TXDriverSeatStatus;

//疲劳报警类型
typedef enum TXDrowsinessType_ {
    Drowsiness_Normal = 0,      // 正常
    Drowsiness_Invalid = 1,     // 疲劳报警无效
    Drowsiness_Fatigue2 = 2,    // 疲劳报警2级
    Drowsiness_Fatigue3 = 3,    // 疲劳报警3级
    Drowsiness_Fatigue4 = 4,    // 疲劳报警4级
    Drowsiness_NoResponse = 5,  // 疲劳报警无响应
} TXDrowsinessType;

//分神报警类型
typedef enum TXDistractionType_ {
    Distraction_Normal = 0,         // 正常
    Distraction_Invalid = 1,        // 分神报警无效
    Distraction_Fatigue = 2,        // 分神报警
    Distraction_NoResponse = 3,     // 分神报警无响应
    Distraction_Fatigue_Short = 4,  // 短时分心
    Distraction_Fatigue_Long = 5,   // 长时分心

} TXDistractionType;

//摄像头状态
typedef enum TXCameraStatus_ {
    Camera_Normal = 0,     //正常
    Camera_Occlusion = 1,  //相机遮挡
    Face_Occlusion = 2,    //人脸遮挡
    Camera_Invalid = 3     //摄像头不可用
} TXCameraStatus;

typedef enum TXLogLevelType_ {
    LEVEL_DEBUG = 0,
    LEVEL_INFO = 1,
    LEVEL_WARN = 2,
    LEVEL_ERROR = 3,
    LEVEL_FATAL = 4,
} TXLogLevelType;

//  描述图像的结构体
typedef struct TXImageInfo_ {
    TXInputFormat dataType;  // 图像类型
    int height;              // 图像高度
    int width;               // 图像宽度
    int stride;              // 图像每行像素的跨度
                             // 以RBG,BGR为例stride=width*3
                             // 以NV12,YV12为例stride=width
    int dataLen;             // 像素数据的数据字节数
    char* data;              // 像素数据的缓冲区指针
} TXImageInfo;

//车辆信息车速更新掩码位
#define TX_CAR_INFO_MASK_SPEED (0x00000001)  //1
//车辆信息方向盘转角更新掩码位
#define TX_CAR_INFO_MASK_STEER_WHL_SNSR (0x00000002)  //2
//车辆信息方向盘转角速度更新掩码位
#define TX_CAR_INFO_MASK_STEER_WHL_SNSR_SPEED (0x00000004)  //4
//车辆信息档位状态更新掩码位
#define TX_CAR_INFO_MASK_GEARPOSITION (0x00000008)  //8
//车辆信息车道偏离更新掩码位
#define TX_CAR_INFO_MASK_LDW (0x0000010)  //16
//车辆信息转向灯更新掩码位
#define TX_CAR_INFO_MASK_TURN_LIGHT (0x0000020)  //32
//can故障掩码位
#define TX_CAR_CAN_FAULT (0x0000040)  //64
//相机故障掩码位
#define TX_CAR_CAMERA_FAULT (0x0000080)  //128
//左前车门状态更新掩码位
#define TX_CAR_DRIVER_DOOR (0x0000100)  //256
//主驾座椅更新掩码位
#define TX_CAR_DRIVER_SEAT (0x0000200)  //512

// 车辆状态信息
typedef struct TXCarInfo_ {
    uint32_t mask;                 //车辆信息更新掩码
    uint32_t speed;                // 当前车速
    float steer_whl_snsr_rad;      //方向盘转角，单位：弧度
    float steer_whl_snsr_rad_spd;  //方向盘转速，单位：弧度/ms
    TXGearPition gear;             // 档位状态
    TXTurnSignal turn_light;       //转向灯状态
    uint32_t ldw;                  // 车道偏离预警
    uint32_t can_fault;            // can故障信息
    uint32_t camera_fault;         // 相机故障信息
    uint32_t driver_door_status;   // 左前门状态
    uint32_t driver_seat_status;   // 主驾座椅状态
} TXCarInfo;

//具体报警信息，移植使用不上
typedef struct TXWarnInfo_ {
    bool eye1_60s_10p;   //
    bool mouth1_120s_2;  //
    bool mouth1_1l_1;    //

    bool eye2_20s_1f5;  //
    bool eye2_2_0f75;   //
    bool eye2_1l_0f75;
    bool eye2_60s_12p;
    bool mouth2_1l_2;
    bool mouth2_120s_3;

    bool eye3_20s_2f4;
    bool eye3_2_1f2;
    bool eye3_1l_1f2;

    bool repetition;

} TXWarnInfo;

// 描述2维整数坐标点
typedef struct TXPoint2i_ {
    int x;
    int y;
} TXPoint2i;
// 描述2维Size
typedef struct TXSize2i_ {
    int width;
    int height;
} TXSize2i;
// 描述眼睛信息
typedef struct TXEyeLandmark_ {
    float eye_score;
    TXPoint2i eye_center;
    TXSize2i eye_size;
    float eye_angle;

    float iris_score;
    TXPoint2i iris_center;
    float iris_radius;

    float pupil_score;
    TXPoint2i pupil_center;
    float pupil_radius;

    float yaw;
    float pitch;

    float opening;

} TXEyeLandmark;
// 描述人脸信息
typedef struct TXDmsFaceInfo_ {
    float score;       // 人脸检测的置信度
    int xmin;          // 人脸检测框的左上方的X坐标
    int ymin;          // 人脸检测框的左上方的Y坐标
    int xmax;          // 人脸检测框的右下方的X坐标
    int ymax;          // 人脸检测框的右下方的Y坐标
    float head_yaw;    // 左右扭头角度,画面中左正右负,无效为-99.0f
    float head_pitch;  // 低抬头角度,画面中抬头正低头负,无效为-99.0f
    float head_roll;   // 摆头角度,无效为-99.0f
    int isMask;   // 是否带口罩，0表示没有戴口罩，1表示有戴口罩 ,2表示未知
    int isGlass;  // 是否带眼镜，0表示没有戴眼镜，1表示有戴眼镜 ,2表示未知
    int isIRBlock;  // 是否红外阻断，0表示没有红外阻断，1表示有红外阻断 ,2表示未知
    float right_close_eye_score;           //右眼闭眼自信度
    float left_close_eye_score;            //左眼闭眼自信度
    float mouth_opening;                   //嘴巴开合度
    TXPoint2i landmarks[TX_MAX_FLD_SIZE];  // 人脸特征点
    TXWarnInfo warnInfo;
    TXEyeLandmark right_eye_landmark;
    TXEyeLandmark left_eye_landmark;
} TXDmsFaceInfo;

//自动分神标定状态枚举
typedef enum TXDistracCaliStatus_ {
    CALIBRATE_DONE = 0,   //标定完成
    CALIBRATE_DOING = 1,  //标定中
    //标定条件不满足，未完成（不满足标定车速、档位、人脸角度条件,待条件满足后进行标定）
    CALIBRATE_CONDITION_UNDONE = 2,
    CALIBRATE_FAILED = 3,  //标定失败（人脸角度方差不满足，车速方差等不满足）
    CALIBRATE_INVALID = 4  //标定不可用（摄像头故障及人脸故障或者其他）
} TXDistracCaliStatus;

//系统故障状态枚举
typedef enum TXSystemStatus_ {
    SYSTEM_NORMAL = 0,            //系统正常
    SYSTEM_CAN_FAULT = 1,         //can系统故障
    SYSTEM_CAMERA_FAULT = 2,      //相机系统故障
    SYSTEM_CAN_CAMERA_FAULT = 3,  //can故障与相机系统故障都存在
} TXSystemStatus;

//打电话状态枚举
typedef enum TXPhoneStatus_ {
    NO_PHONE = 0,       //没有检测到电话
    HAS_PHONE = 1,      //检测到电话
    PHONE_INVALID = 2,  //打电话检测无效
} TXPhoneStatus;

//打抽烟状态枚举
typedef enum TXSmokingStatus_ {
    NO_SMOKING = 0,       //没有检测到电话
    HAS_SMOKING = 1,      //检测到电话
    SMOKING_INVALID = 2,  //打电话检测无效
} TXSmokingStatus;

// 分心干预状态
typedef enum TXDistrIntervStatus_ {
    DiSTRINTERV_INVALID = 0,       //无效
    DiSTRINTERV_ACTIVATE = 1,      //分心干预
    DiSTRINTERV_NOT_ACTIVATE = 2,  //不干预
    DiSTRINTERV_REVERSED = 3,      //预留
} TXDistrIntervStatus;

//算法状态枚举
typedef enum TXAlgorithmStatus_ {
    ALGORITHM_NORMAL = 1,                       //算法正常,已激活
    ALGORITHM_UNACTIVATED_EXPERIENCE_TIME = 2,  //未激活，体验时间
    ALGORITHM_UNACTIVATED_NO_WORK = 3           //未激活，停止工作
} TXAlgorithmStatus;

// 描述dms结果
typedef struct TXDmsResult_ {
    long long result_frame_id;             // 算法处理的图像ID
    TXDrowsinessType drowsiness_status;    // 触发的疲劳报警状态
    TXDistractionType distraction_status;  // 分神报警状态
    TXSystemStatus system_status;          // 系统故障：can故障,相机硬件故障
    TXCameraStatus camera_status;          // 摄像头状态
    TXDmsFaceInfo face_info;               // 人脸信息
    TXDistracCaliStatus calibrate_status;  // 标定状态
    TXAlgorithmStatus algorithm_status;    // 算法状态
#ifdef WITH_PHONE_SMOKING_DET
    TXPhoneStatus phone_status;      //打电话检测状态
    TXSmokingStatus smoking_status;  //抽烟检测状态
#endif
#ifdef DISTRACTION_INTERVENTION
    TXDistrIntervStatus distrinterv_status;  //分心干预状态
#endif

} TXDmsResult;

//////////////////////////////////////////////////////////////////////////////////////////
//  int TXDmsSetStandardType(TXStandardType standard_type)
//  功能:
//      设置当前使用的标准类型（欧标 / 非欧标）
//  返回值:
//      返回0表示设置成功，非0表示失败
//  说明:
//      必须在 TXDmsCreate 之前调用，否则无效，不调用默认欧标
//////////////////////////////////////////////////////////////////////////////////////////
int TXDmsSetStandardType(TXStandardType standard_type);

//////////////////////////////////////////////////////////////////////////////////////////
//  TXStandardType TXDmsGetStandardType()
//  功能:
//      获取当前使用的标准类型（欧标 / 非欧标）
//  返回值:
//      当前使用的标准类型
//////////////////////////////////////////////////////////////////////////////////////////
TXStandardType TXDmsGetStandardType();

//////////////////////////////////////////////////////////////////////////////////////////
//	int TXrDmsActivate(char*,int)
//	功能:
//		根据激活码校验算法是否可以被激活
//	返回值:
//		Dms激活状态	返回0时表示激活成功
//	输入:
//      activate_code           激活码
//      activate_code_len       激活码长度
//////////////////////////////////////////////////////////////////////////////////////////
int TXDmsActivate(char* activate_code, int activate_code_len);

//////////////////////////////////////////////////////////////////////////////////////////
//	long TXrDmsCreate(const char *，const char*)
//	功能:
//		Dms创建并初始化
//	返回值:
//		Dms句柄	返回0时表示创建失败
//	输入:
//       config_file    配置文件路径，当设置成NULL时，使用内置的默认配置
//       cache_path     算法缓存路径(可读写)，当设置成NULL时，不使用缓存，不记录算法使能状态，算法使能状态默认为开
//////////////////////////////////////////////////////////////////////////////////////////
long TXDmsCreate(const char* config_file, const char* cache_path);

////////////////////////////////////////////////
//	int TXrDmsDestroy(long)
//	功能:
//		Dms析构。
//	返回值:
//		返回0表示执行成功，非0表示失败
//	输入:
//		hDms			Dms句柄
//////////////////////////////////////////
int TXDmsDestroy(long hDms);

//////////////////////////////////////////////////////////////////////////////////////////
//	const char* TXDmsGetVersion()
//	功能:
//		Dms获取算法版本号
//	返回值:
//		返回版本字符串
//////////////////////////////////////////////////////////////////////////////////////////
const char* TXDmsGetVersion();

const char* TXDmsGetRealVersion();  //获取实际的版本号

//////////////////////////////////////////////////////////////////////
//	int TXDmsSetInput(long hDms, const CalmcarImageInfo *image,TXDmsResult* result);
//	功能:
//		往算法传入图像和车速
//	返回值:
//      返回0表示执行成功，非0表示失败
//	输入:
//		hDms			Dms句柄
//		image 		    图像信息
//      result          结果输出
//////////////////////////////////////////////////////////////////////
int TXDmsSetInput(long hDms, const TXImageInfo* image, TXDmsResult* result);

//////////////////////////////////////////////////////////////////////////////////////////
//	int TXDmsRestAlarm()
//	功能:
//		复位报警当前报警。（疲劳）
//	返回值:
//		返回0表示执行成功，非0表示失败
//
//////////////////////////////////////////////////////////////////////////////////////////
int TXDmsRestAlarm(long hDms);

//////////////////////////////////////////////////////////////////////////////////////////
//	int TXDmsDistractRestAlarm()
//	功能:
//		复位分心报警。
//	返回值:
//		返回0表示执行成功，非0表示失败
//
//////////////////////////////////////////////////////////////////////////////////////////
int TXDmsDistractRestAlarm(long hDms);

//////////////////////////////////////////////////////////////////////
//	int TXDmsUpdataCarInfo(long，const TXCarInfo *)
//	功能:
//		更新车辆信息
//	返回值:
//      返回0表示执行成功，非0表示失败
//	输入:
//		hDms			Dms句柄
//		carInfo 		车型信息指针
//////////////////////////////////////////////////////////////////////
int TXDmsUpdataCarInfo(long hDms, const TXCarInfo* carInfo);

//////////////////////////////////////////////////////////////////////
//	int TXrDmsSetDriverRoi(long, TXPoint2i*, TXPoint2i*)
//	功能:
//		设置司机检测区域，用于区分司机和乘客
//	返回值:
//      返回0表示执行成功，非0表示失败
//	输入:
//		hDms			Dms句柄
//		left_top_point 		司机检测区域在的左上点坐标
//      right_bottom_point       司机检测区域在的右下点坐标
//////////////////////////////////////////////////////////////////////
int TXDmsSetDriverRoi(long hDms, TXPoint2i* left_top_point, TXPoint2i* right_bottom_point);

#ifdef WITH_PHONE_SMOKING_DET
//////////////////////////////////////////////////////////////////////
//	int TXDmsEnablePhoneAndSmokingDetect(long hDms, unsigned char enable_flag);
//	功能:
//		开启和关闭抽烟打电话检测功能
//	返回值:
//      返回0表示执行成功，非0表示失败
//	输入:
//		hDms			Dms句柄
//		enable_flag     开启标志，1为开启，0为关闭
//////////////////////////////////////////////////////////////////////
int TXDmsEnablePhoneAndSmokingDetect(long hDms, unsigned char enable_flag);
#endif

//////////////////////////////////////////////////////////////////////////////////////////
//	int TXDmsSetLogLevel(TXLogLevelType log_level)
//	功能:
//		设置日志等级
//	返回值:
//		返回0表示执行成功，非0表示失败
//	输入:
//		log_level			日志级别
//////////////////////////////////////////////////////////////////////////////////////////
int TXDmsSetLogLevel(TXLogLevelType log_level);

#ifdef __cplusplus
}
#endif
#endif