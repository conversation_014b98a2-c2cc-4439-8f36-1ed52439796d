#ifndef _TX_DMS_WARNING_H_
#define _TX_DMS_WARNING_H_

//此处只定义内部报警信息

typedef struct tired_ {
    int total_eye_count;                        // 眼睛状态数据总数
    int fatigue_blink_count;                    // 疲劳眨眼总次数
    std::string fatigue_blink_duration_ms;      // 疲劳眨眼持续时间(ms)
    std::string fatigue_blink_start_end_index;  // 疲劳眨眼起始帧号和结束帧号
    int close_eye_count;                        // 疲劳闭眼总次数
    std::string close_eye_duration_ms;          // 闭眼持续时间(ms)
    std::string close_eye_start_end_index;      // 闭眼起始帧号和结束帧号
    int total_mouth_count;                      // 嘴巴状态数据总数
    int yawn_count;                             // 疲劳打哈欠次数
    std::string open_mouth_duration_ms;         //张嘴持续时长(ms)
    std::string open_mouth_start_end_index;     //张嘴开始结束帧索引
} tx_tired;

typedef struct internal_analysis_distraction_info_ {
    long distraction_continue_time;        //长时间分心持续时长(ms)
    float distraction_continue_percent;    //长时间分心百分比
    long distraction_sum_time;             //短时分心累计时长(ms)
    long distraction_front_continue_time;  //回正持续时长(ms)
    long time_gap;                         //累计的帧数据的时间跨度(ms)

} internal_analysis_distraction_info;

#endif