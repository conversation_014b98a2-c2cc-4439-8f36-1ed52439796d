#ifndef _DMS_CALCULATE_WARNING_BYD_NON_EU_H_
#define _DMS_CALCULATE_WARNING_BYD_NON_EU_H_
#include <algorithm>
#include <chrono>
#include <deque>
#include <iostream>
#include <vector>
#include "dms_warning_interface.h"
namespace tongxing {
// 此类使用与ddaw报警逻辑

class DrowsinessWarnNonEU final : public DrowsinessWarnInterface {
  public:
    DrowsinessWarnNonEU();
    ~DrowsinessWarnNonEU();

    void Update(const Warn_Info& info) override;
    WarningType GetWarnStatus() override;
    void Reset() override;
    void SetOk() override;
    void GetTired(tx_tired& tired_info) override;

  private:
    bool is_eye_open = true;
    bool ok_flag = false;
    WarningType histor_warn_type = NORMAL;  //历史结果

    tx_tired tired_info_;

    //缓存时间点
    long cache_time;
    long last_ts = 0;
    int warn_count = 0;                  //报警帧数
    std::string last_warning_type = "";  //历史报警类型

    // 非欧标疲劳缓存队列
    std::deque<std::pair<long, bool>> eye_3s_result;  // 2.5s眼睛状态数据队列
    std::deque<std::pair<long, bool>> eye_5s_result;  // 4.5s眼睛状态数据队列
    std::deque<std::pair<long, bool>> eye_7s_result;  // 6.5s眼睛状态数据队列

    Warn_Info temp_warn;  //临时存放报警数据

    // 非欧标报警时间点记录
    long last_2_5s_eye_trigger_time = 0;      //记录长闭眼2.5s最后报警时间点
    long last_4_5s_eye_trigger_time = 0;      //记录长闭眼4.5s最后报警时间点
    long last_6_5s_eye_trigger_time = 0;      //记录长闭眼6.5s最后报警时间点
    WarningType last_warning_level = NORMAL;  // 记录上次报警的等级

    // 获取时间毫秒
    long getMsec(const long begin, const long end) { return end - begin; }
    // 获取时间秒
    long getSec(const long begin, const long end) { return (end - begin) * 1.0 / 1000; }

    bool GetEyeCloseAddUpTime(std::deque<std::pair<long, bool>>& eye_deque,
                              const int& count,
                              long interval_start_ms);

    void PrintFatigueLog(WarningType status);
};

}  // namespace tongxing
#endif