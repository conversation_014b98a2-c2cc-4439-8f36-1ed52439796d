#ifndef _DMS_CALCULATE_WARNING_INTERFACE_H_
#define _DMS_CALCULATE_WARNING_INTERFACE_H_

#include <iostream>
#include <opencv2/core.hpp>
#include <vector>
#include "tx_dms_sdk.h"
#include "tx_dms_warning.h"

namespace tongxing {
// 此类ddaw报警逻辑
// 报警类型
typedef enum WarningType_ {
    NORMAL = 0,                   // 正常
    DROWSINESS_LEVEL_LIGHT = 2,   // 轻度疲劳
    DROWSINESS_LEVEL_MEDIUM = 3,  // 中度疲劳
    DROWSINESS_LEVEL_HEAVY = 4,   // 重度疲劳
    NO_RESPONSE = 5,              // 无响应状态
} WarningType;

// 报警相关性分类
typedef enum WarningClass_ {
    NONE_RELATED = 0,   // 无相关
    EYES_RELATED = 1,   // 眼睛相关
    MOUTH_RELATED = 2,  // 张嘴相关
} WarningClass;

// 报警类型信息结构体
typedef struct Warn_Info_ {
    // 闭眼状态
    bool eye_close_status;
    // 张嘴状态
    size_t mouth_open_status;
    // ldw状态
    bool ldw_status;
    //输入时间点
    long time_input;

} Warn_Info;

// 疲劳报警虚基类（欧标/非欧标统一接口）
class DrowsinessWarnInterface {
  public:
    virtual ~DrowsinessWarnInterface() {}

    // 更新报警信息
    virtual void Update(const Warn_Info& info) = 0;

    // 获取报警类型
    virtual WarningType GetWarnStatus() = 0;

    // 重置所有报警
    virtual void Reset() = 0;

    // 收到确认OK信号
    virtual void SetOk() = 0;

    // 获取闭眼张嘴信息
    virtual void GetTired(tx_tired& tired_info) = 0;

    // 清除闭眼信息
    virtual void Cleareye();
};

// 此类addw报警逻辑

// addw报警类型
typedef enum DistractionType_ {
    DISTRACTION_NORMAL = 0,       //正常
    DISTRACTION_FATIGUE1 = 1,     //分心1（长时分心、addw）
    DISTRACTION_FATIGUE2 = 2,     //分心2(短时分心)
    DISTRACTION_NO_RESPONSE = 3,  //无响应状态
} DistractionType;

// 报警类型信息结构体
typedef struct Distraction_Info_ {
    // 分神状态
    bool distraction_status;
    //输入时间点
    long time_input;

} Distraction_Info;

typedef struct distraction_warning_param_ {
    float head_yaw_min;
    float head_yaw_max;
    float head_pitch_min;
    float head_pitch_max;
    float right_gaze_vf_yaw_min;
    float right_gaze_vf_yaw_max;
    float right_gaze_vf_pitch_min;
    float right_gaze_vf_pitch_max;
    float left_gaze_vf_yaw_min;
    float left_gaze_vf_yaw_max;
    float left_gaze_vf_pitch_min;
    float left_gaze_vf_pitch_max;

    float current_right_gaze_vf_yaw;
    float current_right_gaze_vf_pitch;
    float current_left_gaze_vf_yaw;
    float current_left_gaze_vf_pitch;

    float head_yaw_bias;
} distraction_warning_param;

typedef struct distra_related_eyeinfo_ {
    float right_up_down_proportion;
    float left_up_down_proportion;
    float right_pupil_to_up_down_dis;
    float left_pupil_to_up_down_dis;
    float leye_coutour_upper_curve_score;
    float reye_coutour_upper_curve_score;
} distra_related_eyeinfo;

// 分神报警虚基类（欧标/非欧标统一接口）
class DistractionWarnInterface {
  public:
    virtual ~DistractionWarnInterface() {}

    // 更新报警信息
    virtual void Update(const Distraction_Info& info) = 0;

    // 获取报警类型
    virtual DistractionType GetWarnStatus() = 0;

    // 重置所有报警
    virtual void Reset() = 0;

    // 重置长时分心报警
    virtual void LongReset();

    // 重置无脸混合数据
    virtual void ResetNofaceMixdata() = 0;

    // 收到确认OK信号
    virtual void SetOk() = 0;

    // 自动标定
    virtual TXDistracCaliStatus auto_calibration_distraction(TXDmsFaceInfo face_info,
                                                             const TXCarInfo* car_info,
                                                             const float leye_uper_curve_score,
                                                             const float reye_uper_curve_score,
                                                             long now_ts) = 0;

    // 清除标定
    virtual void ClearCalibration() = 0;

    // 获取标定状态
    virtual bool GetCalibrationStatus() = 0;

    // 判断是否分神状态
    virtual bool IsDistracted(TXDmsFaceInfo face_info,
                              const TXCarInfo* car_info,
                              const distra_related_eyeinfo& related_eyeinfo,
                              bool is_face_keypoints_valid,
                              long now_ts) = 0;

    // 获取头部姿态标定pitch角度
    virtual void GetHeadPosePitch(float& min_value, float& max_value) = 0;

    // 获取头部姿态标定后roll角度
    virtual void GetHeadPoseRoll(float& min_value, float& max_value) = 0;

    // 获取头部姿态标定后yaw角度
    virtual void GetHeadPoseYaw(float& min_value, float& max_value) = 0;

    // 获取分神原因
    virtual std::string GetDistractReason() = 0;

    // 获取分神参数信息
    virtual std::string GetDistractParamers() = 0;

    // 获取分神信息
    virtual void GetDistractionInfo(internal_analysis_distraction_info& info) = 0;

    // 获取混合分神状态
    virtual bool GetMixDistraStatus(bool isFaceValid,
                                    const float face_keypoints_score,
                                    const TXCarInfo* car_info,
                                    bool camera_occlusion,
                                    bool distraction_status,
                                    long now_ts) = 0;

    // 获取非分神头部转动角度范围
    virtual void getNonDistractHeadRotation(float& headyaw_min,
                                            float& headyaw_max,
                                            float& headpitch_min,
                                            float& headpitch_max) = 0;

  protected:
    int WINDOW_TIME;                //分神时间窗
    float DISTRACTION_THR;          //分神阈值
    int HEAD_YAW_BIAS_WINDOW_TIME;  //head_yaw_bias时间窗

    float HEADPOSE_PITCH_THR;  //头部姿态pitch 阈值
    float GAZE_PITCH_THR;      //视线 pitch阈值

    float HEADPOSE_YAW_THR;  //头部姿态yaw 阈值
    float GAZE_YAW_THR;      //视线 yaw阈值

    float PITCH_UP;    //临时过滤低头误报闭眼pitch
    float PITCH_DOWN;  //临时过滤低头误报闭眼pitch
    float YAW_LEFT;    //临时过滤偏头误报闭眼yaw偏差值
    float YAW_RIGHT;   //临时过滤偏头误报闭眼yaw偏差值
    float ROLL_LIFT;   //临时过滤偏头误报分神roll
    float ROLL_RIGHT;  //临时过滤偏头误报分神roll

    float STEERING_WHEEL_ANGLE_MIN;  //标定方向盘转角最小值
    float STEERING_WHEEL_ANGLE_MAX;  //标定方向盘转角最大值

    float CALIBRATE_HEADPOSE_YAW_NORMAL_MIN;    //标定人脸角度yaw最小值
    float CALIBRATE_HEADPOSE_YAW_NORMAL_MAX;    //标定人脸角度yaw最大值
    float CALIBRATE_HEADPOSE_PITCH_NORMAL_MIN;  //标定人脸角度pitch最小值
    float CALIBRATE_HEADPOSE_PITCH_NORMAL_MAX;  //标定人脸角度pitch最大值
    float CALIBRATE_HEADPOSE_ROLL_NORMAL_MIN;   //标定人脸角度roll最小值
    float CALIBRATE_HEADPOSE_ROLL_NORMAL_MAX;   //标定人脸角度roll最大值

    float HEADPOSE_YAW_NORMAL_MIN;    //正常人脸角度yaw最小值
    float HEADPOSE_YAW_NORMAL_MAX;    //正常人脸角度yaw最大值
    float HEADPOSE_PITCH_NORMAL_MIN;  //正常人脸角度pitch最小值
    float HEADPOSE_PITCH_NORMAL_MAX;  //正常人脸角度pitch最大值
    float HEADPOSE_ROLL_NORMAL_MIN;   //正常人脸角度roll最小值
    float HEADPOSE_ROLL_NORMAL_MAX;   //正常人脸角度roll最大值

    float HEAD_POSE_YAW_L;    //head pose yaw角 左方向绝对偏差值
    float HEAD_POSE_YAW_R;    //head pose yaw角 右方向绝对偏差值
    float HEAD_POSE_PITCH_U;  //head pose pitch 上方向绝对偏差值
    float HEAD_POSE_PITCH_D;  //head pose pitch 下方向绝对偏差值

    float HEAD_POSE_SPE_GLASSES_YAW_L;  //戴眼镜且眼睛不可见时设置的更多偏差
    float HEAD_POSE_SPE_GLASSES_YAW_R;
    float HEAD_POSE_SPE_GLASSES_PITCH_U;
    float HEAD_POSE_SPE_GLASSES_PITCH_D;

    float HEAD_YAW_L_OFFSET;  //视线融合头部姿态偏差值(用于判断是否优先使用视线融合做分心判断)
    float HEAD_YAW_R_OFFSET;  //视线融合头部姿态偏差值(用于判断是否优先使用视线融合做分心判断)

    float RIGHTEYE_UP_DOWN_PROPORTION;  //视线往下看,右眼绝对值
    float LEFTEYE_UP_DOWN_PROPORTION;   //视线往下看,左眼绝对值

    float HEAD_YAW_BIAS;  //头部yaw最大最小值的绝对值，用于辅助判断是否注释一个点位，延迟报警

    int FUSION_USE_EYE;                                  //融合角度计算所使用的眼睛
    int REGION_MAPPING_WIDTH;                            //区域映射宽度
    int REGION_MAPPING_HEIGHT;                           //区域映射高度
    float TOLERATE_PERCENTAGE;                           //容忍度百分比
    std::vector<std::vector<cv::Point2f>> REGION_HULLS;  //区域轮廓点集
};

}  // namespace tongxing

#endif