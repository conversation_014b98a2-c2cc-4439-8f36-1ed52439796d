#ifndef _DMS_CALCULATE_WARNING_BYD_H_
#define _DMS_CALCULATE_WARNING_BYD_H_
#include <algorithm>
#include <chrono>
#include <deque>
#include <iostream>
#include <vector>
#include "dms_warning_interface.h"
namespace tongxing {
// 此类使用与ddaw报警逻辑
class DrowsinessWarn final : public DrowsinessWarnInterface {
  public:
    DrowsinessWarn();
    ~DrowsinessWarn();

    //更新报警信息
    void Update(const Warn_Info& info) override;

    //获取报警类型
    WarningType GetWarnStatus() override;

    //重置所有报警
    void Reset() override;

    //收到确认OK信号
    void SetOk() override;

    //获取闭眼张嘴信息
    void GetTired(tx_tired& tired_info) override;

    // 清除闭眼信息
    void Cleareye() override;

  private:
    bool time_2min_flag = false;  //2分钟时间窗标准位
    long time_2min_start = 0;
    long time_2min_end = 0;

    bool time_2min_arrived = false;

    bool ok_flag = false;
    WarningType histor_warn_type = NORMAL;  //历史结果
    WarningClass his_warn_class = NONE_RELATED;

    tx_tired tired_info_;

    //报警计时时间
    long alarm_start_time = 0;
    long alarm_end_time = 0;

    long alarm_ok_start_time = 0;
    long alarm_ok_end_time = 0;

    //缓存时间点
    long cache_time;
    long last_ts = 0;
    int warn_count = 0;                  //报警帧数
    std::string last_warning_type = "";  //历史报警类型

    std::deque<std::pair<long, bool>> eye_result;      // 眼睛状态数据队列
    std::deque<std::pair<long, size_t>> mouth_result;  // 嘴巴状态数据队列
    std::deque<std::pair<long, bool>> eye_2s_result;   //2s眼睛状态数据队列
    std::deque<std::pair<long, bool>> eye_3s_result;   //3s眼睛状态数据队列

    bool warn_no_response_flag = false;

    Warn_Info temp_warn;  //临时存放报警数据
    std::deque<std::pair<long, bool>> eye_temp_result;
    std::deque<std::pair<long, bool>> mouth_temp_result;
    bool history_flag = false;  //标记是否为历史结果
    float percentage = 0.0;     //内部测试使用

    // long last_trigger_time = 0;               // 记录上次报警的时间
    long last_eye_trigger_time = 0;           //记录眨眼最后报警时间点
    long last_mouth_trigger_time = 0;         //记录哈欠最后报警时间点
    long last_2s_eye_trigger_time = 0;        //记录长闭眼2s最后报警时间点
    long last_3s_eye_trigger_time = 0;        //记录长闭眼3s最后报警时间点
    WarningType last_warning_level = NORMAL;  // 记录上次报警的等级

    // 获取时间毫秒
    long getMsec(const long begin, const long end) { return end - begin; }
    // 获取时间秒
    long getSec(const long begin, const long end) { return (end - begin) * 1.0 / 1000; }

    bool GetEyeCloseAddUpTime(std::deque<std::pair<long, bool>>& eye_deque,
                              const int& count,
                              long interval_start_ms);

    bool GetMouthCount(std::deque<std::pair<long, size_t>>& mouth_deque, const int& count);

    bool DeterFatigueInducedBlinking(const int& requiredBlinkCount);  //疲劳性眨眼判断
    void PrintFatigueLog(WarningType status);
};

}  // namespace tongxing
#endif