#include "dms_calculate_warning_byd_non_eu.h"
#include "CalmCarLog.h"

namespace tongxing {

DrowsinessWarnNonEU::DrowsinessWarnNonEU() {
    // 非欧标数据重置
    eye_3s_result.clear();
    eye_5s_result.clear();
    eye_7s_result.clear();

    tired_info_ = {0};  //详细占比
}

DrowsinessWarnNonEU::~DrowsinessWarnNonEU() {}

void DrowsinessWarnNonEU::Update(const Warn_Info& info) {
    //更新眼睛数据队列
    std::pair<long, bool> eye_pair;
    eye_pair.first = info.time_input;
    eye_pair.second = info.eye_close_status;
    std::cout << "eye_pair.first:" << eye_pair.first << ", eye_pair.second:" << eye_pair.second
              << std::endl;

    eye_3s_result.emplace_back(eye_pair);
    eye_5s_result.emplace_back(eye_pair);
    eye_7s_result.emplace_back(eye_pair);

    if (eye_3s_result.size() >= 2) {
        if ((eye_3s_result.back().first - eye_3s_result.front().first) > 3000) {  //2.5秒数据
            eye_3s_result.pop_front();
        }
    }

    if (eye_5s_result.size() >= 2) {
        if ((eye_5s_result.back().first - eye_5s_result.front().first) > 5000) {  //4.5秒数据
            eye_5s_result.pop_front();
        }
    }

    if (eye_7s_result.size() >= 2) {
        if ((eye_7s_result.back().first - eye_7s_result.front().first) > 7000) {  //6.5秒数据
            eye_7s_result.pop_front();
        }
    }

    cache_time = info.time_input;
    temp_warn = info;

    return;
}

//这里计算各个报警状态

//闭眼次数+没有单次闭眼时长限制
bool DrowsinessWarnNonEU::GetEyeCloseAddUpTime(std::deque<std::pair<long, bool>>& eye_deque,
                                               const int& count,
                                               long interval_start_ms) {
    int valid_count = 0;
    int consecutive_eye_open_count = 0;
    int consecutive_eye_close_count = 0;
    int total_count = 0;
    int eye_close_count = 0;
    bool accumulate_flag = false;  // 是否累积标志
    long start_time = 0, end_time = 0;
    const int EYE_REDURANCY_FRAME = 2;  // 冗余帧数
    const int EYE_CLOSE_FRAME = 3;      // 连续闭眼帧数
    const float EYE_CLOSE_THRESHOLD = 0.9f;
    int eye_index = 0;
    int eye_start_index = 0;
    int eye_end_index = 0;

    // 遍历眼睛状态队列
    for (const auto& v : eye_deque) {
        bool is_eye_close = v.second;  // 是否闭眼
        long cur_time = v.first;
        if (is_eye_close) {
            is_eye_open = false;
            consecutive_eye_open_count = 0;  // 重置睁眼计数
            consecutive_eye_close_count++;

            // 记录闭眼开始时间
            if (consecutive_eye_close_count == 1 && !accumulate_flag) {
                start_time = cur_time;
                eye_start_index = eye_index;
            }
            // 达到连续闭眼帧数
            if (consecutive_eye_close_count >= EYE_CLOSE_FRAME && !accumulate_flag) {
                accumulate_flag = true;
                total_count += consecutive_eye_close_count - 1;
                eye_close_count += consecutive_eye_close_count - 1;
            }

        } else {
            consecutive_eye_close_count = 0;
            consecutive_eye_open_count++;
        }

        if (accumulate_flag) {
            end_time = cur_time;
            long cost_time = end_time - start_time;
            if (is_eye_close)
                eye_close_count++;
            total_count++;

            float close_ratio = 0.0f;

            // 满足条件时记录有效闭眼周期
            // std::cout << "cost_time:" << cost_time << " eye_close_count:" << eye_close_count
            //           << " interval_start_ms:" << interval_start_ms
            //           << " consecutive_eye_open_count:" << consecutive_eye_open_count << std::endl;
            if (consecutive_eye_open_count <= EYE_REDURANCY_FRAME &&
                cost_time >= interval_start_ms) {
                if (total_count > 0) {
                    close_ratio = static_cast<float>(eye_close_count) / total_count;
                    // 小概率偶发性bug实在无法捕捉到，先增加个正确性判断补丁(在10fps情况下)
                    if (start_time == 0 ||
                        eye_close_count <
                            ((interval_start_ms - 100) / 100) * EYE_CLOSE_THRESHOLD * 0.8) {
                        accumulate_flag = false;
                        close_ratio = 0.0f;
                        printf("eye_close_count || 0 start_time limit... \n ", __LINE__);
                    }
                    if (close_ratio >= EYE_CLOSE_THRESHOLD) {
                        valid_count++;
                        accumulate_flag = false;

                        // 可视化信息输出
                        tired_info_.close_eye_count = valid_count;
                        eye_end_index = eye_index + 1;
                        if (valid_count == 1) {
                            tired_info_.close_eye_duration_ms = std::to_string(cost_time);
                            tired_info_.close_eye_start_end_index =
                                std::to_string(eye_start_index) + "-" +
                                std::to_string(eye_end_index);
                        } else {
                            tired_info_.close_eye_duration_ms += ";" + std::to_string(cost_time);
                            tired_info_.close_eye_start_end_index += ";" +
                                std::to_string(eye_start_index) + "-" +
                                std::to_string(eye_end_index);
                        }
                    }
                }

            } else if (consecutive_eye_open_count > EYE_REDURANCY_FRAME) {
                accumulate_flag = false;
                is_eye_open = true;
            }
            // 重置计数器
            if (!accumulate_flag) {
                total_count = 0;
                eye_close_count = 0;
                consecutive_eye_open_count = 0;
                consecutive_eye_close_count = 0;
                start_time = 0;
                end_time = 0;
            }
        }
        eye_index++;
    }

    return valid_count >= count;
}

void DrowsinessWarnNonEU::Reset() {
    ok_flag = false;

    // 非欧标数据重置
    eye_3s_result.clear();
    eye_5s_result.clear();
    eye_7s_result.clear();

    warn_count = 0;
    last_warning_type.clear();
    last_warning_type = "";
    histor_warn_type = NORMAL;
    tired_info_ = {0};

    last_2_5s_eye_trigger_time = 0;  //记录长闭眼2.5s最后报警时间点
    last_4_5s_eye_trigger_time = 0;  //记录长闭眼4.5s最后报警时间点
    last_6_5s_eye_trigger_time = 0;  //记录长闭眼6.5s最后报警时间点
    last_warning_level = NORMAL;     // 记录上次报警的等级

    return;
}

static WarningClass CurWarnType(const std::string& cur_type) {
    int index = 0;
    std::vector<std::string> eye_types = {"light_60_9_eye",      "medium_60_10_eye",
                                          "medium_20_2eye_0_75", "medium_eye_close_continue",
                                          "heavy_20_2eye_1_2",   "heavy_20_eye_2_4"};
    std::vector<std::string> mouth_types = {"light_60_2mouth", "medium_60_3mouth", "mouth3_120s_4"};

    if (std::find(eye_types.begin(), eye_types.end(), cur_type) != eye_types.end()) {
        index = 1;
    } else if (std::find(mouth_types.begin(), mouth_types.end(), cur_type) != mouth_types.end()) {
        index = 2;
    }

    return (WarningClass)index;
}

void DrowsinessWarnNonEU::PrintFatigueLog(WarningType status) {
    // 打印fatigue log
    struct timeval tv;
    gettimeofday(&tv, NULL);
    long now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    // if (now_ts - last_ts >= 1000) {
    {
        std::cout << "[DMS Fatigue]:" << status << "||" << tired_info_.total_eye_count << "|"
                  << tired_info_.fatigue_blink_count << " " << tired_info_.fatigue_blink_duration_ms
                  << " " << tired_info_.fatigue_blink_start_end_index << "|"
                  << tired_info_.close_eye_count << " " << tired_info_.close_eye_duration_ms << " "
                  << tired_info_.close_eye_start_end_index << "|" << tired_info_.total_mouth_count
                  << "|" << tired_info_.yawn_count << " " << tired_info_.open_mouth_duration_ms
                  << " " << tired_info_.open_mouth_start_end_index << std::endl;
        last_ts = now_ts;
    }
}

WarningType DrowsinessWarnNonEU::GetWarnStatus() {
    // 获取当前时间
    // long current_trigger_time = std::chrono::duration_cast<std::chrono::milliseconds>(
    //                                 std::chrono::system_clock::now().time_since_epoch())
    //                                 .count();
    long current_trigger_time = cache_time;
    tired_info_.total_eye_count = 0;
    // 哈欠可视化信息清除，非欧标没有哈欠
    tired_info_.yawn_count = 0;
    tired_info_.open_mouth_duration_ms = "";
    tired_info_.open_mouth_start_end_index = "";
    tired_info_.total_mouth_count = 0;
    // printf("last_warning_level:%d,warn_count:%d\n", last_warning_level, warn_count);
    if (is_eye_open && histor_warn_type != NORMAL) {
        printf("Eye opened, reset histor_warn_type:%d .\n", histor_warn_type);
        histor_warn_type = WarningType::NORMAL;
    }
    //1.先判断历史帧是否存在疲劳等级
    if (last_warning_level != WarningType::NORMAL) {
        //每次报警要报5帧
        if (warn_count < 4) {
            warn_count++;
            PrintFatigueLog(last_warning_level);
            return last_warning_level;  //直接返回上次疲劳等级
        }
        warn_count = 0;

        //根据上次报警类型，清除对应的报警数据
        if (last_warning_type == "light_eye") {
            last_2_5s_eye_trigger_time = current_trigger_time;
            eye_3s_result.clear();
            // 可视化信息清除
            tired_info_.close_eye_count = 0;
            tired_info_.close_eye_duration_ms = "";
            tired_info_.close_eye_start_end_index = "";
        } else if (last_warning_type == "medium_eye") {
            last_4_5s_eye_trigger_time = current_trigger_time;
            eye_5s_result.clear();
            // 可视化信息清除
            tired_info_.close_eye_count = 0;
            tired_info_.close_eye_duration_ms = "";
            tired_info_.close_eye_start_end_index = "";
        } else if (last_warning_type == "heavy_eye") {
            last_6_5s_eye_trigger_time = current_trigger_time;
            eye_7s_result.clear();
            // 可视化信息清除
            tired_info_.close_eye_count = 0;
            tired_info_.close_eye_duration_ms = "";
            tired_info_.close_eye_start_end_index = "";
        }
    }

    WarningType status = NORMAL;    //默认正常
    std::string warning_type = "";  //记录本次报警的类型
    //2./2.判断疲劳等级
    bool light_eye = false;
    light_eye = GetEyeCloseAddUpTime(eye_3s_result, 1, 2500);  //判断是否长闭眼2.5s

    if (light_eye) {
        status = DROWSINESS_LEVEL_LIGHT;
        warning_type = "light_eye";
    }

    bool medium_eye = false;
    medium_eye = GetEyeCloseAddUpTime(eye_5s_result, 1, 4500);  //判断是否长闭眼4.5s
    if (medium_eye) {
        status = DROWSINESS_LEVEL_MEDIUM;
        warning_type = "medium_eye";
    }

    bool heavy_eye = false;
    heavy_eye = GetEyeCloseAddUpTime(eye_7s_result, 1, 6500);  //判断是否长闭眼6.5s
    if (heavy_eye) {
        status = DROWSINESS_LEVEL_HEAVY;
        warning_type = "heavy_eye";
    }

    if (status > histor_warn_type) {
        histor_warn_type = status;
    }

    std::cout << "warning_type:" << warning_type << " status:" << status
              << " histor_warn_type:" << histor_warn_type << " is_eye_open:" << is_eye_open
              << " last_warning_type:" << last_warning_type << " warn_count:" << warn_count
              << std::endl;

    // 如果历史等级已经升到重度，且眼睛没睁开，则不能降级报警，只能等重度满足
    if (histor_warn_type == DROWSINESS_LEVEL_HEAVY && !is_eye_open) {
        if (!heavy_eye) {
            // 当前检测不到重度闭眼，就返回NORMAL，不允许降级
            printf("Force keep silence after heavy level without eye open!\n");
            status = NORMAL;  //默认正常
            warning_type = "";
            eye_3s_result.clear();
            eye_5s_result.clear();
        } else {
            // 检测到了重度闭眼，允许报重度
            status = DROWSINESS_LEVEL_HEAVY;
            warning_type = "heavy_eye";
        }
    }

    // 同等级触发时，如果间隔小于3s，不得再次报警
    if (status != NORMAL && warning_type == "light_eye" &&
        (current_trigger_time - last_2_5s_eye_trigger_time) < 3000) {
        eye_3s_result.clear();
        printf("trigger time1:%ld.\n", (current_trigger_time - last_2_5s_eye_trigger_time));
        status = NORMAL;  // 如果间隔小于3秒，返回当前状态，不进行报警
        PrintFatigueLog(status);
        return status;
    } else if (status != NORMAL && warning_type == "medium_eye" &&
               (current_trigger_time - last_4_5s_eye_trigger_time) < 3000) {
        eye_5s_result.clear();
        printf("trigger time2:%ld.\n", (current_trigger_time - last_4_5s_eye_trigger_time));
        status = NORMAL;  // 如果间隔小于3秒，返回当前状态，不进行报警
        PrintFatigueLog(status);
        return status;
    } else if (status != NORMAL && warning_type == "heavy_eye" &&
               (current_trigger_time - last_6_5s_eye_trigger_time) < 3000) {
        eye_7s_result.clear();
        printf("trigger time3:%ld.\n", (current_trigger_time - last_6_5s_eye_trigger_time));
        status = NORMAL;  // 如果间隔小于3秒，返回当前状态，不进行报警
        PrintFatigueLog(status);
        return status;
    }

    if (last_warning_type != warning_type && !warning_type.empty()) {
        last_warning_type = warning_type;
        last_warning_level = status;  // 更新上次报警等级
    } else if (warning_type.empty()) {
        last_warning_type = "";
        last_warning_level = NORMAL;
    }
    
    PrintFatigueLog(status);
    printf("status:%d, close_eye_count:%d, close_eye_duration_ms:%s, close_eye_start_end_index:%s, "
           "total_eye_count:%d\n",
           status, tired_info_.close_eye_count, tired_info_.close_eye_duration_ms.c_str(),
           tired_info_.close_eye_start_end_index.c_str(), tired_info_.total_eye_count);
    return status;
}

void DrowsinessWarnNonEU::SetOk() {
    ok_flag = true;
}

void DrowsinessWarnNonEU::GetTired(tx_tired& tired_info) {
    // 闭眼眨眼相关信息
    tired_info.total_eye_count = tired_info_.total_eye_count;
    tired_info.fatigue_blink_count = tired_info_.fatigue_blink_count;
    tired_info.fatigue_blink_duration_ms = tired_info_.fatigue_blink_duration_ms;
    tired_info.fatigue_blink_start_end_index = tired_info_.fatigue_blink_start_end_index;

    tired_info.close_eye_count = tired_info_.close_eye_count;
    tired_info.close_eye_duration_ms = tired_info_.close_eye_duration_ms;
    tired_info.close_eye_start_end_index = tired_info_.close_eye_start_end_index;

    // 哈欠相关信息
    tired_info.total_mouth_count = tired_info_.total_mouth_count;
    tired_info.yawn_count = tired_info_.yawn_count;
    tired_info.open_mouth_duration_ms = tired_info_.open_mouth_duration_ms;
    tired_info.open_mouth_start_end_index = tired_info_.open_mouth_start_end_index;

    return;
}

}  // namespace tongxing
