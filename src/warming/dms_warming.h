#ifndef __DMS_WARMINH_H__
#define __DMS_WARMINH_H__
#include "cc_bool_time_windows.h"
namespace tongxing{
    class cc_dms_warming{
        public:
            int init(long time_width,float percent_th,int intrval,int speed=0);
            bool update(bool input_value,long ms_ts,int speed);
            void clear(long ms_ts);
            void set_enable(bool enable);
        private:
            CcBoolTimeWindowsStatistics time_windows;
            long last_warming_ts;
            long intrval_;
            int speed_;
            bool enable_=true;
    };

}


#endif
