#include "dms_calculate_warning_byd.h"
#include "CalmCarLog.h"

namespace tongxing {

DrowsinessWarn::DrowsinessWarn() {
    eye_result.clear();
    mouth_result.clear();
    eye_2s_result.clear();
    eye_3s_result.clear();

    tired_info_ = {0};  //详细占比
}

DrowsinessWarn::~DrowsinessWarn() {}

void DrowsinessWarn::Update(const Warn_Info& info) {
    //更新眼睛数据队列
    std::pair<long, bool> eye_pair;
    eye_pair.first = info.time_input;
    eye_pair.second = info.eye_close_status;
    eye_result.emplace_back(eye_pair);
    eye_2s_result.emplace_back(eye_pair);
    eye_3s_result.emplace_back(eye_pair);

    //更新嘴巴数据队列
    std::pair<long, size_t> mouth_pair;
    mouth_pair.first = info.time_input;
    mouth_pair.second = info.mouth_open_status; // 外层需要使用双端阈值，来区分不止2种状态
    mouth_result.emplace_back(mouth_pair);

    if ((mouth_result.size() >= 2)) {
        if ((mouth_result.back().first - mouth_result.front().first) > 300000) {  //5分钟数据
            mouth_result.pop_front();  //移除队列最前面的数据
        }
    }

    if (eye_result.size() >= 2) {
        if ((eye_result.back().first - eye_result.front().first) > 60000) {  //60秒数据
            eye_result.pop_front();
        }
    }

    if (eye_2s_result.size() >= 2) {
        if ((eye_2s_result.back().first - eye_2s_result.front().first) > 2000) {  //2秒数据
            eye_2s_result.pop_front();
        }
    }

    if (eye_3s_result.size() >= 2) {
        if ((eye_3s_result.back().first - eye_3s_result.front().first) > 3000) {  //3秒数据
            eye_3s_result.pop_front();
        }
    }

    cache_time = info.time_input;
    temp_warn = info;

    return;
}

//这里计算各个报警状态

//闭眼次数+没有单次闭眼时长限制
bool DrowsinessWarn::GetEyeCloseAddUpTime(std::deque<std::pair<long, bool>>& eye_deque,
                                          const int& count,
                                          long interval_start_ms) {
    int valid_count = 0;
    int consecutive_eye_open_count = 0;
    int consecutive_eye_close_count = 0;
    int total_count = 0;
    int eye_close_count = 0;
    bool accumulate_flag = false;  // 是否累积标志
    long start_time = 0, end_time = 0;
    const int EYE_REDURANCY_FRAME = 2;  // 冗余帧数
    const int EYE_CLOSE_FRAME = 3;      // 连续闭眼帧数
    const float EYE_CLOSE_THRESHOLD = 0.9f;
    int eye_index = 0;
    int eye_start_index = 0;          
    int eye_end_index = 0;

    // 遍历眼睛状态队列
    for (const auto& v : eye_deque) {
        bool is_eye_close = v.second;  // 是否闭眼
        long cur_time = v.first;
        if (is_eye_close) {
            consecutive_eye_open_count = 0;  // 重置睁眼计数
            consecutive_eye_close_count++;

            // 记录闭眼开始时间
            if (consecutive_eye_close_count == 1 && !accumulate_flag) {
                start_time = cur_time;
                eye_start_index = eye_index;
            }
            // 达到连续闭眼帧数
            if (consecutive_eye_close_count >= EYE_CLOSE_FRAME && !accumulate_flag) {
                accumulate_flag = true;
                total_count += consecutive_eye_close_count - 1;
                eye_close_count += consecutive_eye_close_count - 1;
            }

        } else {
            consecutive_eye_close_count = 0;
            consecutive_eye_open_count++;
        }

        if (accumulate_flag) {
            end_time = cur_time;
            long cost_time = end_time - start_time;
            if (is_eye_close)
                eye_close_count++;
            total_count++;

            float close_ratio = 0.0f;

            // 满足条件时记录有效闭眼周期
            if (consecutive_eye_open_count <= EYE_REDURANCY_FRAME &&
                cost_time >= interval_start_ms) {
                if (total_count > 0) {
                    close_ratio = static_cast<float>(eye_close_count) / total_count;
                    // 小概率偶发性bug实在无法捕捉到，先增加个正确性判断补丁(在10fps情况下)
                    if (start_time == 0 ||
                        eye_close_count <
                            ((interval_start_ms - 100) / 100) * EYE_CLOSE_THRESHOLD * 0.8) {
                        accumulate_flag = false;
                        close_ratio = 0.0f;
                    }
                    if (close_ratio >= EYE_CLOSE_THRESHOLD) {
                        valid_count++;
                        accumulate_flag = false;

                        // 可视化信息输出
                        tired_info_.close_eye_count = valid_count;
                        eye_end_index = eye_index + 1;
                        if (valid_count == 1) {
                            tired_info_.close_eye_duration_ms = std::to_string(cost_time);
                            tired_info_.close_eye_start_end_index = std::to_string(eye_start_index) +
                                                                "-" + std::to_string(eye_end_index);
                        } else {
                            tired_info_.close_eye_duration_ms += ";" + std::to_string(cost_time);
                            tired_info_.close_eye_start_end_index += ";" + std::to_string(eye_start_index) +
                                                                "-" + std::to_string(eye_end_index);
                        }
                    }
                }

            } else if (consecutive_eye_open_count > EYE_REDURANCY_FRAME) {
                accumulate_flag = false;
            }

            // if (/*interval_start_ms == 1200 || */interval_start_ms == 2400)
            //     std::cout << " interval_start_ms:" << interval_start_ms  << " count:" <<  count << " cost_time:" << cost_time
            //     << " consecutive_eye_close_count:" << consecutive_eye_close_count <<" consecutive_eye_open_count:" <<
            //     consecutive_eye_open_count << " valid_count:" << valid_count <<
            //     " accumulate_flag:" << accumulate_flag << " close_ratio:" << close_ratio << " eye_close_count:" << eye_close_count <<
            //     " total_count:" << total_count << std::endl;
            // 重置计数器
            if (!accumulate_flag) {
                total_count = 0;
                eye_close_count = 0;
                consecutive_eye_open_count = 0;
                consecutive_eye_close_count = 0;
                start_time = 0;
                end_time = 0;
            }
        }
        eye_index ++;

        // // 如果已经达到所需的有效闭眼周期，提前退出
        // if (valid_count >= count) {
        //     return true;
        // }
    }

    return valid_count >= count;
}

//单位时间内打哈欠
bool DrowsinessWarn::GetMouthCount(std::deque<std::pair<long, size_t>>& mouth_deque,
                                   const int& count) {
    int valid_yawn = 0;                     // 有效哈欠个数
    int consecutive_mouth_open_count = 0;   // 连续张嘴的帧数
    int consecutive_mouth_close_count = 0;  // 连续闭嘴的帧数
    bool accumulate_flag = false;           // 是否累计标志
    long start_time = 0, end_time = 0;
    const int MOUTH_OPEN_FRAME = 3;        // 连续张嘴帧数
    const int MOUTH_REDUNDANCY_FRAME = 2;  // 冗余帧数
    const long MIN_YAWN_TIME = 3000;       // 最小有效哈欠时间
    const long MAX_YAWN_TIME = 60000;      // 最大有效哈欠时间
    int mouth_index = 0;
    int mouth_start_index = 0;
    int mouth_end_index = 0;

    // 遍历嘴巴状态队列
    for (const auto& v : mouth_deque) {
        size_t yawn_status = v.second;  // 是否张嘴
        long cur_time = v.first;

        if (yawn_status == 1) { // 0: 闭嘴;1：张嘴;2：张嘴到闭嘴之间的中间态
            consecutive_mouth_close_count = 0;  // 重置非张嘴计数
            consecutive_mouth_open_count++;

            // 记录第一次张嘴时间
            if (consecutive_mouth_open_count == 1 && !accumulate_flag) {
                start_time = cur_time;
                mouth_start_index = mouth_index;
            }

            // 达到连续张嘴帧数
            if (consecutive_mouth_open_count >= MOUTH_OPEN_FRAME) {
                accumulate_flag = true;
            }
        } else if (yawn_status == 0 && accumulate_flag) {
            consecutive_mouth_open_count = 0;
            consecutive_mouth_close_count++;
            end_time = cur_time;
            long cost_time = end_time - start_time;

            if (consecutive_mouth_close_count <= MOUTH_REDUNDANCY_FRAME &&
                (cost_time >= MIN_YAWN_TIME && cost_time <= MAX_YAWN_TIME)) {
                valid_yawn++;
                accumulate_flag = false;

                // 可视化信息输出
                tired_info_.yawn_count = valid_yawn;
                mouth_end_index = mouth_index + 1;
                if (valid_yawn == 1) {
                    tired_info_.open_mouth_duration_ms = std::to_string(cost_time);
                    tired_info_.open_mouth_start_end_index = std::to_string(mouth_start_index) +
                                                        "-" + std::to_string(mouth_end_index);
                } else {
                    tired_info_.open_mouth_duration_ms += ";" + std::to_string(cost_time);
                    tired_info_.open_mouth_start_end_index += ";" + std::to_string(mouth_start_index) +
                                                        "-" + std::to_string(mouth_end_index);
                }
            } else if (consecutive_mouth_close_count > MOUTH_REDUNDANCY_FRAME) {
                accumulate_flag = false;
            }

            // 重置计数器
            if (!accumulate_flag) {
                consecutive_mouth_close_count = 0;
                start_time = 0;
                end_time = 0;
            }
        } else {
            consecutive_mouth_open_count = 0;
        }
        mouth_index ++;
    }

    // 返回是否满足哈欠数量
    return valid_yawn >= count;
}

void DrowsinessWarn::Reset() {
    ok_flag = false;
    warn_no_response_flag = false;

    eye_temp_result.clear();
    mouth_temp_result.clear();
    history_flag = false;

    eye_result.clear();
    mouth_result.clear();
    eye_2s_result.clear();
    eye_3s_result.clear();

    alarm_start_time = cache_time;

    warn_count = 0;
    last_warning_type.clear();

    histor_warn_type = NORMAL;
    his_warn_class = NONE_RELATED;
    tired_info_ = {0};
    // last_trigger_time = 0;        // 记录上次报警的时间
    last_warning_type = "";
    last_eye_trigger_time = 0;     //记录眨眼最后报警时间点
    last_mouth_trigger_time = 0;   //记录哈欠最后报警时间点
    last_2s_eye_trigger_time = 0;  //记录长闭眼2s最后报警时间点
    last_3s_eye_trigger_time = 0;  //记录长闭眼3s最后报警时间点
    last_warning_level = NORMAL;   // 记录上次报警的等级

    return;
}

void DrowsinessWarn::Cleareye() {
    const size_t removeCount = 30;
    size_t total_size = eye_result.size();
    for (size_t i = 0; i < removeCount && total_size>=removeCount; ++i) {
        eye_result[total_size - i - 1].second = false; //清除看某些分心点位导致的闭眼状态累计
    }
}

static WarningClass CurWarnType(const std::string& cur_type) {
    int index = 0;
    std::vector<std::string> eye_types = {"light_60_9_eye",      "medium_60_10_eye",
                                          "medium_20_2eye_0_75", "medium_eye_close_continue",
                                          "heavy_20_2eye_1_2",   "heavy_20_eye_2_4"};
    std::vector<std::string> mouth_types = {"light_60_2mouth", "medium_60_3mouth", "mouth3_120s_4"};

    if (std::find(eye_types.begin(), eye_types.end(), cur_type) != eye_types.end()) {
        index = 1;
    } else if (std::find(mouth_types.begin(), mouth_types.end(), cur_type) != mouth_types.end()) {
        index = 2;
    }

    return (WarningClass)index;
}

bool DrowsinessWarn::DeterFatigueInducedBlinking(const int& requiredBlinkCount) {
    const long BLINK_DURATION_THRESHOLD = 500;       // 疲劳性眨眼阈值：500ms
    const long BLINK_DURATION_THRESHOLD_MAX = 2000;  // 疲劳性眨眼最大阈值：2000ms
    const long EYE_OPEN_DURATION_THRESHOLD = 1000;   // 两次眨眼间睁眼时间阈值：1000ms

    int detectedBlinkCount = 0;
    long lastBlinkEndTime = -1;     // 初始化为-1，表示尚未有有效的眨眼记录
    long lastEyeOpenStartTime = 0;  // 记录上一次有效眨眼之后的眼睛睁开时间
    int eye_start_index = 0;
    for (auto it = eye_result.begin(); it != eye_result.end(); ++it) {       
        if (it->second) {  // 找到闭眼开始
            auto blinkStartIt = it;
            auto blinkEndIt = std::next(it);
            int eye_end_index = eye_start_index+1;

            // 查找闭眼结束的位置
            while (blinkEndIt != eye_result.end() && blinkEndIt->second) {
                ++blinkEndIt;
                ++eye_end_index;
            }

            if (blinkEndIt == eye_result.end())
                break;  // 没有找到对应的睁眼时刻

            long closeDuration = (blinkEndIt->first - blinkStartIt->first);
            if (closeDuration > BLINK_DURATION_THRESHOLD &&
                closeDuration < BLINK_DURATION_THRESHOLD_MAX) {  // 判断是否为疲劳性眨眼
                if (detectedBlinkCount > 0) {
                    long openDuration = (blinkStartIt->first - lastEyeOpenStartTime);
                    printf("openDuration:%ld\n", openDuration);
                    if (openDuration < EYE_OPEN_DURATION_THRESHOLD) {
                        // 如果睁眼时间不够，则不计为有效眨眼
                        continue;
                    }
                }
                detectedBlinkCount++;
                lastBlinkEndTime = blinkEndIt->first;
                lastEyeOpenStartTime =
                    blinkEndIt->first;  // 更新上一次有效眨眼结束时间为当前眨眼结束时间

                // 可视化信息
                tired_info_.fatigue_blink_count = detectedBlinkCount;
                if (detectedBlinkCount == 1) {
                    tired_info_.fatigue_blink_duration_ms = std::to_string(closeDuration);
                    tired_info_.fatigue_blink_start_end_index =
                        std::to_string(eye_start_index) + "-" + std::to_string(eye_end_index);
                } else {
                    tired_info_.fatigue_blink_duration_ms += ";" + std::to_string(closeDuration);
                    tired_info_.fatigue_blink_start_end_index +=
                        ";" + std::to_string(eye_start_index) + "-" + std::to_string(eye_end_index);
                }

                if (detectedBlinkCount >= requiredBlinkCount) {
                    return true;  // 找到了所需次数的疲劳性眨眼
                }
            }
            it = blinkEndIt - 1;  // 更新迭代器位置，跳过已处理的闭眼区间
        } else {
            // 只有当没有正在进行的闭眼周期时，更新眼睛打开的起始时间
            // if (lastBlinkEndTime != -1) {
            //     lastEyeOpenStartTime = it->first;
            // }
        }
        eye_start_index ++;
    }

    return false;  // 未找到指定次数的疲劳性眨眼
}

void DrowsinessWarn::PrintFatigueLog(WarningType status) {
    // 打印fatigue log
    struct timeval tv;
    gettimeofday(&tv, NULL);
    long now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    if (now_ts - last_ts >= 1000) {
        std::cout << "[DMS Fatigue]:" << status << "||" 
                    << tired_info_.total_eye_count << "|"
                    << tired_info_.fatigue_blink_count << " "
                    << tired_info_.fatigue_blink_duration_ms << " "
                    << tired_info_.fatigue_blink_start_end_index << "|"
                    << tired_info_.close_eye_count << " "
                    << tired_info_.close_eye_duration_ms << " "
                    << tired_info_.close_eye_start_end_index << "|"
                    << tired_info_.total_mouth_count << "|"
                    << tired_info_.yawn_count << " "
                    << tired_info_.open_mouth_duration_ms << " "
                    <<tired_info_.open_mouth_start_end_index<< std::endl;
        last_ts = now_ts;
    }
}

WarningType DrowsinessWarn::GetWarnStatus() {

    // 获取当前时间
    long current_trigger_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                                    std::chrono::system_clock::now().time_since_epoch())
                                    .count();
   
    tired_info_.total_eye_count = eye_result.size();
    tired_info_.total_mouth_count = mouth_result.size();
    // printf("last_warning_level:%d,warn_count:%d\n", last_warning_level, warn_count);
    //1.先判断历史帧是否存在疲劳等级
    if (last_warning_level != WarningType::NORMAL) {
        //每次报警要报5帧
        if (warn_count < 5) {
            warn_count++;
            // printf("now dict return last_warning_level.\n");
            PrintFatigueLog(last_warning_level);
            return last_warning_level;  //直接返回上次疲劳等级
        }
        warn_count = 0;

        //根据上次报警类型，清除对应的报警数据
        if (last_warning_type == "light_eye") {
            last_eye_trigger_time = current_trigger_time;
            eye_result.clear();
            // 可视化信息清除
            tired_info_.fatigue_blink_count = 0;
            tired_info_.fatigue_blink_duration_ms = "";
            tired_info_.fatigue_blink_start_end_index = "";
        } else if (last_warning_type == "light_mouth") {
            last_mouth_trigger_time = current_trigger_time;
            mouth_result.clear();
            // 可视化信息清除
            tired_info_.yawn_count = 0;
            tired_info_.open_mouth_duration_ms = "";
            tired_info_.open_mouth_start_end_index = "";
        } else if (last_warning_type == "medium_eye") {
            last_2s_eye_trigger_time = current_trigger_time;
            eye_2s_result.clear();
            // 可视化信息清除
            tired_info_.close_eye_count = 0;
            tired_info_.close_eye_duration_ms = "";
            tired_info_.close_eye_start_end_index = "";

            eye_result.clear();
            // 可视化信息清除
            tired_info_.fatigue_blink_count = 0;
            tired_info_.fatigue_blink_duration_ms = "";
            tired_info_.fatigue_blink_start_end_index = "";
        } else if (last_warning_type == "heavey_eye") {
            last_3s_eye_trigger_time = current_trigger_time;
            eye_3s_result.clear();
            // 可视化信息清除
            tired_info_.close_eye_count = 0;
            tired_info_.close_eye_duration_ms = "";
            tired_info_.close_eye_start_end_index = "";

            eye_result.clear();
            // 可视化信息清除
            tired_info_.fatigue_blink_count = 0;
            tired_info_.fatigue_blink_duration_ms = "";
            tired_info_.fatigue_blink_start_end_index = "";
        }
    }

    WarningType status = NORMAL;    //默认正常
    std::string warning_type = "";  //记录本次报警的类型
    //2./2.判断疲劳等级
    bool light_eye = false;
    bool light_mouth = false;

    light_eye = DeterFatigueInducedBlinking(2);    //判断疲劳性眨眼
    light_mouth = GetMouthCount(mouth_result, 2);  //判断疲劳性打哈欠

    if (light_eye || light_mouth) {
        status = DROWSINESS_LEVEL_LIGHT;
        if (light_eye) {
            warning_type = "light_eye";

        } else {
            warning_type = "light_mouth";
        }
    }

    bool medium_eye = false;
    medium_eye = GetEyeCloseAddUpTime(eye_2s_result, 1, 1900);  //判断是否长闭眼2s
    if (medium_eye) {
        status = DROWSINESS_LEVEL_MEDIUM;
        warning_type = "medium_eye";
    }

    bool heavey_eye = false;
    heavey_eye = GetEyeCloseAddUpTime(eye_3s_result, 1, 2900);  //判断是否长闭眼3s
    if (heavey_eye) {
        status = DROWSINESS_LEVEL_HEAVY;
        warning_type = "heavey_eye";
    }
    // std::cout << "1 warning_type:" << warning_type << " status:" << status 
    // << " histor_warn_type:" << histor_warn_type << " last_warning_type:" << last_warning_type << " warn_count:" << warn_count << std::endl;

    // 同等级触发时，如果间隔小于3s，不得再次报警
    if (status != NORMAL && warning_type == "light_eye" &&
        (current_trigger_time - last_eye_trigger_time) < 3000) {
        // printf("trigger time1.\n");
        status = NORMAL;  // 如果间隔小于3秒，返回当前状态，不进行报警
        PrintFatigueLog(status);
        return status;
    } else if (status != NORMAL && warning_type == "light_mouth" &&
               (current_trigger_time - last_mouth_trigger_time) < 3000) {
        // printf("trigger time2.\n");
        status = NORMAL;  // 如果间隔小于3秒，返回当前状态，不进行报警
        PrintFatigueLog(status);
        return status;
    } else if (status != NORMAL && warning_type == "medium_eye" &&
               (current_trigger_time - last_2s_eye_trigger_time) < 3000) {
        // printf("trigger time3.\n");
        status = NORMAL;  // 如果间隔小于3秒，返回当前状态，不进行报警
        PrintFatigueLog(status);
        return status;
    } else if (status != NORMAL && warning_type == "heavey_eye" &&
               (current_trigger_time - last_3s_eye_trigger_time) < 3000) {
        // printf("trigger time4.\n");
        status = NORMAL;  // 如果间隔小于3秒，返回当前状态，不进行报警
        PrintFatigueLog(status);
        return status;
    }

    // printf("last_warning_type=%s,warning_type=%s\n", last_warning_type.c_str(),
    //        warning_type.c_str());
    if (last_warning_type != warning_type && !warning_type.empty()) {
        last_warning_type = warning_type;
        last_warning_level = status;  // 更新上次报警等级
    } else if (warning_type.empty()) {
        last_warning_type = "";
        last_warning_level = NORMAL;
    }
    // std::cout << "2 warning_type:" << warning_type << " last_warning_type:" << last_warning_type << " status:" << status << std::endl;
    PrintFatigueLog(status);
    return status;
}

void DrowsinessWarn::SetOk() {
    ok_flag = true;
}

void DrowsinessWarn::GetTired(tx_tired& tired_info) {
    // 闭眼眨眼相关信息
    tired_info.total_eye_count = tired_info_.total_eye_count;
    tired_info.fatigue_blink_count = tired_info_.fatigue_blink_count;
    tired_info.fatigue_blink_duration_ms = tired_info_.fatigue_blink_duration_ms;
    tired_info.fatigue_blink_start_end_index = tired_info_.fatigue_blink_start_end_index;

    tired_info.close_eye_count = tired_info_.close_eye_count;
    tired_info.close_eye_duration_ms = tired_info_.close_eye_duration_ms;
    tired_info.close_eye_start_end_index = tired_info_.close_eye_start_end_index;

    // 哈欠相关信息
    tired_info.total_mouth_count = tired_info_.total_mouth_count;
    tired_info.yawn_count = tired_info_.yawn_count;
    tired_info.open_mouth_duration_ms = tired_info_.open_mouth_duration_ms;
    tired_info.open_mouth_start_end_index = tired_info_.open_mouth_start_end_index;

    return;
}

}  // namespace tongxing
