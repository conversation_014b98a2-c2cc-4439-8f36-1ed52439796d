#include "dms_warming.h"
#include <time.h>
#include "CalmCarLog.h"
namespace tongxing
{
    int cc_dms_warming::init(long time_width, float percent_th, int intrval,int speed){
        struct timeval tv;
        gettimeofday(&tv, NULL);
        long now_ts = tv.tv_sec*1000+tv.tv_usec/1000;
        intrval_=intrval;
        last_warming_ts= now_ts;
        speed_=speed;
        return time_windows.init(time_width,percent_th);
    }
    bool cc_dms_warming::update(bool input_value, long ms_ts,int speed){
        float percent;
        bool status=time_windows.update(input_value,ms_ts,percent);
        if(last_warming_ts>ms_ts){
            last_warming_ts=ms_ts;
        }
        if(status && (ms_ts-last_warming_ts)>=intrval_ && input_value && speed>=speed_ && enable_){
            last_warming_ts=ms_ts;
            return true;
        }
        return false;
    }
    void cc_dms_warming::clear(long ms_ts){
        time_windows.clear();
        last_warming_ts=ms_ts;
    }
    void cc_dms_warming::set_enable(bool enable){
        enable_=enable;
    }
}