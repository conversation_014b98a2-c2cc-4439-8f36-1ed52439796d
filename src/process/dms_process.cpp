#include "dms_process.h"
#include "calmcar_dms_process.h"
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include "CalmCarLog.h"
#include "activate.h"
#include "cc_mouth_opening.h"
#include "json.h"

#define DROWSINESS_MASK (1 << 2 | 1 << 3 | 1 << 4)
#define DISTRACTION_MASK (1 << 2 | 1 << 4 | 1 << 5)

static std::string getCurrentTimes() {
    struct timeval tv;
    gettimeofday(&tv, NULL);

    static const int MAX_BUFFER_SIZE = 128;
    char timestamp_str[MAX_BUFFER_SIZE];
    time_t sec = static_cast<time_t>(tv.tv_sec);
    int ms = static_cast<int>(tv.tv_usec) / 1000;

    struct tm tm_time;
    localtime_r(&sec, &tm_time);
    static const char* formater = "%4d-%02d-%02d %02d:%02d:%02d.%03d";
    int wsize = snprintf(timestamp_str, MAX_BUFFER_SIZE, formater, tm_time.tm_year + 1900,
                         tm_time.tm_mon + 1, tm_time.tm_mday, tm_time.tm_hour, tm_time.tm_min,
                         tm_time.tm_sec, ms);

    timestamp_str[std::min(wsize, MAX_BUFFER_SIZE - 1)] = '\0';
    return std::string(timestamp_str);
}

#if defined(BYD_HKH_L) && !defined(X86_64)
extern "C" {
#include "isp.h"
}

// extern int ovt_AIAssisted_isp_set_sunglasses(int isp_id, int is_sunglasses);
// extern int ovt_AIAssisted_isp_set_aec(int vs_id, ovt_isp_aec_roi_boundary_t *val);
int ovt_isp_set_sunglasses(int is_sunglasses) {
    return ovt_AIAssisted_isp_set_sunglasses(0, is_sunglasses);
}

int ovt_isp_set_aec(int min_x, int max_x, int max_y, int min_y) {
    // TX_LOG_DEBUG("dms_process.cpp","debug");
    ovt_isp_aec_roi_boundary_t boundary;
    boundary.left = min_x;
    boundary.right = max_x;
    boundary.top = min_y;
    boundary.bottom = max_y;
    return ovt_AIAssisted_isp_set_aec_roi(0, &boundary);
    return 0;
}
#endif

namespace tongxing {
int DmsProcess::Init(const GetBboxFun& get_driving_face_bbox,
                     const GetPointArrayFun& get_driving_face_keypoint,
                     const GetFloatArrayFun& get_driving_face_angle,
                     const GetPointArrayFun& get_driving_eye_keypoint,
                     const GetFloatArrayFun& get_driving_right_eye_close_score,
                     const GetFloatArrayFun& get_driving_left_eye_close_score,
                     const GetIntFun& get_occlusion,
                     const GetFloatArrayFun& get_driving_face_attr,
                     const GetFloatArrayFun& get_driving_right_eye_landmarks,
                     const GetFloatArrayFun& get_driving_left_eye_landmarks,
                     const GetFloatArrayFun& get_exact_lum_info,
                     const GetBboxArrayFun& get_phone_cig_bbox,
                     const GetStabilityAnalysisFun& get_keypoints_stability_analysis,
                     const char* config_file) {
    get_driving_face_bbox_ = get_driving_face_bbox;
    get_driving_face_keypoint_ = get_driving_face_keypoint;
    get_driving_face_angle_ = get_driving_face_angle;
    get_driving_eye_keypoint_ = get_driving_eye_keypoint;
    get_driving_right_eye_close_score_ = get_driving_right_eye_close_score;
    get_driving_left_eye_close_score_ = get_driving_left_eye_close_score;
    get_occlusion_ = get_occlusion;
    get_driving_face_attr_ = get_driving_face_attr;
    get_driving_right_eye_landmarks_ = get_driving_right_eye_landmarks;
    get_driving_left_eye_landmarks_ = get_driving_left_eye_landmarks;
    get_exact_lum_info_ = get_exact_lum_info;
    get_phone_cig_bbox_ = get_phone_cig_bbox;
    get_keypoints_stability_analysis_ = get_keypoints_stability_analysis;
    tx_dms_out_warning.reset(new dms_out_warning());
    tx_dms_out_warning->dms_init_warning();

    noface_warning.init(kMinFaceBlockedEnterTimeMs, 0.9, 0, 10);  //无人脸
    // irblock_warning.init(3000, 1.0, 0, 10);   //红外阻断眼睛
    // mask_warning.init(3000, 1.0, 0, 10);      //戴口罩
    occlusion_warning.init(5000, 0.8, 0, 0);  //遮挡

    phone_det_win.init(1000, 0.7);
    smoking_det_win.init(1000, 0.7);
    phone_smoking_detect_enable_status_ = 0;
    //算法状态初始化时间
    struct timeval tv;
    gettimeofday(&tv, NULL);
    algorithm_start_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;

    return 0;
}

int DmsProcess::SetFatigue(bool type) {
    enable_fatigue_ = type;
    return 0;
}

bool DmsProcess::GetFatigue() {
    return enable_fatigue_;
}

int DmsProcess::DrowsiRestAlarm() {
    //enable_fatigue_ = State;
    return tx_dms_out_warning->DrowsiRestAlarm();
}

int DmsProcess::DmsDistractRestAlarm() {
    return tx_dms_out_warning->DistracRestAlarm();
}

int DmsProcess::DmsAlarmSetOk() {
    return tx_dms_out_warning->DmsAlarmSetOk();
}

void DmsProcess::getNonDistractHeadRotation(float& headyaw_min,
                                            float& headyaw_max,
                                            float& headpitch_min,
                                            float& headpitch_max) {
    tx_dms_out_warning->getNonDistractHeadRotation(headyaw_min, headyaw_max, headpitch_min,
                                                   headpitch_max);
}

void DmsProcess::GetTiredInfo(tx_tired& tired_info) {
    return tx_dms_out_warning->GetTiredInfo(tired_info);
}

void DmsProcess::GetDistractionInfo(internal_analysis_distraction_info& info) {
    return tx_dms_out_warning->GetDistractionInfo(info);
}

static long getTimeSleep(const long& ts, long& now_ts, long& last_frame_control_ts) {
    long time_need_sleep = 0;

    if (ts == 0) {
        struct timeval tv;
        gettimeofday(&tv, NULL);
        now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    } else {
        now_ts = ts;
        return time_need_sleep;  // ts不为0时，认为是debug模式，不进行帧率控制
    }

    long time_gap = now_ts - last_frame_control_ts;
    if (time_gap < 100) {
        time_need_sleep = (100 - time_gap) * 1000;
        // printf("time_wait_time:%ld\n", 100 - time_gap);
    }
    last_frame_control_ts = now_ts;

    return time_need_sleep;
}

bool DmsProcess::isAlgoActivate(TXDmsResult& result, const long& now_ts) {
    bool is_activate = true;
#ifdef __USE_ACTIVATE__
    if (ActivateService::instance().GetActivateStatus() == true) {
        result.algorithm_status = ALGORITHM_NORMAL;
    } else {
        if (is_algorithm_experience ||
            ((now_ts - algorithm_start_time) > algorithm_experience_time)) {
            is_algorithm_experience = true;
            result.algorithm_status = ALGORITHM_UNACTIVATED_NO_WORK;
            TX_LOG_WARN("DmsProcess", "DMS IS NOT Activate!!!!!!!!!!");
            is_activate = false;
        } else {
            result.algorithm_status = ALGORITHM_UNACTIVATED_EXPERIENCE_TIME;
        }
    }

#endif
    return is_activate;
}

bool DmsProcess::isHardwareError(const TXCarInfo* car_info, TXDmsResult& result) {
    TXSystemStatus current_system_status = SYSTEM_NORMAL;  //当前系统状态
    // 打印车机信息
    // printf("car_speed:%d,time_gap:%ld\n", car_info->speed, now_ts - last_ts);
    // printf("car_info:[mask:%d,speed:%d,steer_whl_snsr_rad:%f,steer_whl_snsr_rad_spd:%f,gear:%d,"
    //        "turn_light:%d,ldw:%d,can_info:%d,camera_info:%d]\n",
    //        car_info->mask, car_info->speed, car_info->steer_whl_snsr_rad,
    //        car_info->steer_whl_snsr_rad_spd, car_info->gear, car_info->turn_light, car_info->ldw,
    //        car_info->can_fault, car_info->camera_fault);

    if (car_info->can_fault != 0 || car_info->camera_fault != 0) {
        if (car_info->can_fault != 0 && car_info->camera_fault != 0) {
            //todo
        } else if (car_info->can_fault != 0) {
            //十六进制数，转换成二进制后，第0位表示开关信号状态，第1位表示车速信号状态，第2位表示方向盘信号状态，第3位表示档位信号状态，第4位表示转向灯信号状态。(0:正常，1:丢失)
            std::vector<int> binaryArray;
            int num = car_info->can_fault;
            // 将整数转换为二进制并存入数组
            while (num > 0) {
                int bit = num % 2;
                binaryArray.push_back(bit);
                num /= 2;
            }

            if (binaryArray.size() <= 4) {
                if (binaryArray.size() == 2 && binaryArray[1])
                    current_system_status = SYSTEM_CAN_FAULT;
                else if (binaryArray.size() == 3 && (binaryArray[1] || binaryArray[2]))
                    current_system_status = SYSTEM_CAN_FAULT;
                else if (binaryArray.size() == 4 &&
                         (binaryArray[1] || binaryArray[2] || binaryArray[3]))
                    current_system_status = SYSTEM_CAN_FAULT;
            } else {
                if (binaryArray[1] || binaryArray[2] || binaryArray[3]) {
                    current_system_status = SYSTEM_CAN_FAULT;
                }
            }
        } else {
            //todo
            current_system_status = SYSTEM_CAMERA_FAULT;
        }
    }

    result.system_status = current_system_status;  //系统故障结果
    if (result.system_status != SYSTEM_NORMAL) {   //如果是硬件故障则立即返回
        if (result.system_status == SYSTEM_CAMERA_FAULT ||
            result.system_status == SYSTEM_CAN_CAMERA_FAULT)
            result.camera_status = Camera_Invalid;
        result.drowsiness_status = Drowsiness_Invalid;
        result.distraction_status = Distraction_Invalid;
        if (tx_dms_out_warning->GetCalibrationStatus()) {
            result.calibrate_status = CALIBRATE_DONE;
        } else {
            result.calibrate_status = CALIBRATE_INVALID;
        }
        return true;
    }
    return false;
}

static bool checkResetDistraAndCali(const TXCarInfo* car_info) {
    bool need_reset = false;
#if defined(BYD_SC2EDE) || defined(BYD_SC3EFE) || defined(BYD_SC3EFE_R)
    //二次标定逻辑
    if (car_info->driver_door_status == TXDriverDoorStatus::DOOR_OPEN ||
        car_info->driver_seat_status != TXDriverSeatStatus::SEAT_STATIC) {
        need_reset = true;
    }
#endif

#if defined(BYD_SA2) || defined(BYD_HKH_L) || defined(BYD_HKH_R) || defined(BYD_EM2E) || defined(BYD_EK_L) || defined(BYD_SA2_R) || defined(BYD_EM2E_R)
    //二次标定逻辑
    if (car_info->driver_door_status == TXDriverDoorStatus::DOOR_OPEN) {
        need_reset = true;
    }
#endif

    return need_reset;
}

void DmsProcess::phoneAndSmokingProcess(TXDmsResult& result, const long& now_ts) {
#ifdef WITH_PHONE_SMOKING_DET
    if (phone_smoking_detect_enable_status_) {
        result.phone_status = NO_PHONE;
        result.smoking_status = NO_SMOKING;
    } else {
        result.phone_status = PHONE_INVALID;
        result.smoking_status = SMOKING_INVALID;
    }

    if (phone_smoking_detect_enable_status_ && isFaceValid()) {
        auto phone_cig_bboxs = get_phone_cig_bbox_();
        bool flag_has_phone = false;
        bool flag_has_smoking = false;
        for (auto bbox : *phone_cig_bboxs) {
            if (bbox.label == 1) {
                flag_has_phone = true;
            } else if (bbox.label == 2) {
                flag_has_smoking = true;
            }
        }
        float percent;
        if (phone_det_win.update(flag_has_phone, now_ts, percent)) {
            result.phone_status = HAS_PHONE;
            std::cout << "HAS_PHONE" << std::endl;
        }
        if (smoking_det_win.update(flag_has_smoking, now_ts, percent)) {
            result.smoking_status = HAS_SMOKING;
        }
        /*complete logic with phone and smoking...*/
    }
#endif
}

void DmsProcess::parseFaceBboxInfo(TXDmsFaceInfo& face_info) {
    auto bbox_ptr = get_driving_face_bbox_();
    if (bbox_ptr->score > kFaceBboxThr) {
        is_face_valid = true;

        if (bbox_ptr->bbox.height < kMinFaceHeightPx) {
            is_face_valid = false;
        }
        TX_LOG_DEBUG("DmsProcess", "%f %d %d %d %d", bbox_ptr->score, bbox_ptr->bbox.x,
                     bbox_ptr->bbox.y, bbox_ptr->bbox.width, bbox_ptr->bbox.height);
        float score = bbox_ptr->score;
        int xmin = bbox_ptr->bbox.x;
        int ymin = bbox_ptr->bbox.y;
        int width = bbox_ptr->bbox.width;
        int height = bbox_ptr->bbox.height;
        int xmax = xmin + width;
        int ymax = ymin + height;
        face_info.score = score;
        face_info.xmin = xmin;
        face_info.xmax = xmax;
        face_info.ymin = ymin;
        face_info.ymax = ymax;
    }
}

bool DmsProcess::isFaceValid() {
    return is_face_valid;
}
bool DmsProcess::isFaceKeypointsValid() {
    return is_face_keypoints_valid;
}
void DmsProcess::parseFaceAngle(TXDmsFaceInfo& face_info,
                                float& face_angle_score,
                                float& mouthpoint_score) {
    auto face_angle_ptr = get_driving_face_angle_();
    face_info.head_yaw = face_angle_ptr->operator[](1);
    face_info.head_pitch = face_angle_ptr->operator[](0);
    face_info.head_roll = face_angle_ptr->operator[](2);
    face_angle_score = face_angle_ptr->operator[](3);
    mouthpoint_score = face_angle_ptr->operator[](4);

    if (face_angle_score >= kFaceAngleThreshold)
        is_face_keypoints_valid = true;
}

void DmsProcess::parseFaceAttr(TXDmsFaceInfo& face_info) {
    auto face_attr_ptr = get_driving_face_attr_();

    face_info.isMask = face_attr_ptr->operator[](1) >= kMinFaceAttrThr;
    face_info.isGlass = face_attr_ptr->operator[](0) >= kMinFaceAttrThr;
    face_info.isIRBlock = face_attr_ptr->operator[](2) >= kMinFaceAttrThr;
    // //大角度防止误报红外阻断
    // if (fabs(face_info.head_yaw) >= 48) {
    //     face_info.isIRBlock = 0;
    // }
    // printf("is_mask:%d,is_irblock:%d \n", result.face_info.isMask, result.face_info.isIRBlock);
    // printf("is_mask_score:%f,is_glass_score:%f,is_irblock_score:%f \n",
    //        face_attr_ptr->operator[](1), face_attr_ptr->operator[](0),
    //        face_attr_ptr->operator[](2));
}

void DmsProcess::checkKeypointsInstability(TXDmsFaceInfo& face_info) {
    // 获取关键点稳定性分析结果
    auto keypoint_stability_result = get_keypoints_stability_analysis_();
    suppress_fatigue = false;
    suppress_distraction = false;
    // 大角度侧脸关键点质量评估容易误检
    bool is_headyaw_valid = (face_info.head_yaw < 45 && face_info.head_yaw > -45);

    // 处理关键点不稳定情况
    if (keypoint_stability_result && keypoint_stability_result->keypoint_instability.has_any_instability()) {
        // 分别处理左右眼的不稳定状态
        if (keypoint_stability_result->keypoint_instability.has_left_eye_instability() 
            && face_info.isIRBlock == 0 && is_headyaw_valid) {
            // printf("DmsProcess, Left eye keypoints are unstable, setting scores to -4\n");
            face_info.left_eye_landmark.eye_score = -4.0f;
            face_info.left_eye_landmark.iris_score = -4.0f;
            face_info.left_eye_landmark.pupil_score = -4.0f;
        }
        
        if (keypoint_stability_result->keypoint_instability.has_right_eye_instability() 
            && face_info.isIRBlock == 0 && is_headyaw_valid) {
            // printf("DmsProcess, Right eye keypoints are unstable, setting scores to -4\n");
            face_info.right_eye_landmark.eye_score = -4.0f;
            face_info.right_eye_landmark.iris_score = -4.0f;
            face_info.right_eye_landmark.pupil_score = -4.0f;
        }
        
        // 设置抑制标志，后续算法会使用
        if ((keypoint_stability_result->keypoint_instability.has_left_eye_instability() || 
            keypoint_stability_result->keypoint_instability.has_right_eye_instability()) && face_info.isIRBlock == 0){
            // 眼部关键点不稳定时，抑制疲劳检测
            suppress_fatigue = true;
            // printf("DmsProcess, Eye keypoints unstable, will suppress fatigue detection\n");
        }
        
        if (keypoint_stability_result->keypoint_instability.has_severe_instability()) {
            // 严重不稳定（≥3个区域）时，抑制分神检测
            suppress_distraction = true;
            // printf("DmsProcess, Severe keypoint instability detected, will suppress distraction detection\n");
        }
    }
}

void DmsProcess::isEyeLumAbnormal(TXDmsFaceInfo& face_info, std::shared_ptr<std::vector<float>> lum_info_ptr) {
    const float kEyeLumAbnormalThreshold = 3.0f;
    // 根据眼睛亮度信息判断是否异常
    // auto lum_info_ptr = get_exact_lum_info_();
    float leye_lum_mean = lum_info_ptr->operator[](0);
    float leye_lum_peak = lum_info_ptr->operator[](1);
    float leye_255_percent = lum_info_ptr->operator[](2);

    float reye_lum_mean = lum_info_ptr->operator[](3);
    float reye_lum_peak = lum_info_ptr->operator[](4);
    float reye_255_percent = lum_info_ptr->operator[](5);

    float mouth_lum_mean = lum_info_ptr->operator[](6);
    float mouth_lum_peak = lum_info_ptr->operator[](7);
    float mouth_255_percent = lum_info_ptr->operator[](8);

    // 对于较暗的场景下，鲁棒性不够！！！
    // // 增加戴墨镜的再判断
    // const float irblocked_Threshold = 40.0f;
    // if (leye_lum_mean < irblocked_Threshold || reye_lum_mean < irblocked_Threshold)
    //     face_info.isIRBlock = 1;
#if defined(BYD_HKH_R)
const int lum_mean_diff = 12;
#elif defined(BYD_SC3EFE)
const int lum_mean_diff = 12;
#elif defined(BYD_EM2E_R) || defined(BYD_EM2E)
const int lum_mean_diff = 15;
#elif defined(BYD_EK_L)
const int lum_mean_diff = 15;
#elif defined(BYD_SC2EDE)
const int lum_mean_diff = 15;
#else
const int lum_mean_diff = 5;
#endif
    if(face_info.left_eye_landmark.eye_score < 0) {
        if (leye_lum_mean > mouth_lum_mean /** 1.05*/) {
            face_info.left_eye_landmark.eye_score = -2;
            face_info.left_eye_landmark.iris_score = -2;
            face_info.left_eye_landmark.pupil_score = -2;
            face_info.left_eye_landmark.yaw = 0;
            face_info.left_eye_landmark.pitch = 0;
        } else {  //2.0用于区分正常的[0,1]范围，表示这是一个经过修正后的值
            face_info.left_eye_landmark.eye_score = 2;
            face_info.left_eye_landmark.iris_score = 2;
            face_info.left_eye_landmark.pupil_score = 2;
        }
    } else {
        bool is_lum_abnormal = false;
        if (face_info.isMask == 1 || mouth_lum_mean < 100) {
            if (leye_lum_mean > 175)
                is_lum_abnormal = true;
        } else {
            if (leye_lum_mean > mouth_lum_mean + lum_mean_diff)
                is_lum_abnormal = true;
        }
        if (is_lum_abnormal) {
            face_info.left_eye_landmark.eye_score = -2;
            face_info.left_eye_landmark.iris_score = -2;
            face_info.left_eye_landmark.pupil_score = -2;
            face_info.left_eye_landmark.yaw = 0;
            face_info.left_eye_landmark.pitch = 0;
        }
    }

    if(face_info.right_eye_landmark.eye_score < 0){
        if (reye_lum_mean > mouth_lum_mean /** 1.05*/) {
            face_info.right_eye_landmark.eye_score = -2;
            face_info.right_eye_landmark.iris_score = -2;
            face_info.right_eye_landmark.pupil_score = -2;
            face_info.right_eye_landmark.yaw = 0;
            face_info.right_eye_landmark.pitch = 0;
        } else {
            face_info.right_eye_landmark.eye_score = 2;
            face_info.right_eye_landmark.iris_score = 2;
            face_info.right_eye_landmark.pupil_score = 2;
        }
    } else {
        bool is_lum_abnormal = false;
        if (face_info.isMask == 1 || mouth_lum_mean < 100) {
            if (reye_lum_mean > 175)
                is_lum_abnormal = true;
        } else {
            if (reye_lum_mean > mouth_lum_mean + lum_mean_diff)
                is_lum_abnormal = true;
        }
        if (is_lum_abnormal) {
            face_info.right_eye_landmark.eye_score = -2;
            face_info.right_eye_landmark.iris_score = -2;
            face_info.right_eye_landmark.pupil_score = -2;
            face_info.right_eye_landmark.yaw = 0;
            face_info.right_eye_landmark.pitch = 0;
        }
    }

    // if (leye_255_percent > kEyeLumAbnormalThreshold) {
    //     face_info.left_eye_landmark.eye_score = -3;
    //     face_info.left_eye_landmark.iris_score = -3;
    //     face_info.left_eye_landmark.pupil_score = -3;
    //     face_info.left_eye_landmark.yaw = 0;
    //     face_info.left_eye_landmark.pitch = 0;
    // } else if(face_info.left_eye_landmark.eye_score < 0) {
    //     if (leye_lum_mean > mouth_lum_mean /** 1.05*/) {
    //         face_info.left_eye_landmark.eye_score = -2;
    //         face_info.left_eye_landmark.iris_score = -2;
    //         face_info.left_eye_landmark.pupil_score = -2;
    //         face_info.left_eye_landmark.yaw = 0;
    //         face_info.left_eye_landmark.pitch = 0;
    //     } else {  //2.0用于区分正常的[0,1]范围，表示这是一个经过修正后的值
    //         face_info.left_eye_landmark.eye_score = 2;
    //         face_info.left_eye_landmark.iris_score = 2;
    //         face_info.left_eye_landmark.pupil_score = 2;
    //     }
    // }

    // if (reye_255_percent > kEyeLumAbnormalThreshold) {
    //     face_info.right_eye_landmark.eye_score = -3;
    //     face_info.right_eye_landmark.iris_score = -3;
    //     face_info.right_eye_landmark.pupil_score = -3;
    //     face_info.right_eye_landmark.yaw = 0;
    //     face_info.right_eye_landmark.pitch = 0;
    // } else if(face_info.right_eye_landmark.eye_score < 0){
    //     if (reye_lum_mean > mouth_lum_mean /** 1.05*/) {
    //         face_info.right_eye_landmark.eye_score = -2;
    //         face_info.right_eye_landmark.iris_score = -2;
    //         face_info.right_eye_landmark.pupil_score = -2;
    //         face_info.right_eye_landmark.yaw = 0;
    //         face_info.right_eye_landmark.pitch = 0;
    //     } else {
    //         face_info.right_eye_landmark.eye_score = 2;
    //         face_info.right_eye_landmark.iris_score = 2;
    //         face_info.right_eye_landmark.pupil_score = 2;
    //     }
    // }
    // std::cout << "face_info.left_eye_landmark.eye_score:" << face_info.left_eye_landmark.eye_score
    //           << " face_info.right_eye_landmark.eye_score:"
    //           << face_info.right_eye_landmark.eye_score << std::endl;
    // std::cout << "leye_lum_mean:" << leye_lum_mean << " leye_lum_peak:" << leye_lum_peak
    //           << " leye_255_percent:" << leye_255_percent << std::endl;
    // std::cout << "reye_lum_mean:" << reye_lum_mean << " reye_lum_peak:" << reye_lum_peak
    //           << " reye_255_percent:" << reye_255_percent << std::endl;
    // std::cout << "mouth_lum_mean:" << mouth_lum_mean << " mouth_lum_peak:" << mouth_lum_peak
    //           << " mouth_255_percent:" << mouth_255_percent << std::endl;
}

void DmsProcess::parseFaceKeypoints(TXDmsFaceInfo& face_info,
                                    std::vector<cv::Point>& mouth_point,
                                    bool& is_leye_x_px_valid,
                                    bool& is_reye_x_px_valid) {
    auto face_keypoint_ptr = get_driving_face_keypoint_();
    for (int i = 0; i < TX_MAX_FLD_SIZE; i++) {
        face_info.landmarks[i].x = face_keypoint_ptr->operator[](i).x;
        face_info.landmarks[i].y = face_keypoint_ptr->operator[](i).y;
        if (i > 4 && i < 9) {
            mouth_point.push_back(face_keypoint_ptr->operator[](i));
        }
    }
    if (face_info.landmarks[1].x - face_info.landmarks[0].x >= kMinEyeXPx)
        is_leye_x_px_valid = true;
    if (face_info.landmarks[3].x - face_info.landmarks[2].x >= kMinEyeXPx)
        is_reye_x_px_valid = true;
}

template <typename T>
static void processEye(const T& eye_data,
                       TXEyeLandmark& eye_result,
                       float& up_down_proportion,
                       float& pupil_to_up_down_dis,
                       float& eye_coutour_upper_curve_score) {
    eye_result.eye_score = eye_data->operator[](0);
    eye_result.eye_angle = eye_data->operator[](1);
    eye_result.eye_center.x = eye_data->operator[](2);
    eye_result.eye_center.y = eye_data->operator[](3);
    eye_result.eye_size.width = eye_data->operator[](4);
    eye_result.eye_size.height = eye_data->operator[](5);
    eye_result.iris_score = eye_data->operator[](6);
    eye_result.iris_center.x = eye_data->operator[](7);
    eye_result.iris_center.y = eye_data->operator[](8);
    eye_result.iris_radius = eye_data->operator[](9);
    eye_result.pupil_score = eye_data->operator[](10);
    eye_result.pupil_center.x = eye_data->operator[](11);
    eye_result.pupil_center.y = eye_data->operator[](12);
    eye_result.pupil_radius = eye_data->operator[](13);
    eye_result.yaw = eye_data->operator[](14);
    eye_result.pitch = eye_data->operator[](15);
    eye_result.opening = eye_data->operator[](16);
    up_down_proportion = eye_data->operator[](17);
    pupil_to_up_down_dis = eye_data->operator[](55);
    eye_coutour_upper_curve_score = eye_data->operator[](58);
}

void DmsProcess::checkFatigueWithEye(TXEyeLandmark& eye_result, float& fatigue_score) {
    if (eye_result.eye_score > 0 && eye_result.pupil_score <= 0 && eye_result.iris_score <= 0 &&
        eye_result.opening <= kMinEyeOpening) {
        fatigue_score = 1.0;
    } else {
        fatigue_score = 0.0;
    }
}

void DmsProcess::checkFatigueWithMouth(const std::vector<cv::Point>& mouth_points,
                                       float head_pitch,
                                       float& fatigue_score) {
    // 根据嘴角坐标的差值判断点坐标的可信性
    if (std::abs(mouth_points[0].x - mouth_points[2].x) < 50) {
        fatigue_score = -2; // -1：人脸关键点相对位置异常 -2：嘴部关键点像素不满足阈值要求 其它：正常嘴巴张合度
    } else {
        float original_mouth_ratio = calculate_mouth_opening(mouth_points); 

        // 根据头部pitch值补偿嘴巴张合度
        const float k_pitch_compensation_factor = 0.005f; 
        const float pitch_base = 15.0f;
        const float compensation_factor = std::min(0.35f, k_pitch_compensation_factor * std::abs(head_pitch-pitch_base));
        float compensated_mouth_ratio = original_mouth_ratio * (1.0f + compensation_factor);
        
        // 限制补偿后的值在合理范围[0, 1.0]
        if (original_mouth_ratio < 0) {
            fatigue_score = original_mouth_ratio; // 如果原始值不可信，直接返回
        } else {
            fatigue_score = std::max(0.0f, std::min(compensated_mouth_ratio, 1.0f));
        }
    }
}

inline bool checkCameraState(uint8_t states, InternalCameraType flag) {
    return (states & flag) == flag;
}

uint8_t DmsProcess::getCurCamStatus(TXDmsFaceInfo& face_info) {
    uint8_t camera_states = 0;
    if (isFaceValid()) {
        if (face_info.isMask)
            camera_states |= No_face_mask;
        if (face_info.isIRBlock)
            camera_states |= No_face_irblock;
    } else {
        int i_occlusion = get_occlusion_();
        if (i_occlusion == 3 || i_occlusion == 4) {
            camera_states |= Camera_Occ;
        } else {
            camera_states |= No_face;
        }
    }

    return camera_states;
}
TXCameraStatus DmsProcess::getWindowCamStatus(const TXCarInfo* car_info,
                                              const long& now_ts,
                                              uint8_t camera_states) {
    TXCameraStatus camera_status = Camera_Normal;
    
    // 提取各种状态标志
    bool camera_occ = checkCameraState(camera_states, Camera_Occ);
    bool no_face = checkCameraState(camera_states, No_face);
    bool wear_mask = checkCameraState(camera_states, No_face_mask);
    bool wear_ir_block = checkCameraState(camera_states, No_face_irblock);
    
    // 处理低速帧逻辑 - 必须达到5帧后才重置，只影响人脸相关检测
    bool should_reset_face_warnings = handleLowSpeedFrames(car_info);
    
    // 相机遮挡检测不受速度限制，人脸相关检测受速度限制
    camera_status = updateCameraState(last_camera_history_warn_type,
                                     camera_occ, no_face, wear_mask, wear_ir_block,
                                     car_info, now_ts, should_reset_face_warnings);

    return camera_status;
}

TXDistractionType DmsProcess::distraProcess(const TXCarInfo* car_info,
                                            TXDmsFaceInfo& face_info,
                                            const distra_related_eyeinfo& related_eyeinfo,
                                            const bool& current_camera_occlusion,
                                            long& now_ts) {
    TXDistractionType current_distract_type = Distraction_Normal;  //当前时间窗分心报警类型

    // 获取当前帧的分心结果
    bool current_distraction_status = false;
    if (last_camera_history_warn_type == No_face || last_camera_history_warn_type == Camera_Norm) {
        if (!current_camera_occlusion) {
            current_distraction_status = tx_dms_out_warning->GetCurrentDistractStatus(
                face_info, car_info, related_eyeinfo, isFaceKeypointsValid(), now_ts);
        }
    } else {
        if (last_camera_history_warn_type == Camera_Occ) {
            tx_dms_out_warning->DistracRestAlarm();
        }
        current_distraction_status = false;
    }
    //笑时误报分神,由于yaw引起的分神过滤
    if (face_info.mouth_opening > 0.5 && current_distraction_status && (face_info.head_yaw < 40)) {
        current_distraction_status = false;
    }

    // 获取当前时间窗的分心统计结果
    current_distract_type =
        tx_dms_out_warning->distrac_run_warning(now_ts, car_info, current_distraction_status);

    // 嘴巴，鼻子，鼻梁关键点不稳定时将分心视为不可信
    if (suppress_distraction) {
        face_angle_score = 0.99f;
        if (face_info.isMask == 0)
            current_distract_type = Distraction_Normal;
    }

    // 增加分心+无人脸的组合统计
    std::string distraction_reason = tx_dms_out_warning->GetDistractReason();
    bool is_headyaw_bias = (distraction_reason.find("no_distraction2") != std::string::npos) ? true : false;

    bool isNoFaceMixDistra = tx_dms_out_warning->IsNoFaceMixDistraction(
        isFaceValid(), face_angle_score, car_info, current_camera_occlusion, current_distraction_status, now_ts);
    // std::cout << "isNoFaceMixDistra:" << isNoFaceMixDistra << " current_distract_type:" << current_distract_type << std::endl;
    if (current_distract_type == Distraction_Normal && isNoFaceMixDistra && !is_headyaw_bias) {
        current_distract_type = Distraction_Fatigue;
    }
    // std::cout << "current_distraction_status:" << current_distraction_status << " current_distract_type:" << current_distract_type << std::endl;
    // 分心触发的首帧清除3s闭眼状态的累计，防止因为分心动作造成的疲劳早报
    if ((current_distract_type == Distraction_Fatigue ||
         current_distract_type == Distraction_Fatigue_Short ||
         current_distract_type == Distraction_Fatigue_Long) &&
        is_clear_eyeframes == false) {
        tx_dms_out_warning->DmsRestEyeAlarm();
        is_clear_eyeframes = true;
    } else if (current_distract_type == Distraction_Normal) {
        is_clear_eyeframes = false;
    }
    // std::cout<<tx_dms_out_warning->GetDistractReason()<<" current_distract_type="<<current_distract_type<<" current_distraction_status="<<current_distraction_status<<std::endl;

    // 打印distraction log
    if (now_ts - last_ts >= 1000) {
        internal_analysis_distraction_info distra_info = {0};
        GetDistractionInfo(distra_info);
        std::cout << "[DMS Distraction]:" << current_distraction_status << "||"
                  << distraction_reason << " "
                  << face_info.right_eye_landmark.eye_score << " "
                  << face_info.left_eye_landmark.eye_score << "|" << distra_info.time_gap << " "
                  << distra_info.distraction_continue_time << " "
                  << distra_info.distraction_continue_percent << std::endl;
    }
    return current_distract_type;
}

TXDrowsinessType DmsProcess::fatigueProcess(const TXCarInfo* car_info,
                                            TXDmsFaceInfo& face_info,
                                            TXModelOut& current_model_out,
                                            long& now_ts) {
    TXDrowsinessType current_fatigue_type = Drowsiness_Normal;
    TXWarnInfo warn_info_out{0};

    // 标定成功的前提下，如果头部角度不在合理的范围内，则进行因为闭眼累计导致的疲劳报警
    if (tx_dms_out_warning->GetCalibrationStatus()) {
        float min_value = 0.0f;
        float max_value = 0.0f;
        //在头的roll角符合范围时进行疲劳误检的抑制
        tx_dms_out_warning->GetHeadPoseRoll(min_value, max_value);
        if (face_info.head_roll >= min_value && face_info.head_roll <= max_value) {
            tx_dms_out_warning->GetHeadPosePitch(min_value, max_value);
            if (face_info.head_pitch < min_value || face_info.head_pitch > max_value) {
                //当头部pitch角度小于标定的最小pitch角度时，模型检出闭眼不算闭眼（防止误报疲劳）
                current_model_out.l_eye_close = 0.1f;
                current_model_out.r_eye_close = 0.1f;
            }

            tx_dms_out_warning->GetHeadPoseYaw(min_value, max_value);
            if (face_info.head_yaw < min_value || face_info.head_yaw > max_value) {
                //当头部yaw角度小于标定减去偏差值的最小yaw角度时，模型检出闭眼不算闭眼（防止误报疲劳）
                current_model_out.l_eye_close = 0.1f;
                current_model_out.r_eye_close = 0.1f;
            }
        }
    }

    // 获取当前时间窗的疲劳统计结果
    current_fatigue_type = tx_dms_out_warning->drowsi_run_warning(
        now_ts, face_info, car_info, &current_model_out, warn_info_out);
    face_info.warnInfo = warn_info_out;

    return current_fatigue_type;
}

void DmsProcess::processKeypointsForDistraction(TXDmsFaceInfo& face_info,
                                               distra_related_eyeinfo& related_eyeinfo,
                                               long now_ts) {
    // 获取人脸关键点数据
    auto face_keypoints = get_driving_face_keypoint_();
    
    // 获取眼睛关键点数据
    auto eye_keypoints = get_driving_eye_keypoint_();
    
    // 日志记录关键点数据
    if (face_keypoints) {
        TX_LOG_DEBUG("DMS Distraction", "Face keypoints count: %d", (int)face_keypoints->size());
    }
    
    if (eye_keypoints) {
        TX_LOG_DEBUG("DMS Distraction", "Eye keypoints count: %d", (int)eye_keypoints->size());
    }
    
    // 将关键点数据传递给分心报警模块
    if (tx_dms_out_warning && (face_keypoints || eye_keypoints)) {
        tx_dms_out_warning->ProcessKeypoints(face_keypoints, eye_keypoints, now_ts);
        TX_LOG_DEBUG("DMS Distraction", "Keypoints sent to distraction warning module at timestamp: %ld", now_ts);
    }
}

void DmsProcess::parseFaceProcess(TXDmsFaceInfo& face_info,
                                  TXModelOut& current_model_out,
                                  distra_related_eyeinfo& related_eyeinfo,
                                  long& now_ts) {
    face_angle_score = 0, mouthpoint_score = 0;
    // 解析人脸框
    parseFaceBboxInfo(face_info);

    if (isFaceValid()) {
        std::vector<cv::Point> mouth_point;
        bool is_leye_x_px_valid = false;
        bool is_reye_x_px_valid = false;
        bool is_leye_valid = false;
        bool is_reye_valid = false;
        TXModelOut temp_model_out{0};

        // 获取人脸关键点和眼睛关键点数据传递给分心报警模块
        processKeypointsForDistraction(face_info, related_eyeinfo, now_ts);

        // 解析人脸关键点
        parseFaceKeypoints(face_info, mouth_point, is_leye_x_px_valid, is_reye_x_px_valid);
        // 解析人脸角度
        parseFaceAngle(face_info, face_angle_score, mouthpoint_score);
        // 解析人脸属性
        parseFaceAttr(face_info);

        // 眼睛横向像素足够且未被红外阻断则被认为眼睛数据有效
        is_leye_valid = is_leye_x_px_valid && face_info.isIRBlock == 0;
        is_reye_valid = is_reye_x_px_valid && face_info.isIRBlock == 0;
        // 处理眼睛数据
        auto process_eye = [&](auto get_eye_data, auto& eye_landmark, float& up_down_proportion,
                               float& pupil_to_up_down_dis, float& eye_coutour_upper_curve_score) {
            auto data = get_eye_data();
            processEye(data, eye_landmark, up_down_proportion, pupil_to_up_down_dis,
                       eye_coutour_upper_curve_score);
        };

        auto lum_info_ptr = get_exact_lum_info_();

        // 判断眼睛和嘴巴各自的疲劳状态
        float fatigue_leye = -1, fatigue_reye = -1, fatigue_mouth = -1;
        if (is_leye_valid) {
            process_eye(get_driving_left_eye_landmarks_, face_info.left_eye_landmark,
                        related_eyeinfo.left_up_down_proportion,
                        related_eyeinfo.left_pupil_to_up_down_dis,
                        related_eyeinfo.leye_coutour_upper_curve_score);
            checkFatigueWithEye(face_info.left_eye_landmark, fatigue_leye);
        }
        if (is_reye_valid) {
            process_eye(get_driving_right_eye_landmarks_, face_info.right_eye_landmark,
                        related_eyeinfo.right_up_down_proportion,
                        related_eyeinfo.right_pupil_to_up_down_dis,
                        related_eyeinfo.reye_coutour_upper_curve_score);
            checkFatigueWithEye(face_info.right_eye_landmark, fatigue_reye);
        }
        if (mouthpoint_score >= 0 && face_info.isMask == 0) {
            checkFatigueWithMouth(mouth_point, face_info.head_pitch, fatigue_mouth);
        }

        // 校验眼睛信息是否异常
        if (face_info.isGlass == 1 && /*face_info.isMask == 0 &&*/ face_info.isIRBlock == 0)
            isEyeLumAbnormal(face_info, lum_info_ptr);

        // 检查关键点稳定性
        checkKeypointsInstability(face_info);

        // 处理红外阻断但眼睛却能检测出的场景
        if(face_info.isIRBlock == 1) {
            face_info.left_eye_landmark.eye_score = -5.0f;
            face_info.left_eye_landmark.pupil_score = -5.0f;
            face_info.left_eye_landmark.iris_score = -5.0f;
            face_info.right_eye_landmark.eye_score = -5.0f;
            face_info.right_eye_landmark.pupil_score = -5.0f;
            face_info.right_eye_landmark.iris_score = -5.0f;
        }
        
        // 再判断眼睛状态用于疲劳模块 
        if (face_info.left_eye_landmark.eye_score < 0)
               fatigue_leye = face_info.left_eye_landmark.eye_score;
        if (face_info.right_eye_landmark.eye_score < 0)
               fatigue_reye = face_info.right_eye_landmark.eye_score;

        face_info.left_close_eye_score = temp_model_out.l_eye_close = fatigue_leye;
        face_info.right_close_eye_score = temp_model_out.r_eye_close = fatigue_reye;
        face_info.mouth_opening = temp_model_out.mouth_open = fatigue_mouth;

        current_model_out = temp_model_out;

        // test tmp
        leye_thr = related_eyeinfo.left_pupil_to_up_down_dis;
        reye_thr = related_eyeinfo.right_pupil_to_up_down_dis;
        // leye_thr = related_eyeinfo.left_up_down_proportion;
        // reye_thr = related_eyeinfo.right_up_down_proportion;
    }
}

#if defined(DISTRACTION_INTERVENTION)
TXDistrIntervStatus DmsProcess::getDistrIntervStatus() {
    TXDistrIntervStatus current_distra_interv_status = DiSTRINTERV_NOT_ACTIVATE;  //当前分心干预状态

    // 当前分心状态如果持续时间大于1s则进行分心干预信号的输出
    internal_analysis_distraction_info distra_info = {0};
    GetDistractionInfo(distra_info);
    // 1s的数据且完全是分心帧或者累计数据大于1.3s
    if (distra_info.time_gap >= 1000 - 100 && distra_info.distraction_continue_percent >= 1 ||
        distra_info.time_gap >= 1300 - 100) {
        is_distra_interv_keep = true;
    } else {
        if (distra_info.time_gap == 0)
            is_distra_interv_keep = false;
    }
    if (is_distra_interv_keep) {
        current_distra_interv_status = DiSTRINTERV_ACTIVATE;
    } else {
        current_distra_interv_status = DiSTRINTERV_NOT_ACTIVATE;
    }

    return current_distra_interv_status;
}
#endif

void DmsProcess::arbitrateAlarmOutput(TXDistractionType& current_distract_type,
                                      TXDrowsinessType& current_fatigue_type,
                                      const long& now_ts) {
    // 增加疲劳和分心报警输出的仲裁逻辑（任一报警之间的时间间隔得大于等于3s）
    bool is_distract_alarm_trigger = DISTRACTION_MASK & (1 << current_distract_type);
    bool is_fatigue_alarm_trigger = DROWSINESS_MASK & (1 << current_fatigue_type);
    bool is_last_distralarm_trigger = (DISTRACTION_MASK & (1 << last_distract_type));
    bool is_last_fatialarm_trigger = (DROWSINESS_MASK & (1 << last_drows_type));
    // 上一帧为报警，当前帧不为报警，开始统计间隔时间
    // std::cout << "is_distract_alarm_trigger:" << is_distract_alarm_trigger
    // << " is_fatigue_alarm_trigger:" << is_fatigue_alarm_trigger <<
    // " is_last_distralarm_trigger:" << is_last_distralarm_trigger <<
    //     " is_last_fatialarm_trigger:" << is_last_fatialarm_trigger << std::endl;

    // 默认允许输出当前报警
    bool allow_output_distract = true;
    bool allow_output_fatigue = true;

    // Step 1: 处理上一帧报警结束，记录结束时间
    if ((is_last_distralarm_trigger && !is_distract_alarm_trigger) ||
        (is_last_fatialarm_trigger && !is_fatigue_alarm_trigger)) {
        last_warn_trigger_time = last_frame_time;
        // printf("\033[33m DmsProcess line:%d info: 报警结束，记录 last_warn_trigger_time=%ld \033[0m\n",
        //        __LINE__, last_warn_trigger_time);
    }

    // Step 2: 报警等级不能倒退（疲劳报警）
    if (is_fatigue_alarm_trigger && last_drows_type > current_fatigue_type) {
        // printf("\033[33m DmsProcess line:%d info: 报警等级降低，抑制 \033[0m\n", __LINE__);
        current_fatigue_type = Drowsiness_Normal;
        last_warn_trigger_time = last_frame_time;
        is_fatigue_alarm_trigger = false;
    }

    // Step 3: 判断当前报警是否满足3秒间隔仲裁要求
    if ((last_warn_trigger_time != 0) && (now_ts - last_warn_trigger_time < 3000)) {
        if (is_distract_alarm_trigger) {
            current_distract_type = Distraction_Normal;
            is_distract_alarm_trigger = false;
            allow_output_distract = false;
            // printf("\033[33m DmsProcess line:%d info: 分心报警间隔不足3秒，抑制输出 \033[0m\n",
            //        __LINE__);
        }
        if (is_fatigue_alarm_trigger && current_fatigue_type <= last_fatigue_trigger_type) {
            current_fatigue_type = Drowsiness_Normal;
            is_fatigue_alarm_trigger = false;
            allow_output_fatigue = false;
            // printf("\033[33m DmsProcess line:%d info: 疲劳报警非升级 & 间隔不足3秒，抑制输出 \033[0m\n",
            //        __LINE__);
        }
    }

    // Step 4: 报警互斥处理：已有报警持续触发时，禁止另一个报警新触发
    if (is_last_distralarm_trigger && is_distract_alarm_trigger && allow_output_distract) {
        if (!is_last_fatialarm_trigger && is_fatigue_alarm_trigger && allow_output_fatigue) {
            current_fatigue_type = Drowsiness_Normal;
            is_fatigue_alarm_trigger = false;
            // printf("\033[33m DmsProcess line:%d info: 分心持续报警中抑制疲劳新报警 \033[0m\n",
            //        __LINE__);
        }
    }

    if (is_last_fatialarm_trigger && is_fatigue_alarm_trigger && allow_output_fatigue) {
        if (!is_last_distralarm_trigger && is_distract_alarm_trigger && allow_output_distract) {
            current_distract_type = Distraction_Normal;
            is_distract_alarm_trigger = false;
            // printf("\033[33m DmsProcess line:%d info: 疲劳持续报警中抑制分心新报警 \033[0m\n",
            //        __LINE__);
        }
    }

    // Step 5: 记录上一次疲劳报警等级（用于升级判断）
    if (is_last_fatialarm_trigger && !is_fatigue_alarm_trigger) {
        last_fatigue_trigger_type = last_drows_type;
    }

    // Step 6: 更新时间戳和历史状态
    last_drows_type = current_fatigue_type;
    last_distract_type = current_distract_type;
    last_frame_time = now_ts;

    // Step 7: 如果间隔大于等于3秒，允许所有报警升级
    if (now_ts - last_warn_trigger_time >= 3000) {
        last_fatigue_trigger_type = Drowsiness_Fatigue4;  // 重置升级基准
        // printf("\033[33m DmsProcess line:%d info: 满足时间间隔，允许输出升级报警 \033[0m\n", __LINE__);
    }

    // std::cout << "last_warn_trigger_time:" << last_warn_trigger_time << " now_ts:" << now_ts <<
    // " consume time:" << now_ts - last_warn_trigger_time << std::endl;
    // std::cout  << " current_fatigue_type:" << current_fatigue_type << " current_distract_type:" << current_distract_type << " last_fatigue_trigger_type:" << last_fatigue_trigger_type << std::endl;
    // std::cout << "----------------"<< cnt++ <<"---------------" << std::endl;
}

int DmsProcess::execute(const TXCarInfo* car_info,
                        long long frame_id,
                        TXDmsResult& result_out,
                        long ts) {
    // 帧率控制
    long now_ts = 0;
    if (ts == 0) {
        struct timeval tv;
        gettimeofday(&tv, NULL);
        now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    } else {
        now_ts = ts;
    }

    TXDmsResult result = {0};
    result.result_frame_id = frame_id;
    result.camera_status = Camera_Normal;
    result.drowsiness_status = Drowsiness_Normal;
    result.distraction_status = Distraction_Normal;
    result.calibrate_status = CALIBRATE_INVALID;
    result.system_status = SYSTEM_NORMAL;
    result.algorithm_status = ALGORITHM_NORMAL;
#ifdef DISTRACTION_INTERVENTION
    result.distrinterv_status = DiSTRINTERV_NOT_ACTIVATE;
    TXDistrIntervStatus current_distra_interv_status = DiSTRINTERV_NOT_ACTIVATE;  //当前分心干预状态
#endif
    // 加入激活限制
    if (!isAlgoActivate(result, now_ts)) {
        result_out = result;  // 需要赋值初始状态的result
        return 0;
    }

    TXDistractionType current_distract_type = Distraction_Normal;      //当前分心报警类型
    TXDrowsinessType current_fatigue_type = Drowsiness_Normal;         //当前疲劳报警类型
    TXDistracCaliStatus current_calibrate_status = CALIBRATE_INVALID;  //当前分神标定状态

    is_face_valid = false;            // 当前帧人脸是否有效
    is_face_keypoints_valid = false;  // 当前帧人脸关键点是否有效

    //存放当前帧摄像头状态信息
    uint8_t camera_states = 0;

    current_distra_related_eyeinfo.right_up_down_proportion = 1;
    current_distra_related_eyeinfo.left_up_down_proportion = 1;
    current_distra_related_eyeinfo.right_pupil_to_up_down_dis = 0.0;
    current_distra_related_eyeinfo.left_pupil_to_up_down_dis = 0.0;
    current_distra_related_eyeinfo.leye_coutour_upper_curve_score = 1.0f;
    current_distra_related_eyeinfo.reye_coutour_upper_curve_score = 1.0f;

    float right_pupil_to_left_right_dis = 0.0;
    float left_pupil_to_left_right_dis = 0.0;

    //TODO:需要完善根据使能位决定是否运行疲劳和分心模块或未来更多其它模块
    // enable_fatigue_ and more...

    //解析系统故障码
    if (isHardwareError(car_info, result)) {
        result_out = result;
        tx_dms_out_warning->DrowsiRestAlarm();   //重置疲劳时间窗
        tx_dms_out_warning->DistracRestAlarm();  //重置分神时间窗
        return -1;
    }

    // 根据车机状态判断是否需要重置分心以及其依赖的标定
    if (checkResetDistraAndCali(car_info)) {
        tx_dms_out_warning->ResetCalibration();  //重置标定状态
        tx_dms_out_warning->DistracRestAlarm();  //重置分神时间窗
    }
    // 解析人脸相关的信息
    TXModelOut current_model_out = {0};
    parseFaceProcess(result.face_info, current_model_out, current_distra_related_eyeinfo, now_ts);

    // 检测手机和香烟
    phoneAndSmokingProcess(result, now_ts);

    // 获取当前帧的摄像头状态
    camera_states = getCurCamStatus(result.face_info);
    // 获取时间窗内统计的摄像头状态
    TXCameraStatus camera_status = getWindowCamStatus(car_info, now_ts, camera_states);

    bool wear_mask = checkCameraState(camera_states, No_face_mask);
    bool wear_ir_block = checkCameraState(camera_states, No_face_irblock);
    // 报警模块处理流程
    if (camera_status == Camera_Normal || (camera_status == Face_Occlusion)) {
        // 获取当前标定状态
        current_calibrate_status = tx_dms_out_warning->auto_calibration_distraction(
            result.face_info, car_info,
            current_distra_related_eyeinfo.leye_coutour_upper_curve_score,
            current_distra_related_eyeinfo.reye_coutour_upper_curve_score, now_ts);  //自动标定

        // 获取当前时间窗的分心统计结果
        current_distract_type =
            distraProcess(car_info, result.face_info, current_distra_related_eyeinfo,
                          checkCameraState(camera_states, Camera_Occ), now_ts);
#ifdef DISTRACTION_INTERVENTION
        // 获取当前的分心干预输出
        current_distra_interv_status = getDistrIntervStatus();
#endif

        // 获取当前时间窗的疲劳统计结果
        current_fatigue_type =
            fatigueProcess(car_info, result.face_info, current_model_out, now_ts);

        // 报警需要满足最小速度阈值
        if (TXDmsGetStandardType() == TX_STANDARD_EU) {
            kFatigueSpeedThr = 10;
            kDistraSpeedThr = 20;
        } else if (TXDmsGetStandardType() == TX_STANDARD_NON_EU) {
            kFatigueSpeedThr = 60;
            kDistraSpeedThr = 60;
        }

        if (camera_status == Camera_Normal) {
            if (car_info->speed < kDistraSpeedThr) {
                current_distract_type = Distraction_Normal;
#ifdef DISTRACTION_INTERVENTION
            current_distra_interv_status = DiSTRINTERV_NOT_ACTIVATE;
#endif
        }

            if (car_info->speed < kFatigueSpeedThr)
                current_fatigue_type = Drowsiness_Normal;

        } else if (camera_status == Face_Occlusion) {
            if (car_info->speed < kFaceOccTriggerSpeed) 
                camera_status = Camera_Normal;
        }
        // 仲裁报警输出
        arbitrateAlarmOutput(current_distract_type, current_fatigue_type, now_ts);
    } else {
        tx_dms_out_warning->DrowsiRestAlarm();  //重置报警
        tx_dms_out_warning->DistracRestAlarm();

        if (tx_dms_out_warning->GetCalibrationStatus() == false) {
            //当在未标定成功的状态下，异常情况下需要重新标定
            tx_dms_out_warning->ResetCalibration();
            current_calibrate_status = CALIBRATE_INVALID;
        } else {
            current_calibrate_status = CALIBRATE_DONE;
        }
        current_fatigue_type = Drowsiness_Invalid;
        current_distract_type = Distraction_Invalid;
#ifdef DISTRACTION_INTERVENTION
        current_distra_interv_status = DiSTRINTERV_INVALID;
#endif
        // 去除一次上电周期只进行一次人脸遮挡报警的机制
        // if (is_first_face_occ && camera_status == Face_Occlusion) {
        //     camera_status = Camera_Normal;
        // }

        // if (!is_first_face_occ && camera_status == Face_Occlusion &&
        //     last_camera_history_warn_type == Camera_Norm) {
        //     is_first_face_occ = true;
        // }
    }

    // 最终结果赋值
    result.camera_status = camera_status;
    result.calibrate_status = current_calibrate_status;
    result.distraction_status = current_distract_type;
    result.drowsiness_status = current_fatigue_type;
#ifdef DISTRACTION_INTERVENTION
    result.distrinterv_status = current_distra_interv_status;
#endif
    result_out = result;

    // 重要信息打印输出
    {
        TX_LOG_DEBUG(
            "DmsProcess",
            "frame_id:%ld face info %f ,%d ,%d ,%d ,%d camera_status:%d drowsiness_status:%d "
            "distraction_status:%d,calibrate_status:%d ",
            result.result_frame_id, result.face_info.score, result.face_info.xmin,
            result.face_info.ymin, result.face_info.xmax, result.face_info.ymax,
            result.camera_status, result.drowsiness_status, result.distraction_status,
            result.calibrate_status);
        if (now_ts - last_ts >= 1000) {
#if defined(DISTRACTION_INTERVENTION)
            printf("[%s]DmsProcess [camera_status:%d drowsiness_status:%d "
                   "distraction_status:%d,calibrate_status:%d,distrinterv_status:%d ] \n",
                   getCurrentTimes().c_str(), result.camera_status, result.drowsiness_status,
                   result.distraction_status, result.calibrate_status, result.distrinterv_status);
#else
            printf("[%s]DmsProcess [camera_status:%d drowsiness_status:%d "
                   "distraction_status:%d,calibrate_status:%d ] \n",
                   getCurrentTimes().c_str(), result.camera_status, result.drowsiness_status,
                   result.distraction_status, result.calibrate_status);
#endif
            last_ts = now_ts;
        }
    }
    return 0;
}

int DmsProcess::GetRightLeftEyeThr(float& left_eye_thr, float& right_eye_thr) {
    left_eye_thr = leye_thr;
    right_eye_thr = reye_thr;
    return 0;
}

std::string DmsProcess::GetDistractReason(TXCameraStatus camera_status) {
    if (camera_status == Camera_Normal || camera_status == Face_Occlusion)
        return tx_dms_out_warning->GetDistractReason();
    else
        return "invalid";
}

std::string DmsProcess::GetDistractParamers() {
    return tx_dms_out_warning->GetDistractParamers();
}

int DmsProcess::SetPhoneAndSmokingDetectEnableStatus(unsigned char flag) {
    phone_smoking_detect_enable_status_ = flag;
    phone_det_win.clear();
    smoking_det_win.clear();
    return 0;
}

// 状态管理辅助函数实现
void DmsProcess::resetWarningStates(InternalCameraType target_state) {
    const long dummy_ts = 0; // 清除时不需要时间戳
    
    switch (target_state) {
        case Camera_Norm:
            occlusion_warning.clear(dummy_ts);
            occlusion_warning.init(5000, 0.8, 0);  // 相机遮挡进入条件
            noface_warning.clear(dummy_ts);
            noface_warning.init(kMinFaceBlockedEnterTimeMs, 0.9, 10);  // 人脸遮挡进入条件
            break;
            
        case Camera_Occ:
            occlusion_warning.clear(dummy_ts);
            occlusion_warning.init(1000, 0.99, 0);  // 退出条件
            noface_warning.clear(dummy_ts);
            noface_warning.init(kMinFaceBlockedEnterTimeMs, 0.9, 10);  // 人脸遮挡进入条件
            break;
            
        case No_face:
            noface_warning.clear(dummy_ts);
            noface_warning.init(kMinFaceBlockedExitTimeMs, 0.99, 0);  // 退出条件
            occlusion_warning.clear(dummy_ts);
            occlusion_warning.init(5000, 0.8, 0);  // 相机遮挡进入条件
            break;
            
        default:
            break;
    }
}

void DmsProcess::resetFaceWarningStates() {
    const long dummy_ts = 0; // 清除时不需要时间戳
    
    // 只重置人脸相关的警告状态，不影响相机遮挡状态
    noface_warning.clear(dummy_ts);
    noface_warning.init(kMinFaceBlockedEnterTimeMs, 0.9, 10);  // 人脸遮挡进入条件
    
    // 如果当前状态是人脸相关问题，重置为正常状态
    if (last_camera_history_warn_type == No_face) {
        last_camera_history_warn_type = Camera_Norm;
    }
    // 注意：不重置 Camera_Occ 状态，因为相机遮挡不受速度限制
}

bool DmsProcess::handleLowSpeedFrames(const TXCarInfo* car_info) {
    // 只检查人脸相关的速度阈值，不包括相机遮挡
    const int face_related_min_speed = std::min(std::min(kFatigueSpeedThr, kDistraSpeedThr), kFaceOccTriggerSpeed);
    
    if (car_info->speed < face_related_min_speed) {
        low_speed_frame_count_++;
        
        // 达到5帧后才触发重置，只重置人脸相关警告
        if (low_speed_frame_count_ >= kLowSpeedResetFrameCount && !low_speed_reset_triggered_) {
            // 只重置人脸相关的警告状态，不影响相机遮挡
            resetFaceWarningStates();
            low_speed_reset_triggered_ = true;
            std::cout << "[DMS Camera State] Low speed reset for face warnings triggered after " << low_speed_frame_count_ << " frames" << std::endl;
        }
        
        return true;  // 需要重置人脸相关警告
    } else {
        // 高速状态，重置计数器
        low_speed_frame_count_ = 0;
        low_speed_reset_triggered_ = false;
        return false;  // 不需要重置
    }
}

TXCameraStatus DmsProcess::updateCameraState(InternalCameraType current_state,
                                           bool camera_occ, bool no_face,
                                           bool wear_mask, bool wear_ir_block,
                                           const TXCarInfo* car_info, const long& now_ts,
                                           bool should_reset_face_warnings) {
    TXCameraStatus result_status = Camera_Normal;
    bool face_issue = no_face || wear_mask || wear_ir_block;
    
    // 如果需要重置人脸相关警告，且当前是人脸问题状态，则强制返回正常状态
    if (should_reset_face_warnings && (current_state == No_face)) {
        result_status = Camera_Normal;
        return result_status;
    }
    
    // 明确的状态优先级：摄像头遮挡 > 人脸问题
    // 状态机模式处理，避免状态转换延迟
    switch (current_state) {
        case Camera_Norm: {
            bool occlusion_triggered = occlusion_warning.update(camera_occ, now_ts, car_info->speed);
            // 人脸相关检测：如果需要重置，则不触发新的人脸问题
            bool face_issue_triggered = should_reset_face_warnings ? false : 
                                      noface_warning.update(face_issue, now_ts, car_info->speed);
            
            if (occlusion_triggered) {
                last_camera_history_warn_type = Camera_Occ;
                resetWarningStates(Camera_Occ);
                result_status = Camera_Occlusion;  // 立即反映状态变化
                std::cout << "[DMS Camera State] Camera_Norm -> Camera_Occ: Occlusion triggered" << std::endl;
            } else if (face_issue_triggered) {
                last_camera_history_warn_type = No_face;
                resetWarningStates(No_face);
                result_status = Face_Occlusion;  // 立即反映状态变化
                std::cout << "[DMS Camera State] Camera_Norm -> No_face: Face issue triggered" << std::endl;
            } else {
                result_status = Camera_Normal;
            }
            break;
        }
        
        case Camera_Occ: {
            bool exit_occlusion = occlusion_warning.update(!camera_occ, now_ts, car_info->speed);
            
            if (exit_occlusion) {
                // 退出遮挡后，基于可靠的检测数据决定下一状态
                // if (face_issue) {
                //     last_camera_history_warn_type = No_face;
                //     resetWarningStates(No_face);
                //     result_status = Face_Occlusion;
                //     std::cout << "Camera_Occ -> No_face: Face issue triggered" << std::endl;
                // } else {
                    last_camera_history_warn_type = Camera_Norm;
                    resetWarningStates(Camera_Norm);
                    result_status = Camera_Normal;
                    std::cout << "[DMS Camera State] Camera_Occ -> Camera_Normal: Exit occlusion" << std::endl;
                // }
            } else {
                result_status = Camera_Occlusion;
            }
            break;
        }
        
        case No_face: {
            // 人脸相关检测：如果需要重置，则强制退出人脸问题状态
            bool exit_face_issue = should_reset_face_warnings ? true : 
                                  noface_warning.update(!face_issue, now_ts, car_info->speed);
            bool occlusion_triggered = occlusion_warning.update(camera_occ, now_ts, car_info->speed);
            
            if (occlusion_triggered) {
                // 摄像头遮挡优先级更高
                last_camera_history_warn_type = Camera_Occ;
                resetWarningStates(Camera_Occ);
                result_status = Camera_Occlusion;
                std::cout << "[DMS Camera State] No_face -> Camera_Occ: Occlusion triggered (priority)" << std::endl;
            } else if (exit_face_issue) {
                last_camera_history_warn_type = Camera_Norm;
                resetWarningStates(Camera_Norm);
                result_status = Camera_Normal;
                std::cout << "[DMS Camera State] No_face -> Camera_Norm: Face issue resolved" << std::endl;
            } else {
                result_status = Face_Occlusion;
            }
            break;
        }
        
        default:
            result_status = Camera_Normal;
            break;
    }
    
    return result_status;
}
}  // namespace tongxing