#ifndef __CALMCAR_N50_PROCESS_H__
#define __CALMCAR_N50_PROCESS_H__
#include <memory>
#include <string>
#include <map>
#include <deque>
#include "cc_math_tool.h"
#include "cc_module_grop.h"
#include "dms_process.h"
#include "json.h"
#include "tx_dms_sdk.h"

namespace tongxing {

// 面部区域枚举
enum class FacialRegion {
    LEFT_EYE,
    RIGHT_EYE,
    NOSE,
    MOUTH,
    NOSE_BRIDGE,
    OTHER_POINTS
};

// 眼部区域枚举
enum class EyeRegion {
    EYE_CONTOUR,    // 眼睛轮廓 (0-7)
    IRIS_CONTOUR,   // 虹膜轮廓 (8-15)
    PUPIL          // 瞳孔 (16)
};

// 关键点类型枚举
enum class KeypointType {
    FACE = 12,      // 面部关键点 (getCachedOutput(12))
    LEFT_EYE = 13,  // 左眼关键点 (getCachedOutput(13))
    RIGHT_EYE = 14  // 右眼关键点 (getCachedOutput(14))
};

// 关键点不稳定性评分结构体
struct KeypointInstabilityInfo {
    int index;          // 关键点索引
    float score;        // 不稳定性评分

    KeypointInstabilityInfo(int idx, float sc) : index(idx), score(sc) {}
};

// 区域不稳定性信息结构体
struct RegionInstabilityInfo {
    FacialRegion region;        // 面部区域
    std::string region_name;    // 区域名称
    int total_points;           // 区域总关键点数
    int unstable_points;        // 不稳定关键点数
    float unstable_ratio;       // 不稳定比例
    bool is_unstable;           // 区域是否不稳定

    RegionInstabilityInfo(FacialRegion r, const std::string& name, int total, int unstable, float ratio, bool unstable_flag)
        : region(r), region_name(name), total_points(total), unstable_points(unstable), unstable_ratio(ratio), is_unstable(unstable_flag) {}
};

// 眼部区域不稳定性信息结构体
struct EyeRegionInstabilityInfo {
    EyeRegion region;           // 眼部区域
    std::string region_name;    // 区域名称
    int total_points;           // 区域总关键点数
    int unstable_points;        // 不稳定关键点数
    float unstable_ratio;       // 不稳定比例
    bool is_unstable;           // 区域是否不稳定

    EyeRegionInstabilityInfo(EyeRegion r, const std::string& name, int total, int unstable, float ratio, bool unstable_flag)
        : region(r), region_name(name), total_points(total), unstable_points(unstable), unstable_ratio(ratio), is_unstable(unstable_flag) {}
};

// 关键点稳定性分析综合结果
struct KeypointsStabilityAnalysisResult {
    struct KeypointInstabilityStatus {
        bool left_eye_unstable;
        bool right_eye_unstable; 
        bool nose_unstable;
        bool mouth_unstable;
        bool nose_bridge_unstable;

        bool left_eye_keypoints_unstable;
        bool right_eye_keypoints_unstable;
        bool has_any_instability() const {
            return left_eye_unstable || right_eye_unstable || nose_unstable || 
                   mouth_unstable || nose_bridge_unstable || left_eye_keypoints_unstable || right_eye_keypoints_unstable;
        }
        
        bool has_left_eye_instability() const {
            return left_eye_unstable || left_eye_keypoints_unstable;
        }
        
        bool has_right_eye_instability() const {
            return right_eye_unstable || right_eye_keypoints_unstable;
        }
        
        bool has_severe_instability() const {
            int count = 0;
            // if (left_eye_unstable) count++;
            // if (right_eye_unstable) count++;
            if (nose_unstable) count++;
            if (mouth_unstable) count++;
            if (nose_bridge_unstable) count++;
            return count >= 3;
        }
    } keypoint_instability;
    
    // KeypointsStabilityAnalysisResult() : left_eye_instability_score(0.0f), right_eye_instability_score(0.0f),
    //                                      left_eye_keypoints_unstable(false), right_eye_keypoints_unstable(false) {}
};

class CcDmsProcess {
  public:
    int Init(const char* config_file = NULL, const char* cache_path = NULL);
    int SetInput(const TXImageInfo* image, TXDmsResult* result);
    int SetInput(const TXImageInfo* image, TXDmsResult* result, long ts);  //内部测试使用
    int SetInputByEaTensor(void* image, int device, long long frame_id, int speed);
    int GetVersion(std::string& version);
    int SetDmsState(bool state);
    int updateCarInfo(const TXCarInfo* carInfo);
    bool GetDmsState();
    int RestAlarm();
    int RestDistractAlarm();
    int AlarmSetOk();
    int SetDriverRoi(TXPoint2i* left_top_point, TXPoint2i* right_bottom_point);
    int SetPhoneAndSmokingDetectEnableStatus(unsigned char flag);

    std::string GetDistractReason(TXCameraStatus camera_status);  //供外层测试调用
    std::string GetDistractParamers();                            //供外层测试调用
    void GetTiredInfo(tx_tired& tired_info);
    int GetRightLeftEyeThr(float& left_eye_thr, float& right_eye_thr);
    void GetDistractionInfo(internal_analysis_distraction_info& info);
    const std::string& GetBuildType() const;

  public:
    int once_process(std::shared_ptr<NumArray> image, TXDmsResult* result);
    int once_process(std::shared_ptr<NumArray> image, TXDmsResult* result, long ts);  //内部测试使用
    std::shared_ptr<CcObjBBox> get_driving_face_bbox();
    std::shared_ptr<std::vector<cv::Point2f>> get_driving_face_keypoint();
    std::shared_ptr<std::vector<float>> get_driving_face_angle();
    std::shared_ptr<std::vector<float>> get_driving_right_eye_close_score();
    std::shared_ptr<std::vector<float>> get_driving_left_eye_close_score();
    std::shared_ptr<std::vector<float>> get_driving_face_attr();
    int get_occlusion_status();
    std::shared_ptr<std::vector<float>> get_exact_lum_info();
    std::shared_ptr<std::vector<cv::Point2f>> get_driving_eye_keypoint();

    std::shared_ptr<std::vector<KeypointInstabilityInfo>> get_face_keypoints_instability_scores(
        bool sort_by_score = false,
        const std::vector<int>& exclude_indices = std::vector<int>());

    // 区域级别的不稳定性评估方法
    std::shared_ptr<std::vector<RegionInstabilityInfo>> get_facial_regions_instability_status(
        const std::shared_ptr<std::vector<KeypointInstabilityInfo>>& instability_scores,
        float instability_threshold = 0.4f,
        const std::vector<int>& exclude_indices = std::vector<int>());

    // 获取关键点稳定性分析结果
    std::shared_ptr<KeypointsStabilityAnalysisResult> get_keypoints_stability_analysis();
    
    typedef struct EyeInfo_ {
        float true_eye_score;
        TXPoint2i eye_coutours[8];  // 眼睛轮廓点
        float true_iris_score;
        TXPoint2i iris_coutours[8];  // 虹膜轮廓点
        float true_pupil_score;
        TXPoint2i pupil;  // 瞳孔点
        bool is_curve;
        float curve_score;
    } EyeInfo;

    typedef struct EyeInfos_ {
        EyeInfo left_eye;
        EyeInfo right_eye;
    } EyeInfos;
    void get_eye_info_(EyeInfos& eyeinfos);
    std::shared_ptr<std::vector<float>> get_driving_right_eye_landmarks();
    std::shared_ptr<std::vector<float>> get_driving_left_eye_landmarks();

    std::shared_ptr<std::vector<CcObjBBox>> get_phone_cig_bbox();

  private:
    std::shared_ptr<std::vector<float>> get_driving_eye_landmarks(
        const std::shared_ptr<NumArray>& feat,
        const std::shared_ptr<NumArray>& offset_scale,
        const std::shared_ptr<std::vector<float>>& angle,
        const std::shared_ptr<std::vector<cv::Point2f>> keypoints,
        const bool is_glasses,
        const bool is_righteye,
        EyeInfo& eye_info);

  private:
    TXPoint2i driver_roi_left_top_point;
    TXPoint2i driver_roi_right_bottom_point;
    DmsProcess dms_process_;
    std::shared_ptr<tongxing::CcModule> grop;
    bool flag_enable_dms_ = false;
    std::string cache_path_;
    Json::Value config_param_;
    std::string cache_param_filename_;
    int status_process_ = 0;  // 0:DMS_PROCESS 1:FR_Regist_process 2,FR_Comparison
    bool enable_fatigue_ = true;
    bool enable_distraction_ = true;
    long long frame_id = 0;
    TXCarInfo car_info_;
    std::string build_type_;

    EyeInfo l_eyeinfo;
    EyeInfo r_eyeinfo;
    std::deque<float> leye_lcorner_pos_queue;
    std::deque<float> leye_rcorner_pos_queue;
    std::deque<float> lheadangle_queue;

    std::deque<float> reye_lcorner_pos_queue;
    std::deque<float> reye_rcorner_pos_queue;
    std::deque<float> rheadangle_queue;
    static constexpr float kMinEyeScore = 0.5f;
    static constexpr float kMinIrisScore = 0.7f;
    static constexpr float kMinPupilScore = 0.7f;
    static constexpr float KMinCurveScore = 0.0015f;
    static constexpr long KExpectExcuteTimeMs = 100;
    // 添加历史帧存储（保留最近5帧）
    std::deque<std::vector<cv::Point2f>> m_eye_contour_history;
    const int HISTORY_SIZE = 5; // 可配置参数

    float l_pupil_pos_x_verify = -1.0f;
    float l_pupil_pos_y_verify = -1.0f;
    float r_pupil_pos_x_verify = -1.0f;
    float r_pupil_pos_y_verify = -1.0f;
    
    // 输出缓存相关成员变量
    std::map<int, std::shared_ptr<NumArray>> output_cache_;
    bool cache_valid_ = false;
    long long cache_frame_id_ = -1;
    // 缓存管理方法
    std::shared_ptr<NumArray> getCachedOutput(int index);
    void preloadCache();
    
    bool validateAndCorrectPupil(float pupil_x, float pupil_y,
                               const std::vector<cv::Point2f>& iris_contour,
                               cv::Point2f& pupil, float& pupil_flag);
    std::string case_save_path;  //用于保存结果路径
    bool first_boot_up = true;   //首次启动标识
    void SaveResultToJson(TXDmsResult& dms_result, const TXImageInfo* image);
    void SaveResultToWeb(TXDmsResult& dms_result, const TXImageInfo* image);

    bool eyeCurveProcess(const std::vector<cv::Point2f>& eye_contour, float &curve_score);
    void setVerifyPupilPos(std::vector<float>& verify_pupil_pos);

private:
    // 静态映射定义
    static const std::map<FacialRegion, std::vector<int>> REGION_KEYPOINTS;
    static const std::map<FacialRegion, std::string> REGION_NAMES;
    static const std::map<EyeRegion, std::vector<int>> EYE_REGION_KEYPOINTS;
    static const std::map<EyeRegion, std::string> EYE_REGION_NAMES;

    // 统一的关键点不稳定性评分获取方法
    std::shared_ptr<std::vector<KeypointInstabilityInfo>> get_keypoints_instability_scores(
        KeypointType keypoint_type,
        bool sort_by_score = false,
        const std::vector<int>& exclude_indices = std::vector<int>());

    // 左右眼关键点质量评估方法
    std::shared_ptr<std::vector<KeypointInstabilityInfo>> get_left_eye_keypoints_instability_scores(
        bool sort_by_score = false,
        const std::vector<int>& exclude_indices = std::vector<int>());
    
    std::shared_ptr<std::vector<KeypointInstabilityInfo>> get_right_eye_keypoints_instability_scores(
        bool sort_by_score = false,
        const std::vector<int>& exclude_indices = std::vector<int>());

    // 眼部区域级别的不稳定性评估方法
    std::shared_ptr<std::vector<EyeRegionInstabilityInfo>> get_eye_regions_instability_status(
        const std::shared_ptr<std::vector<KeypointInstabilityInfo>>& instability_scores,
        float instability_threshold = 0.4f,
        const std::vector<int>& exclude_indices = std::vector<int>());
};
}  // namespace tongxing

#endif