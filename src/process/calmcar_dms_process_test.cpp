#include "calmcar_dms_process_test.h"
#include <string.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <cstdio>
#include <fstream>
#include <string>
#include "CalmCarLog.h"
#include "android_log.h"
#include "cc_numarray_tool.h"
#include "cc_resource_register.h"
#include "cc_version.h"
#include "json.h"
#include "opencv2/opencv.hpp"

#if defined(INTEGRATION_TEST_MODE)
#include "cc_media_server.h"
#endif

#ifdef AMBA_SDK
#include "eazyai.h"
#endif
namespace tongxing {
static void argmin(float* data, size_t data_size, float& minvalue, size_t& min_index) {
    minvalue = data[0];
    min_index = 0;
    for (size_t i = 1; i < data_size; i++) {
        if (minvalue > data[i]) {
            minvalue = data[i];
            min_index = i;
        }
    }
}
static void argmax(float* data, size_t data_size, float& maxvalue, size_t& max_index) {
    maxvalue = data[0];
    max_index = 0;
    for (size_t i = 1; i < data_size; i++) {
        if (maxvalue < data[i]) {
            maxvalue = data[i];
            max_index = i;
        }
    }
}
static std::string getCurrentTimes() {
    struct timeval tv;
    gettimeofday(&tv, NULL);

    static const int MAX_BUFFER_SIZE = 128;
    char timestamp_str[MAX_BUFFER_SIZE];
    time_t sec = static_cast<time_t>(tv.tv_sec);
    int ms = static_cast<int>(tv.tv_usec) / 1000;

    struct tm tm_time;
    localtime_r(&sec, &tm_time);
    static const char* formater = "%4d-%02d-%02d %02d:%02d:%02d.%03d";
    int wsize = snprintf(timestamp_str, MAX_BUFFER_SIZE, formater, tm_time.tm_year + 1900,
                         tm_time.tm_mon + 1, tm_time.tm_mday, tm_time.tm_hour, tm_time.tm_min,
                         tm_time.tm_sec, ms);

    timestamp_str[std::min(wsize, MAX_BUFFER_SIZE - 1)] = '\0';
    return std::string(timestamp_str);
}
#if ANDROID
void androidLog(char* data, int data_size) {
    DLOGE("%s", data);
}
#endif
int CcDmsProcess::Init(const char* config_file, const char* cache_path) {
    // CalmCarLog::getInstance()->set_calmcar_log_type(FILE_OUTPUT, "/mnt/sdcard/", "calmcar_dms_log_", 20000, NULL);
    // CalmCarLog::getInstance()->set_calmcar_log_type(FILE_OUTPUT, "./", "calmcar_n50_dms_log_", 2000, NULL);

#if ANDROID
    CalmCarLog::getInstance()->set_calmcar_log_type(REDIRECT_OUTPUT, nullptr, nullptr, 0,
                                                    androidLog);
    CalmCarLog::getInstance()->set_calmcar_log_level(INFO);
#else
    CalmCarLog::getInstance()->set_calmcar_log_level(INFO);
#endif
    frame_id = 0;
    if (cache_path) {
        cache_path_ = std::string(cache_path);
    }

    // Json::Reader model_config_json_reader;
    // Json::Value model_config_root;
    // auto model_config_json_data = CcResourcDataRegister::instance().get_function("dms_config.json");
    // // assert(config_json_data!=NULL);
    // // std::cout<<(const char*)(config_json_data.second)<<std::endl;
    // std::string model_config_doc =
    //     std::string((char*)model_config_json_data.second, model_config_json_data.first);
    // if (!model_config_json_reader.parse(model_config_doc, model_config_root)) {
    //     TX_LOG_FATAL("TX DMS", "Parse json config file failed!");
    //     return -1;
    // }
    // grop = tongxing::get_cc_module(model_config_root);
    dms_process_.Init(std::bind(&CcDmsProcess::get_driving_face_bbox, this),
                      std::bind(&CcDmsProcess::get_driving_face_keypoint, this),
                      std::bind(&CcDmsProcess::get_driving_face_angle, this),
                      std::bind(&CcDmsProcess::get_driving_right_eye_close_score, this),
                      std::bind(&CcDmsProcess::get_driving_left_eye_close_score, this),
                      std::bind(&CcDmsProcess::get_occlusion_status, this),
                      std::bind(&CcDmsProcess::get_driving_face_attr, this),
                      std::bind(&CcDmsProcess::get_driving_right_eye_landmarks, this),
                      std::bind(&CcDmsProcess::get_driving_left_eye_landmarks, this),
                      std::bind(&CcDmsProcess::get_phone_cig_bbox, this), config_file);
    return 0;
}

int CcDmsProcess::GetVersion(std::string& version) {
    version = dms_get_version();
    TX_LOG_INFO("process", "ver : %s", version.c_str());
    return 0;
}

// bool flag_e=false;
// char v_data[1600*1300];
long id = 0;
int CcDmsProcess::SetInput(const TXImageInfo* image, TXDmsResult* result) {
    auto start_time = std::chrono::system_clock::now();
    std::shared_ptr<NumArray> image_numArray(new NumArray());
    image_numArray->type = tongxing::NumArray::UINT8;
    image_numArray->word_size = 1;
    image_numArray->data = (unsigned char*)image->data;
    image_numArray->shape.push_back(1);
    image_numArray->shape.push_back(1);
    image_numArray->shape.push_back(image->height);
    image_numArray->shape.push_back(image->width);

    int iRet = once_process(image_numArray, result);

    // 写入各种类型的数据到stringstream
    // auto starttime = std::chrono::system_clock::now();
    std::stringstream ss;
    ss << getCurrentTimes();
    ss << "[ABCD]{";
    ss << "\"result_frame_id\":" << result->result_frame_id << ",";
    ss << "\"camera_status\":" << result->camera_status << ",";
    ss << "\"drowsiness_type\":" << result->drowsiness_status << ",";
    ss << "\"distraction_type\":" << result->distraction_status << ",";
    ss << "\"calibrate_status\":" << result->calibrate_status << ",";
    ss << "\"system_status\":" << result->system_status << ",";
    ss << "\"car_speed\":" << car_info_.speed << ",";
    ss << "\"car_gear\":" << car_info_.gear << ",";
    ss << "\"car_steer_whl_snsr_rad\":" << car_info_.steer_whl_snsr_rad << ",";
    ss << "\"turn_light\":" << car_info_.turn_light << ",";
    ss << "\"distraction_reason\": \"" << dms_process_.GetDistractReason(result->camera_status)
       << "\",";
    ss << "\"sdk_version\":\"" << TXDmsGetVersion() << "\",";
    ss << "\"face_info\":{"
       << "\"score\":" << result->face_info.score << ",";
    ss << "\"yaw\":" << result->face_info.head_yaw << ",";
    ss << "\"pitch\":" << result->face_info.head_pitch << ",";
    ss << "\"roll\":" << result->face_info.head_roll << ",";
    ss << "\"isMask\":" << result->face_info.isMask << ",";
    ss << "\"isIRBlock\":" << result->face_info.isIRBlock << ",";
    ss << "\"isGlass\":" << result->face_info.isGlass << ",";
    ss << "\"right_close_eye_score\":" << result->face_info.right_close_eye_score << ",";
    ss << "\"left_close_eye_score\":" << result->face_info.left_close_eye_score << ",";
    ss << "\"mouth_opening\":" << result->face_info.mouth_opening << ",";
    ss << "\"right_eye_score\":" << result->face_info.right_eye_landmark.eye_score << ",";
    ss << "\"right_eye_iris_score\":" << result->face_info.right_eye_landmark.iris_score << ",";
    ss << "\"right_eye_pupil_score\":" << result->face_info.right_eye_landmark.pupil_score << ",";
    ss << "\"right_eye_yaw\":" << result->face_info.right_eye_landmark.yaw << ",";
    ss << "\"right_eye_pitch\":" << result->face_info.right_eye_landmark.pitch << ",";
    ss << "\"left_eye_score\":" << result->face_info.left_eye_landmark.eye_score << ",";
    ss << "\"left_eye_iris_score\":" << result->face_info.left_eye_landmark.iris_score << ",";
    ss << "\"left_eye_pupil_score\":" << result->face_info.left_eye_landmark.pupil_score << ",";
    ss << "\"left_eye_yaw\":" << result->face_info.left_eye_landmark.yaw << ",";
    ss << "\"left_eye_pitch\":" << result->face_info.left_eye_landmark.pitch << "}";
    ss << "}";
    // printf("[%s][SDK-LOG][%s] \n", getCurrentTime().c_str(), ss.str().c_str());
    // auto endtime = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> diff = endtime - starttime;
    // std::cout << "日志所耗时间为：" << diff.count() << "ms" << std::endl;
    auto end_time = std::chrono::system_clock::now();
    std::chrono::duration<double, std::milli> cost_time = end_time - start_time;
    std::cout << "当前帧算法所耗时间为：" << cost_time.count() << "ms" << std::endl;

    std::string filename = "/tmp/sdk-log.txt";
    std::ofstream outfile;
    outfile.open(filename, std::ios::out | std::ios::app);
    // 检查文件是否成功打开
    if (outfile.is_open()) {
        outfile << ss.str() << std::endl;
        outfile << cost_time.count() << " ms" << std::endl;
    }
    // 关闭文件
    outfile.close();
#if 0
    //save image
    std::string picture_path = "/fatfs/app/";
    std::string pic_name;
    if (result->camera_status == Camera_Occlusion) {
        cv::Mat img = cv::Mat(image->height, image->width, CV_8UC1, image->data);
        pic_name = "camera_occ.png";
        if (id % 100 == 0)
            cv::imwrite(picture_path + pic_name, img);
    } else if (result->camera_status == Face_Occlusion) {
        cv::Mat img = cv::Mat(image->height, image->width, CV_8UC1, image->data);
        pic_name = "face_occ.png";
        if (id % 100 == 0)
            cv::imwrite(picture_path + pic_name, img);
    }
    id++;
#endif
#if defined(INTEGRATION_TEST_MODE)
    // SaveResultToJson(*result, image);  //保存结果输出
    SaveResultToWeb(*result, image);
#endif

    return iRet;
}

int CcDmsProcess::SetInput(const TXImageInfo* image, TXDmsResult* result, long ts) {
    std::shared_ptr<NumArray> image_numArray(new NumArray());
    image_numArray->type = tongxing::NumArray::UINT8;
    image_numArray->word_size = 1;
    image_numArray->data = (unsigned char*)image->data;
    image_numArray->shape.push_back(1);
    image_numArray->shape.push_back(1);
    image_numArray->shape.push_back(image->height);
    image_numArray->shape.push_back(image->width);
    return once_process(image_numArray, result, ts);
}

int CcDmsProcess::SetInput(const char* json_file, TXDmsResult* result) {
    Json::Reader json_reader;
    Json::Value root;

    std::ifstream infile(json_file, std::ios::in);
    if (!infile.is_open()) {
        std::cout << "Open config file failed!" << std::endl;
        return -1;
    }

    if (!json_reader.parse(infile, root)) {
        std::cout << "Parse json config file failed!" << std::endl;
        return -1;
    }

    root_temp_ = root;

    car_info_.speed = root["car_info"]["speed"].asInt();
    car_info_.gear = (TXGearPition)root["car_info"]["gear"].asInt();
    car_info_.steer_whl_snsr_rad = root["car_info"]["steer_whl_snsr_rad"].asFloat();
    car_info_.turn_light = (TXTurnSignal)root["car_info"]["turn_light"].asInt();
    car_info_.can_fault = root["car_info"]["can_fault"].asInt();
    car_info_.camera_fault = root["car_info"]["camera_fault"].asInt();

    long ts = std::stoll(root["now_ts"].asString());

    dms_process_.execute(&car_info_, frame_id++, *result, ts);

    return 0;
}

int CcDmsProcess::SetInputByEaTensor(void* image, int device, long long frame_id, int speed) {
#ifdef AMBA_SDK
    std::shared_ptr<NumArray> image_numArray(new NumArray());
    image_numArray->type = tongxing::NumArray::UINT8;
    image_numArray->word_size = 1;
    if (device != EA_CPU) {
        ea_tensor_sync_cache((ea_tensor_t*)image, device, EA_CPU);
    }
    image_numArray->data = (unsigned char*)ea_tensor_data((ea_tensor_t*)image);
    const size_t* shape = ea_tensor_shape((ea_tensor_t*)image);
    image_numArray->data_handle = image;
    image_numArray->shape.push_back(shape[0]);
    image_numArray->shape.push_back(shape[1]);
    image_numArray->shape.push_back(shape[2]);
    image_numArray->shape.push_back(shape[3]);
    return once_process(image_numArray, frame_id, speed);
#else
    TX_LOG_ERROR("TX DMS", "SetInputByEaTensor not support");
    return -1;
#endif
}

int CcDmsProcess::SetDriverRoi(TXPoint2i* left_top_point, TXPoint2i* right_bottom_point) {
    driver_roi_left_top_point = *left_top_point;
    driver_roi_right_bottom_point = *right_bottom_point;

    return 0;
}

int CcDmsProcess::SetDmsState(bool state) {
    return dms_process_.SetFatigue(state);
}

bool CcDmsProcess::GetDmsState() {
    return dms_process_.GetFatigue();
}

int CcDmsProcess::RestAlarm() {
    return dms_process_.DmsRestAlarm();
}

int CcDmsProcess::RestDistractAlarm() {
    return dms_process_.DmsDistractRestAlarm();
}

int CcDmsProcess::AlarmSetOk() {
    return dms_process_.DmsAlarmSetOk();
}

int CcDmsProcess::once_process(std::shared_ptr<NumArray> image, TXDmsResult* result) {
    std::vector<std::shared_ptr<tongxing::NumArray>> input;
    std::shared_ptr<tongxing::NumArray> face_roi =
        creat_numarray({1, 4}, NumArray::DataType::FLOAT32);
    float* face_roi_data_ptr = (float*)face_roi->data;
    face_roi_data_ptr[0] = driver_roi_left_top_point.x;
    face_roi_data_ptr[1] = driver_roi_left_top_point.y;
    face_roi_data_ptr[2] = driver_roi_right_bottom_point.x - driver_roi_left_top_point.x;
    face_roi_data_ptr[3] = driver_roi_right_bottom_point.y - driver_roi_left_top_point.y;
    // TX_LOG_FATAL("TX DMS", "%f %f %f %f",face_roi_data_ptr[0],face_roi_data_ptr[1],face_roi_data_ptr[2],face_roi_data_ptr[3]);
    input.push_back(image);
    input.push_back(face_roi);
    grop->setInput(input);
    // 获取司机人脸bbox
    if (status_process_ == 0)  // DMS_PROCESS
    {
        //  TX_LOG_DEBUG("TX DMS", "start");
        dms_process_.execute(&car_info_, frame_id++, *result);
        // TX_LOG_DEBUG("TX DMS", "end");
    }
    return 0;
}

int CcDmsProcess::once_process(std::shared_ptr<NumArray> image, TXDmsResult* result, long ts) {
    std::vector<std::shared_ptr<tongxing::NumArray>> input;
    std::shared_ptr<tongxing::NumArray> face_roi =
        creat_numarray({1, 4}, NumArray::DataType::FLOAT32);
    float* face_roi_data_ptr = (float*)face_roi->data;
    face_roi_data_ptr[0] = driver_roi_left_top_point.x;
    face_roi_data_ptr[1] = driver_roi_left_top_point.y;
    face_roi_data_ptr[2] = driver_roi_right_bottom_point.x - driver_roi_left_top_point.x;
    face_roi_data_ptr[3] = driver_roi_right_bottom_point.y - driver_roi_left_top_point.y;
    // TX_LOG_FATAL("TX DMS", "%f %f %f %f",face_roi_data_ptr[0],face_roi_data_ptr[1],face_roi_data_ptr[2],face_roi_data_ptr[3]);
    input.push_back(image);
    input.push_back(face_roi);
    grop->setInput(input);
    // 获取司机人脸bbox
    if (status_process_ == 0)  // DMS_PROCESS
    {
        //  TX_LOG_DEBUG("TX DMS", "start");
        dms_process_.execute(&car_info_, frame_id++, *result, ts);
        // TX_LOG_DEBUG("TX DMS", "end");
    }
    return 0;
}

std::shared_ptr<std::vector<CcObjBBox>> CcDmsProcess::get_phone_cig_bbox() {
    std::shared_ptr<std::vector<CcObjBBox>> output(new std::vector<CcObjBBox>);
    // auto feat = grop->getOutput(11);
    // auto offset_scale = grop->getOutput(12);
    // auto offset_scale_ptr = (float*)offset_scale->data;

    // auto seg = feat->data;
    // int h = feat->shape[2];
    // int w = feat->shape[3];

    // cv::Mat cig_mat = cv::Mat(h, w, CV_8UC1, &seg[1 * h * w]);
    // cv::Mat phone_mat = cv::Mat(h, w, CV_8UC1, &seg[2 * h * w]);
    // std::vector<std::vector<cv::Point>> phone_contours;
    // std::vector<std::vector<cv::Point>> cig_contours;
    // cv::findContours(phone_mat, phone_contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    // cv::findContours(cig_mat, cig_contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    // for (int i = 0; i < phone_contours.size(); ++i) {
    //     cv::Rect rect = cv::boundingRect(cv::Mat(phone_contours[i]));
    //     rect.x = rect.x * offset_scale_ptr[2] + offset_scale_ptr[0];
    //     rect.y = rect.y * offset_scale_ptr[3] + offset_scale_ptr[1];
    //     rect.width = rect.width * offset_scale_ptr[2];
    //     rect.height = rect.height * offset_scale_ptr[3];
    //     CcObjBBox bbox;
    //     bbox.label = 1;
    //     bbox.score = 1;
    //     bbox.bbox = rect;
    //     output->push_back(bbox);
    // }
    // for (int i = 0; i < cig_contours.size(); ++i) {
    //     cv::Rect rect = cv::boundingRect(cv::Mat(cig_contours[i]));
    //     rect.x = rect.x * offset_scale_ptr[2] + offset_scale_ptr[0];
    //     rect.y = rect.y * offset_scale_ptr[3] + offset_scale_ptr[1];
    //     rect.width = rect.width * offset_scale_ptr[2];
    //     rect.height = rect.height * offset_scale_ptr[3];
    //     CcObjBBox bbox;
    //     bbox.label = 2;
    //     bbox.score = 1;
    //     bbox.bbox = rect;
    //     output->push_back(bbox);
    // }

    return output;
}

std::shared_ptr<CcObjBBox> CcDmsProcess::get_driving_face_bbox() {
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_bbox start");
    std::shared_ptr<CcObjBBox> ret(new CcObjBBox);
    // auto driving_face_bbox_na = grop->getOutput(0);
    // if (driving_face_bbox_na->shape[1] > 0) {
    //     auto driving_face_bbox = driving_face_bbox_na->getTensor<float>()->operator[](0)[0];
    //     ret->score = driving_face_bbox[0].get();
    //     ret->label = driving_face_bbox[1].get();
    //     ret->bbox.x = driving_face_bbox[2].get();
    //     ret->bbox.y = driving_face_bbox[3].get();
    //     ret->bbox.width = driving_face_bbox[4].get();
    //     ret->bbox.height = driving_face_bbox[5].get();
    // } else {
    //     ret->score = 0;
    // }
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_bbox end");
    // TX_LOG_DEBUG(
    //     "TX DMS",
    //     "ret->score=%f ret->bbox.x=%d ret->bbox.y=%d ret->bbox.width=%d ret->bbox.height=%d",
    //     ret->score, ret->bbox.x, ret->bbox.y, ret->bbox.width, ret->bbox.height);

    ret->score = root_temp_["model_info"]["face_score"].asFloat();
    if (ret->score > 0) {
        ret->bbox.x = root_temp_["model_info"]["face_left"].asInt();
        ret->bbox.y = root_temp_["model_info"]["face_top"].asInt();
        ret->bbox.width = root_temp_["model_info"]["face_width"].asInt();
        ret->bbox.height = root_temp_["model_info"]["face_height"].asInt();
    }

    return ret;
}

std::shared_ptr<std::vector<cv::Point>> CcDmsProcess::get_driving_face_keypoint() {
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_keypoint start");
    std::shared_ptr<std::vector<cv::Point>> ret(new std::vector<cv::Point>);
    // auto face_keypoint = grop->getOutput(1)->getTensor<float>();
    // for (int i = 0; i < face_keypoint->shape()[1]; i++) {
    //     cv::Point p;
    //     p.x = face_keypoint->operator[](0)[i][1].get();
    //     p.y = face_keypoint->operator[](0)[i][2].get();
    //     ret->push_back(p);
    // }
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_keypoint end");
    auto face_land_mask = root_temp_["model_info"]["face_keypoint"];
    int arrayLength = root_temp_["model_info"]["face_keypoint"].size();
    for (int i = 0; i < arrayLength; i++) {
        cv::Point p;
        p.x = root_temp_["model_info"]["face_keypoint"][i][0].asInt();
        p.y = root_temp_["model_info"]["face_keypoint"][i][1].asInt();
        ret->push_back(p);
    }

    return ret;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_face_angle() {
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_angle start");
    std::shared_ptr<std::vector<float>> ret(new std::vector<float>);
    // auto face_angle = grop->getOutput(4)->getTensor<float>()->operator[](0).data_;
    // ret->push_back(face_angle[0]);
    // ret->push_back(face_angle[1]);
    // ret->push_back(face_angle[2]);
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_angle end %f %f %f", face_angle[0],
    //              face_angle[1], face_angle[2]);
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_angle end");
    ret->push_back(root_temp_["model_info"]["face_pitch"].asFloat());
    ret->push_back(root_temp_["model_info"]["face_yaw"].asFloat());
    ret->push_back(root_temp_["model_info"]["face_roll"].asFloat());
    return ret;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_right_eye_close_score() {
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_right_eye_close_score start");
    std::shared_ptr<std::vector<float>> ret(new std::vector<float>);
    // auto right_eye_close_score = grop->getOutput(2)->getTensor<float>()->operator[](0);
    // ret->push_back(right_eye_close_score[0].get());
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_right_eye_close_score %f %f",
    //              right_eye_close_score[0].get(), right_eye_close_score[1].get());
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_right_eye_close_score end");

    ret->push_back(root_temp_["model_info"]["right_eye_close_score"].asFloat());

    return ret;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_left_eye_close_score() {
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_left_eye_close_score start");
    std::shared_ptr<std::vector<float>> ret(new std::vector<float>);
    // auto left_eye_close_score = grop->getOutput(3)->getTensor<float>()->operator[](0);
    // ret->push_back(left_eye_close_score[0].get());
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_left_eye_close_score end");
    ret->push_back(root_temp_["model_info"]["left_eye_close_score"].asFloat());
    return ret;
}
int CcDmsProcess::get_occlusion_status() {
#ifndef ONLY_LANDMARKS
    // float occlusion_status = grop->getOutput(5)->getTensor<float>()->operator[](0).get();
    float occlusion_status = root_temp_["model_info"]["occlusion_score"].asFloat();
    return occlusion_status;
#else
    return false;
#endif
}
inline double findFarthestDistance(const std::vector<cv::Point>& points,
                                   cv::Point& p1,
                                   cv::Point& p2) {
    double maxDistance = 0;
    for (size_t i = 0; i < points.size(); i++) {
        for (size_t j = i + 1; j < points.size(); j++) {
            double distance = cv::norm(points[i] - points[j]);
            if (distance > maxDistance) {
                maxDistance = distance;
                p1 = points[j];
                p2 = points[i];
            }
        }
    }
    return maxDistance;
}
inline bool get_eye_info(const std::vector<cv::Point>& input,
                         cv::RotatedRect& output_rect,
                         std::vector<cv::Point>& output_point,
                         float& opening) {
    std::vector<cv::Point> max_x_points;
    std::vector<cv::Point> min_x_points;
    std::vector<cv::Point> max_y_points;
    std::vector<cv::Point> min_y_points;
    std::vector<cv::Point> edage_points;
    cv::Point max_point(-1, -1);
    cv::Point min_point(99999, 99999);
    for (auto point : input) {
        if (point.x > max_point.x) {
            max_point.x = point.x;
            max_x_points.clear();
            max_x_points.push_back(point);
        } else if (point.x == max_point.x) {
            max_x_points.push_back(point);
        }
        if (point.y > max_point.y) {
            max_point.y = point.y;
            max_y_points.clear();
            max_y_points.push_back(point);
        } else if (point.y == max_point.y) {
            max_y_points.push_back(point);
        }

        if (point.x < min_point.x) {
            min_point.x = point.x;
            min_x_points.clear();
            min_x_points.push_back(point);
        } else if (point.x == min_point.x) {
            min_x_points.push_back(point);
        }
        if (point.y < min_point.y) {
            min_point.y = point.y;
            min_y_points.clear();
            min_y_points.push_back(point);
        } else if (point.y == min_point.y) {
            min_y_points.push_back(point);
        }
    }

    for (auto point : max_x_points) {
        edage_points.push_back(point);
    }
    for (auto point : max_y_points) {
        edage_points.push_back(point);
    }
    for (auto point : min_x_points) {
        edage_points.push_back(point);
    }
    for (auto point : min_y_points) {
        edage_points.push_back(point);
    }

    cv::Point p1;
    cv::Point p2;
    double dis = findFarthestDistance(edage_points, p1, p2);

    output_point.clear();
    output_point.push_back(p1);
    output_point.push_back(p2);
    cv::Point2f roll_vec = p2 - p1;
    float roll = atan2(roll_vec.y, roll_vec.x) / CV_PI * 180.0f;
    output_rect.center = (p1 + p2) / 2;
    output_rect.angle = roll;
    output_rect.size.width = dis;
    cv::Mat rot_mat = cv::getRotationMatrix2D(output_rect.center, roll, 1.0);

    std::vector<cv::Point> rot_edage_points;
    std::vector<cv::Point> rot_end_points;
    cv::transform(edage_points, rot_edage_points, rot_mat);
    cv::Rect rot_rect = cv::boundingRect(rot_edage_points);
    output_rect.size.height = rot_rect.height;
    cv::transform(output_point, rot_end_points, rot_mat);
    float up_value = rot_end_points[0].y - rot_rect.y;
    float down_value = (rot_rect.y + rot_rect.height) - rot_end_points[0].y;
    opening = (float)(up_value + down_value) / (rot_rect.width / 2);

    std::cout << up_value << " " << down_value << " " << opening << std::endl;
    return true;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_eye_landmarks(
    const std::shared_ptr<NumArray>& feat,
    const std::shared_ptr<NumArray>& size,
    const std::shared_ptr<std::vector<float>>& angle,
    const cv::Point& left_point,
    const cv::Point& right_point) {
    // TX_LOG_INFO("CcDmsProcess", " CcDmsProcess::get_phone_cig_points start");
    // grop->getOutput(5)->getTensor<int>()->operator[](0).printShape();
    std::vector<std::vector<cv::Point>> res;
    res.resize(3);

    auto num_ptr = size->getTensor<int>()->operator[](0).data_;
    // std::cout<<num_ptr[0]<<" "<<num_ptr[1]<<" "<<num_ptr[2]<<std::endl;

    auto seg = feat->getTensor<float>()->operator[](0);

    // printf("%d %d\n",phone_cig_num_ptr[0],phone_cig_num_ptr[1]);
    // grop->getOutput(5)->getTensor<int>()->operator[](0).printShape();
    cv::RotatedRect eye_rect;
    float eye_flag = 0;

    cv::RotatedRect iris_rect;
    float iris_flag = 0;

    cv::RotatedRect pupil_rect;
    float pupil_flag = 0;

    float eye_opening = 0;
    std::vector<cv::Point> eye_max_points;
    if (num_ptr[1] > 0) {
        // res[1].resize(num_ptr[1]);
        for (int i = 0; i < num_ptr[1]; i++) {
            cv::Point2f p;
            p.x = seg.at({1, i, 0});
            p.y = seg.at({1, i, 1});
            res[1].push_back(p);
            res[0].push_back(p);
        }

        if (res[1].size() == 1) {
            iris_rect.center = res[1][0];
        } else if (res[1].size() == 2) {
            iris_rect.center = (res[1][0] + res[1][1]) / 2;
            iris_rect.size.width = sqrt((res[1][0].x - res[1][1].x) * (res[1][0].x - res[1][1].x) +
                                        (res[1][0].y - res[1][1].y) * (res[1][0].y - res[1][1].y));
            iris_rect.size.height = iris_rect.size.width;
        } else if (res[1].size() > 2) {
            iris_rect = minAreaRect(res[1]);
            iris_rect.size.width = std::min(iris_rect.size.width, iris_rect.size.height);
            iris_rect.size.height = iris_rect.size.width;
        }
        iris_flag = 1;
    }

    if (num_ptr[2] > 0) {
        // res[2].resize(num_ptr[2]);
        // std::cout<<num_ptr[2]<<std::endl;
        for (int i = 0; i < num_ptr[2]; i++) {
            cv::Point2f p;
            p.x = seg.at({2, i, 0});
            p.y = seg.at({2, i, 1});
            res[2].push_back(p);
            res[0].push_back(p);
        }
        if (res[2].size() == 1) {
            pupil_rect.center = res[2][0];
        } else if (res[2].size() == 2) {
            pupil_rect.center = (res[2][0] + res[2][1]) / 2;
            pupil_rect.size.width = sqrt((res[2][0].x - res[2][1].x) * (res[2][0].x - res[2][1].x) +
                                         (res[2][0].y - res[2][1].y) * (res[2][0].y - res[2][1].y));
            pupil_rect.size.height = pupil_rect.size.width;
        } else if (res[2].size() > 2) {
            pupil_rect = minAreaRect(res[2]);
            pupil_rect.size.width = std::min(pupil_rect.size.width, pupil_rect.size.height);
            pupil_rect.size.height = pupil_rect.size.width;
        }
        pupil_flag = 1;
    }

    if ((num_ptr[0] + num_ptr[1] + num_ptr[2]) > 0) {
        for (int i = 0; i < num_ptr[0]; i++) {
            cv::Point2f p;
            p.x = seg.at({0, i, 0});
            p.y = seg.at({0, i, 1});
            res[0].push_back(p);
            // std::cout<<cv::Point2f(seg.at({0,i,0}),seg.at({0,i,1}))<<std::endl;
        }
        if (res[0].size() > 5) {
            // eye_rect = minAreaRect(res[0]);

            if (get_eye_info(res[0], eye_rect, eye_max_points, eye_opening)) {
                eye_flag = 1;
            }
        }
    }

    // std::cout<<"yaw:"<<angle->operator[](0)<<" pit:"<<angle->operator[](1)<<" roll:"<<angle->operator[](2)<<std::endl;
    // std::cout<<"----------------------------------------------------------------"<<std::endl;
    // std::cout<<eye_rect.angle<<" "<<eye_rect.center<<" "<<eye_rect.size<<" "<<angle->operator[](2)<<left_point<<right_point<<std::endl;
    // std::cout<<iris_rect.angle<<" "<<iris_rect.center<<" "<<iris_rect.size<<std::endl;
    // std::cout<<pupil_rect.angle<<" "<<pupil_rect.center<<" "<<pupil_rect.size<<std::endl;
    cv::Point2f targe_point;
    if (eye_flag > 0) {
        std::vector<cv::Point> point_set;
        point_set.push_back(eye_max_points[0]);
        point_set.push_back(eye_max_points[1]);
        if (pupil_flag > 0) {
            point_set.push_back(cv::Point(iris_rect.center.x, pupil_rect.center.y));
            // point_set.push_back(pupil_rect.center);
        } else {
            point_set.push_back(iris_rect.center);
        }
        // point_set.push_back(iris_rect.center);
        cv::Point2f eye_center = (left_point + right_point) / 2;
        cv::Mat rot_mat = cv::getRotationMatrix2D(eye_rect.center, eye_rect.angle, 1.0);
        // std::cout<<"befor:"<<point_set[0]<<point_set[1]<<point_set[2]<<std::endl;
        cv::transform(point_set, point_set, rot_mat);
        cv::Point rot_center = (point_set[0] + point_set[1]) / 2;
        // std::cout<<"after:"<<rot_center<<" "<<rot_rect<<std::endl;
        // std::cout<<"after:"<<y_up<<" "<<y_down<<std::endl;
        std::vector<int> x_set;
        x_set.push_back(point_set[0].x);
        x_set.push_back(point_set[1].x);
        x_set.push_back(point_set[2].x);
        std::vector<int> y_set;
        y_set.push_back(point_set[0].y);
        y_set.push_back(point_set[1].y);
        y_set.push_back(point_set[2].y);
        int min_x = std::min(std::min(x_set[0], x_set[1]), x_set[2]);
        int max_x = std::max(std::max(x_set[0], x_set[1]), x_set[2]);
        int min_y = std::min(y_set[0], y_set[1]);
        int max_value = max_x - min_x;
        targe_point = (point_set[2] - cv::Point(min_x, min_y));
        // std::cout<<"targe_point1"<<targe_point<<max_value<<cv::Point(min_x, min_y)<<std::endl;

        targe_point.x = targe_point.x - (max_value / 2);
        targe_point.x = (targe_point.x / (max_value / 2) * 85);

        // targe_point.y = (int)eye_pitch;

        targe_point.y = -targe_point.y / (max_value / 2) * 30;

        // std::cout<<"targe_point2"<<targe_point<<std::endl;

        // std::cout<<targe_point<<(int)eye_pitch<<std::endl;
    }
    // std::cout<<"targe_point3"<<targe_point<<std::endl;
    std::shared_ptr<std::vector<float>> data(new std::vector<float>);
    data->resize(17);
    data->operator[](0) = eye_flag;
    data->operator[](1) = eye_rect.angle;
    data->operator[](2) = eye_rect.center.x;
    data->operator[](3) = eye_rect.center.y;
    data->operator[](4) = eye_rect.size.width;
    data->operator[](5) = eye_rect.size.height;
    data->operator[](6) = iris_flag;
    data->operator[](7) = iris_rect.center.x;
    data->operator[](8) = iris_rect.center.y;
    data->operator[](9) = iris_rect.size.width;
    data->operator[](10) = pupil_flag;
    data->operator[](11) = pupil_rect.center.x;
    data->operator[](12) = pupil_rect.center.y;
    data->operator[](13) = pupil_rect.size.width;
    if (0 == iris_flag) {
        data->operator[](14) = 0;
        data->operator[](15) = 0;
    } else {
        data->operator[](14) = targe_point.x;
        data->operator[](15) = targe_point.y;
    }
    data->operator[](16) = eye_opening;
    return data;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_right_eye_landmarks() {
    TX_LOG_DEBUG("ADDAW", "get_driving_right_eye_pose1");
    // auto key_points = get_driving_face_keypoint();
    // return get_driving_eye_landmarks(grop->getOutput(7), grop->getOutput(8),
    //                                  get_driving_face_angle(), key_points->operator[](2),
    //                                  key_points->operator[](3));
    std::shared_ptr<std::vector<float>> data(new std::vector<float>);
    data->resize(17);
    data->operator[](0) = root_temp_["model_info"]["right_eye_landmark"]["eye_score"].asFloat();
    data->operator[](1) = root_temp_["model_info"]["right_eye_landmark"]["eye_angle"].asFloat();
    data->operator[](2) = root_temp_["model_info"]["right_eye_landmark"]["eye_center_x"].asFloat();
    data->operator[](3) = root_temp_["model_info"]["right_eye_landmark"]["eye_center_y"].asFloat();
    data->operator[](4) =
        root_temp_["model_info"]["right_eye_landmark"]["eye_size_width"].asFloat();
    data->operator[](5) =
        root_temp_["model_info"]["right_eye_landmark"]["eye_size_height"].asFloat();
    data->operator[](6) = root_temp_["model_info"]["right_eye_landmark"]["iris_score"].asFloat();
    data->operator[](7) = root_temp_["model_info"]["right_eye_landmark"]["iris_center_x"].asFloat();
    data->operator[](8) = root_temp_["model_info"]["right_eye_landmark"]["iris_center_y"].asFloat();
    data->operator[](9) = root_temp_["model_info"]["right_eye_landmark"]["iris_radius"].asFloat();
    data->operator[](10) = root_temp_["model_info"]["right_eye_landmark"]["pupil_score"].asFloat();
    data->operator[](11) =
        root_temp_["model_info"]["right_eye_landmark"]["pupil_center_x"].asFloat();
    data->operator[](12) =
        root_temp_["model_info"]["right_eye_landmark"]["pupil_center_y"].asFloat();
    data->operator[](13) = root_temp_["model_info"]["right_eye_landmark"]["pupil_radius"].asFloat();
    if (0 == data->operator[](6)) {
        data->operator[](14) = 0;
        data->operator[](15) = 0;
    } else {
        data->operator[](14) = root_temp_["model_info"]["right_eye_landmark"]["yaw"].asFloat();
        data->operator[](15) = root_temp_["model_info"]["right_eye_landmark"]["pitch"].asFloat();
    }
    data->operator[](16) = root_temp_["model_info"]["right_eye_landmark"]["eye_opening"].asFloat();
    return data;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_left_eye_landmarks() {
    TX_LOG_DEBUG("ADDAW", "get_driving_left_eye_pose1");
    // auto key_points = get_driving_face_keypoint();
    // return get_driving_eye_landmarks(grop->getOutput(9), grop->getOutput(10),
    //                                  get_driving_face_angle(), key_points->operator[](0),
    //                                  key_points->operator[](1));
    std::shared_ptr<std::vector<float>> data(new std::vector<float>);
    data->resize(17);
    data->operator[](0) = root_temp_["model_info"]["left_eye_landmark"]["eye_score"].asFloat();
    data->operator[](1) = root_temp_["model_info"]["left_eye_landmark"]["eye_angle"].asFloat();
    data->operator[](2) = root_temp_["model_info"]["left_eye_landmark"]["eye_center_x"].asFloat();
    data->operator[](3) = root_temp_["model_info"]["left_eye_landmark"]["eye_center_y"].asFloat();
    data->operator[](4) = root_temp_["model_info"]["left_eye_landmark"]["eye_size_width"].asFloat();
    data->operator[](5) =
        root_temp_["model_info"]["left_eye_landmark"]["eye_size_height"].asFloat();
    data->operator[](6) = root_temp_["model_info"]["left_eye_landmark"]["iris_score"].asFloat();
    data->operator[](7) = root_temp_["model_info"]["left_eye_landmark"]["iris_center_x"].asFloat();
    data->operator[](8) = root_temp_["model_info"]["left_eye_landmark"]["iris_center_y"].asFloat();
    data->operator[](9) = root_temp_["model_info"]["left_eye_landmark"]["iris_radius"].asFloat();
    data->operator[](10) = root_temp_["model_info"]["left_eye_landmark"]["pupil_score"].asFloat();
    data->operator[](11) =
        root_temp_["model_info"]["left_eye_landmark"]["pupil_center_x"].asFloat();
    data->operator[](12) =
        root_temp_["model_info"]["left_eye_landmark"]["pupil_center_y"].asFloat();
    data->operator[](13) = root_temp_["model_info"]["left_eye_landmark"]["pupil_radius"].asFloat();
    if (0 == data->operator[](6)) {
        data->operator[](14) = 0;
        data->operator[](15) = 0;
    } else {
        data->operator[](14) = root_temp_["model_info"]["left_eye_landmark"]["yaw"].asFloat();
        data->operator[](15) = root_temp_["model_info"]["left_eye_landmark"]["pitch"].asFloat();
    }
    data->operator[](16) = root_temp_["model_info"]["left_eye_landmark"]["eye_opening"].asFloat();
    return data;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_face_attr() {
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_attr start");
    std::shared_ptr<std::vector<float>> ret(new std::vector<float>);
    // float* face_attr = (float*)grop->getOutput(6)->getTensor<float>()->operator[](0).data_;
    // ret->push_back(face_attr[0]);
    // ret->push_back(face_attr[1]);
    // ret->push_back(face_attr[2]);
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_attr end");

    ret->push_back(root_temp_["model_info"]["face_attr_isGlass"].asFloat());
    ret->push_back(root_temp_["model_info"]["face_attr_isMask"].asFloat());
    ret->push_back(root_temp_["model_info"]["face_attr_isIRBlock"].asFloat());

    return ret;
}
int CcDmsProcess::updateCarInfo(const TXCarInfo* carInfo) {
    if (carInfo->mask & TX_CAR_INFO_MASK_SPEED) {
        car_info_.speed = carInfo->speed;
    }
    if (carInfo->mask & TX_CAR_INFO_MASK_STEER_WHL_SNSR) {
        car_info_.steer_whl_snsr_rad = carInfo->steer_whl_snsr_rad;
    }
    if (carInfo->mask & TX_CAR_INFO_MASK_STEER_WHL_SNSR_SPEED) {
        car_info_.steer_whl_snsr_rad_spd = carInfo->steer_whl_snsr_rad_spd;
    }
    if (carInfo->mask & TX_CAR_INFO_MASK_GEARPOSITION) {
        car_info_.gear = carInfo->gear;
    }
    if (carInfo->mask & TX_CAR_INFO_MASK_LDW) {
        car_info_.ldw = carInfo->ldw;
    }
    if (carInfo->mask & TX_CAR_INFO_MASK_TURN_LIGHT) {
        car_info_.turn_light = carInfo->turn_light;
    }
    if (carInfo->mask & TX_CAR_CAN_FAULT) {
        car_info_.can_fault = carInfo->can_fault;
    }
    if (carInfo->mask & TX_CAR_CAMERA_FAULT) {
        car_info_.camera_fault = carInfo->camera_fault;
    }
    return 0;
}
//以下函数用于保存图像及结果json到nfs挂载路径下
#if defined(INTEGRATION_TEST_MODE)
inline std::string getCurrentTime() {
    struct timeval tv;
    gettimeofday(&tv, NULL);

    static const int MAX_BUFFER_SIZE = 128;
    char timestamp_str[MAX_BUFFER_SIZE];
    time_t sec = static_cast<time_t>(tv.tv_sec);
    int ms = static_cast<int>(tv.tv_usec) / 1000;

    struct tm tm_time;
    localtime_r(&sec, &tm_time);
    static const char* formater = "%4d%02d%02d%02d%02d%02d%03d";
    int wsize = snprintf(timestamp_str, MAX_BUFFER_SIZE, formater, tm_time.tm_year + 1900,
                         tm_time.tm_mon + 1, tm_time.tm_mday, tm_time.tm_hour, tm_time.tm_min,
                         tm_time.tm_sec, ms);

    timestamp_str[std::min(wsize, MAX_BUFFER_SIZE - 1)] = '\0';
    return std::string(timestamp_str);
}

void CcDmsProcess::SaveResultToJson(TXDmsResult& dms_result, const TXImageInfo* image) {
    std::string save_path_root = "/fatfs/nfs_share";
    if ((access(save_path_root.c_str(), 0)) == -1) {
        return;  //挂载路径不存在则立即返回
    }

    if (first_boot_up) {
        case_save_path = save_path_root + "/" + getCurrentTime();
        mkdir(case_save_path.c_str(), S_IRWXU);  //每次启动生成一个新的case目录
        first_boot_up = false;
    }

    struct timeval tv;
    gettimeofday(&tv, NULL);
    long now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    std::string ts = std::to_string(now_ts);

    Json::Value root;
    Json::Value dms_result_json;
    Json::Value warnInfo_json;
    dms_result_json["result_frame_id"] = dms_result.result_frame_id;
    dms_result_json["camera_status"] = dms_result.camera_status;
    dms_result_json["drowsiness_type"] = dms_result.drowsiness_status;
    dms_result_json["distraction_type"] = dms_result.distraction_status;
    dms_result_json["calibrate_status"] = dms_result.calibrate_status;
    dms_result_json["car_speed"] = car_info_.speed;
    dms_result_json["car_gear"] = car_info_.gear;
    dms_result_json["car_steer_whl_snsr_rad"] = car_info_.steer_whl_snsr_rad;
    dms_result_json["turn_light"] = car_info_.turn_light;
    {
        Json::Value face_info;
        face_info["score"] = dms_result.face_info.score;
        face_info["xmin"] = dms_result.face_info.xmin / 2;
        face_info["ymin"] = dms_result.face_info.ymin / 2;
        face_info["xmax"] = dms_result.face_info.xmax / 2;
        face_info["ymax"] = dms_result.face_info.ymax / 2;
        face_info["yaw"] = dms_result.face_info.head_yaw;
        face_info["pitch"] = dms_result.face_info.head_pitch;
        face_info["roll"] = dms_result.face_info.head_roll;
        face_info["isMask"] = dms_result.face_info.isMask;
        face_info["isGlass"] = dms_result.face_info.isGlass;
        face_info["isIRBlock"] = dms_result.face_info.isIRBlock;
        face_info["right_close_eye_score"] = dms_result.face_info.right_close_eye_score;
        face_info["left_close_eye_score"] = dms_result.face_info.left_close_eye_score;
        face_info["mouth_opening"] = dms_result.face_info.mouth_opening;
        Json::Value landmarks(Json::arrayValue);
        for (int i = 0; i < TX_MAX_FLD_SIZE; i++) {
            Json::Value point;
            point["x"] = dms_result.face_info.landmarks[i].x / 2;
            point["y"] = dms_result.face_info.landmarks[i].y / 2;
            landmarks[i] = point;
        }

        {
            Json::Value right_eye_landmark;
            right_eye_landmark["eye_score"] = dms_result.face_info.right_eye_landmark.eye_score;
            right_eye_landmark["eye_angle"] = dms_result.face_info.right_eye_landmark.eye_angle;
            right_eye_landmark["eye_center"] = Json::Value();
            right_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.right_eye_landmark.eye_center.x / 2;
            right_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.right_eye_landmark.eye_center.y / 2;
            right_eye_landmark["eye_size"] = Json::Value();
            right_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.right_eye_landmark.eye_size.width / 2;
            right_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.right_eye_landmark.eye_size.height / 2;
            right_eye_landmark["iris_score"] = dms_result.face_info.right_eye_landmark.iris_score;
            right_eye_landmark["iris_center"] = Json::Value();
            right_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.right_eye_landmark.iris_center.x / 2;
            right_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.right_eye_landmark.iris_center.y / 2;
            right_eye_landmark["iris_radius"] =
                dms_result.face_info.right_eye_landmark.iris_radius / 2;
            right_eye_landmark["pupil_score"] = dms_result.face_info.right_eye_landmark.pupil_score;
            right_eye_landmark["pupil_center"] = Json::Value();
            right_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.right_eye_landmark.pupil_center.x / 2;
            right_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.right_eye_landmark.pupil_center.y / 2;
            right_eye_landmark["pupil_radius"] =
                dms_result.face_info.right_eye_landmark.pupil_radius / 2;
            right_eye_landmark["yaw"] = dms_result.face_info.right_eye_landmark.yaw;
            right_eye_landmark["pitch"] = dms_result.face_info.right_eye_landmark.pitch;
            face_info["right_eye_landmark"] = right_eye_landmark;
            Json::Value left_eye_landmark;
            left_eye_landmark["eye_score"] = dms_result.face_info.left_eye_landmark.eye_score;
            left_eye_landmark["eye_angle"] = dms_result.face_info.left_eye_landmark.eye_angle;
            left_eye_landmark["eye_center"] = Json::Value();
            left_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.left_eye_landmark.eye_center.x / 2;
            left_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.left_eye_landmark.eye_center.y / 2;
            left_eye_landmark["eye_size"] = Json::Value();
            left_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.left_eye_landmark.eye_size.width / 2;
            left_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.left_eye_landmark.eye_size.height / 2;
            left_eye_landmark["iris_score"] = dms_result.face_info.left_eye_landmark.iris_score;
            left_eye_landmark["iris_center"] = Json::Value();
            left_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.left_eye_landmark.iris_center.x / 2;
            left_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.left_eye_landmark.iris_center.y / 2;
            left_eye_landmark["iris_radius"] =
                dms_result.face_info.left_eye_landmark.iris_radius / 2;
            left_eye_landmark["pupil_score"] = dms_result.face_info.left_eye_landmark.pupil_score;
            left_eye_landmark["pupil_center"] = Json::Value();
            left_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.left_eye_landmark.pupil_center.x / 2;
            left_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.left_eye_landmark.pupil_center.y / 2;
            left_eye_landmark["pupil_radius"] =
                dms_result.face_info.left_eye_landmark.pupil_radius / 2;
            left_eye_landmark["yaw"] = dms_result.face_info.left_eye_landmark.yaw;
            left_eye_landmark["pitch"] = dms_result.face_info.left_eye_landmark.pitch;
            face_info["left_eye_landmark"] = left_eye_landmark;
        }
        face_info["landmarks"] = landmarks;
        dms_result_json["face_info"] = face_info;
    }
    {
        warnInfo_json["eye1_60s_10p"] = dms_result.face_info.warnInfo.eye1_60s_10p;
        warnInfo_json["mouth1_120s_2"] = dms_result.face_info.warnInfo.mouth1_120s_2;
        warnInfo_json["mouth1_1l_1"] = dms_result.face_info.warnInfo.mouth1_1l_1;
        warnInfo_json["eye2_20s_1f5"] = dms_result.face_info.warnInfo.eye2_20s_1f5;
        warnInfo_json["eye2_2_0f75"] = dms_result.face_info.warnInfo.eye2_2_0f75;
        warnInfo_json["eye2_1l_0f75"] = dms_result.face_info.warnInfo.eye2_1l_0f75;
        warnInfo_json["eye2_60s_12p"] = dms_result.face_info.warnInfo.eye2_60s_12p;
        warnInfo_json["mouth2_1l_2"] = dms_result.face_info.warnInfo.mouth2_1l_2;
        warnInfo_json["mouth2_120s_3"] = dms_result.face_info.warnInfo.mouth2_120s_3;
        warnInfo_json["eye3_20s_2f4"] = dms_result.face_info.warnInfo.eye3_20s_2f4;
        warnInfo_json["eye3_2_1f2"] = dms_result.face_info.warnInfo.eye3_2_1f2;
        warnInfo_json["eye3_1l_1f2"] = dms_result.face_info.warnInfo.eye3_1l_1f2;
        warnInfo_json["repetition"] = dms_result.face_info.warnInfo.repetition;
    }
    root["ts"] = ts;
    root["dms_result"] = dms_result_json;
    root["warnInfo"] = warnInfo_json;
    root["sdk_version"] = TXDmsGetVersion();

    //写入json
    Json::StyledWriter writer;
    std::ofstream os;

    std::string json_output_file = case_save_path + "/" + ts + ".json";
    std::string img_output_file = case_save_path + "/" + ts + ".jpg";
    os.open(json_output_file);
    os << writer.write(root);
    os.close();
    //保存图片
    cv::Mat img = cv::Mat(image->height, image->width, CV_8UC1, image->data);
    cv::imwrite(img_output_file, img);
    // 压缩图像
    // cv::Mat compressedImage;
    // cv::resize(img, compressedImage, cv::Size(), 0.5, 0.5);  // 压缩为原图的50%
    // 使用临近插值恢复图像
    // cv::Mat recoveredImage;
    // cv::resize(compressedImage, recoveredImage, img.size(), 0, 0, cv::INTER_NEAREST);
    // cv::imwrite(img_output_file, recoveredImage);
}

void CcDmsProcess::SaveResultToWeb(TXDmsResult& dms_result, const TXImageInfo* image) {
    if (tongxing::CcMediaServer::instance().get_push_data_status()) {
        struct timeval tv;
        gettimeofday(&tv, NULL);
        long now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
        std::string ts = std::to_string(now_ts);

        Json::Value root;
        Json::Value dms_result_json;
        Json::Value warnInfo_json;
        dms_result_json["result_frame_id"] = dms_result.result_frame_id;
        dms_result_json["camera_status"] = dms_result.camera_status;
        dms_result_json["drowsiness_type"] = dms_result.drowsiness_status;
        dms_result_json["distraction_type"] = dms_result.distraction_status;
        dms_result_json["calibrate_status"] = dms_result.calibrate_status;
        dms_result_json["system_status"] = dms_result.system_status;
        dms_result_json["car_speed"] = car_info_.speed;
        dms_result_json["car_gear"] = car_info_.gear;
        dms_result_json["car_steer_whl_snsr_rad"] = car_info_.steer_whl_snsr_rad;
        dms_result_json["turn_light"] = car_info_.turn_light;
        dms_result_json["distraction_reason"] =
            dms_process_.GetDistractReason(dms_result.camera_status);
        {
            Json::Value face_info;
            face_info["score"] = dms_result.face_info.score;
            face_info["xmin"] = dms_result.face_info.xmin / 2;
            face_info["ymin"] = dms_result.face_info.ymin / 2;
            face_info["xmax"] = dms_result.face_info.xmax / 2;
            face_info["ymax"] = dms_result.face_info.ymax / 2;
            face_info["yaw"] = dms_result.face_info.head_yaw;
            face_info["pitch"] = dms_result.face_info.head_pitch;
            face_info["roll"] = dms_result.face_info.head_roll;
            face_info["isMask"] = dms_result.face_info.isMask;
            face_info["isGlass"] = dms_result.face_info.isGlass;
            face_info["isIRBlock"] = dms_result.face_info.isIRBlock;
            face_info["right_close_eye_score"] = dms_result.face_info.right_close_eye_score;
            face_info["left_close_eye_score"] = dms_result.face_info.left_close_eye_score;
            face_info["mouth_opening"] = dms_result.face_info.mouth_opening;
            Json::Value landmarks(Json::arrayValue);
            for (int i = 0; i < TX_MAX_FLD_SIZE; i++) {
                Json::Value point;
                point["x"] = dms_result.face_info.landmarks[i].x / 2;
                point["y"] = dms_result.face_info.landmarks[i].y / 2;
                landmarks[i] = point;
            }

            {
                Json::Value right_eye_landmark;
                right_eye_landmark["eye_score"] = dms_result.face_info.right_eye_landmark.eye_score;
                right_eye_landmark["eye_angle"] = dms_result.face_info.right_eye_landmark.eye_angle;
                right_eye_landmark["eye_center"] = Json::Value();
                right_eye_landmark["eye_center"]["x"] =
                    dms_result.face_info.right_eye_landmark.eye_center.x / 2;
                right_eye_landmark["eye_center"]["y"] =
                    dms_result.face_info.right_eye_landmark.eye_center.y / 2;
                right_eye_landmark["eye_size"] = Json::Value();
                right_eye_landmark["eye_size"]["width"] =
                    dms_result.face_info.right_eye_landmark.eye_size.width / 2;
                right_eye_landmark["eye_size"]["height"] =
                    dms_result.face_info.right_eye_landmark.eye_size.height / 2;
                right_eye_landmark["iris_score"] =
                    dms_result.face_info.right_eye_landmark.iris_score;
                right_eye_landmark["iris_center"] = Json::Value();
                right_eye_landmark["iris_center"]["x"] =
                    dms_result.face_info.right_eye_landmark.iris_center.x / 2;
                right_eye_landmark["iris_center"]["y"] =
                    dms_result.face_info.right_eye_landmark.iris_center.y / 2;
                right_eye_landmark["iris_radius"] =
                    dms_result.face_info.right_eye_landmark.iris_radius / 2;
                right_eye_landmark["pupil_score"] =
                    dms_result.face_info.right_eye_landmark.pupil_score;
                right_eye_landmark["pupil_center"] = Json::Value();
                right_eye_landmark["pupil_center"]["x"] =
                    dms_result.face_info.right_eye_landmark.pupil_center.x / 2;
                right_eye_landmark["pupil_center"]["y"] =
                    dms_result.face_info.right_eye_landmark.pupil_center.y / 2;
                right_eye_landmark["pupil_radius"] =
                    dms_result.face_info.right_eye_landmark.pupil_radius / 2;
                right_eye_landmark["yaw"] = dms_result.face_info.right_eye_landmark.yaw;
                right_eye_landmark["pitch"] = dms_result.face_info.right_eye_landmark.pitch;
                face_info["right_eye_landmark"] = right_eye_landmark;
                Json::Value left_eye_landmark;
                left_eye_landmark["eye_score"] = dms_result.face_info.left_eye_landmark.eye_score;
                left_eye_landmark["eye_angle"] = dms_result.face_info.left_eye_landmark.eye_angle;
                left_eye_landmark["eye_center"] = Json::Value();
                left_eye_landmark["eye_center"]["x"] =
                    dms_result.face_info.left_eye_landmark.eye_center.x / 2;
                left_eye_landmark["eye_center"]["y"] =
                    dms_result.face_info.left_eye_landmark.eye_center.y / 2;
                left_eye_landmark["eye_size"] = Json::Value();
                left_eye_landmark["eye_size"]["width"] =
                    dms_result.face_info.left_eye_landmark.eye_size.width / 2;
                left_eye_landmark["eye_size"]["height"] =
                    dms_result.face_info.left_eye_landmark.eye_size.height / 2;
                left_eye_landmark["iris_score"] = dms_result.face_info.left_eye_landmark.iris_score;
                left_eye_landmark["iris_center"] = Json::Value();
                left_eye_landmark["iris_center"]["x"] =
                    dms_result.face_info.left_eye_landmark.iris_center.x / 2;
                left_eye_landmark["iris_center"]["y"] =
                    dms_result.face_info.left_eye_landmark.iris_center.y / 2;
                left_eye_landmark["iris_radius"] =
                    dms_result.face_info.left_eye_landmark.iris_radius / 2;
                left_eye_landmark["pupil_score"] =
                    dms_result.face_info.left_eye_landmark.pupil_score;
                left_eye_landmark["pupil_center"] = Json::Value();
                left_eye_landmark["pupil_center"]["x"] =
                    dms_result.face_info.left_eye_landmark.pupil_center.x / 2;
                left_eye_landmark["pupil_center"]["y"] =
                    dms_result.face_info.left_eye_landmark.pupil_center.y / 2;
                left_eye_landmark["pupil_radius"] =
                    dms_result.face_info.left_eye_landmark.pupil_radius / 2;
                left_eye_landmark["yaw"] = dms_result.face_info.left_eye_landmark.yaw;
                left_eye_landmark["pitch"] = dms_result.face_info.left_eye_landmark.pitch;
                face_info["left_eye_landmark"] = left_eye_landmark;
            }
            face_info["landmarks"] = landmarks;
            dms_result_json["face_info"] = face_info;
        }
        {
            warnInfo_json["eye1_60s_10p"] = dms_result.face_info.warnInfo.eye1_60s_10p;
            warnInfo_json["mouth1_120s_2"] = dms_result.face_info.warnInfo.mouth1_120s_2;
            warnInfo_json["mouth1_1l_1"] = dms_result.face_info.warnInfo.mouth1_1l_1;
            warnInfo_json["eye2_20s_1f5"] = dms_result.face_info.warnInfo.eye2_20s_1f5;
            warnInfo_json["eye2_2_0f75"] = dms_result.face_info.warnInfo.eye2_2_0f75;
            warnInfo_json["eye2_1l_0f75"] = dms_result.face_info.warnInfo.eye2_1l_0f75;
            warnInfo_json["eye2_60s_12p"] = dms_result.face_info.warnInfo.eye2_60s_12p;
            warnInfo_json["mouth2_1l_2"] = dms_result.face_info.warnInfo.mouth2_1l_2;
            warnInfo_json["mouth2_120s_3"] = dms_result.face_info.warnInfo.mouth2_120s_3;
            warnInfo_json["eye3_20s_2f4"] = dms_result.face_info.warnInfo.eye3_20s_2f4;
            warnInfo_json["eye3_2_1f2"] = dms_result.face_info.warnInfo.eye3_2_1f2;
            warnInfo_json["eye3_1l_1f2"] = dms_result.face_info.warnInfo.eye3_1l_1f2;
            warnInfo_json["repetition"] = dms_result.face_info.warnInfo.repetition;
        }
        root["ts"] = ts;
        root["dms_result"] = dms_result_json;
        root["warnInfo"] = warnInfo_json;
        root["sdk_version"] = TXDmsGetVersion();

        tongxing::CcMediaServer::ImageInfo media_image;
        media_image.data = image->data;
        media_image.dataLen = image->dataLen;
        media_image.dataType = (tongxing::CcMediaServer::ImageFormat)image->dataType;
        media_image.height = image->height;
        media_image.stride = image->stride;
        media_image.width = image->width;
        tongxing::CcMediaServer::instance().push_image_and_message(
            (const tongxing::CcMediaServer::ImageInfo*)&media_image, root);
    }
}
#endif
}  // namespace tongxing
