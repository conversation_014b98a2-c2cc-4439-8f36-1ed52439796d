#include "CHttpClient.hpp"

#include <string>

#include "curl/curl.h"

std::shared_ptr<CHttpClient> CHttpClient::instance = std::make_shared<CHttpClient>();

std::shared_ptr<CHttpClient> CHttpClient::getInstance(){
    if (instance.get() == nullptr){
        instance.reset(new CHttpClient);
    }
    return  instance;
}

CHttpClient::CHttpClient(void) : m_bDebug(false) {}

CHttpClient::~CHttpClient(void) {}

static int OnDebug(CURL*, curl_infotype itype, char* pData, size_t size, void*) {
    if (itype == CURLINFO_TEXT) {
        // printf("[TEXT]%s\n", pData);
    } else if (itype == CURLINFO_HEADER_IN) {
        printf("[HEADER_IN]%s\n", pData);
    } else if (itype == CURLINFO_HEADER_OUT) {
        printf("[HEADER_OUT]%s\n", pData);
    } else if (itype == CURLINFO_DATA_IN) {
        printf("[DATA_IN]%s\n", pData);
    } else if (itype == CURLINFO_DATA_OUT) {
        printf("[DATA_OUT]%s\n", pData);
    }
    return 0;
}

static size_t OnWriteData(void* buffer, size_t size, size_t nmemb, void* lpVoid) {
    std::string* str = dynamic_cast<std::string*>((std::string*)lpVoid);
    if (NULL == str || NULL == buffer) {
        return -1;
    }

    char* pData = (char*)buffer;
    str->append(pData, size * nmemb);
    return nmemb;
}

static size_t OnWriteFile(void* ptr, size_t size, size_t nmemb, FILE* stream) {
    size_t written;
    written = fwrite(ptr, size, nmemb, stream);
    return written;
}

int CHttpClient::Post(const std::string& strUrl,
                      const std::string& strPost,
                      std::string& strResponse) {
    CURLcode res;
    CURL* curl = curl_easy_init();
    if (NULL == curl) {
        return CURLE_FAILED_INIT;
    }
    if (m_bDebug) {
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 1);
        curl_easy_setopt(curl, CURLOPT_DEBUGFUNCTION, OnDebug);
    }
    curl_easy_setopt(curl, CURLOPT_URL, strUrl.c_str());
    curl_easy_setopt(curl, CURLOPT_POST, 1);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, strPost.c_str());
    curl_easy_setopt(curl, CURLOPT_READFUNCTION, NULL);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, OnWriteData);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void*)&strResponse);
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1);
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 3);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 3);
    res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);
    return res;
}

int CHttpClient::Get(const std::string& strUrl, std::string& strResponse) {
    CURLcode res;
    CURL* curl = curl_easy_init();
    if (NULL == curl) {
        return CURLE_FAILED_INIT;
    }
    if (m_bDebug) {
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 1);
        curl_easy_setopt(curl, CURLOPT_DEBUGFUNCTION, OnDebug);
    }

    curl_easy_setopt(curl, CURLOPT_URL, strUrl.c_str());
    curl_easy_setopt(curl, CURLOPT_READFUNCTION, NULL);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, OnWriteData);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void*)&strResponse);
    /**
   * 當多個線程都使用超時處理的時候，同時主線程中有sleep或是wait等操作。
   * 如果不設置這個選項，libcurl將會發信號打斷這個wait從而導致程序退出。
   */
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1);
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 3);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 3);
    res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);
    return res;
}

int CHttpClient::Download(const std::string& strUrl, std::string& strFilePath) {
    CURLcode res;
    CURL* curl = curl_easy_init();
    if (NULL == curl) {
        return CURLE_FAILED_INIT;
    }
    if (m_bDebug) {
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 1);
        curl_easy_setopt(curl, CURLOPT_DEBUGFUNCTION, OnDebug);
    }

    FILE* fp;
    fp = fopen(strFilePath.c_str(), "wb");

    curl_easy_setopt(curl, CURLOPT_URL, strUrl.c_str());
    // curl_easy_setopt(curl, CURLOPT_READFUNCTION, NULL);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, OnWriteFile);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, fp);
    /**
   * 當多個線程都使用超時處理的時候，同時主線程中有sleep或是wait等操作。
   * 如果不設置這個選項，libcurl將會發信號打斷這個wait從而導致程序退出。
   */
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1);
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 3);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 3);

    res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);

    fclose(fp);

    return res;
}