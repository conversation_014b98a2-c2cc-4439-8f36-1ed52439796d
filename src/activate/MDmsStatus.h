#ifndef _SDK_MMDMS_STATUS_H_
#define _SDK_MMDMS_STATUS_H_

/** A enum to represent running status */
typedef enum MDmsStatus {
    MDMS_SUCCESS = 0,              //!< success
    MDMS_PARAM_INVALID = 1,        //!< input parameter is invalid
    MDMS_FILE_OPEN_FAIL = 2,       //!< open file failed
    MDMS_FILE_PATH_OPEN_FAIL = 3,  //!< open file path failed
    MDMS_SDK_HANDLE_INVALID = 4,   //!< sdk handle invaild

    // MDMS config errors
    MDMS_CONFIG_OPEN_FAIL = 100,   //!< config file open failed
    MDMS_CONFIG_PARSE_FAIL = 101,  //!< config parse failed

    // MDMS license errors
    MDMS_LICENSE_UUID_FAIL = 200,                  //! 获取uuid失败
    MDMS_LICENSE_ONLINE_DEVICE_NO_NETWORK = 201,   //! 设备没有网络
    MDMS_LICENSE_ONLINE_ACTIVATE_UUID_FAIL = 202,  //! uuid没有在平台上录入
    MDMS_LICENSE_ONLINE_ACTIVATE_FAIL = 203,       //! online activate failed
    MDMS_LICENSE_TIMES_OUTOF_LIMIT = 204,          //! 激活次数超限
    MDMS_LICENSE_ACTIVATE_CODE_INVALID = 205,  //! 激活码无效，请删除激活码文件重新联网激活
    MDMS_LICENSE_UUID_INVALID = 206,          //! uuid非法
    MDMS_LICENSE_ONLINE_UUID_MISMATCH = 207,  //! uuid不匹配
    MDMS_LICENSE_SN_FAIL = 208,               //! 获取sn失败

    MDMS_INTERNAL_ERROR = 1000  //!< internal error
} MDmsStatus;

#endif