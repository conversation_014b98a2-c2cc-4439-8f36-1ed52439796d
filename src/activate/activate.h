#ifndef __ACTIVATE_H__
#define __ACTIVATE_H__

#include <iostream>
#include <string>

// #include "MDmsStatus.h"

class ActivateService {
  public:
    static ActivateService& instance();
    ActivateService();
    ~ActivateService();
    int run(const std::string activate_code, const int activate_code_len);
    // MDmsStatus dealActivate(const std::string& uuid_sn, const std::string& activate_code_file);
    void setuuid(const std::string& uuid_str);
    bool GetActivateStatus();

  private:
    std::string m_uuid;
    std::string getuuid();
    std::string getsn();
    std::string getdid();  // 获取设备指纹
  private:
    bool flag_activate = false;
};

#endif