#ifndef __HTTP_CURL_H__
#define __HTTP_CURL_H__

#include <string>
#include <memory>

class CHttpClient {
  public:
    CHttpClient(void);
    ~CHttpClient(void);

    static std::shared_ptr<CHttpClient> instance;
    static std::shared_ptr<CHttpClient> getInstance();

  public:
    /**
   * @brief HTTP POST請求
   * @param strUrl 輸入參數,請求的Url地址,如:http://www.baidu.com
   * @param strPost 輸入參數,使用如下格式para1=val1¶2=val2&…
   * @param strResponse 輸出參數,返回的內容
   * @return 返回是否Post成功
   */
    int Post(const std::string& strUrl, const std::string& strPost, std::string& strResponse);

    /**
   * @brief HTTP GET請求
   * @param strUrl 輸入參數,請求的Url地址,如:http://www.baidu.com
   * @param strResponse 輸出參數,返回的內容
   * @return 返回是否Post成功
   */
    int Get(const std::string& strUrl, std::string& strResponse);

    /**
   * @brief HTTPS POST請求,無證書版本
   * @param strUrl 輸入參數,請求的Url地址,如:https://www.alipay.com
   * @param strPost 輸入參數,使用如下格式para1=val1¶2=val2&…
   * @param strResponse 輸出參數,返回的內容
   * @param pCaPath
   * 輸入參數,爲CA證書的路徑.如果輸入爲NULL,則不驗證服務器端證書的有效性.
   * @return 返回是否Post成功
   */
    int Posts(const std::string& strUrl,
              const std::string& strPost,
              std::string& strResponse,
              const char* pCaPath = NULL);

    /**
   * @brief HTTPS GET請求,無證書版本
   * @param strUrl 輸入參數,請求的Url地址,如:https://www.alipay.com
   * @param strResponse 輸出參數,返回的內容
   * @param pCaPath
   * 輸入參數,爲CA證書的路徑.如果輸入爲NULL,則不驗證服務器端證書的有效性.
   * @return 返回是否Post成功
   */
    int Gets(const std::string& strUrl, std::string& strResponse, const char* pCaPath = NULL);

    /**
   * @brief HTTPS GET請求,無證書版本
   * @param strUrl 輸入參數,請求的Url地址,如:https://www.alipay.com
   * @param strFilePath 输入參數,文件保存的路径
   * @return 返回是否下载成功
   */
    int Download(const std::string& strUrl, std::string& strFilePath);

  public:
    void SetDebug(bool bDebug);

  private:
    bool m_bDebug;
};

#endif