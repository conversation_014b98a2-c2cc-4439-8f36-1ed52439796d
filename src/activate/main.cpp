#include "activate.h"

// 比亚迪测试算法激活demo
int main(int argc, char* argv[]) {
    std::string temp_activate_code = "A3D9489B8DB63B34F92F94C980319168";
    int status = ActivateService::instance().run(temp_activate_code, temp_activate_code.length());
    if (status == 0) {
        std::cout << "activate success" << std::endl;
    } else {
        std::cout << "activate fail." << std::endl;
        std::cout << "status:" << status << std::endl;
    }

    return 0;
}