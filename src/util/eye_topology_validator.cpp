#include "eye_topology_validator.h"
#include <algorithm>
#include <cmath>
#include <numeric>
#include <cstdio>

namespace tongxing {

EyeTopologyValidator::EyeTopologyValidator()
    : min_confidence_threshold_(0.3f)
    , min_pupil_iris_ratio_(0.2f)
    , max_pupil_iris_ratio_(0.6f)
    , min_iris_eye_ratio_(0.2f)  // 调整为更宽松的阈值
    , max_iris_eye_ratio_(0.9f)  // 调整为更宽松的阈值
    , max_contour_distance_ratio_(3.0f) {
}

EyeTopologyValidator::EyeKeypoints EyeTopologyValidator::ParseEyeKeypoints(const float* raw_keypoints) const {
    if (!raw_keypoints) {
        return EyeKeypoints(); // 返回空的结构体
    }

    EyeKeypoints keypoints;
    
    try {
        // 解析眼睛轮廓点 (0-7)
        for (int i = 0; i < 8; i++) {
            keypoints.confidences[i] = raw_keypoints[3 * i];
            keypoints.eye_contour[i].x = raw_keypoints[3 * i + 1];
            keypoints.eye_contour[i].y = raw_keypoints[3 * i + 2];
        }
        
        // 解析虹膜轮廓点 (8-15)
        for (int i = 0; i < 8; i++) {
            int idx = i + 8;
            keypoints.confidences[idx] = raw_keypoints[3 * idx];
            keypoints.iris_contour[i].x = raw_keypoints[3 * idx + 1];
            keypoints.iris_contour[i].y = raw_keypoints[3 * idx + 2];
        }
        
        // 解析瞳孔点 (16)
        keypoints.confidences[16] = raw_keypoints[3 * 16];
        keypoints.pupil_point.x = raw_keypoints[3 * 16 + 1];
        keypoints.pupil_point.y = raw_keypoints[3 * 16 + 2];
        
    } catch (const std::exception& e) {
        // 解析失败，返回空结构体
        return EyeKeypoints();
    }
    
    return keypoints;
}

EyeTopologyValidator::TopologyValidationResult EyeTopologyValidator::ValidateTopology(const EyeKeypoints& keypoints) const {
    TopologyValidationResult result;
    
    try {
        // 1. 基础置信度检查
        bool has_valid_confidence = false;
        for (float conf : keypoints.confidences) {
            if (conf >= min_confidence_threshold_) {
                has_valid_confidence = true;
                break;
            }
        }
        
        if (!has_valid_confidence) {
            result.error_message = "All keypoints have low confidence";
            return result;
        }
        
        // 2. 验证眼睛轮廓
        result.eye_contour_valid = ValidateEyeContour(keypoints.eye_contour);
        
        // 3. 验证虹膜轮廓
        result.iris_contour_valid = ValidateIrisContour(keypoints.iris_contour);
        
        // 4. 检查瞳孔在虹膜内
        result.pupil_in_iris = CheckPupilInIris(keypoints);
        
        // 5. 检查虹膜在眼睛轮廓内
        result.iris_in_eye = CheckIrisInEye(keypoints);
        
        // 6. 验证尺寸比例
        result.size_ratio_valid = ValidateSizeRatios(keypoints);

        // 7. 验证关键点分布合理性
        result.distribution_valid = ValidateKeypointDistribution(keypoints);

        // 8. 验证轮廓平滑性
        result.smoothness_valid = ValidateContourSmoothness(keypoints);

        // 9. 计算整体置信度评分
        result.confidence_score = CalculateConfidenceScore(keypoints, result);

        // 10. 综合判断
        result.is_valid = result.eye_contour_valid &&
                         result.iris_contour_valid &&
                         result.pupil_in_iris &&
                         result.iris_in_eye &&
                         result.size_ratio_valid &&
                         result.distribution_valid &&
                         result.smoothness_valid;
        
        if (!result.is_valid && result.error_message.empty()) {
            result.error_message = "Topology validation failed";
        }
        
    } catch (const std::exception& e) {
        result.error_message = std::string("Validation error: ") + e.what();
        result.is_valid = false;
    }
    
    return result;
}

bool EyeTopologyValidator::CheckPupilInIris(const EyeKeypoints& keypoints) const {
    // 检查瞳孔置信度
    if (keypoints.confidences[16] < min_confidence_threshold_) {
        return false;
    }
    
    // 检查虹膜轮廓置信度
    bool iris_valid = true;
    for (int i = 8; i < 16; i++) {
        if (keypoints.confidences[i] < min_confidence_threshold_) {
            iris_valid = false;
            break;
        }
    }
    
    if (!iris_valid) {
        return false;
    }
    
    // 使用点在多边形内算法检查
    return IsPointInPolygon(keypoints.pupil_point, keypoints.iris_contour);
}

bool EyeTopologyValidator::CheckIrisInEye(const EyeKeypoints& keypoints) const {
    // 检查眼睛轮廓置信度
    bool eye_valid = true;
    for (int i = 0; i < 8; i++) {
        if (keypoints.confidences[i] < min_confidence_threshold_) {
            eye_valid = false;
            break;
        }
    }
    
    if (!eye_valid) {
        return false;
    }
    
    // 检查虹膜轮廓的所有点是否都在眼睛轮廓内
    for (const auto& iris_point : keypoints.iris_contour) {
        if (!IsPointInPolygon(iris_point, keypoints.eye_contour)) {
            return false;
        }
    }
    
    return true;
}

bool EyeTopologyValidator::ValidateEyeContour(const std::vector<cv::Point2f>& contour) const {
    if (contour.size() != 8) {
        return false;
    }
    
    // 检查是否有自相交
    if (HasSelfIntersection(contour)) {
        return false;
    }
    
    // 检查相邻点距离的合理性
    for (size_t i = 0; i < contour.size(); i++) {
        size_t next = (i + 1) % contour.size();
        float dist1 = cv::norm(contour[i] - contour[next]);
        
        if (i > 0) {
            size_t prev = (i - 1 + contour.size()) % contour.size();
            float dist2 = cv::norm(contour[prev] - contour[i]);
            
            if (dist1 > 0 && dist2 > 0) {
                float ratio = std::max(dist1, dist2) / std::min(dist1, dist2);
                if (ratio > max_contour_distance_ratio_) {
                    return false;
                }
            }
        }
    }
    
    return true;
}

bool EyeTopologyValidator::ValidateIrisContour(const std::vector<cv::Point2f>& contour) const {
    if (contour.size() != 8) {
        return false;
    }
    
    // 虹膜应该接近圆形，检查点到中心的距离变化
    cv::Point2f center(0, 0);
    for (const auto& point : contour) {
        center += point;
    }
    center /= static_cast<float>(contour.size());
    
    std::vector<float> distances;
    for (const auto& point : contour) {
        distances.push_back(cv::norm(point - center));
    }
    
    if (distances.empty()) {
        return false;
    }
    
    float mean_dist = std::accumulate(distances.begin(), distances.end(), 0.0f) / distances.size();
    
    // 检查距离变化是否在合理范围内
    for (float dist : distances) {
        if (mean_dist > 0) {
            float ratio = std::abs(dist - mean_dist) / mean_dist;
            if (ratio > 0.3f) { // 允许30%的变化
                return false;
            }
        }
    }
    
    return true;
}

bool EyeTopologyValidator::ValidateSizeRatios(const EyeKeypoints& keypoints) const {
    try {
        // 计算虹膜面积
        float iris_area = CalculateContourArea(keypoints.iris_contour);
        if (iris_area <= 0) {
            return false;
        }

        // 计算眼睛轮廓面积
        float eye_area = CalculateContourArea(keypoints.eye_contour);
        if (eye_area <= 0) {
            return false;
        }

        // 检查虹膜/眼睛面积比例
        float iris_eye_ratio = iris_area / eye_area;
        if (iris_eye_ratio < min_iris_eye_ratio_ || iris_eye_ratio > max_iris_eye_ratio_) {
            return false;
        }

        // 估算瞳孔面积（假设瞳孔是小圆）
        // 由于只有一个瞳孔点，我们使用虹膜轮廓来估算瞳孔大小
        cv::Point2f iris_center(0, 0);
        for (const auto& point : keypoints.iris_contour) {
            iris_center += point;
        }
        iris_center /= static_cast<float>(keypoints.iris_contour.size());

        float iris_radius = 0;
        for (const auto& point : keypoints.iris_contour) {
            iris_radius += cv::norm(point - iris_center);
        }
        iris_radius /= static_cast<float>(keypoints.iris_contour.size());

        // 瞳孔到虹膜中心的距离应该合理
        float pupil_to_center_dist = cv::norm(keypoints.pupil_point - iris_center);
        if (pupil_to_center_dist > iris_radius * 0.5f) { // 瞳孔不应偏离中心太远
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        return false;
    }
}

float EyeTopologyValidator::CalculateConfidenceScore(const EyeKeypoints& keypoints,
                                                   const TopologyValidationResult& partial_result) const {
    float score = 0.0f;

    // 1. 置信度评分 (权重: 0.3)
    float avg_confidence = 0.0f;
    for (float conf : keypoints.confidences) {
        avg_confidence += conf;
    }
    avg_confidence /= keypoints.confidences.size();
    score += 0.3f * std::min(1.0f, avg_confidence);

    // 2. 拓扑结构评分 (权重: 0.4)
    float topology_score = 0.0f;
    if (partial_result.pupil_in_iris) topology_score += 0.2f;
    if (partial_result.iris_in_eye) topology_score += 0.2f;
    if (partial_result.eye_contour_valid) topology_score += 0.2f;
    if (partial_result.iris_contour_valid) topology_score += 0.2f;
    if (partial_result.distribution_valid) topology_score += 0.1f;
    if (partial_result.smoothness_valid) topology_score += 0.1f;
    score += 0.4f * topology_score;

    // 3. 尺寸比例评分 (权重: 0.3)
    if (partial_result.size_ratio_valid) {
        score += 0.3f;
    }

    return std::max(0.0f, std::min(1.0f, score));
}

bool EyeTopologyValidator::IsPointInPolygon(const cv::Point2f& point,
                                           const std::vector<cv::Point2f>& polygon) const {
    if (polygon.size() < 3) {
        return false;
    }

    // 使用射线法判断点是否在多边形内
    int intersections = 0;
    for (size_t i = 0; i < polygon.size(); i++) {
        size_t j = (i + 1) % polygon.size();

        if (((polygon[i].y > point.y) != (polygon[j].y > point.y)) &&
            (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) /
             (polygon[j].y - polygon[i].y) + polygon[i].x)) {
            intersections++;
        }
    }

    return (intersections % 2) == 1;
}

bool EyeTopologyValidator::HasSelfIntersection(const std::vector<cv::Point2f>& contour) const {
    if (contour.size() < 4) {
        return false;
    }

    // 检查所有非相邻线段是否相交
    for (size_t i = 0; i < contour.size(); i++) {
        size_t i_next = (i + 1) % contour.size();

        for (size_t j = i + 2; j < contour.size(); j++) {
            if (j == contour.size() - 1 && i == 0) continue; // 跳过首尾相邻的情况

            size_t j_next = (j + 1) % contour.size();

            // 检查线段 (i, i_next) 和 (j, j_next) 是否相交
            cv::Point2f p1 = contour[i], p2 = contour[i_next];
            cv::Point2f p3 = contour[j], p4 = contour[j_next];

            // 使用向量叉积判断线段相交
            float d1 = (p4.x - p3.x) * (p1.y - p3.y) - (p4.y - p3.y) * (p1.x - p3.x);
            float d2 = (p4.x - p3.x) * (p2.y - p3.y) - (p4.y - p3.y) * (p2.x - p3.x);
            float d3 = (p2.x - p1.x) * (p3.y - p1.y) - (p2.y - p1.y) * (p3.x - p1.x);
            float d4 = (p2.x - p1.x) * (p4.y - p1.y) - (p2.y - p1.y) * (p4.x - p1.x);

            if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
                ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
                return true;
            }
        }
    }

    return false;
}

float EyeTopologyValidator::CalculateContourArea(const std::vector<cv::Point2f>& contour) const {
    if (contour.size() < 3) {
        return 0.0f;
    }

    // 使用鞋带公式计算多边形面积
    float area = 0.0f;
    for (size_t i = 0; i < contour.size(); i++) {
        size_t j = (i + 1) % contour.size();
        area += contour[i].x * contour[j].y;
        area -= contour[j].x * contour[i].y;
    }

    return std::abs(area) / 2.0f;
}

cv::RotatedRect EyeTopologyValidator::FitEllipse(const std::vector<cv::Point2f>& points) const {
    if (points.size() < 5) {
        return cv::RotatedRect();
    }

    try {
        return cv::fitEllipse(points);
    } catch (const cv::Exception& e) {
        return cv::RotatedRect();
    }
}

bool EyeTopologyValidator::ValidateKeypointDistribution(const EyeKeypoints& keypoints) const {
    try {
        // 1. 检查眼睛轮廓的相邻距离合理性
        if (!CheckAdjacentDistances(keypoints.eye_contour)) {
            return false;
        }

        // 2. 检查虹膜轮廓的相邻距离合理性
        if (!CheckAdjacentDistances(keypoints.iris_contour)) {
            return false;
        }

        // 3. 检查眼睛轮廓是否自相交
        if (HasSelfIntersection(keypoints.eye_contour)) {
            return false;
        }

        // 4. 检查虹膜轮廓是否自相交
        if (HasSelfIntersection(keypoints.iris_contour)) {
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        return false;
    }
}

bool EyeTopologyValidator::ValidateContourSmoothness(const EyeKeypoints& keypoints) const {
    try {
        // 1. 检查眼睛轮廓的凸性（眼睛轮廓应该是凸的）
        if (!IsContourConvex(keypoints.eye_contour)) {
            return false;
        }

        // 2. 检查虹膜轮廓的曲率变化
        auto iris_curvatures = CalculateCurvature(keypoints.iris_contour);
        if (iris_curvatures.empty()) {
            return false;
        }

        // 虹膜应该接近圆形，曲率变化应该较小
        float mean_curvature = 0.0f;
        for (float curvature : iris_curvatures) {
            mean_curvature += curvature;
        }
        mean_curvature /= iris_curvatures.size();

        // 检查曲率变化是否在合理范围内
        for (float curvature : iris_curvatures) {
            if (mean_curvature > 0) {
                float variation = std::abs(curvature - mean_curvature) / mean_curvature;
                if (variation > 0.5f) { // 允许50%的曲率变化
                    return false;
                }
            }
        }

        return true;

    } catch (const std::exception& e) {
        return false;
    }
}

bool EyeTopologyValidator::CheckAdjacentDistances(const std::vector<cv::Point2f>& contour) const {
    if (contour.size() < 3) {
        return false;
    }

    std::vector<float> distances;
    for (size_t i = 0; i < contour.size(); i++) {
        size_t next = (i + 1) % contour.size();
        float dist = cv::norm(contour[i] - contour[next]);
        distances.push_back(dist);
    }

    // 检查相邻距离的变化是否过大
    for (size_t i = 0; i < distances.size(); i++) {
        size_t next = (i + 1) % distances.size();
        if (distances[i] > 0 && distances[next] > 0) {
            float ratio = std::max(distances[i], distances[next]) / std::min(distances[i], distances[next]);
            if (ratio > max_contour_distance_ratio_) {
                return false;
            }
        }
    }

    return true;
}

std::vector<float> EyeTopologyValidator::CalculateCurvature(const std::vector<cv::Point2f>& contour) const {
    std::vector<float> curvatures;

    if (contour.size() < 3) {
        return curvatures;
    }

    for (size_t i = 0; i < contour.size(); i++) {
        size_t prev = (i - 1 + contour.size()) % contour.size();
        size_t next = (i + 1) % contour.size();

        cv::Point2f p1 = contour[prev];
        cv::Point2f p2 = contour[i];
        cv::Point2f p3 = contour[next];

        // 计算向量
        cv::Point2f v1 = p2 - p1;
        cv::Point2f v2 = p3 - p2;

        // 计算叉积（曲率的近似）
        float cross_product = v1.x * v2.y - v1.y * v2.x;
        float v1_norm = cv::norm(v1);
        float v2_norm = cv::norm(v2);

        if (v1_norm > 0 && v2_norm > 0) {
            float curvature = std::abs(cross_product) / (v1_norm * v2_norm);
            curvatures.push_back(curvature);
        } else {
            curvatures.push_back(0.0f);
        }
    }

    return curvatures;
}

bool EyeTopologyValidator::IsContourConvex(const std::vector<cv::Point2f>& contour) const {
    if (contour.size() < 3) {
        return false;
    }

    bool sign_positive = false;
    bool sign_negative = false;

    for (size_t i = 0; i < contour.size(); i++) {
        size_t j = (i + 1) % contour.size();
        size_t k = (i + 2) % contour.size();

        cv::Point2f v1 = contour[j] - contour[i];
        cv::Point2f v2 = contour[k] - contour[j];

        float cross_product = v1.x * v2.y - v1.y * v2.x;

        if (cross_product > 0) {
            sign_positive = true;
        } else if (cross_product < 0) {
            sign_negative = true;
        }

        // 如果同时有正负号，说明不是凸多边形
        if (sign_positive && sign_negative) {
            return false;
        }
    }

    return true;
}

} // namespace tongxing
