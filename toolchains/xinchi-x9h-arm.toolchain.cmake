# set cross-compiled system type, it's better not use the type which cmake cannot recognize

message("---X9HP---")
include("/opt/android-ndk-r25b/build/cmake/android-legacy.toolchain.cmake")


#set compiler name
SET (COMPILER_NAME xinchi-x9h)


option(X9HP "SDK_X9SP support" ON)
option(Android_r25b "SDK_X9SP support" ON)




SET(SDK_PATH thirdparty/xinchi/X9HP)


add_definitions(-DXLOCALE_NOT_USED)
add_definitions(-DUPDATE_NCNN)
add_definitions(-D__ANDROID__)
add_definitions(-DANDROID)

#
####
# cmake .. \
# -DCMAKE_TOOLCHAIN_FILE=../toolchains/xinchi-x9h-arm.toolchain.cmake \
# -DCMAKE_BUILD_TYPE=Release \
# -DANDROID_ABI=arm64-v8a \
# -DANDROID_STL=c++_shared \
# -DANDROID_NATIVE_API_LEVEL=29  \
# -DANDROID_TOOLCHAIN=clang++
####