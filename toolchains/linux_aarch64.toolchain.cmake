# set cross-compiled system type, it's better not use the type which cmake cannot recognized.
SET (CMAKE_SYSTEM_NAME Linux)
SET (CMAKE_SYSTEM_PROCESSOR aarch64)

# gcc-arm-linux-gnueabi DO NOT need to be installed, so make sure arm-linux-gnueabihf-gcc and arm-linux-gnueabihf-g++ can be found in $PATH:
# SET (CMAKE_C_COMPILER   "arm-linux-gnueabihf-gcc")
# SET (CMAKE_CXX_COMPILER "arm-linux-gnueabihf-g++")
SET ( CMAKE_C_COMPILER "/opt/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc" )
SET ( CMAKE_CXX_COMPILER "/opt/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++" )

# set searching rules for cross-compiler
SET(COMPILER_NAME Linux_aarch64)
SET(Linux_aarch64 ON)

