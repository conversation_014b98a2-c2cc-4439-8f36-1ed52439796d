# set cross-compiled system type, it's better not use the type which cmake cannot recognized.
SET (CMAKE_SYSTEM_NAME Linux)
SET (CMAKE_SYSTEM_PROCESSOR aarch32)

SET (CMAKE_C_COMPILER   "/opt/arm-ov-linux-20210322/bin/arm-ov-linux-uclibcgnueabihf-gcc")
SET (CMAKE_CXX_COMPILER "/opt/arm-ov-linux-20210322/bin/arm-ov-linux-uclibcgnueabihf-g++")

# set searching rules for cross-compiler
SET(COMPILER_NAME Linux_ov_aarch32)
SET(Linux_ov_aarch32 ON)
