import asyncio
import websockets
import cv2
import tongxing_dms_pb2
import numpy as np
import json
import time
import datetime
import os
from PIL import Image, ImageDraw, ImageFont
import queue
from threading import Thread
import math
url='ws://192.168.3.138:1181'
bbox_color = (0,255,0)
point_color = (0,255,0)
auto_make_folder_time=60

save_flag = False
output_path="./output2"

preview_queue=queue.Queue(10)
def create_rotate_matrix(angle, axis):
    if axis == 'x':
        matrix = np.array([[1.0, 0.0, 0.0, 0.0], [0.0, np.cos(angle), -np.sin(angle), 0.0],
                           [0.0, np.sin(angle), np.cos(angle), 0.0], [0.0, 0.0, 0.0, 1.0]], dtype=np.float32)
        return matrix
    elif axis == 'y':
        matrix = np.array([[np.cos(angle), 0.0, -np.sin(angle), 0.0], [0.0, 1.0, 0.0, 0.0],
                           [np.sin(angle), 0.0, np.cos(angle), 0.0], [0.0, 0.0, 0.0, 1.0]], dtype=np.float32)
        return matrix
    elif axis == 'z':
        matrix = np.array([[np.cos(angle), -np.sin(angle), 0.0, 0.0], [np.sin(angle), np.cos(angle), 0.0, 0.0],
                           [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0]], dtype=np.float32)
        return matrix
    else:
        print("wrong axis")
        return False

def create_perspective_matrix(fovy, aspect, n, f):
    q = 1.0 / np.tan((0.5 * fovy) / 180.0 * np.pi)
    A = q / aspect
    B = (n + f) / (n - f)
    C = (2.0 * n * f) / (n - f)
    return np.array([[A, 0.0, 0.0, 0.0], [0.0, q, 0.0, 0.0], [0.0, 0.0, B, C], [0.0, 0.0, -1.0, 0.0]], dtype=np.float32)

def create_translate_matrix(vec):
    return np.array([[1.0, 0.0, 0.0, vec[0]], [0.0, 1.0, 0.0, vec[1]],
                     [0.0, 0.0, 1.0, vec[2]], [0.0, 0.0, 0.0, 1.0]], dtype=np.float32)

def create_lookat_matrix(eye, center, up):
    f = (center - eye)/np.linalg.norm(eye-center, axis=0, keepdims=False)
    up= up/np.linalg.norm(up, axis=0, keepdims=False)
    s = np.cross(f, up)
    u = np.cross(s, f)
    M = np.array([[s[0], s[1], s[2], 0], [u[0], u[1], u[2], 0],
                 [-f[0], -f[1], -f[2], 0], [0, 0, 0, 1]], dtype=np.float32)
    return np.dot(M, create_translate_matrix(-eye))

def calculate_coordinate(roll, pitch, yaw, length):
    x_line = np.array([[1], [0], [0], [1]], dtype=np.float32)
    y_line = np.array([[0], [1], [0], [1]], dtype=np.float32)
    z_line = np.array([[0], [0], [1], [1]], dtype=np.float32)
    X_rotate = create_rotate_matrix(roll, 'x')
    Y_rotate = create_rotate_matrix(pitch, 'y')
    Z_rotate = create_rotate_matrix(yaw, 'z')
    lookat = create_lookat_matrix(np.array([5.0, 0.0, 0.0]), np.array([-1.0, 0.0, 0.0]), np.array([0.0, 0.0, 1.0]))
    perspective = create_perspective_matrix(60.0, 1.0, 0.1, 100.0)
    x_out = np.dot(perspective, np.dot(lookat, np.dot(X_rotate, np.dot(Z_rotate, np.dot(Y_rotate, x_line)))))
    y_out = np.dot(perspective, np.dot(lookat, np.dot(X_rotate, np.dot(Z_rotate, np.dot(Y_rotate, y_line)))))
    z_out = np.dot(perspective, np.dot(lookat, np.dot(X_rotate, np.dot(Z_rotate, np.dot(Y_rotate, z_line)))))
    return length * np.array([[x_out[0]/x_out[3], x_out[1]/x_out[3]], [y_out[0]/y_out[3], y_out[1]/y_out[3]],
                     [z_out[0]/z_out[3], z_out[1]/z_out[3]]], dtype=np.float32)


def draw_axis(img, axis, center, bbox=None):
    # center = center.astype(np.int32)
    axis.astype(np.int32)
    if bbox is not None:
        cv2.rectangle(img, (bbox[0], bbox[1]), (bbox[0] + bbox[2], bbox[1] + bbox[3]), (0, 0, 255), 5)
    # cv2.line(img, (center[0], center[1]), (int(center[0] + axis[1][0]), int(center[1] - axis[1][1])), (0, 102, 80), 1)
    # cv2.line(img, (center[0], center[1]), (int(center[0] + axis[2][0]), int(center[1] - axis[2][1])), (255, 0, 0), 1)
    cv2.line(img, (center[0], center[1]), (int(center[0] + axis[0][0]), int(center[1] - axis[0][1])), (0, 0, 255), 1)
    return img


def cv2ImgAddText(img, text, left, top, textColor=(0, 255, 0), textSize=20):    
    if (isinstance(img, np.ndarray)):  #判断是否OpenCV图片类型        
        img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))    
        draw = ImageDraw.Draw(img)    
        # fontText = ImageFont.truetype("simhei.ttf", textSize, encoding="utf-8")    
        draw.text((left, top), text, textColor)    
        return cv2.cvtColor(np.asarray(img), cv2.COLOR_RGB2BGR)
def preview_thread_enter():
    cv2.namedWindow("dms") 
    while(True):
         image_data,message_data= preview_queue.get()
         message=json.loads(message_data)
         image=cv2.imdecode(image_data,0)
         show=cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
         cv2.rectangle(show, (message["dms_result"]["face_info"]["xmin"],message["dms_result"]["face_info"]["ymin"]), (message["dms_result"]["face_info"]["xmax"],message["dms_result"]["face_info"]["ymax"]), bbox_color,2)
         for p in message["dms_result"]["face_info"]["landmarks"]:
             cv2.circle(show,(p["x"],p["y"]),2,point_color,-1)
         print("score:",message["dms_result"]["face_info"]["score"])
         right_eye_score=message["dms_result"]["face_info"]["right_eye_landmark"]["eye_score"]
         right_eye_center=message["dms_result"]["face_info"]["right_eye_landmark"]["eye_center"]
         right_eye_size=message["dms_result"]["face_info"]["right_eye_landmark"]["eye_size"]
         right_eye_angle=message["dms_result"]["face_info"]["right_eye_landmark"]["eye_angle"]
        #  cv2.ellipse(show,(right_eye_center["x"],right_eye_center["y"]),(right_eye_size["width"],right_eye_size["height"]),right_eye_angle,0,360,(0, 0, 255),2)
         right_iris_score=message["dms_result"]["face_info"]["right_eye_landmark"]["iris_score"]
         right_iris_center=message["dms_result"]["face_info"]["right_eye_landmark"]["iris_center"]
         right_iris_radius=int(message["dms_result"]["face_info"]["right_eye_landmark"]["iris_radius"])
        #  if(right_iris_score>0.5):
        #     cv2.circle(show,(right_iris_center["x"],right_iris_center["y"]),right_iris_radius,point_color,1)
         right_pupli_score=message["dms_result"]["face_info"]["right_eye_landmark"]["pupil_score"]
         right_pupil_center=message["dms_result"]["face_info"]["right_eye_landmark"]["pupil_center"]
         if(right_pupli_score>0.5):
            cv2.circle(show,(right_pupil_center["x"],right_pupil_center["y"]),2,point_color,-1)
         if(right_pupli_score>0.5):
            angle_start_point=[right_pupil_center["x"],right_pupil_center["y"]]
            yaw=message["dms_result"]["face_info"]["right_eye_landmark"]["yaw"]+message["dms_result"]["face_info"]["yaw"]
            pitch=message["dms_result"]["face_info"]["right_eye_landmark"]["pitch"]+message["dms_result"]["face_info"]["pitch"]
            roll=message["dms_result"]["face_info"]["roll"]
            length = 200
            axis = calculate_coordinate(np.deg2rad(roll),np.deg2rad(pitch),np.deg2rad(-yaw), length)
            show = draw_axis(show, axis, angle_start_point)
            print("right",roll,pitch,yaw)
            # end_point[1]=end_point[1]+(end_point[1]-start_point[1])*3
            # end_point[0]=end_point[0]+(end_point[0]-start_point[0])*5
            # cv2.line(show,start_point2,end_point,(255,0,0),1)



         left_eye_score=message["dms_result"]["face_info"]["left_eye_landmark"]["eye_score"]
         left_eye_center=message["dms_result"]["face_info"]["left_eye_landmark"]["eye_center"]
         left_eye_size=message["dms_result"]["face_info"]["left_eye_landmark"]["eye_size"]
         left_eye_angle=message["dms_result"]["face_info"]["left_eye_landmark"]["eye_angle"]
        #  cv2.ellipse(show,(left_eye_center["x"],left_eye_center["y"]),(left_eye_size["width"],left_eye_size["height"]),left_eye_angle,0,360,(0, 0, 255),2)
         left_iris_score=message["dms_result"]["face_info"]["left_eye_landmark"]["iris_score"]
         left_iris_center=message["dms_result"]["face_info"]["left_eye_landmark"]["iris_center"]
         left_iris_radius=int(message["dms_result"]["face_info"]["left_eye_landmark"]["iris_radius"])
        #  if(left_iris_score>0.5):
        #      cv2.circle(show,(left_iris_center["x"],left_iris_center["y"]),left_iris_radius,point_color,1)
         left_pupli_score=message["dms_result"]["face_info"]["left_eye_landmark"]["pupil_score"]
         left_pupil_center=message["dms_result"]["face_info"]["left_eye_landmark"]["pupil_center"]
         if(left_pupli_score>0.5):
            cv2.circle(show,(left_pupil_center["x"],left_pupil_center["y"]),2,point_color,-1)
         if(left_pupli_score>0.5):
            angle_start_point=[left_pupil_center["x"],left_pupil_center["y"]]
            yaw=message["dms_result"]["face_info"]["left_eye_landmark"]["yaw"]+message["dms_result"]["face_info"]["yaw"]
            pitch=message["dms_result"]["face_info"]["left_eye_landmark"]["pitch"]+message["dms_result"]["face_info"]["pitch"]
            roll=message["dms_result"]["face_info"]["roll"]
            length = 200
            print("left",roll,pitch,yaw)
            axis = calculate_coordinate(np.deg2rad(roll),np.deg2rad(pitch),np.deg2rad(-yaw), length)
            show = draw_axis(show, axis, angle_start_point)
            

         drowsiness_type=message["dms_result"]["drowsiness_type"]
         camera_status=message["dms_result"]["camera_status"]
         distraction_type=message["dms_result"]["distraction_type"]
         calibrate_status=message["dms_result"]["calibrate_status"]
         left_eye = message["dms_result"]["face_info"]["left_close_eye_score"]
         right_eye = message["dms_result"]["face_info"]["right_close_eye_score"]
         mouth = message["dms_result"]["face_info"]["mouth_opening"]


         eye1_60s_10p = message["warnInfo"]["eye1_60s_10p"]
         eye2_60s_12p = message["warnInfo"]["eye2_60s_12p"]
         eye2_1l_0f75 = message["warnInfo"]["eye2_1l_0f75"]
         eye2_2_0f75 = message["warnInfo"]["eye2_2_0f75"]
         eye2_20s_1f5 = message["warnInfo"]["eye2_20s_1f5"]
         eye3_1l_1f2 = message["warnInfo"]["eye3_1l_1f2"]
         eye3_20s_2f4 = message["warnInfo"]["eye3_20s_2f4"]
         eye3_2_1f2 = message["warnInfo"]["eye3_2_1f2"]

         mouth1_120s_2 = message["warnInfo"]["mouth1_120s_2"]
         mouth1_1l_1 = message["warnInfo"]["mouth1_1l_1"]
         mouth2_120s_3 = message["warnInfo"]["mouth2_120s_3"]
         mouth2_1l_2 = message["warnInfo"]["mouth2_1l_2"]

         repetition = message["warnInfo"]["repetition"]

         show = cv2ImgAddText(show, "is_mask:"+str(message["dms_result"]["face_info"]["isMask"]), 30, 10, (255, 0, 0), 11)
         show = cv2ImgAddText(show, "is_glass:"+str(message["dms_result"]["face_info"]["isGlass"]), 30, 25, (255, 0, 0), 11)
         show = cv2ImgAddText(show, "is_irblock:"+str(message["dms_result"]["face_info"]["isIRBlock"]), 30, 40, (255, 0, 0), 11)
         show = cv2ImgAddText(show, "camera_status:"+str(message["dms_result"]["camera_status"]), 30, 55, (255, 0, 0), 11)
         show = cv2ImgAddText(show, "left_eye_close:"+str(round(left_eye,2)), 500, 10, (255, 0, 0), 11)
         show = cv2ImgAddText(show, "right_eye_close:"+str(round(right_eye,2)), 500, 25, (255, 0, 0), 11)
         show = cv2ImgAddText(show, "mouth:"+str(round(mouth,2)), 500, 40, (255, 0, 0), 11)
         show = cv2ImgAddText(show, "drowsiness_type:"+str(drowsiness_type), 500, 55, (255, 0, 0), 11)
         show = cv2ImgAddText(show, "distraction_type:"+str(distraction_type), 500, 75, (255, 0, 0), 11)
         show = cv2ImgAddText(show, "calibrate_status:"+str(calibrate_status), 280, 10, (255, 0, 0), 11)
         
         show = cv2ImgAddText(show, "face_yaw:"+str(int(message["dms_result"]["face_info"]["yaw"])), 500, 90, (255, 0, 0), 11)
         show = cv2ImgAddText(show, "face_pitch:"+str(int(message["dms_result"]["face_info"]["pitch"])), 500, 105, (255, 0, 0), 11)
         
         show = cv2ImgAddText(show, "righrt_eye_yaw:"+str(int(message["dms_result"]["face_info"]["right_eye_landmark"]["yaw"])), 500, 120, (255, 0, 0), 11)
         show = cv2ImgAddText(show, "righrt_eye_pitch:"+str(int(message["dms_result"]["face_info"]["right_eye_landmark"]["pitch"])), 500, 135, (255, 0, 0), 11)
        #  show = cv2ImgAddText(show, "righrt_eye_opening:"+str(float(message["dms_result"]["face_info"]["right_eye_landmark"]["opening"])), 500, 150, (255, 0, 0), 11)
        #  show = cv2ImgAddText(show, "left_eye_pitch:"+str(int(message["dms_result"]["face_info"]["left_eye_landmark"]["pitch"])), 500, 165, (255, 0, 0), 11)

         show = cv2ImgAddText(show, "face_roll:"+str(int(message["dms_result"]["face_info"]["roll"])), 500, 180, (255, 0, 0), 11)
        #  show = cv2ImgAddText(show, "eye2_2_0f75:"+str(eye2_2_0f75), 500, 120, (255, 0, 0), 11)
        #  show = cv2ImgAddText(show, "eye2_20s_1f5:"+str(eye2_20s_1f5), 500, 135, (255, 0, 0), 11)
        #  show = cv2ImgAddText(show, "eye3_1l_1f2:"+str(eye3_1l_1f2), 500, 150, (255, 0, 0), 11)
        #  show = cv2ImgAddText(show, "eye3_20s_2f4:"+str(eye3_20s_2f4), 500, 165, (255, 0, 0), 11)
        #  show = cv2ImgAddText(show, "eye3_2_1f2:"+str(eye3_2_1f2), 500, 180, (255, 0, 0), 11)
        #  show = cv2ImgAddText(show, "mouth1_120s_2:"+str(mouth1_120s_2), 250, 390, (255, 0, 0), 11)
        #  show = cv2ImgAddText(show, "mouth1_1l_1:"+str(mouth1_1l_1), 500, 210, (255, 0, 0), 11)
        #  show = cv2ImgAddText(show, "mouth2_120s_3:"+str(mouth2_120s_3), 250, 450, (255, 0, 0), 11)
        #  show = cv2ImgAddText(show, "mouth2_1l_2:"+str(mouth2_1l_2), 500, 240, (255, 0, 0), 11)

        #  show = cv2ImgAddText(show, "repetition:" + str(repetition), 500, 255, (255, 0, 0), 11)
         show=cv2.resize(show,(1280,720))
         cv2.imshow("dms",show)
         cv2.waitKey(1)

async def recv_ws_data():
    last_save_path=""
    async with websockets.connect(url) as websocket:
        while websocket.open:
            # try:
                response = await websocket.recv()
                dms_frame_data = tongxing_dms_pb2.DMS_Frame()
                dms_frame_data.ParseFromString(response)
                # print(f'Received: {dms_frame_data.image_type},{dms_frame_data.image_height},{dms_frame_data.image_width}')
                if(dms_frame_data.image_type=="JPG"):
                    image_data=np.frombuffer(dms_frame_data.image_data, dtype=np.uint8)
                    # image=np.reshape(image,[dms_frame_data.image_height,dms_frame_data.image_width])
                    #自动保存数据
                    timestamp = time.time()
                    date = datetime.datetime.fromtimestamp(int(timestamp/auto_make_folder_time)*auto_make_folder_time)
                    date_str=date.strftime("%Y-%m-%d-%H-%M-%S")
                    output_folder_name=output_path+"/"+date_str
                    output_image_file_name=output_folder_name+"/"+str(int(timestamp*1000))+".jpg"
                    output_image_json_name=output_folder_name+"/"+str(int(timestamp*1000))+".json"
                    # cv2.imwrite(output_image_file_name,image)
                    # print(dms_frame_data.message_data)
                    message=json.loads(dms_frame_data.message_data)
                    if save_flag or message["dms_result"]["face_info"]["score"]<0.5:
                        if(last_save_path != output_folder_name):
                            os.makedirs(output_folder_name, exist_ok=True)
                        with open(output_image_json_name, 'w') as f:
                            f.write(dms_frame_data.message_data)
                        with open(output_image_file_name, 'wb') as f:
                            f.write(image_data)
                    if(preview_queue.full()):
                        preview_queue.get()
                    preview_queue.put((image_data,dms_frame_data.message_data))           
            # except:
            #     print("disconnect")
preview_thread = Thread(target=preview_thread_enter,args=())
preview_thread.start()
asyncio.run(recv_ws_data())