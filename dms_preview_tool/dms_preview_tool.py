import asyncio
import websockets
import cv2
import tongxing_dms_pb2
import numpy as np
import json
import time
import datetime
import os
url='ws://192.168.7.1:1181'
bbox_color = (0,255,0)
point_color = (0,255,0)
auto_make_folder_time=60
output_path="./output"
async def hello():
    async with websockets.connect(url) as websocket:
        while websocket.open:
            # try:
                response = await websocket.recv()
                dms_frame_data = tongxing_dms_pb2.DMS_Frame()
                dms_frame_data.ParseFromString(response)
                # print(f'Received: {dms_frame_data.image_type},{dms_frame_data.image_height},{dms_frame_data.image_width}')
                if(dms_frame_data.image_type=="JPG"):
                    image_data=np.frombuffer(dms_frame_data.image_data, dtype=np.uint8)
                    image=cv2.imdecode(image_data,0)
                    # image=np.reshape(image,[dms_frame_data.image_height,dms_frame_data.image_width])
                    message=json.loads(dms_frame_data.message_data)

                    #自动保存数据
                    timestamp = time.time()
                    date = datetime.datetime.fromtimestamp(int(timestamp/auto_make_folder_time)*auto_make_folder_time)
                    date_str=date.strftime("%Y-%m-%d-%H-%M-%S")
                    output_folder_name=output_path+"/"+date_str
                    os.makedirs(output_folder_name, exist_ok=True)
                    output_image_file_name=output_folder_name+"/"+str(int(timestamp*1000))+".jpg"
                    output_image_json_name=output_folder_name+"/"+str(int(timestamp*1000))+".json"
                    # cv2.imwrite(output_image_file_name,image)
                    with open(output_image_json_name, 'w') as f:
                        json.dump(message, f)
                    with open(output_image_file_name, 'wb') as f:
                        f.write(image_data)
                    show=cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
                    cv2.rectangle(show, (message["dms_result"]["face_info"]["xmin"],message["dms_result"]["face_info"]["ymin"]), (message["dms_result"]["face_info"]["xmax"],message["dms_result"]["face_info"]["ymax"]), bbox_color,2)
                    for p in message["dms_result"]["face_info"]["landmarks"]:
                        cv2.circle(show,(p["x"],p["y"]),1,point_color,-1)
                    
                    cv2.imshow("dms",show)
                    cv2.waitKey(1)
            
            # except:
            #     print("disconnect")

cv2.namedWindow("dms") 
asyncio.run(hello())