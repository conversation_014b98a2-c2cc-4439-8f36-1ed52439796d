# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tongxing_dms.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='tongxing_dms.proto',
  package='tongxing.proto',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x12tongxing_dms.proto\x12\x0etongxing.proto\"t\n\tDMS_Frame\x12\x12\n\nimage_type\x18\x01 \x01(\t\x12\x13\n\x0bimage_width\x18\x02 \x01(\r\x12\x14\n\x0cimage_height\x18\x03 \x01(\r\x12\x12\n\nimage_data\x18\x04 \x01(\x0c\x12\x14\n\x0cmessage_data\x18\x05 \x01(\tb\x06proto3')
)




_DMS_FRAME = _descriptor.Descriptor(
  name='DMS_Frame',
  full_name='tongxing.proto.DMS_Frame',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='image_type', full_name='tongxing.proto.DMS_Frame.image_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='image_width', full_name='tongxing.proto.DMS_Frame.image_width', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='image_height', full_name='tongxing.proto.DMS_Frame.image_height', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='image_data', full_name='tongxing.proto.DMS_Frame.image_data', index=3,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message_data', full_name='tongxing.proto.DMS_Frame.message_data', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=38,
  serialized_end=154,
)

DESCRIPTOR.message_types_by_name['DMS_Frame'] = _DMS_FRAME
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DMS_Frame = _reflection.GeneratedProtocolMessageType('DMS_Frame', (_message.Message,), dict(
  DESCRIPTOR = _DMS_FRAME,
  __module__ = 'tongxing_dms_pb2'
  # @@protoc_insertion_point(class_scope:tongxing.proto.DMS_Frame)
  ))
_sym_db.RegisterMessage(DMS_Frame)


# @@protoc_insertion_point(module_scope)
