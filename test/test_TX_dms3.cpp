#include "tx_dms_sdk.h"
#include <stdio.h>
#include <unistd.h>
#include "eazyai.h"
#include <sys/time.h>

long g_dms_handle = 0;
#include "opencv2/opencv.hpp"
void CalmcarDmsCallback(const TXDmsResult_ *result, void *user_data) // DMS结果获取
{

    printf("-----callback start-----\n");
    printf("frame id: %lld\n", result->result_frame_id);
    printf("dms_status: %d\n", result->dms_status);
    printf("------callback end------\n");
}

int main(int argc, char **argv)
{
    int features = EA_ENV_ENABLE_CAVALRY | EA_ENV_ENABLE_VPROC | EA_ENV_ENABLE_NNCTRL;
    ea_env_open(features);
    long img_index = 0;
    g_dms_handle = TXDmsCreate(NULL, "./"); // 创建dms句柄
    if (g_dms_handle == 0)
    {
        printf("creat dms handle create fail\n");
        return -1;
    }
    char  version[50];
    TXDmsGetVersion(g_dms_handle,version); //获取版本号
    printf(" ver :%s", version);
    TXDmsRegistCallback(g_dms_handle, CalmcarDmsCallback, NULL); // 注册回调函数
    TXPoint2i left_top_point;
    TXPoint2i right_bottom_point;
    left_top_point.x = 0;
    left_top_point.y = 0;
    right_bottom_point.x = 1280;
    right_bottom_point.y = 720;
    
    TXDmsSetDriverRoi(g_dms_handle, &left_top_point, &right_bottom_point); //设置roi区
    cv::Mat image = cv::imread(argv[1], 0);  //读取图片
    cv::Mat image_re;
  
    cv::resize(image, image_re, cv::Size(1280, 720));
 

    size_t shape[4];
    shape[0]=1;
    shape[1]=1;
    shape[2]=image_re.size().height;
    shape[3]=image_re.size().width;
    ea_tensor_t* input_tensor=ea_tensor_new(EA_U8,shape,0);
    void* tensor_data=ea_tensor_data(input_tensor);
    memcpy(tensor_data,image_re.data,image_re.size().height*image_re.size().width);
    // ea_tensor_sync_cache(input_tensor,EA_CPU,EA_VP);
    while (1)
    {

        struct timeval tv;
        gettimeofday(&tv, NULL);
        long now_ts_start = tv.tv_sec * 1000 + tv.tv_usec / 1000;
        TXDmsSetInputByEaTensor(g_dms_handle, input_tensor,EA_CPU, img_index++, 71);
        gettimeofday(&tv, NULL);
        long now_ts_end = tv.tv_sec * 1000 + tv.tv_usec / 1000;
        std::cout << " ------------- long time : "<< now_ts_end - now_ts_start << std::endl;
        
        usleep(100* 1000);
    }

    TXDmsDestroy(g_dms_handle);
    ea_env_close();
    return 0;
}