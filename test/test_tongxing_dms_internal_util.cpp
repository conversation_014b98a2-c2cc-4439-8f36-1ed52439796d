/**
本文件为内部单元测试用例，通过json文件读取指定图片、时间戳、车速等信息，模拟测试功能逻辑;json配置如下：
{
    "image":"/home/<USER>/case1/1.jpg",
    "car_speed":74,
    "time_points":"1694404284218",
    "car_speed":45,
    "car_gear":2,
    "car_steer_whl_snsr_rad":0.0,
    "reset_drowsiness_warn":false,
    "reset_distraction_warn":false
}

*/
#include <stdio.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include "calmcar_dms_process.h"
#include "json.h"
#include "opencv2/opencv.hpp"
#include "tx_dms_sdk.h"

// long hDms = 0;
TXDmsResult dms_result;

tongxing::CcDmsProcess handle;

static std::string extractDirectoryName(const std::string& filePath) {
    std::istringstream pathStream(filePath);
    std::string item;
    std::vector<std::string> pathParts;

    while (std::getline(pathStream, item, '/')) {
        if (!item.empty()) {
            pathParts.push_back(item);
        }
    }

    if (pathParts.size() >= 2) {
        return pathParts[pathParts.size() - 2];
    } else {
        return "";
    }
}

static int InitSdk() {
    std::cout << "dms verson: " << TXDmsGetVersion() << std::endl;
    // hDms = TXDmsCreate(NULL, "cache/");
    int iRet = handle.Init(NULL, "cache/");
    if (iRet != 0) {
        std::cout << "creat dms fail" << std::endl;
        return -1;
    } else {
        std::cout << "creat dms success" << std::endl;
    }

    // TXPoint2i left_top_point;
    // TXPoint2i right_bottom_point;
    // left_top_point.x = 280;
    // left_top_point.y = 0;
    // right_bottom_point.x = 1000;
    // right_bottom_point.y = 720;
    // TXDmsSetDriverRoi(hDms, &left_top_point, &right_bottom_point);
    return 0;
}

static void RunSdk(
    std::string& image_path, int car_speed, int car_gear, float car_steer_whl_snsr_rad, long ts) {
    // cv::Mat image = cv::imread(root["image"].asString(), 0);  //读取图片;
    cv::Mat image = cv::imread(image_path, 0);

    TXImageInfo image_;
    cv::resize(image, image, cv::Size(1280, 720));
    image_.dataType = TXInputFormat::GRAY;
    image_.height = image.size().height;
    image_.width = image.size().width;
    image_.stride = image.size().width;
    image_.dataLen = image_.height * image_.width;
    image_.data = (char*)image.data;

    TXCarInfo carInfo = {0};
    carInfo.speed = car_speed;
    carInfo.gear = (TXGearPition)car_gear;
    carInfo.steer_whl_snsr_rad = car_steer_whl_snsr_rad;
    carInfo.mask =
        TX_CAR_INFO_MASK_SPEED | TX_CAR_INFO_MASK_STEER_WHL_SNSR | TX_CAR_INFO_MASK_GEARPOSITION;

    // TXDmsUpdataCarInfo(hDms, &carInfo);
    // TXDmsSetInput(hDms, &image_, &dms_result);
    handle.updateCarInfo(&carInfo);
    handle.SetInput(&image_, &dms_result, ts);
}

static void SaveResultToJsonFile(std::string& str, std::string ts) {
    Json::Value root;
    Json::Value dms_result_json;
    Json::Value warnInfo_json;
    dms_result_json["result_frame_id"] = dms_result.result_frame_id;
    dms_result_json["camera_status"] = dms_result.camera_status;
    dms_result_json["drowsiness_type"] = dms_result.drowsiness_status;
    dms_result_json["distraction_type"] = dms_result.distraction_status;
    dms_result_json["calibrate_status"] = dms_result.calibrate_status;
    dms_result_json["system_status"] = dms_result.system_status;
    {
        Json::Value face_info;
        face_info["score"] = dms_result.face_info.score;
        face_info["xmin"] = dms_result.face_info.xmin / 2;
        face_info["ymin"] = dms_result.face_info.ymin / 2;
        face_info["xmax"] = dms_result.face_info.xmax / 2;
        face_info["ymax"] = dms_result.face_info.ymax / 2;
        face_info["yaw"] = dms_result.face_info.head_yaw;
        face_info["pitch"] = dms_result.face_info.head_pitch;
        face_info["roll"] = dms_result.face_info.head_roll;
        face_info["isMask"] = dms_result.face_info.isMask;
        face_info["isGlass"] = dms_result.face_info.isGlass;
        face_info["isIRBlock"] = dms_result.face_info.isIRBlock;
        face_info["right_close_eye_score"] = dms_result.face_info.right_close_eye_score;
        face_info["left_close_eye_score"] = dms_result.face_info.left_close_eye_score;
        face_info["mouth_opening"] = dms_result.face_info.mouth_opening;
        Json::Value landmarks(Json::arrayValue);
        for (int i = 0; i < TX_MAX_FLD_SIZE; i++) {
            Json::Value point;
            point["x"] = dms_result.face_info.landmarks[i].x / 2;
            point["y"] = dms_result.face_info.landmarks[i].y / 2;
            landmarks[i] = point;
        }

        {
            Json::Value right_eye_landmark;
            right_eye_landmark["eye_score"] = dms_result.face_info.right_eye_landmark.eye_score;
            right_eye_landmark["eye_angle"] = dms_result.face_info.right_eye_landmark.eye_angle;
            right_eye_landmark["eye_center"] = Json::Value();
            right_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.right_eye_landmark.eye_center.x / 2;
            right_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.right_eye_landmark.eye_center.y / 2;
            right_eye_landmark["eye_size"] = Json::Value();
            right_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.right_eye_landmark.eye_size.width / 2;
            right_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.right_eye_landmark.eye_size.height / 2;
            right_eye_landmark["iris_score"] = dms_result.face_info.right_eye_landmark.iris_score;
            right_eye_landmark["iris_center"] = Json::Value();
            right_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.right_eye_landmark.iris_center.x / 2;
            right_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.right_eye_landmark.iris_center.y / 2;
            right_eye_landmark["iris_radius"] =
                dms_result.face_info.right_eye_landmark.iris_radius / 2;
            right_eye_landmark["pupil_score"] = dms_result.face_info.right_eye_landmark.pupil_score;
            right_eye_landmark["pupil_center"] = Json::Value();
            right_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.right_eye_landmark.pupil_center.x / 2;
            right_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.right_eye_landmark.pupil_center.y / 2;
            right_eye_landmark["pupil_radius"] =
                dms_result.face_info.right_eye_landmark.pupil_radius / 2;
            right_eye_landmark["yaw"] = dms_result.face_info.right_eye_landmark.yaw;
            right_eye_landmark["pitch"] = dms_result.face_info.right_eye_landmark.pitch;
            face_info["right_eye_landmark"] = right_eye_landmark;
            Json::Value left_eye_landmark;
            left_eye_landmark["eye_score"] = dms_result.face_info.left_eye_landmark.eye_score;
            left_eye_landmark["eye_angle"] = dms_result.face_info.left_eye_landmark.eye_angle;
            left_eye_landmark["eye_center"] = Json::Value();
            left_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.left_eye_landmark.eye_center.x / 2;
            left_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.left_eye_landmark.eye_center.y / 2;
            left_eye_landmark["eye_size"] = Json::Value();
            left_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.left_eye_landmark.eye_size.width / 2;
            left_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.left_eye_landmark.eye_size.height / 2;
            left_eye_landmark["iris_score"] = dms_result.face_info.left_eye_landmark.iris_score;
            left_eye_landmark["iris_center"] = Json::Value();
            left_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.left_eye_landmark.iris_center.x / 2;
            left_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.left_eye_landmark.iris_center.y / 2;
            left_eye_landmark["iris_radius"] =
                dms_result.face_info.left_eye_landmark.iris_radius / 2;
            left_eye_landmark["pupil_score"] = dms_result.face_info.left_eye_landmark.pupil_score;
            left_eye_landmark["pupil_center"] = Json::Value();
            left_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.left_eye_landmark.pupil_center.x / 2;
            left_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.left_eye_landmark.pupil_center.y / 2;
            left_eye_landmark["pupil_radius"] =
                dms_result.face_info.left_eye_landmark.pupil_radius / 2;
            left_eye_landmark["yaw"] = dms_result.face_info.left_eye_landmark.yaw;
            left_eye_landmark["pitch"] = dms_result.face_info.left_eye_landmark.pitch;
            face_info["left_eye_landmark"] = left_eye_landmark;
        }
        face_info["landmarks"] = landmarks;
        dms_result_json["face_info"] = face_info;
    }
    {
        warnInfo_json["eye1_60s_10p"] = dms_result.face_info.warnInfo.eye1_60s_10p;
        warnInfo_json["mouth1_120s_2"] = dms_result.face_info.warnInfo.mouth1_120s_2;
        warnInfo_json["mouth1_1l_1"] = dms_result.face_info.warnInfo.mouth1_1l_1;
        warnInfo_json["eye2_20s_1f5"] = dms_result.face_info.warnInfo.eye2_20s_1f5;
        warnInfo_json["eye2_2_0f75"] = dms_result.face_info.warnInfo.eye2_2_0f75;
        warnInfo_json["eye2_1l_0f75"] = dms_result.face_info.warnInfo.eye2_1l_0f75;
        warnInfo_json["eye2_60s_12p"] = dms_result.face_info.warnInfo.eye2_60s_12p;
        warnInfo_json["mouth2_1l_2"] = dms_result.face_info.warnInfo.mouth2_1l_2;
        warnInfo_json["mouth2_120s_3"] = dms_result.face_info.warnInfo.mouth2_120s_3;
        warnInfo_json["eye3_20s_2f4"] = dms_result.face_info.warnInfo.eye3_20s_2f4;
        warnInfo_json["eye3_2_1f2"] = dms_result.face_info.warnInfo.eye3_2_1f2;
        warnInfo_json["eye3_1l_1f2"] = dms_result.face_info.warnInfo.eye3_1l_1f2;
        warnInfo_json["repetition"] = dms_result.face_info.warnInfo.repetition;
    }
    root["ts"] = ts;
    root["dms_result"] = dms_result_json;
    root["warnInfo"] = warnInfo_json;
    root["sdk_version"] = TXDmsGetVersion();

    //写入json
    Json::StyledWriter writer;
    std::ofstream os;
    std::string json_path_root = "./output_json/" + extractDirectoryName(str) + "/";
    std::cout << json_path_root << std::endl;

    if ((access(json_path_root.c_str(), 0)) != -1) {
    } else {
        mkdir(json_path_root.c_str(), S_IRWXU);
    }

    std::string::size_type at_start = str.find_last_of("/");
    std::string::size_type at_end = str.find_last_of(".");
    std::string json_path = str.substr(at_start, at_end - at_start);
    std::string json_output_file = json_path_root + json_path + "_out" + ".json";

    os.open(json_output_file);
    os << writer.write(root);
    os.close();
}

int main(int argc, char* argv[]) {
    //判断参数信息是否满足
    if (argc != 2) {
        printf("Usage %s  case_list.txt \n", argv[0]);
        return -1;
    }

    auto txt_file = argv[1];

    std::ifstream readFile;
    readFile.open(txt_file, std::ios::in);

    if (!readFile.is_open()) {
        printf("Open File[%s] Failure!\n", txt_file);
        return -1;
    }

    if (InitSdk() != 0) {
        return -2;
    }

    std::string str;
    while (std::getline(readFile, str)) {
        std::cout << "json_file name: " << str << std::endl;
        Json::Reader json_reader;
        Json::Value root;

        std::ifstream infile(str, std::ios::in);
        if (!infile.is_open()) {
            std::cout << "Open config file failed!" << std::endl;
            continue;
        }

        if (!json_reader.parse(infile, root)) {
            std::cout << "Parse json config file failed!" << std::endl;
            return -1;
        }

        std::string image_path = root["image"].asString();
        int car_speed = root["car_speed"].asInt();
        int car_gear = root["car_gear"].asInt();
        float car_steer_whl_snsr_rad = root["car_steer_whl_snsr_rad"].asFloat();
        long ts = std::stoll(root["time_points"].asString());
        bool reset_drowsiness_warn = root["reset_drowsiness_warn"].asBool();
        bool reset_distraction_warn = root["reset_distraction_warn"].asBool();

        if (reset_drowsiness_warn)
            handle.RestAlarm();

        if (reset_distraction_warn)
            handle.RestDistractAlarm();

        //运行算法
        RunSdk(image_path, car_speed, car_gear, car_steer_whl_snsr_rad, ts);

        //保存结果
        SaveResultToJsonFile(str, root["time_points"].asString());
    }

    readFile.close();
    return 0;
}