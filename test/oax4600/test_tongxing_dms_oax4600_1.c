
#include <stdio.h>
#include "tx_dms_sdk.h"
#include "libovt.h"
int cnn_init(void);

#define WIDTH  1280
#define HEIGHT 720
/*
 * Sensor
 */
int vs_id = 5;
void sensor_init(void)
{
	int ret;

	ret = ovt_datapath_init(0);
	if (ret) {
		fprintf(stderr, "ovt_datapath_init(0) err %d\n", ret);
		return;
	}
	ret = ovt_datapath_start();
	assert(!ret);

	ret = ovt_vs_init(vs_id);
	assert(!ret);

	ret = ovt_vs_set_resolution(vs_id, (WIDTH << 16) | HEIGHT);
	assert(!ret);

	ret = ovt_vs_start(vs_id);
	assert(!ret);
}

void sensor_exit(void)
{
	ovt_vs_stop(vs_id);
	ovt_vs_exit(vs_id);
	ovt_datapath_stop();
	ovt_datapath_exit();
}



int main(int argc, char **argv){
    cnn_init();
    sensor_init();
    long hDms = TXDmsCreate(NULL, "cache/");

    TXImageInfo image_;
    image_.dataType = GRAY;
    image_.height = HEIGHT;
    image_.width = WIDTH;
    image_.stride=WIDTH;
    image_.dataLen=WIDTH*HEIGHT;
    
    while (1)
    {
        ovt_frame_t * frame = ovt_vs_get_frame(vs_id, 200);
		if (!frame) {
			fprintf(stderr, "ERROR: vs_get_frame(%d)\n", vs_id);
			continue;
		}

        image_.data = (char *)frame->addr;

        TXCarInfo carInfo={0};
        carInfo.speed=80;//设置车速
        carInfo.mask=TX_CAR_INFO_MASK_SPEED;//只更新车速
        TXDmsUpdataCarInfo(hDms,&carInfo);//更新can信息
        TXDmsResult dms_result;
        TXDmsSetInput(hDms, &image_,&dms_result);//传入图片

        printf("报警状态:%d \n",dms_result.drowsiness_status);//获取报警状态

        ovt_vs_remove_frame(vs_id, frame);
    }
    sensor_exit();
    return 0;
}