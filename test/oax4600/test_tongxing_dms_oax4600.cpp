
#include <stdio.h>
#include <csignal>
#include <fstream>
#include <iostream>
#include "calmcar_dms_process.h"
#include "cc_media_server.h"
#include "libovt.h"
#include "md5.h"
#include "opencv2/opencv.hpp"
#include "tx_dms_sdk.h"

extern "C" {
int cnn_init(void);
}
#define WIDTH 1280
#define HEIGHT 800
#define DEBUG_WITH_CAM 1
/*
 * Sensor
 */
int vs_id = 5;
void sensor_init(void) {
    int ret;

    ret = ovt_datapath_init(0);
    if (ret) {
        fprintf(stderr, "ovt_datapath_init(0) err %d\n", ret);
        return;
    }
    ret = ovt_datapath_start();
    assert(!ret);

    ret = ovt_vs_init(vs_id);
    assert(!ret);

    ret = ovt_vs_set_resolution(vs_id, (WIDTH << 16) | HEIGHT);
    assert(!ret);

    ret = ovt_vs_start(vs_id);
    assert(!ret);
}

void sensor_exit(void) {
    ovt_vs_stop(vs_id);
    ovt_vs_exit(vs_id);
    ovt_datapath_stop();
    ovt_datapath_exit();
}

std::atomic_bool isRunning(true);
void signalHandler(int signal) {
    if (signal == SIGINT) {
        std::cout << "Caught Ctrl + C, exiting gracefully..." << std::endl;
        sensor_exit();
        isRunning.store(false);
    }
}

// 构建DMS回调函数
long last_ts = 0;
TXDmsResult dms_result;

tongxing::CcDmsProcess handle;

static long get_timestamp() {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    long now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    return now_ts;
}

static int activate_test() {
    std::string ov4600_str;
    const char* filePath = "/proc/uid";
    std::ifstream file(filePath);

    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filePath << std::endl;
        return -1;
    }
    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();
    ov4600_str = content;

    std::string proid = "byd";
    std::string did = ov4600_str;
    // key是固定的，对应于特定的算法
    // 1. adas算法：96k018515hk；
    // 2. 脱敏算法：96k018522hk;
    // 3. dms算法：96k018516hk;
    std::string data_key = "96k018516hk";
    std::string uuid_str = data_key + did;
    std::string proid_str = "r7" + proid;
    // std::cout<< "uuid_str:" << uuid_str <<std::endl;
    std::string md5_result = MD5(uuid_str).toStr();
    // std::cout<< "md5_result:" << md5_result <<std::endl;
    std::string result = MD5(md5_result + proid_str).toStr();
    int activate_code_len = 32;
    int status = TXDmsActivate(result.c_str(), activate_code_len);

    return status;
}

int main(int argc, char** argv) {
    // 注册信号处理函数
    std::signal(SIGINT, signalHandler);
    // TXDmsSetLogLevel(LEVEL_DEBUG);
    cnn_init();
    sensor_init();
    long img_index = 0;
    int ret = 0;
#ifdef TX_STANDARD_NON_EU_TYPE
    ret = TXDmsSetStandardType(TX_STANDARD_NON_EU);
     if (ret != 0) {
        std::cout << "set Non EU standard type fail" << std::endl;
        return -1;
    } else {
        std::cout << "set Non EU standard type success" << std::endl;
    }
#endif
    TXStandardType type = TXDmsGetStandardType();
    std::string type_str = "";
    if (type == TX_STANDARD_EU)
        type_str = "EU";
    else if (type == TX_STANDARD_NON_EU)
        type_str = "NON_EU";

    int iRet = handle.Init(NULL, "cache/");
    std::cout << "dms verson: " << TXDmsGetVersion() << std::endl;
    std::cout << "dms really verson: "
              << handle.GetBuildType() + std::string("_") + TXDmsGetRealVersion() + "_" + type_str << std::endl;
    if (iRet != 0) {
        std::cout << "creat dms fail" << std::endl;
        return -1;
    } else {
        std::cout << "creat dms success" << std::endl;
    }

    // 激活test
    int status = activate_test();
    std::cout << "activate status:" << status << std::endl;

    TXImageInfo image_;
    image_.dataType = TXInputFormat::GRAY;
    image_.height = HEIGHT;
    image_.width = WIDTH;
    image_.stride = WIDTH;
    image_.dataLen = WIDTH * HEIGHT;

#if DEBUG_WITH_CAM

#else
    cv::Mat image = cv::imread("./test.jpg", 0);
    cv::resize(image, image, cv::Size(WIDTH, HEIGHT));
#endif

    while (isRunning.load()) {
        // 检查是否需要退出
        if (!isRunning.load()) {
            break;
        }
        ovt_frame_t* frame = nullptr;
#if DEBUG_WITH_CAM
        frame = ovt_vs_get_frame(vs_id, 200);
        if (!frame) {
            fprintf(stderr, "ERROR: vs_get_frame(%d)\n", vs_id);
            continue;
        }
        image_.data = (char*)frame->addr;
#else
        image_.data = image.data;
#endif

        TXCarInfo carInfo = {0};
        carInfo.speed = 80;
        carInfo.gear = TXGearPition::FORWARD;
        carInfo.steer_whl_snsr_rad = 0;
        carInfo.turn_light = TXTurnSignal::TURN_OFF;
        carInfo.driver_door_status = TXDriverDoorStatus::DOOR_CLOSE;
        carInfo.driver_seat_status = TXDriverSeatStatus::SEAT_STATIC;
        carInfo.mask = (TX_CAR_INFO_MASK_SPEED | TX_CAR_INFO_MASK_STEER_WHL_SNSR |
                        TX_CAR_INFO_MASK_GEARPOSITION | TX_CAR_INFO_MASK_TURN_LIGHT |
                        TX_CAR_DRIVER_DOOR | TX_CAR_DRIVER_SEAT);
        handle.updateCarInfo(&carInfo);

        double start_ts = get_timestamp();
        handle.SetInput(&image_, &dms_result);
        double end_ts = get_timestamp();
        printf("TXDmsSetInput ts:%f\n", (float)(end_ts - start_ts));

#if defined DEBUG_WITH_CAM
        ovt_vs_remove_frame(vs_id, frame);
#endif
    }
    sensor_exit();
    return 0;
}