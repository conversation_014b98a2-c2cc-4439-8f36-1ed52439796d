#include "libovt.h"

#define REG_SCRM_ADDR (0xa0000000u)
#define REG_SCRM_SIZE (0x102fffffu)

__attribute__((visibility("default"))) uint32_t cnn_read_reg32(uint64_t address) {
    if (address >= REG_SCRM_ADDR && (address <= (REG_SCRM_ADDR + REG_SCRM_SIZE))) {
        //Assume physical address of registers
        uintptr_t virt_address = ovt_phys_to_virt((uint32_t)address);
        if (virt_address != 0)
            return *(volatile uint32_t*)virt_address;

        return 0;
    }
    //Virtual address
    return *(volatile uint32_t*)(address);
}

__attribute__((visibility("default"))) uint16_t cnn_read_reg16(uint64_t address) {
    if (address >= REG_SCRM_ADDR && (address <= (REG_SCRM_ADDR + REG_SCRM_SIZE))) {
        //Assume physical address of registers
        uintptr_t virt_address = ovt_phys_to_virt((uint32_t)address);
        if (virt_address != 0)
            return *(volatile uint16_t*)virt_address;

        return 0;
    }
    //Virtual address
    return *(volatile uint16_t*)(address);
}

__attribute__((visibility("default"))) uint8_t cnn_read_reg8(uint64_t address) {
    if (address >= REG_SCRM_ADDR && (address <= (REG_SCRM_ADDR + REG_SCRM_SIZE))) {
        //Assume physical address of registers
        uintptr_t virt_address = ovt_phys_to_virt((uint32_t)address);
        if (virt_address != 0)
            return *(volatile uint8_t*)virt_address;

        return 0;
    }
    //Virtual address
    return *(volatile uint8_t*)(address);
}

__attribute__((visibility("default"))) void cnn_write_reg32(uint64_t address, uint32_t data) {
    if (address >= REG_SCRM_ADDR && (address <= (REG_SCRM_ADDR + REG_SCRM_SIZE))) {
        //Assume physical address of registers
        uintptr_t virt_address = ovt_phys_to_virt((uint32_t)address);
        if (virt_address != 0)
            *(volatile uint32_t*)virt_address = data;
        return;
    }
    //Virtual address
    *(volatile uint32_t*)(address) = data;
}

__attribute__((visibility("default"))) void cnn_write_reg16(uint64_t address, uint16_t data) {
    if (address >= REG_SCRM_ADDR && (address <= (REG_SCRM_ADDR + REG_SCRM_SIZE))) {
        //Assume physical address of registers
        uintptr_t virt_address = ovt_phys_to_virt((uint32_t)address);
        if (virt_address != 0)
            *(volatile uint16_t*)virt_address = data;
        return;
    }
    //Virtual address
    *(volatile uint16_t*)(address) = data;
}

__attribute__((visibility("default"))) void cnn_write_reg8(uint64_t address, uint8_t data) {
    if (address >= REG_SCRM_ADDR && (address <= (REG_SCRM_ADDR + REG_SCRM_SIZE))) {
        //Assume physical address of registers
        uintptr_t virt_address = ovt_phys_to_virt((uint32_t)address);
        if (virt_address != 0)
            *(volatile uint8_t*)virt_address = data;
        return;
    }
    //Virtual address
    *(volatile uint8_t*)(address) = data;
}

__attribute__((visibility("default"))) void cnn_write_reg32_bulk(volatile uint32_t* start_addr,
                                                                 uint32_t* data,
                                                                 int count) {
    uint32_t address = (uintptr_t)start_addr;
    int i;

    if (address >= REG_SCRM_ADDR && (address <= (REG_SCRM_ADDR + REG_SCRM_SIZE))) {
        //Assume physical address of registers
        uintptr_t virt_address = ovt_phys_to_virt(address);

        if (virt_address != 0) {
            start_addr = (volatile uint32_t*)virt_address;
            // printf("%s[%d]: %p %p\n", __FUNCTION__, __LINE__, (uint32_t)address, start_addr);
            for (i = 0; i < count; i++) {
                start_addr[i] = data[i];
            }
        }
        return;
    }
    //Virtual address
    for (i = 0; i < count; i++)
        start_addr[i] = data[i];
}
