/****************************************************************************
 *
 * Copyright (c) 2018 OmniVision Technologies, Inc.
 * The material in this file is subject to copyright. It may not
 * be used, copied or transferred by any means without the prior written
 * approval of OmniVision Technologies, Inc.
 *
 * OMNIVISION TECHNOLOGIES, INC. DISCLAIMS ALL WARRANTIES WITH REGARD TO
 * THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
 * FITNESS, IN NO EVENT SHALL OMNIVISION TECHNOLOGIES, INC. BE LIABLE FOR
 * ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER
 * RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF
 * CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
 * CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 *
 ****************************************************************************/
/**
 @file      cnn.c
 @brief     CNN library integration code.
 @version   0.6
 @date      16-Mar-22
 */
#include "libovt.h"
#include "libcnnapi.h"

//#define DEBUG_CUSTOM_HEAP	//Debug custom heap

static uint32_t profile_en = 0;
static volatile int cnninit_cnt = 0;
static void * custom_heap_base_addr = NULL;
static size_t custom_heap_size = 0x1400000; //default custom heap size

// Declaring mutex
static pthread_mutex_t reset_lock = PTHREAD_MUTEX_INITIALIZER;

// variables used for libcnn/npu profiling in profile/fps mode
static struct timespec node_time_start;
static struct timespec frame_time_start;

/* Integrator can define the relevant API using compile switch. */
#ifndef NPU_CRITICAL_SECTION_ENTER
#define NPU_CRITICAL_SECTION_ENTER() pthread_mutex_lock(&reset_lock)
#endif

/* Integrator can define the relevant API using compile switch. */
#ifndef NPU_CRITICAL_SECTION_EXIT
#define NPU_CRITICAL_SECTION_EXIT() pthread_mutex_unlock(&reset_lock)
#endif

#define OSAL_START_NPU_RST 1

#define OSAL_POLL_APPLICATION 0

//==============================================================================
//API from libcnn_api
//==============================================================================

#define CORE_RST (1 << 0)
#define CFG_RST  (1 << 1)

#define PLATFORM_AB1_FPGA_MPCORE (1 << 3) //0x8: AB1 FPGA MPCore for NTO.
#define PLATFORM                 PLATFORM_AB1_FPGA_MPCORE

/*********************************************
	NPU REG
*********************************************/
#define WriteReg32(a, d) cnn_write_reg32(a, d)
#define ReadReg32(a)     cnn_read_reg32(a)

#undef NPU_BASE_ADDR
#define NPU_BASE_ADDR       0xA0204000
#define _FIELD_(a, v, l, o) WriteReg32((a), (ReadReg32(a) & ~((((1ULL << (l)) - 1) << (o)))) | (((v) & ((1ULL << (l)) - 1)) << (o)))
#undef REG_NPU_CTL
#define REG_NPU_CTL (NPU_BASE_ADDR + 0xC)

#if CONFIG_WFI_MODE
// By default use out-of-box WFI API provided by libnpu library. If the below macro
// is set to 1, then use the application provided WFI API.
#define OSAL_WFI_APPLICATION 1

// npu_wfi_mode variable is defined as a weak symbol in libnpu and is used to control whether to run in WFI mode for last pass
// else would be running in all polling mode.
int npu_wfi_mode = 1;
#endif

//==============================================================================
//Custom Heap with Buffer from MPU RTOS
//==============================================================================
#if OSAL_POLL_APPLICATION
#define OSAL_DEBUG_NOP       0
#define REG_XNPU_IRQ_STATUS  (NPU_BASE_ADDR + 0x0018)
#define RD_XNPU_IRQ_STATUS() ReadReg32(REG_XNPU_IRQ_STATUS)

#define XNPU_IRQ_STATUS_LAYER_DONE_LEN    1
#define XNPU_IRQ_STATUS_LAYER_DONE_OFS    0
#define XNPU_IRQ_STATUS_LAYER_DONE(value) _FIELD_(REG_XNPU_IRQ_STATUS, value, XNPU_IRQ_STATUS_LAYER_DONE_LEN, XNPU_IRQ_STATUS_LAYER_DONE_OFS)
#define XNPU_IRQ_STATUS_LAYER_DONE_BIT    (1 << XNPU_IRQ_STATUS_LAYER_DONE_OFS)

#define XNPU_IRQ_STATUS_CMAC_OVERFLOW_OCCUR_LEN    1
#define XNPU_IRQ_STATUS_CMAC_OVERFLOW_OCCUR_OFS    3
#define XNPU_IRQ_STATUS_CMAC_OVERFLOW_OCCUR(value) _FIELD_(REG_XNPU_IRQ_STATUS, value, XNPU_IRQ_STATUS_CMAC_OVERFLOW_OCCUR_LEN, XNPU_IRQ_STATUS_CMAC_OVERFLOW_OCCUR_OFS)
#define XNPU_IRQ_STATUS_CMAC_OVERFLOW_OCCUR_BIT    (1 << XNPU_IRQ_STATUS_CMAC_OVERFLOW_OCCUR_OFS)

void _osal_poll(void)
{
	//    printf("%s print from application\n", __FUNCTION__);

#if !OSAL_DEBUG_NOP
	volatile uint32_t irq_status = 0x0;
	do {
		irq_status = RD_XNPU_IRQ_STATUS();
		if (irq_status & XNPU_IRQ_STATUS_LAYER_DONE_BIT) {
			/* Clear IRQ status. */
			XNPU_IRQ_STATUS_LAYER_DONE(1);
			if (irq_status & XNPU_IRQ_STATUS_CMAC_OVERFLOW_OCCUR_BIT) {
				// check if there is any cmac overflow.
				syslog(LOG_ERR, "NPU_IRQ_STATUS_CMAC_OVERFLOW_OCCUR_BIT, irq_status=0x%x\n", irq_status);
			}
			break;
		}
#if OSAL_IRQ_TIMEOUT
		if (OSAL_STATE_TIMEOUT == g_npu_done)
			break;
#endif
	} while (1);
#endif // !OSAL_DEBUG_NOP

#if OSAL_IRQ_TIMEOUT
	g_timeout = -1; // Disable timeout counter.
#endif

#if FIXME_USE_G_NPU_DONE_VARIABLE
	if (OSAL_STATE_BUSY == g_npu_done)
		g_npu_done = OSAL_STATE_DONE;
#endif
}
#endif

#if OSAL_START_NPU_RST
#define REG_XNPU_IRQ_STATUS (NPU_BASE_ADDR + 0x0018)
#define REG_XNPU_ENA        (NPU_BASE_ADDR + 0x0010)
#define REG_XNPU_IRQ_MASK   (NPU_BASE_ADDR + 0x0014)
#define REG_XNPU_MISC_CFG0  (NPU_BASE_ADDR + 0x02e8)

#define XNPU_ENA_ENABLE_LEN    1
#define XNPU_ENA_ENABLE_OFS    0
#define XNPU_ENA_ENABLE(value) _FIELD_(REG_XNPU_ENA, value, XNPU_ENA_ENABLE_LEN, XNPU_ENA_ENABLE_OFS)

#define XNPU_IRQ_MASK_LAYER_DONE_LEN    1
#define XNPU_IRQ_MASK_LAYER_DONE_OFS    0
#define XNPU_IRQ_MASK_LAYER_DONE(value) _FIELD_(REG_XNPU_IRQ_MASK, value, XNPU_IRQ_MASK_LAYER_DONE_LEN, XNPU_IRQ_MASK_LAYER_DONE_OFS)

#define XNPU_MISC_CFG0_FORCE_NPU_CG_EN_LEN    1
#define XNPU_MISC_CFG0_FORCE_NPU_CG_EN_OFS    24
#define XNPU_MISC_CFG0_FORCE_NPU_CG_EN(value) _FIELD_(REG_XNPU_MISC_CFG0, value, XNPU_MISC_CFG0_FORCE_NPU_CG_EN_LEN, XNPU_MISC_CFG0_FORCE_NPU_CG_EN_OFS)

#define RD_XNPU_CTL() ReadReg32(REG_NPU_CTL)

#define XNPU_CTL_LAYER_START_LEN    1
#define XNPU_CTL_LAYER_START_OFS    2
#define XNPU_CTL_LAYER_START(value) _FIELD_(REG_NPU_CTL, value, XNPU_CTL_LAYER_START_LEN, XNPU_CTL_LAYER_START_OFS)
#define XNPU_CTL_LAYER_START_BIT    (1 << XNPU_CTL_LAYER_START_OFS)

#define VA_DDR_MERGE_BASE_ADDR 0xa0202000
#define FIXME_RESET_DDRC_PORTS 1 ///< DDRC port needs to be held in reset before reset NPU

static void _osal_ddrc_rst_assert(void)
{
#if FIXME_RESET_DDRC_PORTS
	for (int i = 0; i < 5; i++) {
		WriteReg32((VA_DDR_MERGE_BASE_ADDR + 0x160 + (0x4 * i)), ReadReg32((VA_DDR_MERGE_BASE_ADDR + 0x160 + (0x4 * i))) | (1 << 9));
	}
#endif
}

static void _osal_ddrc_rst_deassert(void)
{
#if FIXME_RESET_DDRC_PORTS
	for (int i = 0; i < 5; i++) {
		WriteReg32((VA_DDR_MERGE_BASE_ADDR + 0x160 + (0x4 * i)), ReadReg32((VA_DDR_MERGE_BASE_ADDR + 0x160 + (0x4 * i))) & ~(1 << 9));
	}
#endif
}

#define PHY_WIDTH                                                32
#define MERGER2_PROF_RP_STA0_ADDR(__BASE_ADDR__, __REG_OFFSET__) (__BASE_ADDR__ + 0x50 + (__REG_OFFSET__ * 0x4))
#define MERGER2_PROF_WP_STA0_ADDR(__BASE_ADDR__, __REG_OFFSET__) (__BASE_ADDR__ + 0xd0 + (__REG_OFFSET__ * 0x4))
#define LINUX_APP_SHARED_MEMORY                                  (0x90038000 + 0x7c40) //put bw_bytes (4bytes*5ports)

static int _cnn_ddrc_map_merger2_rd_port_id(int id)
{
	int rd_id = 0, tmp_id;

	//assigning  read port id's
	int va_ddr_rd_port_id[5] = { 0, 1, 2, 3, -1 };

	tmp_id = id;
	rd_id = va_ddr_rd_port_id[tmp_id];

	return rd_id;
}

static int _cnn_ddrc_map_merger2_wr_port_id(int id)
{
	int wr_id = 0, tmp_id;

	int va_ddr_wr_port_id[5] = { -1, -1, -1, -1, 0 };

	tmp_id = id;
	wr_id = va_ddr_wr_port_id[tmp_id];

	return wr_id;
}

static void _cnn_ddrc_prof_get(void)
{
	uint32_t clk_cnt, wp_req_cnt, rp_req_cnt;
	uint64_t rp_bw = 0, wp_bw = 0, bw = 0;
	uint64_t bw_val = 0;
	clk_cnt = ovt_read32(VA_DDR_MERGE_BASE_ADDR + 0x48);
	int rd_id, wr_id;
	int id;

	if (clk_cnt != 0) {
		for (id = 0; id < 5; id++) {
			rd_id = _cnn_ddrc_map_merger2_rd_port_id(id);
			if (rd_id == -1) {
				rp_bw = 0; //in bytes
			} else {
				rp_req_cnt = ovt_read32(MERGER2_PROF_RP_STA0_ADDR(VA_DDR_MERGE_BASE_ADDR, rd_id));
				rp_bw = rp_req_cnt * PHY_WIDTH; //in bytes
			}
			wr_id = _cnn_ddrc_map_merger2_wr_port_id(id);

			if (wr_id == -1) {
				wp_bw = 0; //in bytes
			} else {
				wp_req_cnt = ovt_read32(MERGER2_PROF_WP_STA0_ADDR(VA_DDR_MERGE_BASE_ADDR, wr_id));
				wp_bw = wp_req_cnt * PHY_WIDTH; //in bytes
			}
			bw = rp_bw + wp_bw;
			bw_val = ((uint64_t)ovt_read32(LINUX_APP_SHARED_MEMORY + id * 8 + 4) << 32) + ovt_read32(LINUX_APP_SHARED_MEMORY + id * 8);
			bw_val += bw;
			ovt_write32(LINUX_APP_SHARED_MEMORY + id * 8, bw_val & 0xffffffff);
			ovt_write32(LINUX_APP_SHARED_MEMORY + id * 8 + 4, (bw_val >> 32));
		}
	}
}

// Note that the API name must be _osal_wfi as this function is defined as a weak
// symbol in libnpu and if OSAL_START_NPU_RST is 1 this below API is used to
// override libnpu's _osal_npu_rst and _osal_start. Else by default use out-of-box API provided by libnpu library.
void _osal_npu_rst(int soft)
{
	//    printf("%s print from application\n", __FUNCTION__);

	/* Please enter critical section here; it is important this
     * sequence of events is atomic. */
	NPU_CRITICAL_SECTION_ENTER();

	_cnn_ddrc_prof_get();

	/* Per ICD, we need to reset DDRC port before resetting NPU to avoid
     * hanging the interface, which is unrecoverable at system level. */
	_osal_ddrc_rst_assert();

#define VA_SCRM_RST_EN_0_ADDR(__BASE_ADDR__) (__BASE_ADDR__ + 0x70)
#define VA_SCRM_BASE_ADDR                    (0xa0201000)
	//for 8600 BIT 6: npu_rst_n, BIT7: npu_bus_rst_n, BIT 14: npu_grfc_sram_rst_n, BIT 16: npu_grfc_rst_n
	//for 4600 [1] npu_rst_n (NS) [2] npu_bus_rst_n (NS) [3] npu_grfc_rst_n (NS) [4] npu_grfc_sram_rst_n (NS) [5] npu_sga_rst_n (NS)
	if (!soft) {
		WriteReg32(VA_SCRM_RST_EN_0_ADDR(VA_SCRM_BASE_ADDR), (ReadReg32(VA_SCRM_RST_EN_0_ADDR(VA_SCRM_BASE_ADDR)) & ~(0x3 << 1) & ~(0x1 << 4) & ~(0x1 << 3)));
		WriteReg32(VA_SCRM_RST_EN_0_ADDR(VA_SCRM_BASE_ADDR), (ReadReg32(VA_SCRM_RST_EN_0_ADDR(VA_SCRM_BASE_ADDR)) | (0x3 << 1) | (0x1 << 4) | (0x1 << 3)));
	} else {
		WriteReg32(VA_SCRM_RST_EN_0_ADDR(VA_SCRM_BASE_ADDR), (ReadReg32(VA_SCRM_RST_EN_0_ADDR(VA_SCRM_BASE_ADDR)) & ~(0x1 << 1) & ~(0x1 << 4) & ~(0x1 << 3)));
		WriteReg32(VA_SCRM_RST_EN_0_ADDR(VA_SCRM_BASE_ADDR), (ReadReg32(VA_SCRM_RST_EN_0_ADDR(VA_SCRM_BASE_ADDR)) | (0x1 << 1) | (0x1 << 4) | (0x1 << 3)));
	}
	/* Clear IRQ status. */
	WriteReg32(REG_XNPU_IRQ_STATUS, -1);
	XNPU_ENA_ENABLE(1);

	/* Note that we only release the RIU from reset, while keeping
     * NPU core and NPU's DDRC ports in reset state.
     *
     * This is needed to avoid race condition in NPU core when NPU is being programmed
     * (by holding the core in reset state while the RIU is being updated),
     * as some internal blocks are not gated by NPU_CTL_LAYER_START_BIT.
     */

	NPU_CRITICAL_SECTION_EXIT();
}

void _osal_start(int use_wfi)
{
	//    printf("%s print from application\n", __FUNCTION__);

	/* Please enter critical section here; it is important this
     * sequence of events is atomic. */
	NPU_CRITICAL_SECTION_ENTER();

#if FIXME_USE_G_NPU_DONE_VARIABLE
	/* This flag should be asserted by ISR. */
	g_npu_done = OSAL_STATE_BUSY;
#endif

#if OSAL_IRQ_TIMEOUT
	g_timeout = 0;
#endif

#if OSAL_DEBUG_HANG
	_osal_backup_reg();
#endif

#ifndef BUILD_FOR_POLLING
	/* Unmask for IRQ mode. */
	if (use_wfi)
		XNPU_IRQ_MASK_LAYER_DONE(0);
#endif

	/* Per ICD, we need to reset DDRC port before resetting NPU to avoid
     * hanging the interface, which is unrecoverable at system level. */
	_osal_ddrc_rst_deassert();

#if !OSAL_DEBUG_NOP

	/* Force CG cell's enable always 1'b1.
    * This disable NPU core clock gating by layer_done;
     * needed for FSM to continue after layer_done is asserted. */
	XNPU_MISC_CFG0_FORCE_NPU_CG_EN(1);

	XNPU_CTL_LAYER_START(1);
	while ((RD_XNPU_CTL() & XNPU_CTL_LAYER_START_BIT))
		;
#endif // !OSAL_DEBUG_NOP

	NPU_CRITICAL_SECTION_EXIT();
}
#endif

#if OSAL_WFI_APPLICATION
// Note that the API name must be _osal_wfi as this function is defined as a weak
// symbol in libnpu and if OSAL_WFI_APPLICATION is 1 this below API is used to
// override libnpu's _osal_wfi. Else by default use out-of-box WFI API provided by libnpu library.
void _osal_wfi(int fd)
{
	//    printf("print from application\n");
	//Wait NPU interrupt with 200ms timeout
	if (ovt_npu_wait_irq(200) == 0) {
		//_osal_ddrc_rd_check2("cnn_wfi");
		//WriteReg32(REG_NPU_MISC_CFG0, ReadReg32(REG_NPU_MISC_CFG0) & ~FORCE_NPU_CG_ENABLE);
		return;
	}
	//Error
	printf("ovt_wait_npu_irq failed\n");
}
#endif // OSAL_WFI_APPLICATION

// _ovm_node_start, _ovm_node_end, _ovm_frame_start and _ovm_frame_end are strong
// symbols overriding the weak symbols from the library.

// _ovm_frame_start is called per frame at the start
// _ovm_frame_end is called per frame at the end
// _ovm_node_start is called per node/layer of the OVM/model at the start
// _ovm_node_end is called per node/layer of the OVM/model at the end
void * _ovm_node_start(void)
{
	// printf("%s from application\n", __FUNCTION__);
	memset(&node_time_start, 0, sizeof(struct timespec));

	void * time_start = &node_time_start;
	if (time_start) {
		clock_gettime(CLOCK_MONOTONIC_RAW, &node_time_start);
	}
	return time_start;
}

double _ovm_node_end(void * nstart, int32_t node_id)
{
	// printf("%s from application\n", __FUNCTION__);
	double node_time = 0.0;
	if (nstart) {
		struct timespec time_start = *((struct timespec *)nstart);
		struct timespec time_end;
		clock_gettime(CLOCK_MONOTONIC_RAW, &time_end);
		node_time = (time_end.tv_sec - time_start.tv_sec) + (double)(time_end.tv_nsec - time_start.tv_nsec) / 1000000000;
	}
	return node_time;
}

void * _ovm_frame_start(void)
{
	// printf("%s from application\n", __FUNCTION__);
	memset(&frame_time_start, 0, sizeof(struct timespec));

	void * time_start = &frame_time_start;
	if (time_start) {
		clock_gettime(CLOCK_MONOTONIC_RAW, &frame_time_start);
	}
	return time_start;
}

typedef struct ovm_s ovm_t;
double _ovm_frame_end(ovm_t * ovm, void * fstart, int32_t is_dummy)
{
	// printf("%s from application\n", __FUNCTION__);
	double frame_time = 0.0;
	if (fstart) {
		struct timespec frame_start = *((struct timespec *)fstart);
		struct timespec frame_end;
		clock_gettime(CLOCK_MONOTONIC_RAW, &frame_end);
		frame_time = (frame_end.tv_sec - frame_start.tv_sec) + (double)(frame_end.tv_nsec - frame_start.tv_nsec) / 1000000000;
	}
	return frame_time;
}

static ovt_sharedmutex_t cnn_smutex;

void cnn_mutex_lock(void)
{
	(void)ovt_sharedmutex_lock(cnn_smutex);
}

void cnn_mutex_unlock(void)
{
	(void)ovt_sharedmutex_unlock(cnn_smutex);
}

#if 0
static void sigint_handler(int sn)
{
	if(is_locked){
		cnn_unlock();
	}

 	mptr->num--;
	if(mptr->num == 0){
		shm_unlink(CNN_IPC_SHM_MUTEX);
	}
	exit(0);
}
#endif

int cnn_init(void)
{
	int ret = -1;
	char * env_str;

	env_str = ovt_env_get("cnn_heap_size");
	if (env_str) {
		custom_heap_size = strtol(env_str, NULL, 0);
		custom_heap_size &= ~0xfffff; //1M aligned
	}
	NPU_CRITICAL_SECTION_ENTER();
	if (cnninit_cnt > 0) {
		goto initok;
	}

	cnn_smutex = ovt_sharedmutex_create("cnn_smutex");
	if (cnn_smutex == NULL) {
		goto initerr;
	}

	//Use custom heap
	if (!custom_heap_base_addr) {
		custom_heap_base_addr = ovt_cma_alloc(custom_heap_size);
		if (!custom_heap_base_addr) {
			ovt_err(OVT_LOGGER, "\n%s: custom heap alloc %zu err!\n", __func__, custom_heap_size);
			ret = -1;
			goto initerr;
		}

		//		printf("\nGet custom_heap_base_addr 0x%zx@0x%p\n",
		//			custom_heap_size,custom_heap_base_addr);
	}

	//	printf("### clockset_npu done\n");

	profile_en = 0;
#if DEBUG_LEVEL == 4
	// FW emulation with max verbosity
	cnn_set_debug(10);
#elif DEBUG_LEVEL == 3
	// Debugging with max verbosity only
	cnn_set_debug(8);
#elif DEBUG_LEVEL == 2
	// Debugging with max verbosity for libnpu only
	cnn_set_debug(4);
#elif DEBUG_LEVEL == 1
	// Only flag warning and error
	cnn_set_debug(3);
#else
	// Most quiet; only libcnn warning and error
	cnn_set_debug(0);
#endif

	/* Register infrastructure callbacks. */
	cnn_printf_register(printf);
	//cnn_printf_register(NULL);

	// Below is to print error messages when running in deploy mode.
	cnn_logger_register(printf);

	ret = cnn_custom_heap(custom_heap_base_addr, custom_heap_size);
	if (ret) {
		goto initerr;
	}

	cnn_dc_register(ovt_dc_clean_all, ovt_dc_clean_range, ovt_dc_invalidate_all, ovt_dc_invalidate_range);

initok:
	cnninit_cnt++;
	NPU_CRITICAL_SECTION_EXIT();
	return 0;

initerr:
	NPU_CRITICAL_SECTION_EXIT();
	return ret;
}

int cnn_exit(void)
{
	NPU_CRITICAL_SECTION_ENTER();
	cnninit_cnt--;
	if (cnninit_cnt > 0) {
		NPU_CRITICAL_SECTION_EXIT();
		return 0;
	}

	//Clean up custom heap
	cnn_custom_heap(NULL, 0);
	if (custom_heap_base_addr) {
		ovt_cma_free(custom_heap_base_addr);
		custom_heap_base_addr = NULL;
	}

	NPU_CRITICAL_SECTION_EXIT();

	(void)ovt_sharedmutex_delete(cnn_smutex);

	return 0;
}

int cnn_set_profile(uint32_t en)
{
	profile_en = en;
	return 0;
}

void * cnn_get_ovmbuf(uint32_t size)
{
	void * ret;
	if (size < 4 * 1024) {
		size = 4 * 1024; //make sure the return address is page aligned
	}
	ret = ovt_cma_alloc(size); //uncached

	return ret;
}

void cnn_free_ovmbuf(void * buf)
{
	ovt_cma_free(buf);
}

void cnn_get_ver(const char ** cnn, const char ** npu, const char ** fw)
{
	static const char * cnn_ver = CNN_REVISION_S;
	static const char * npu_ver = NN_REVISION_S;
	static const char * fw_ver = FW_REVISION_S;

	if (cnn && *cnn)
		*cnn = cnn_ver;

	if (npu && *npu)
		*npu = npu_ver;

	if (fw && *fw)
		*fw = fw_ver;
}

static void _cnn_ddrc_clear_merger2_all_port_prof(uint32_t base)
{
	ovt_write32(base + 0x3c, 0xFFFFFFFF);
	ovt_write32(base + 0x44, 0xFFFFFFFF);
	ovt_write32(base + 0x34, 0x00000003);

	ovt_write32(base + 0x3c, 0x00000000);
	ovt_write32(base + 0x44, 0x00000000);
	ovt_write32(base + 0x34, 0x0);
}

static void _cnn_ddrc_enable_merger2_all_port_prof(uint32_t base)
{
	_cnn_ddrc_clear_merger2_all_port_prof(base);

	ovt_write32(base + 0x38, 0xFFFFFFFF);
	ovt_write32(base + 0x40, 0xFFFFFFFF);
	ovt_write32(base + 0x34, 0x00000001);
}

static void _cnn_ddrc_disable_merger2_all_port_prof(uint32_t base)
{
	ovt_write32(base + 0x38, 0x0);
	ovt_write32(base + 0x40, 0x0);
	ovt_write32(base + 0x34, 0x0);
}

void cnn_ddrc_start_prof(uint64_t ports)
{
	int i;
	uint32_t base = LINUX_APP_SHARED_MEMORY;

	if (((ports >> 58) & 0x3) != 0) {
		for (i = 0; i < 5; i++) {
			ovt_write32(base + i * 8, 0x0);
			ovt_write32(base + i * 8 + 4, 0x0);
		}
		_cnn_ddrc_enable_merger2_all_port_prof(VA_DDR_MERGE_BASE_ADDR);
	}
}

void cnn_ddrc_get_prof(cnn_ddrc_prof_result_t * prof)
{
	int i;
	uint64_t clk_cnt, pd;
	uint32_t base = LINUX_APP_SHARED_MEMORY;

	_cnn_ddrc_disable_merger2_all_port_prof(VA_DDR_MERGE_BASE_ADDR);

	clk_cnt = ovt_read32(VA_DDR_MERGE_BASE_ADDR + 0x48);

	prof->total_bw_kbytes_per_sec = 0;

	if (clk_cnt) {
		pd = (2 * clk_cnt) / prof->ddr_freq;
		for (i = 0; i < 5; i++) {
			prof->res[i].bw_bytes = (((uint64_t)ovt_read32(base + i * 8 + 4) << 32) + ovt_read32(base + i * 8));
			prof->res[i].bw_kbytes_per_sec = (prof->res[i].bw_bytes * 1000) / pd;
			prof->total_bw_kbytes_per_sec += prof->res[i].bw_kbytes_per_sec;
		}
	}

	_cnn_ddrc_clear_merger2_all_port_prof(VA_DDR_MERGE_BASE_ADDR);
}
