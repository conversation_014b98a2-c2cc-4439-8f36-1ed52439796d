#include <iostream>
#include "json.h"
#include "cc_media_server.h"
#include "tx_dms_sdk.h"
#include "cc_module.h"
#include "libovt.h"
extern "C"{
    #include "libcnnapi.h"
}
#include "cc_resource_register.h"
#include<fstream>
#include "opencv2/opencv.hpp"
#include "inference_result.pb.h"
#include <fstream>
#include <filesystem>
//OVM_ALIGN(a) 的目的是将指针 a 对齐到 256 字节的边界。如果地址 a 已经对齐到 256 字节边界，它不会变化。如果没有对齐，它将通过加上 255 并清除低 8 位来使地址对齐。
#define OVM_ALIGN(a) ((void *)((((uintptr_t)(a)) + (256 - 1)) & ~(256 - 1)))

float norm=255.0;//标准化因子，默认为 255.0
float std_json[3]={1.0,1.0,1.0};//SON 标准化参数的数组，初始值为 {0, 0, 0}
float mean[3]={0,0,0};//均值调整参数的数组，初始值为 {1, 1, 1}
std::string net_name;//存储网络模型文件的名称
void* ovm_data_ptr=NULL;//存储加载的模型数据的指针，初始化为 NULL
size_t ovm_model_size=0;//模型数据的大小，初始化为 0
void* hdl=NULL;//存储模型加载后的句柄，初始化为 NULL
int32_t*  tid;//存储模型输出的 ID
float * output_f[64];//: 存储模型输出的指针数组，最多 64 个输出
size_t element_count[64];//存储每个输出的元素计数
size_t output_count=0;//存储输出的数量，初始化为 0
std::vector<std::shared_ptr<tongxing::NumArray>> input_;//存储输入数据的向量
std::vector<std::shared_ptr<tongxing::NumArray>> output_;//存储输出数据的向量
// 用于存储序列化后的推理结果
std::string serialized_result;
// 模型句柄类型（请根据您的实际实现修改）
using ModelHandle = void*; // 假设模型句柄是一个指针类型
std::unordered_map<std::string, ModelHandle> model_handles; // 存储模型句柄
std::unordered_map<std::string, int> model_output;//存储模型输出张量
std::unordered_map<std::string, int32_t*> model_tid_map;
std::unordered_map<std::string, std::string> model_paths; // 存储模型路径
bool flag_init = false; // 标记模型是否已初始化
std::string current_model_path; // 当前模型路
std::string model_path;

extern "C" {
    int cnn_init(void);
    int cnn_exit(void);
}
 
static FILE * f_ini(const char * fname, size_t * size)
{
    FILE * f;
    f = fopen(fname, "rb");//二进制只读
    if (!f) {
        fprintf(stderr, "%s: Err, open '%s' err\n", __func__, fname);
        return NULL;
    }
    if (fseek(f, 0, SEEK_END)) {
        fprintf(stderr, "%s: Err: fseek end '%s'\n", __func__, fname);
        fclose(f);
        return NULL;
    }
    *size = ftell(f);
    rewind(f);//文件指针复位
    	//printf("Info: '%s' size is %zu\n", fname, *size);
    return f; 
}

static void *load_ovm(const char * fname, size_t * size)//加载 OVM 文件到内存中
{
    void * ovm_addr;//存储模型数据的指针
    FILE * f;
    size_t fsize, sz;// 文件大小和对齐大小
    f = f_ini(fname, &fsize);
    if (!f) {
        return NULL;
    }
    //make ovm buffer size cache line aligned
    sz = (size_t)OVM_ALIGN(fsize);//计算对齐后的大小
    if (!sz) {
        fprintf(stderr, "%s(%s): Err: sz=%zu f=%zu\n", __func__,
                fname, sz, fsize);
        goto l_err;
    }
    ovm_addr = ovt_cma_alloc(sz);//分配内存以存储 OVM 数据
    if (!ovm_addr) {
        fprintf(stderr, "%s(%s): Err: cnn_get_ovmbuf %zu failed\n",
                __func__, fname, sz);
        goto l_err;
    }
    sz = fread(OVM_ALIGN(ovm_addr), 1, fsize, f);//读取文件内容到分配的内存中。
    if (sz != fsize) {
        fprintf(stderr, "%s(%s): Err: read size %zu != %zu\n",
                __func__, fname, fsize, sz);
        ovt_cma_free(ovm_addr);
    l_err:
        fclose(f);
        return NULL;
    }
    fclose(f);
    if (size) {
        *size = fsize;
    }
    return ovm_addr;//返回分配的内存地址
}

// 模型初始化函数
ModelHandle initialize_model(const std::string& name, const std::string& model_path) {
    // 在这里加载模型并返回句柄
    ovm_data_ptr = load_ovm(model_path.c_str(), &ovm_model_size); // 假设这是加载模型的函数
    if (ovm_data_ptr == NULL) {
        fprintf(stderr, "%s(%s): Err: load ovm model file %zu failed\n", __func__, net_name.c_str());
        return -1;
    }
    int ret = 0;
    cnn3_load_t load[1];
    cnn_std_t std_param = { norm, mean, std_json };
    load[0].std = &std_param;
    load[0].csc_mode = CNN_CSC_NONE;
    hdl = cnn3_load_persistent_output(ovm_data_ptr, ovm_model_size, (void**)NULL, NULL, NULL, 0, 0, load);
    
    //fprintf(stderr, "%s %d %p\n", __FILE__, __LINE__, load);
    // fprintf(stderr, "%s %d %p\n", __FILE__, __LINE__, hdl);
    if (hdl == NULL) {
        fprintf(stderr, "%d %s(%s): Err: load ovm model %zu failed\n", __LINE__, __func__, net_name.c_str());
        return -1;
    }
    //fprintf(stderr, "%s %d %p\n", __FILE__, __LINE__, hdl);
    model_handles[model_path] = hdl;//将路径和句柄映射
    // std::cout << "[INFO] Model loaded and mapped to path: " << model_path << std::endl;
    ret = cnn_get_output_list(hdl, &output_count, &tid);
    model_output[model_path] = output_count;
    model_tid_map[model_path] = tid;
    // printf(" ===============================%s :: output_count=%ld\n", net_name.c_str(), output_count);
    if (ret != 0) {
        fprintf(stderr, "%s(%s): Err: %s cnn_get_output_list failed\n", __func__, net_name.c_str());
        return -1;
    }
    int m_channel, m_width, m_height;
    cnn_get_input_shape(hdl, &m_channel, &m_width, &m_height);
    // printf("m_channel %d, m_width %d, m_height %d\n", m_channel, m_width, m_height);

    //flag_init = true;
    return hdl;
}

// 从 JSON 文件加载模型路径
void load_model_paths(const std::string& json_file_path) {
    std::ifstream json_file(json_file_path);
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errs;

    if (Json::parseFromStream(builder, json_file, &root, &errs)) {
        const Json::Value models = root["models"];
        for (const auto& model : models) {
            std::string url = model["name"].asString();
            std::string path = model["path"].asString();
            // 将 url 和 path 映射存储到全局 map 中
            model_paths[url] = path;
        }
    } else {
        std::cerr << "Failed to parse JSON: " << errs << std::endl;
    }
}

// 检查文件是否存在的函数
bool file_exists(const std::string& filename) {
    std::ifstream file(filename);
    return file.good();
}

int deinit(){
    if(flag_init){
        cnn_unload(hdl);
        hdl=NULL;
        ovt_cma_free(ovm_data_ptr);
        ovm_data_ptr=NULL;
        ovm_model_size=0;
        flag_init=false;
    }
    return 0;
}

void send_success_response(mk_http_response_invoker invoker, const std::string& message) {
    Json::Value success_response;
    success_response["status"] = "success";
    success_response["message"] = message;
    std::string json_response = Json::writeString(Json::StreamWriterBuilder(), success_response);

    mk_buffer response_buffer = mk_buffer_from_char(json_response.c_str(), json_response.size(), nullptr, nullptr);
    mk_http_body response_body_obj = mk_http_body_from_buffer(response_buffer);
    const char* response_header[] = {"Content-Type", "application/json", NULL};
    mk_http_response_invoker_do(invoker, 200, response_header, response_body_obj);
    mk_http_body_release(response_body_obj);
}

void send_error_response(mk_http_response_invoker invoker, const std::string& message) {
    Json::Value error_response;
    error_response["status"] = "error";
    error_response["message"] = message;
    std::string json_response = Json::writeString(Json::StreamWriterBuilder(), error_response);

    mk_buffer response_buffer = mk_buffer_from_char(json_response.c_str(), json_response.size(), nullptr, nullptr);
    mk_http_body response_body_obj = mk_http_body_from_buffer(response_buffer);
    const char* response_header[] = {"Content-Type", "application/json", NULL};
    mk_http_response_invoker_do(invoker, 400, response_header, response_body_obj);
    mk_http_body_release(response_body_obj);
}

void free_callback(void *user_data, void *data)
{
    free(data);
}

void process_gray_image_request(const mk_parser& parser, mk_http_response_invoker invoker, ModelHandle handle, int output, int32_t *model_tid) {
    size_t content_length = 0;
    const char* data = mk_parser_get_content(parser, &content_length);
    // if (!data || content_length == 0) {
    //     send_error_response(invoker, "Empty content or failed to get content");
    //     return;
    // }
    // cv::Mat image = cv::imdecode(cv::Mat(std::vector<uchar>(data, data + content_length)), cv::IMREAD_GRAYSCALE);
    // if (image.empty()) {
    //     send_error_response(invoker, "Failed to decode image");
    //     return;
    // }
    //cv::resize(image, image, cv::Size(96, 96));
    //size_t img_size = image.cols * image.rows;
    // printf(" ============================== :: content_length=%zu\n", content_length);

    void* pic_addr = ovt_cma_alloc(content_length);
    if (!pic_addr) {
        send_error_response(invoker, "Failed to allocate memory for image");
        return;
    }

    memcpy(pic_addr, data, content_length);
    std::cout << "[DEBUG]  handle: " << handle << std::endl;
    std::cout << "[DEBUG]  pic_addr: " << pic_addr << std::endl;
    std::cout << "[DEBUG]  output_count: " << output << std::endl;
    std::cout << "[DEBUG]  output_f: " << output_f << std::endl;
    std::cout << "[DEBUG]  element_count: " << element_count << std::endl; 
    int ret = cnn3_inference_with_outputs_f(handle, &pic_addr, output, output_f, element_count);
    std::cout << "[DEBUG]  ret: " << ret << std::endl;
    if (ret != 0) {
        send_error_response(invoker, "Inference failed");
        ovt_cma_free(pic_addr);
        return;
    }


    // std::cout << "[DEBUG]  ovt_cma_free pic_addr: " << pic_addr << std::endl;
    InferenceResult inference_result;
    for (int i = 0; i < output; i++) {
        Tensor* tensor = inference_result.add_tensors();
        int32_t channel = 0, height = 0, width = 0;
        cnn_get_output_shape(handle, model_tid[i], &channel, &height, &width);

        tensor->add_shape(1);
        tensor->add_shape(channel);
        tensor->add_shape(height);
        tensor->add_shape(width);
        std::cout << "[DEBUG]  channel: " << channel << std::endl;
        std::cout << "[DEBUG]  height: " << height << std::endl;
        std::cout << "[DEBUG]  width: " << width << std::endl;  
        float* data_ptr = output_f[i];
        size_t num_elements = channel * height * width;
        for (size_t j = 0; j < num_elements; ++j) {
            tensor->add_data(data_ptr[j]);
            std::cout << "[DEBUG]  handle: " << handle << std::endl;
            std::cout << "[DEBUG]  第几次 " << j << std::endl;
            std::cout << "[DEBUG]  data_ptr[0]: [DEBUG]  data_ptr[1]: [DEBUG]  data_ptr[2]:" << data_ptr[0] << data_ptr[1] << data_ptr[2] << std::endl;
        }
    }

    // 直接构建响应体并返回，而不进行序列化
        size_t result_size = inference_result.ByteSize();
        void* result_data = malloc(result_size);
        if (!result_data) {
            send_error_response(invoker, "Failed to allocate memory for result");
            return;
        }

        // 将结果数据填充到 result_data 中
        inference_result.SerializeToArray(result_data, result_size); 

        const char* response_header[] = { "Content-Type", "application/x-protobuf", NULL };
        mk_buffer response_buffer = mk_buffer_from_char((const char*)result_data, result_size, free_callback, nullptr);
        
        mk_http_body response_body_obj = mk_http_body_from_buffer(response_buffer);
        mk_http_response_invoker_do(invoker, 200, response_header, response_body_obj);
        ovt_cma_free(pic_addr);
        mk_http_body_release(response_body_obj);
        mk_buffer_unref(response_buffer);
        // free(result_data);
       
}


void process_model_json(const mk_http_response_invoker invoker)
{
   if (file_exists("model.json")) {
        // 读取并解析 model.json
        std::ifstream json_file("model.json");
        Json::Value root;
        Json::CharReaderBuilder builder;
        std::string errs;

        if (Json::parseFromStream(builder, json_file, &root, &errs)) {
            // 成功解析 JSON，处理数据
            const Json::Value models = root["models"];
            std::cout << "model.json 内容：" << root.toStyledString() << std::endl;
            for (const auto& model : models) {
                std::string name = model["name"].asString();
                std::string model_path = model["path"].asString();
                // std::cout << "模型名称: " << name << ", 路径: " << model_path << std::endl;          

                initialize_model(name, model_path);
                load_model_paths("model.json");
            }
            send_success_response(invoker, "parse json successful!");
        } else {
            send_error_response(invoker, "Failed to parse model.json");
        }
    } else {
        send_error_response(invoker, "model.json not found");
    }
}

void process_tar_request(const mk_parser& parser, mk_http_response_invoker invoker) {
    //删除旧的文件和文件夹
    if(std::filesystem::exists("model.tar")){
        std::remove("model.tar");
    }
    if(std::filesystem::exists("model")){
        std::filesystem::remove_all("model");
    }
    if(std::filesystem::exists("model.json")){
        std::filesystem::remove_all("model.json");
    }
    size_t tar_size = 0;
    const char* tar_data = mk_parser_get_content(parser, &tar_size);

    std::ofstream out_file("model.tar", std::ios::binary);
    if (!out_file) {
        send_error_response(invoker, "File write failed");
        return;
    }

    out_file.write(tar_data, tar_size);
    out_file.close();
  
    for (auto it = model_handles.begin(); it != model_handles.end(); ++it) {
    const std::string& key = it->first;
    const ModelHandle& handle = it->second;

    std::cout << "Key: " << key << ", Handle: " << handle << std::endl;
    cnn_unload(it->second);
  
    ovt_cma_free(ovm_data_ptr);
    ovm_data_ptr=NULL;
    ovm_model_size=0;
    }
    cnn_exit();
    cnn_init();

     // 解压 tar 文件
    if (system("tar -xvf model.tar") == 0) {
        process_model_json(invoker);
    } else {
        send_error_response(invoker, "Unpack failed");
    }
}
bool tar_processed = false;  // 标志变量，跟踪 /tar 请求是否被处理
void on_mk_http_request(const mk_parser parser, const mk_http_response_invoker invoker, int* consumed, const mk_sock_info sender) {

    *consumed = 0;
    const char* url = mk_parser_get_url(parser);
    const char* method = mk_parser_get_method(parser);
    
    if (strcmp(url, "/tar") == 0 && (strcmp(method, "POST") == 0 || strcmp(method, "post") == 0)) {
        // 处理 /tar 请求
        process_tar_request(parser, invoker);
        tar_processed = true;  // 标记 /tar 已处理
        *consumed = 1;
        return;
    }

    // 处理POST请求
    if (tar_processed) {
        if (strcmp(method, "POST") == 0 || strcmp(method, "post") == 0) {
            // // 将URL转换为字符串，便于操作
            std::string clean_url(url);
            // 去掉首部斜杠（如果有）
            if (!clean_url.empty() && clean_url.front() == '/') {
                clean_url.erase(0, 1);
            }
       
            // 将 url 和 path 映射存储到全局 map 中
            std::string model_path = model_paths[clean_url]; // 查找路径
          
            // 查找模型句柄
            ModelHandle model_handle = model_handles[model_path];
            int output = model_output[model_path];
            int32_t* model_tid = model_tid_map[model_path]; 
            if (!model_handle) {
                // 如果模型句柄不存在，返回错误
                std::cerr << "Error: Model handle not found for path: " << model_path << std::endl;
                send_error_response(invoker, "Model handle not found");
                *consumed = 1;
                return;
            }

            // 处理请求，假设有一个处理函数
            process_gray_image_request(parser, invoker, model_handle, output, model_tid);
            *consumed = 1;
        }
    }
}

int main() {
    cnn_init();
   
    char *ini_path = mk_util_get_exe_dir("config.ini");
    mk_config config; 
    config.ini = ini_path;
    config.ini_is_path = 1;
    config.log_level = 4;
    config.log_mask = LOG_CONSOLE;
    config.log_file_path = NULL;
    config.log_file_days = 0;
    config.ssl = NULL;
    config.ssl_is_path = 1;
    config.ssl_pwd = NULL;
    config.thread_num = 1;
    mk_env_init(&config);
    mk_free(ini_path);
    mk_events events_http = {0};
    events_http.on_mk_http_request = on_mk_http_request;
    mk_events_listen(&events_http);
   
    mk_http_server_start(1180, 0);
    if (file_exists("model.json")) {
        // 读取并解析 model.json
        std::ifstream json_file("model.json");
        Json::Value root;
        Json::CharReaderBuilder builder;
        std::string errs;

        if (Json::parseFromStream(builder, json_file, &root, &errs)) {
            // 成功解析 JSON，处理数据
            const Json::Value models = root["models"];

            for (const auto& model : models) {
                std::string name = model["name"].asString();
                std::string model_path = model["path"].asString();
        
                initialize_model(name, model_path);
                load_model_paths("model.json");
                tar_processed = true;
            }
        } 
    } 
    while(true){

    }
    cnn_exit();
    return 0;
}

