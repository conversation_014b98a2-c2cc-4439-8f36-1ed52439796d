#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <chrono>
#include <fstream>
#include <iostream>
#include "dms_calculate_warning_byd.h"
#include "dms_process.h"
#include "dms_warming.h"
#include "json.h"

using namespace tongxing;

static long get_timestamp() {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    long now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    return now_ts;
}

static double getTimeSecond(const std::chrono::high_resolution_clock::time_point& begin,
                            const std::chrono::high_resolution_clock::time_point& end) {
    std::chrono::duration<double> elapsed = end - begin;
    return elapsed.count();
}

static std::chrono::high_resolution_clock::time_point getTimePoints(long long timestamp) {
    // 输入的时间戳（毫秒）
    // long long timestamp = 1694404284218;

    // 将时间戳转换为 std::chrono::milliseconds
    std::chrono::milliseconds duration(timestamp);

    // 使用 std::chrono::high_resolution_clock 来创建时间点
    std::chrono::high_resolution_clock::time_point time_point(duration);
    return time_point;
}

static std::string extractDirectoryName(const std::string& filePath) {
    std::istringstream pathStream(filePath);
    std::string item;
    std::vector<std::string> pathParts;

    // 将文件路径按 '/' 分割成多个部分
    while (std::getline(pathStream, item, '/')) {
        if (!item.empty()) {
            pathParts.push_back(item);
        }
    }

    // 检查路径中是否包含足够的部分，并且提取出倒数第二个部分（即目录名）
    if (pathParts.size() >= 2) {
        return pathParts[pathParts.size() - 2];
    } else {
        // 如果路径格式不正确，返回空字符串或错误信息
        return "";
    }
}

static bool create_directory_if_not_exists(const std::string& path) {
    struct stat st = {0};
    // 检查路径是否存在
    if (stat(path.c_str(), &st) != 0) {
        // 路径不存在，尝试创建目录
        if (mkdir(path.c_str(), S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) != 0) {
            // 创建目录失败
            perror("Failed to create directory");
            return false;
        }
        std::cout << "Directory created: " << path << std::endl;
    } else {
        // 路径已存在，检查是否为目录
        if (!S_ISDIR(st.st_mode)) {
            std::cerr << "Path exists but is not a directory: " << path << std::endl;
            return false;
        }
        std::cout << "Directory already exists: " << path << std::endl;
    }
    return true;
}

// typedef enum DrowsinessType2_ {
//     Drowsiness_Normal = 0,      // 正常
//     Drowsiness_Invalid = 1,     // 疲劳报警无效
//     Drowsiness_Fatigue2 = 2,    // 疲劳报警2级
//     Drowsiness_Fatigue3 = 3,    // 疲劳报警3级
//     Drowsiness_Fatigue4 = 4,    // 疲劳报警4级
//     Drowsiness_NoResponse = 5,  // 疲劳报警无响应
// } DrowsinessType2;

// typedef enum CameraStatus2_ {
//     Camera_Normal = 0,     //正常
//     Camera_Occlusion = 1,  //相机遮挡
//     No_face = 2,           //无人脸
//     No_face_mask = 3,      //无人脸-口罩
//     No_face_irblock = 4    //无人脸-ir阻断
// } CameraStatus2;

// typedef struct DmsResult_ {
//     CameraStatus2 camera_status;
//     DrowsinessType2 drowsiness_type;
// } DmsResult;

void print_usage(const char* name) {
    std::cout << "\nUsage: " << name << " case_file_txt \n"
              << std::endl
              << "json context example:" << std::endl
              << " {  \n"
              << "   \"car_speed\": 10,  #车速\n"
              << "   \"camera_occlusion\": false,  #摄像头是否遮挡\n"
              << "   \"no_face\": false,  #是否没有人脸\n"
              << "   \"has_ir_block\": false, #是否带有红外阻断眼睛\n"
              << "   \"has_mask\": false,  #是否戴口罩\n"
              << "   \"time_points\": \"1694404284218\",  #时间戳\n"
              << "   \"eye_close_status\": true,   #眼睛是否闭眼\n"
              << "   \"mouth_open_status\": false,  #嘴巴是否张开 \n"
              << "   \"ldw_status\": false,   #ldw是否发生\n"
              << "   \"reset_warn\": false   #是否重置报警\n"
              << "  } \n"
              << "\n"
              << std::endl;
}

int main(int argc, char* argv[]) {
    printf("Build Your Dream DDAW test Tool ........\n");

    if (argc != 2) {
        // printf("Usage %s  json_file\n", argv[0]);
        print_usage(argv[0]);
        return -1;
    }
    auto json_file = argv[1];

    std::ifstream readFile;
    readFile.open(json_file, std::ios::in);

    if (readFile.is_open()) {
        printf("Sucess open %s!\n", json_file);

        std::chrono::high_resolution_clock::time_point current_time_start;
        std::chrono::high_resolution_clock::time_point current_time_end;

        //人脸 摄像头遮挡报警
        cc_dms_warming noface_warning;
        cc_dms_warming occlusion_warning;
        cc_dms_warming irblock_warning;
        cc_dms_warming mask_warning;

        //标记报警是否已经触发
        bool noface_warning_flag = false;
        bool noface_stop_first_flag = false;
        long noface_warning_stop;
        bool occlusion_warning_flag = false;
        bool occlusion_stop_first_flag = false;
        long occlusion_warning_stop;
        bool irblock_warning_flag = false;
        bool irblock_stop_first_flag = false;
        long irblock_warning_stop;
        bool mask_warning_flag = false;
        bool mask_stop_first_flag = false;
        long mask_warning_stop;

        noface_warning.init(3000, 1.0, 0, 10);    //无人脸
        irblock_warning.init(3000, 1.0, 0, 10);   //红外阻断眼睛
        mask_warning.init(3000, 1.0, 0, 10);      //戴口罩
        occlusion_warning.init(5000, 1.0, 0, 0);  //遮挡

        // DrowsinessWarn warn;
        DrowsinessWarn warn;

        // std::string strbeat(json_file);
        std::string str;

        InternalCameraType last_history_warn_type = Camera_Norm;

        while (std::getline(readFile, str)) {
            current_time_start = std::chrono::high_resolution_clock::now();
            std::cout << "json_file name: " << str << std::endl;

            std::string config_file = str;
            // 这里读取json顺序文件
            Json::Reader json_reader;
            Json::Value root;

            std::ifstream infile(config_file, std::ios::in);
            if (!infile.is_open()) {
                std::cout << "Open config file failed!" << std::endl;
                continue;
            }

            if (!json_reader.parse(infile, root)) {
                std::cout << "Parse json config file failed!" << std::endl;
                return -1;
            }

            //报警状态初始化
            InternalCameraType temp_camera_status = Camera_Norm;  //临时摄像头状态结果
            InternalCameraType current_camera_type = Camera_Norm;  //存放当前摄像头状态结果
            TXDrowsinessType current_warn_type = Drowsiness_Normal;  //当前疲劳报警类型

            bool no_face = root["no_face"].asBool();                    //是否没有人脸
            long now_ts = std::stoll(root["time_points"].asString());   //时间戳
            bool camera_occlusion = root["camera_occlusion"].asBool();  //是否有摄像头遮挡
            int car_speed = root["car_speed"].asInt();                  //车速

            bool ir_block = root["has_ir_block"].asBool();  //IR红外阻断
            bool mask = root["has_mask"].asBool();          //mask

            Warn_Info warn_info;
            warn_info.eye_close_status = root["eye_close_status"].asBool();    //是否闭眼
            warn_info.mouth_open_status = root["mouth_open_status"].asBool();  //是否张嘴
            if (root.isMember("ldw_status")) {
                warn_info.ldw_status = root["ldw_status"].asBool();  //ldw状态值
            } else {
                warn_info.ldw_status = false;
            }

            if (root.isMember("time_points")) {
                printf("time_points exist\n");
                warn_info.time_input = now_ts;
            } else {
                warn_info.time_input = get_timestamp();
            }

            bool reset_warn = root["reset_warn"].asBool();  //重置疲劳报警

            //疲劳报警逻辑

            if (reset_warn) {
                warn.Reset();
            } else {
                if (last_history_warn_type == Camera_Normal)
                    //更新数据
                    warn.Update(warn_info);
            }

            //获取结果
            TXWarnInfo warn_info_out;
            WarningType warn_type = warn.GetWarnStatus();
            std::cout << "warn_type_result:" << warn_type << std::endl;
            switch (warn_type) {
                case NORMAL:
                    current_warn_type = Drowsiness_Normal;
                    break;
                case DROWSINESS_LEVEL_LIGHT:
                    current_warn_type = Drowsiness_Fatigue2;
                    break;
                case DROWSINESS_LEVEL_MEDIUM:
                    current_warn_type = Drowsiness_Fatigue3;
                    break;
                case DROWSINESS_LEVEL_HEAVY:
                    current_warn_type = Drowsiness_Fatigue4;
                    break;
                case NO_RESPONSE:
                    current_warn_type = Drowsiness_NoResponse;
                    break;

                default:
                    current_warn_type = Drowsiness_Invalid;
                    break;
            }

            //遮挡人脸报警
            if (occlusion_warning.update(camera_occlusion, now_ts, car_speed)) {
                temp_camera_status = Camera_Occ;
                occlusion_warning_flag = true;
                occlusion_stop_first_flag = false;
            } else {
                if (occlusion_warning_flag && !occlusion_stop_first_flag) {
                    occlusion_stop_first_flag = true;
                    occlusion_warning_stop = now_ts;
                }
            }

            if (noface_warning.update((no_face), now_ts, car_speed)) {
                temp_camera_status = No_face;
                noface_warning_flag = true;
                noface_stop_first_flag = false;
            } else {
                if (noface_warning_flag && !noface_stop_first_flag) {
                    noface_stop_first_flag = true;
                    noface_warning_stop = now_ts;
                }
            }

            if (mask_warning.update((mask), now_ts, car_speed)) {
                temp_camera_status = No_face_mask;
                mask_warning_flag = true;
                mask_stop_first_flag = false;
            } else {
                if (mask_warning_flag && !mask_stop_first_flag) {
                    mask_stop_first_flag = true;
                    mask_warning_stop = now_ts;
                }
            }

            if (irblock_warning.update((ir_block), now_ts, car_speed)) {
                temp_camera_status = No_face_irblock;
                irblock_warning_flag = true;
                irblock_stop_first_flag = false;
            } else {
                if (irblock_warning_flag && !irblock_stop_first_flag) {
                    irblock_stop_first_flag = true;
                    irblock_warning_stop = now_ts;
                }
            }

            //这里针对摄像头故障逻辑处理
            if (last_history_warn_type != Camera_Normal) {
                switch (last_history_warn_type) {
                    case No_face:
                        if (now_ts - noface_warning_stop >= 1000) {
                            current_camera_type = Camera_Norm;
                            last_history_warn_type = Camera_Norm;
                            noface_warning_flag = false;
                            noface_stop_first_flag = false;
                        } else {
                            current_camera_type = last_history_warn_type;
                        }
                        break;
                    case No_face_mask:
                        if (now_ts - mask_warning_stop >= 1000) {
                            current_camera_type = Camera_Norm;
                            last_history_warn_type = Camera_Norm;
                            mask_warning_flag = false;
                            mask_stop_first_flag = false;
                        } else {
                            current_camera_type = last_history_warn_type;
                        }
                        break;
                    case No_face_irblock:
                        if (now_ts - irblock_warning_stop >= 1000) {
                            current_camera_type = Camera_Norm;
                            last_history_warn_type = Camera_Norm;
                            irblock_warning_flag = false;
                            irblock_stop_first_flag = false;
                        } else {
                            current_camera_type = last_history_warn_type;
                        }
                        break;
                    case Camera_Occlusion:
                        if (now_ts - occlusion_warning_stop >= 1000) {
                            current_camera_type = Camera_Norm;
                            last_history_warn_type = Camera_Norm;
                            occlusion_warning_flag = false;
                            occlusion_stop_first_flag = false;
                        } else {
                            current_camera_type = last_history_warn_type;
                        }
                        break;

                    default:
                        break;
                }
            } else {
                last_history_warn_type = temp_camera_status;
                current_camera_type = temp_camera_status;
            }

            TXDmsResult dms;
            if (current_camera_type >= No_face) {
                dms.camera_status = (TXCameraStatus)No_face;
            } else {
                dms.camera_status = (TXCameraStatus)current_camera_type;
            }

            if (current_camera_type != Camera_Normal) {
                dms.drowsiness_status = Drowsiness_Invalid;
                // last_history_warn_type = dms.camera_status;
            } else {
                //速度必须大于10km/h
                if (car_speed >= 10) {
                    dms.drowsiness_status = current_warn_type;
                } else {
                    dms.drowsiness_status = Drowsiness_Normal;
                }
            }

            //这里写入json结果
            Json::Value write_root;
            write_root["json_file_name"] = config_file;
            write_root["dms_warning_type"] = dms.camera_status;
            write_root["drowsiness_warning_type"] = dms.drowsiness_status;

            // 将json内容（缩进格式）输出到文件
            Json::StyledWriter writer;
            std::ofstream os;
            std::string json_path_root = "./output_json/" + extractDirectoryName(config_file) + "/";
            std::cout << json_path_root << std::endl;

            if ((access(json_path_root.c_str(), 0)) != -1) {
            } else {
                mkdir(json_path_root.c_str(), S_IRWXU);
            }

            // if (create_directory_if_not_exists(json_path_root)) {
            //     // 目录创建成功或已存在，继续执行后续操作
            //     std::cout << "Directory is ready for use." << std::endl;
            // } else {
            //     // 目录创建失败，处理错误
            //     std::cerr << "Failed to ensure the directory exists." << std::endl;
            //     return 1;  // 返回非零值表示程序异常退出
            // }

            std::string::size_type at_start = config_file.find_last_of("/");
            std::string::size_type at_end = config_file.find_last_of(".");
            std::string json_path = config_file.substr(at_start, at_end - at_start);
            std::string json_output_file = json_path_root + json_path + "_out" + ".json";

            os.open(json_output_file);
            os << writer.write(write_root);
            os.close();

            current_time_end = std::chrono::high_resolution_clock::now();
            double tt = getTimeSecond(current_time_start, current_time_end);
            std::cout << "time cost:" << tt << " s\n";

            // usleep((33 * 1.0f - 1000 * tt) * 1000);
            // usleep(33000);
        }
    } else {
        std::cout << "Open File Failure!" << std::endl;
    }

    readFile.close();

    printf("Build Your Dream DDAW test Tool end......\n");
    return 0;
}