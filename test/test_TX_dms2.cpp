#include "tx_dms_sdk.h"
#include <stdio.h>
#include <unistd.h>
#include "eazyai.h"
#include <sys/time.h>
#include "cc_cam.h"

long g_dms_handle = 0;
#include "opencv2/opencv.hpp"
void CalmcarDmsCallback(const TXDmsResult_ *result, void *user_data) // DMS结果获取
{
    // if (result->face_info.score < 0.5)
    // {
    //     return;
    // }
    
    printf("-----callback start----- %0.2f \n", result->face_info.score);
    printf("frame id: %lld\n", result->result_frame_id);


    // printf("face score: %f\n", result->face_info.score);
    // printf("face xmin: %d\n", result->face_info.xmin);
    // printf("face ymin: %d\n", result->face_info.ymin);
    // printf("face xmax: %d\n", result->face_info.xmax);
    // printf("face ymax: %d\n", result->face_info.ymax);
    // printf("face head_yaw: %f\n", result->face_info.head_yaw);
    // printf("face head_pitch: %f\n", result->face_info.head_pitch);
    printf("dms_status: %d\n", result->dms_status);
    printf("------callback end------\n");
}

int main(int argc, char **argv)
{
    int features = EA_ENV_ENABLE_CAVALRY | EA_ENV_ENABLE_VPROC | EA_ENV_ENABLE_NNCTRL | EA_ENV_ENABLE_IAV;
  
    ea_env_open(features);
    int ch = 0;
    long img_index = 0;
    ea_img_resource_t *img_resource_cam = NULL;
    ea_img_resource_data_t img_cam;
    img_resource_cam = CamUtil_Init(ch);
    if (img_resource_cam == NULL)
    {
        std::cout << " open cam fail !!!!" << std::endl;
        return -1;
    }
    

    g_dms_handle = TXDmsCreate(NULL, "./"); // 创建dms句柄
    if (g_dms_handle == 0)
    {
        printf("creat dms handle create fail\n");
        return -1;
    }
    char  version[50];
    TXDmsGetVersion(g_dms_handle,version); //获取算法版本号
    printf(" ver :%s", version);
    TXDmsRegistCallback(g_dms_handle, CalmcarDmsCallback, NULL); // 注册回调函数
    TXPoint2i left_top_point;
    TXPoint2i right_bottom_point;
    left_top_point.x = 0;
    left_top_point.y = 0;
    right_bottom_point.x = 1280;
    right_bottom_point.y = 720; 
    
    TXDmsSetDriverRoi(g_dms_handle, &left_top_point, &right_bottom_point);
    while (1)
    {
       
        int retcode = CamUtil_GetFrame(img_resource_cam, &img_cam);
        if (retcode !=0 )
        {
            std::cout << " ------ get frame fail  " << std::endl;
            continue;
        }
        
        struct timeval tv;
        gettimeofday(&tv, NULL);
        long now_ts_start = tv.tv_sec * 1000 + tv.tv_usec / 1000;

        TXDmsSetInputByEaTensor(g_dms_handle, img_cam.tensor_group[0], EA_VP, img_index++, 71);
        gettimeofday(&tv, NULL);
        long now_ts_end = tv.tv_sec * 1000 + tv.tv_usec / 1000;
        std::cout << " ------------- long time : "<< now_ts_end - now_ts_start << std::endl;
        
        // usleep(10* 1000);
    }

    TXDmsDestroy(g_dms_handle);
    ea_env_close();
    return 0;
}