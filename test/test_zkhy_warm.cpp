#include "tx_dms_sdk.h"
#include <stdio.h>
#include <unistd.h>
#include "dms_warming_zkhy_unsafe.h"
#include "dms_warming_zkhy_fatigue.h"

long g_dms_handle = 0;
#include "opencv2/opencv.hpp"


int main(int argc, char **argv)
{
    tongxing::on_dms_fatigue_warming fatigue_warming;
    tongxing::on_dms_unsafe_warming unsafe_warming;
    fatigue_warming.init(1000,10,10);
    unsafe_warming.init(1000,10,10);
   

   long mun_i=0;
    while (1)
    {
        int f_warm = fatigue_warming.update(0, 0.8, 30);
        int u_warm = unsafe_warming.update( 20 , 10, 0 , 30);



        std::cout << " ------------- mun_i: "<<mun_i << std::endl;
        std::cout << " ------------------ f_warm: "<<f_warm << std::endl;
        std::cout << " ---------------------- u_warm: "<<u_warm << std::endl;
        usleep(50*1000);
        mun_i ++ ;
    }

    return 0;
}