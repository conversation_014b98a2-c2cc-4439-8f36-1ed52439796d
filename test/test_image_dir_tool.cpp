/**
本文件为内部 测试图片及渲染结果，仅在x86上进行运行，读取输入目录下的图片文件，先进行排序，在进行顺序读取；

*/
#include <stdio.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include "calmcar_dms_process.h"
#include "json.h"
// #include "libovt.h"
#include <dirent.h>  // For directory reading on POSIX systems
#include <algorithm>
#include <iostream>
#include <string>
#include <vector>
#include "opencv2/opencv.hpp"
#include "tx_dms_sdk.h"

// long hDms = 0;
TXDmsResult dms_result;

tongxing::CcDmsProcess handle;

static int InitSdk() {
    std::cout << "dms verson: " << TXDmsGetVersion() << std::endl;
    std::cout << "dms really verson: " << TXDmsGetRealVersion() << std::endl;
    // hDms = TXDmsCreate(NULL, "cache/");
    int iRet = handle.Init(NULL, "cache/");
    if (iRet != 0) {
        std::cout << "creat dms fail" << std::endl;
        return -1;
    } else {
        std::cout << "creat dms success" << std::endl;
    }

    return 0;
}

static void SaveResultToJsonFile(std::string& dir_str, std::string file_str, TXCarInfo carInfo) {
    Json::Value root;
    Json::Value dms_result_json;
    Json::Value warnInfo_json;
    Json::Value tired_json;
    dms_result_json["result_frame_id"] = dms_result.result_frame_id;
    dms_result_json["camera_status"] = dms_result.camera_status;
    dms_result_json["drowsiness_type"] = dms_result.drowsiness_status;
    dms_result_json["distraction_type"] = dms_result.distraction_status;
    dms_result_json["calibrate_status"] = dms_result.calibrate_status;
    dms_result_json["system_status"] = dms_result.system_status;
    dms_result_json["car_speed"] = carInfo.speed;
    dms_result_json["car_gear"] = carInfo.gear;
    dms_result_json["car_steer_whl_snsr_rad"] = carInfo.steer_whl_snsr_rad;
    dms_result_json["turn_light"] = carInfo.turn_light;
    dms_result_json["door_status"] = carInfo.driver_door_status;
    dms_result_json["seat_status"] = carInfo.driver_seat_status;
    dms_result_json["distraction_params"] = handle.GetDistractParamers();
    dms_result_json["distraction_reason"] = handle.GetDistractReason(dms_result.camera_status);
    float left_eye_thr, right_eye_thr;
    handle.GetRightLeftEyeThr(left_eye_thr, right_eye_thr);
    dms_result_json["left_up_down_proportion"] = left_eye_thr;
    dms_result_json["right_up_down_proportion"] = right_eye_thr;
    {
        Json::Value face_info;
        face_info["score"] = dms_result.face_info.score;
        face_info["xmin"] = dms_result.face_info.xmin;
        face_info["ymin"] = dms_result.face_info.ymin;
        face_info["xmax"] = dms_result.face_info.xmax;
        face_info["ymax"] = dms_result.face_info.ymax;
        face_info["yaw"] = dms_result.face_info.head_yaw;
        face_info["pitch"] = dms_result.face_info.head_pitch;
        face_info["roll"] = dms_result.face_info.head_roll;
        face_info["isMask"] = dms_result.face_info.isMask;
        face_info["isGlass"] = dms_result.face_info.isGlass;
        face_info["isIRBlock"] = dms_result.face_info.isIRBlock;
        face_info["right_close_eye_score"] = dms_result.face_info.right_close_eye_score;
        face_info["left_close_eye_score"] = dms_result.face_info.left_close_eye_score;
        face_info["mouth_opening"] = dms_result.face_info.mouth_opening;
        Json::Value landmarks(Json::arrayValue);
        for (int i = 0; i < TX_MAX_FLD_SIZE; i++) {
            Json::Value point;
            point["x"] = dms_result.face_info.landmarks[i].x;
            point["y"] = dms_result.face_info.landmarks[i].y;
            landmarks[i] = point;
        }

        {
            Json::Value right_eye_landmark;
            right_eye_landmark["eye_score"] = dms_result.face_info.right_eye_landmark.eye_score;
            right_eye_landmark["eye_angle"] = dms_result.face_info.right_eye_landmark.eye_angle;
            right_eye_landmark["eye_center"] = Json::Value();
            right_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.right_eye_landmark.eye_center.x;
            right_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.right_eye_landmark.eye_center.y;
            right_eye_landmark["eye_size"] = Json::Value();
            right_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.right_eye_landmark.eye_size.width;
            right_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.right_eye_landmark.eye_size.height;
            right_eye_landmark["iris_score"] = dms_result.face_info.right_eye_landmark.iris_score;
            right_eye_landmark["iris_center"] = Json::Value();
            right_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.right_eye_landmark.iris_center.x;
            right_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.right_eye_landmark.iris_center.y;
            right_eye_landmark["iris_radius"] = dms_result.face_info.right_eye_landmark.iris_radius;
            right_eye_landmark["pupil_score"] = dms_result.face_info.right_eye_landmark.pupil_score;
            right_eye_landmark["pupil_center"] = Json::Value();
            right_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.right_eye_landmark.pupil_center.x;
            right_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.right_eye_landmark.pupil_center.y;
            right_eye_landmark["pupil_radius"] =
                dms_result.face_info.right_eye_landmark.pupil_radius;
            right_eye_landmark["yaw"] = dms_result.face_info.right_eye_landmark.yaw;
            right_eye_landmark["pitch"] = dms_result.face_info.right_eye_landmark.pitch;
            face_info["right_eye_landmark"] = right_eye_landmark;
            Json::Value left_eye_landmark;
            left_eye_landmark["eye_score"] = dms_result.face_info.left_eye_landmark.eye_score;
            left_eye_landmark["eye_angle"] = dms_result.face_info.left_eye_landmark.eye_angle;
            left_eye_landmark["eye_center"] = Json::Value();
            left_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.left_eye_landmark.eye_center.x;
            left_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.left_eye_landmark.eye_center.y;
            left_eye_landmark["eye_size"] = Json::Value();
            left_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.left_eye_landmark.eye_size.width;
            left_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.left_eye_landmark.eye_size.height;
            left_eye_landmark["iris_score"] = dms_result.face_info.left_eye_landmark.iris_score;
            left_eye_landmark["iris_center"] = Json::Value();
            left_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.left_eye_landmark.iris_center.x;
            left_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.left_eye_landmark.iris_center.y;
            left_eye_landmark["iris_radius"] = dms_result.face_info.left_eye_landmark.iris_radius;
            left_eye_landmark["pupil_score"] = dms_result.face_info.left_eye_landmark.pupil_score;
            left_eye_landmark["pupil_center"] = Json::Value();
            left_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.left_eye_landmark.pupil_center.x;
            left_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.left_eye_landmark.pupil_center.y;
            left_eye_landmark["pupil_radius"] = dms_result.face_info.left_eye_landmark.pupil_radius;
            left_eye_landmark["yaw"] = dms_result.face_info.left_eye_landmark.yaw;
            left_eye_landmark["pitch"] = dms_result.face_info.left_eye_landmark.pitch;
            face_info["left_eye_landmark"] = left_eye_landmark;
        }
        face_info["landmarks"] = landmarks;
        dms_result_json["face_info"] = face_info;
    }
    {
        warnInfo_json["eye1_60s_10p"] = dms_result.face_info.warnInfo.eye1_60s_10p;
        warnInfo_json["mouth1_120s_2"] = dms_result.face_info.warnInfo.mouth1_120s_2;
        warnInfo_json["mouth1_1l_1"] = dms_result.face_info.warnInfo.mouth1_1l_1;
        warnInfo_json["eye2_20s_1f5"] = dms_result.face_info.warnInfo.eye2_20s_1f5;
        warnInfo_json["eye2_2_0f75"] = dms_result.face_info.warnInfo.eye2_2_0f75;
        warnInfo_json["eye2_1l_0f75"] = dms_result.face_info.warnInfo.eye2_1l_0f75;
        warnInfo_json["eye2_60s_12p"] = dms_result.face_info.warnInfo.eye2_60s_12p;
        warnInfo_json["mouth2_1l_2"] = dms_result.face_info.warnInfo.mouth2_1l_2;
        warnInfo_json["mouth2_120s_3"] = dms_result.face_info.warnInfo.mouth2_120s_3;
        warnInfo_json["eye3_20s_2f4"] = dms_result.face_info.warnInfo.eye3_20s_2f4;
        warnInfo_json["eye3_2_1f2"] = dms_result.face_info.warnInfo.eye3_2_1f2;
        warnInfo_json["eye3_1l_1f2"] = dms_result.face_info.warnInfo.eye3_1l_1f2;
        warnInfo_json["repetition"] = dms_result.face_info.warnInfo.repetition;
    }
    {
        tx_tired temp = {0};
        handle.GetTiredInfo(temp);
        tired_json["total_eye_count"] = temp.total_eye_count;
        tired_json["fatigue_blink_count"] = int(temp.fatigue_blink_count);
        tired_json["fatigue_blink_duration_ms"] = temp.fatigue_blink_duration_ms;
        tired_json["fatigue_blink_start_end_index"] = (temp.fatigue_blink_start_end_index);
        tired_json["close_eye_count"] = int(temp.close_eye_count);
        tired_json["close_eye_duration_ms"] = temp.close_eye_duration_ms;
        tired_json["close_eye_start_end_index"] = (temp.close_eye_start_end_index);
        tired_json["total_mouth_count"] = int(temp.total_mouth_count);
        tired_json["yawn_count"] = temp.yawn_count;
        tired_json["open_mouth_duration_ms"] = (temp.open_mouth_duration_ms);
        tired_json["open_mouth_start_end_index"] = (temp.open_mouth_start_end_index);
    }
    // root["ts"] = ts;
    root["dms_result"] = dms_result_json;
    root["warnInfo"] = warnInfo_json;
    root["tiredInfo"] = tired_json;
    root["sdk_version"] = TXDmsGetVersion();
    root["sdk_really_version"] = TXDmsGetRealVersion();

    //写入json
    Json::StyledWriter writer;
    std::ofstream os;
    std::string json_path_root = dir_str + "/";
    std::cout << json_path_root << std::endl;

    if ((access(json_path_root.c_str(), 0)) != -1) {
    } else {
        mkdir(json_path_root.c_str(), S_IRWXU);
    }

    std::string::size_type at_start = file_str.find_last_of("/");
    std::string::size_type at_end = file_str.find_last_of(".");
    std::string json_path = file_str.substr(at_start, at_end - at_start);
    std::string json_output_file = json_path_root + json_path + ".json";

    os.open(json_output_file);
    os << writer.write(root);
    os.close();
}

static void DirSortFile(std::string path, std::vector<std::string>& files) {
    // 打开目录
    DIR* dir;
    struct dirent* ent;
    if ((dir = opendir(path.c_str())) != NULL) {
        // 遍历目录中的文件
        while ((ent = readdir(dir)) != NULL) {
            std::string name = ent->d_name;
            // 检查是否为.jpg文件
            if (name.size() > 4 &&
                (name.substr(name.size() - 4) == ".jpg" ||
                 name.substr(name.size() - 4) == ".png")) {
                files.push_back(name);
            }
        }
        closedir(dir);
    } else {
        // 如果无法打开目录，输出错误信息
        perror("无法打开目录");
        return EXIT_FAILURE;
    }

    // 使用lambda表达式定义排序规则
    std::sort(files.begin(), files.end(), [](const std::string& a, const std::string& b) {
        // 提取文件名中的数字部分
        int numA = std::stoi(a.substr(0, a.find('.')));
        int numB = std::stoi(b.substr(0, b.find('.')));
        return numA < numB;
    });

    // 输出排序后的文件列表
    // for (const auto& file : files) {
    //     std::cout << file << std::endl;
    // }

    return;
}

int main(int argc, char* argv[]) {
    printf("Hello Chen Chao!!!!!!!\n");
    //判断参数信息是否满足
    if (argc != 2) {
        printf("Usage %s  case_dir_path \n", argv[0]);
        return -1;
    }

    if (InitSdk() != 0) {
        return -2;
    }
    printf("InitSdk success!\n");

    std::string file_dir = argv[1];
    std::vector<std::string> files;
    DirSortFile(file_dir, files);

    for (auto f : files) {
        std::string ff = file_dir + "/" + f;
        std::cout << "picture_file name: " << ff << std::endl;

        cv::Mat image = cv::imread(ff, 0);

        TXImageInfo image_;
        // cv::resize(image, image, cv::Size(1280, 720));
        image_.dataType = TXInputFormat::GRAY;
        image_.height = image.size().height;
        image_.width = image.size().width;
        image_.stride = image.size().width;
        image_.dataLen = image_.height * image_.width;
        image_.data = (char*)image.data;

        TXCarInfo carInfo = {0};
        carInfo.speed = 80;
        carInfo.gear = TXGearPition::FORWARD;
        carInfo.steer_whl_snsr_rad = 0;
        carInfo.turn_light = TXTurnSignal::TURN_OFF;
        carInfo.driver_door_status = TXDriverDoorStatus::DOOR_CLOSE;
        carInfo.driver_seat_status = TXDriverSeatStatus::SEAT_STATIC;

        carInfo.mask = (TX_CAR_INFO_MASK_SPEED | TX_CAR_INFO_MASK_STEER_WHL_SNSR |
                        TX_CAR_INFO_MASK_GEARPOSITION | TX_CAR_INFO_MASK_TURN_LIGHT |
                        TX_CAR_DRIVER_DOOR | TX_CAR_DRIVER_SEAT);

        handle.updateCarInfo(&carInfo);
        handle.SetInput(&image_, &dms_result);

        SaveResultToJsonFile(file_dir, ff, carInfo);
    }

    return 0;
}