#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <iostream>
#include "cc_cam.h"
#include "utils/eazyai_utils.h"
EA_LOG_DECLARE_LOCAL(EA_LOG_LEVEL_NOTICE);

#define MAX_PATH_STRLEN 256

ea_img_resource_t *img_resource_cam = NULL;

int cv_env_init()
{
    int rval = 0;
    int features;
    do
    {
        features = EA_ENV_ENABLE_CAVALRY | EA_ENV_ENABLE_VPROC | EA_ENV_ENABLE_NNCTRL | EA_ENV_ENABLE_IAV;
        RVAL_OK(ea_env_open(features));
        // EA_R_OK(ea_utils_env_callbacks_register());
    } while (0);

    return rval;
}
void cv_env_deinit()
{
    ea_env_close();
}
// 获取相机图像的两种方式：画布、金字塔
// 画布，同一张图只有一个固定的分辨率
// 金字塔，同一张图会有6级按照固定缩放比例变化的的分辨率
ea_img_resource_t *CamUtil_Init(int ch)
{
    int rval = 0;
    ea_img_resource_t *img_resource = NULL;
    do
    {
        // 固定分辨率
        img_resource = ea_img_resource_new(EA_CANVAS, (void *)(unsigned long)ch);
        // img_resource = ea_img_resource_new(EA_PYRAMID, (void *)(unsigned long)ch);
        RVAL_ASSERT(img_resource != NULL);
    } while (0);
    return img_resource;
}

int CamUtil_GetFrame(ea_img_resource_t *img_resource, ea_img_resource_data_t *img)
{
    int rval = 0;
    do
    {
        RVAL_ASSERT(img_resource != NULL);
        ea_img_resource_drop_data(img_resource, img);
        RVAL_OK(ea_img_resource_hold_data(img_resource, img));
        // printf("\033[33m --ll-- file:cc_cam.cpp line:%d info:hello %p \033[0m \n ", __LINE__, img->tensor_group[0]);
#if 0
        // save image
        printf("\033[33m --ll-- file:test_cam.cpp line:%d info:save img \033[0m \n ", __LINE__);
        char out_path[2 * MAX_PATH_STRLEN + 1] = {0};
        snprintf(out_path, 2 * MAX_PATH_STRLEN + 1, "%s", "./output/out_2.jpg");
        EA_R_OK(ea_tensor_to_image(img.tensor_group[0], EA_TENSOR_COLOR_MODE_YUV_NV12, out_path));
#endif

    } while (0);
    return rval;
}

int CamUtil_Release(ea_img_resource_t *img_resource)
{
    int rval = 0;
    if (img_resource != NULL)
    {
        ea_img_resource_free(img_resource);
        img_resource = NULL;
    }
    cv_env_deinit();
    return rval;
}
