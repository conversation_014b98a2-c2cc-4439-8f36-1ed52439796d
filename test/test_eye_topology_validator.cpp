#include <gtest/gtest.h>
#include "../src/util/eye_topology_validator.h"
#include <opencv2/opencv.hpp>
#include <chrono>
#include <iostream>

using namespace tongxing;

class EyeTopologyValidatorTest : public ::testing::Test {
protected:
    void SetUp() override {
        validator_ = std::make_unique<EyeTopologyValidator>();
    }

    // 创建有效的眼睛关键点数据
    std::vector<float> CreateValidEyeKeypoints() {
        std::vector<float> keypoints(17 * 3); // 17个关键点，每个3个值
        
        // 眼睛轮廓点 (0-7) - 椭圆形状
        float eye_center_x = 100.0f, eye_center_y = 100.0f;
        float eye_a = 30.0f, eye_b = 15.0f; // 椭圆长短轴
        
        for (int i = 0; i < 8; i++) {
            float angle = i * 2.0f * CV_PI / 8.0f;
            keypoints[3 * i] = 0.8f; // 置信度
            keypoints[3 * i + 1] = eye_center_x + eye_a * cos(angle); // x
            keypoints[3 * i + 2] = eye_center_y + eye_b * sin(angle); // y
        }
        
        // 虹膜轮廓点 (8-15) - 圆形，在眼睛内部
        float iris_center_x = eye_center_x, iris_center_y = eye_center_y;
        float iris_radius = 10.0f;
        
        for (int i = 0; i < 8; i++) {
            int idx = i + 8;
            float angle = i * 2.0f * CV_PI / 8.0f;
            keypoints[3 * idx] = 0.7f; // 置信度
            keypoints[3 * idx + 1] = iris_center_x + iris_radius * cos(angle); // x
            keypoints[3 * idx + 2] = iris_center_y + iris_radius * sin(angle); // y
        }
        
        // 瞳孔点 (16) - 在虹膜中心
        keypoints[3 * 16] = 0.9f; // 置信度
        keypoints[3 * 16 + 1] = iris_center_x; // x
        keypoints[3 * 16 + 2] = iris_center_y; // y
        
        return keypoints;
    }

    // 创建无效的眼睛关键点数据（瞳孔在虹膜外）
    std::vector<float> CreateInvalidEyeKeypoints_PupilOutside() {
        auto keypoints = CreateValidEyeKeypoints();
        
        // 将瞳孔移到虹膜外
        keypoints[3 * 16 + 1] = 150.0f; // x 移到远处
        keypoints[3 * 16 + 2] = 150.0f; // y 移到远处
        
        return keypoints;
    }

    // 创建低置信度的关键点数据
    std::vector<float> CreateLowConfidenceKeypoints() {
        auto keypoints = CreateValidEyeKeypoints();
        
        // 将所有置信度设为很低
        for (int i = 0; i < 17; i++) {
            keypoints[3 * i] = 0.1f; // 低置信度
        }
        
        return keypoints;
    }

    std::unique_ptr<EyeTopologyValidator> validator_;
};

TEST_F(EyeTopologyValidatorTest, ParseValidKeypoints) {
    auto raw_keypoints = CreateValidEyeKeypoints();
    auto parsed = validator_->ParseEyeKeypoints(raw_keypoints.data());
    
    // 检查解析结果
    EXPECT_EQ(parsed.eye_contour.size(), 8);
    EXPECT_EQ(parsed.iris_contour.size(), 8);
    EXPECT_EQ(parsed.confidences.size(), 17);
    
    // 检查置信度
    EXPECT_GT(parsed.confidences[0], 0.5f);
    EXPECT_GT(parsed.confidences[8], 0.5f);
    EXPECT_GT(parsed.confidences[16], 0.5f);
}

TEST_F(EyeTopologyValidatorTest, ParseNullKeypoints) {
    auto parsed = validator_->ParseEyeKeypoints(nullptr);
    
    // 应该返回空的结构体
    EXPECT_EQ(parsed.eye_contour.size(), 8);
    EXPECT_EQ(parsed.iris_contour.size(), 8);
    EXPECT_EQ(parsed.confidences.size(), 17);
}

TEST_F(EyeTopologyValidatorTest, ValidateValidTopology) {
    auto raw_keypoints = CreateValidEyeKeypoints();
    auto parsed = validator_->ParseEyeKeypoints(raw_keypoints.data());
    auto result = validator_->ValidateTopology(parsed);
    
    // 有效的拓扑结构应该通过验证
    EXPECT_TRUE(result.is_valid);
    EXPECT_TRUE(result.pupil_in_iris);
    EXPECT_TRUE(result.iris_in_eye);
    EXPECT_TRUE(result.eye_contour_valid);
    EXPECT_TRUE(result.iris_contour_valid);
    EXPECT_TRUE(result.size_ratio_valid);
    EXPECT_GT(result.confidence_score, 0.5f);
    EXPECT_TRUE(result.error_message.empty());
}

TEST_F(EyeTopologyValidatorTest, ValidateInvalidTopology_PupilOutside) {
    auto raw_keypoints = CreateInvalidEyeKeypoints_PupilOutside();
    auto parsed = validator_->ParseEyeKeypoints(raw_keypoints.data());
    auto result = validator_->ValidateTopology(parsed);
    
    // 瞳孔在虹膜外的情况应该验证失败
    EXPECT_FALSE(result.is_valid);
    EXPECT_FALSE(result.pupil_in_iris);
    EXPECT_LT(result.confidence_score, 1.0f);
}

TEST_F(EyeTopologyValidatorTest, ValidateLowConfidence) {
    auto raw_keypoints = CreateLowConfidenceKeypoints();
    auto parsed = validator_->ParseEyeKeypoints(raw_keypoints.data());
    auto result = validator_->ValidateTopology(parsed);
    
    // 低置信度应该导致验证失败
    EXPECT_FALSE(result.is_valid);
    EXPECT_FALSE(result.error_message.empty());
    EXPECT_LT(result.confidence_score, 0.5f);
}

TEST_F(EyeTopologyValidatorTest, SetValidationParameters) {
    // 测试参数设置
    validator_->SetMinConfidenceThreshold(0.5f);
    validator_->SetPupilIrisRatioRange(0.1f, 0.7f);
    validator_->SetIrisEyeRatioRange(0.2f, 0.9f);
    
    auto raw_keypoints = CreateValidEyeKeypoints();
    auto parsed = validator_->ParseEyeKeypoints(raw_keypoints.data());
    auto result = validator_->ValidateTopology(parsed);
    
    // 参数设置后应该仍然能正常工作
    EXPECT_TRUE(result.is_valid);
}

TEST_F(EyeTopologyValidatorTest, EdgeCaseEmptyContour) {
    EyeTopologyValidator::EyeKeypoints empty_keypoints;
    // 保持默认初始化的空关键点
    
    auto result = validator_->ValidateTopology(empty_keypoints);
    
    // 空关键点应该验证失败
    EXPECT_FALSE(result.is_valid);
    EXPECT_FALSE(result.error_message.empty());
}

// 性能测试
TEST_F(EyeTopologyValidatorTest, PerformanceTest) {
    auto raw_keypoints = CreateValidEyeKeypoints();
    auto parsed = validator_->ParseEyeKeypoints(raw_keypoints.data());
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // 执行1000次验证
    for (int i = 0; i < 1000; i++) {
        auto result = validator_->ValidateTopology(parsed);
        EXPECT_TRUE(result.is_valid);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    // 平均每次验证应该在1ms以内
    double avg_time_ms = duration.count() / 1000.0 / 1000.0;
    EXPECT_LT(avg_time_ms, 1.0);
    
    std::cout << "Average validation time: " << avg_time_ms << " ms" << std::endl;
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
