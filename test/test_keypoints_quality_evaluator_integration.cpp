#include <gtest/gtest.h>
#include "../tongxing_util/src/module/postprocess/filter/keypoints_quality_evaluator.h"
#include <vector>
#include <memory>

using namespace tongxing;

class KeypointsQualityEvaluatorIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        evaluator_ = std::make_unique<CcKeypointsQualityEvaluator>();
        
        // 创建配置JSON
        Json::Value config;
        config["num_keypoints"] = 17;
        config["window_size"] = 10;
        config["std_threshold"] = 2.0;
        config["velocity_threshold"] = 5.0;
        config["acceleration_threshold"] = 10.0;
        config["confidence_threshold"] = 0.7;
        config["instability_ratio_threshold"] = 0.3;
        config["enable_logging"] = false;
        config["enable_eye_topology_validation"] = true;
        
        evaluator_->init(config);
    }

    // 创建有效的17个关键点数据
    std::shared_ptr<NumArray> CreateValidKeypointsNumArray() {
        auto num_array = std::make_shared<NumArray>();
        num_array->shape = {17, 3}; // 17个关键点，每个3个值(confidence, x, y)
        num_array->word_size = sizeof(float);

        std::vector<float> keypoints_data = {
            // 眼睛轮廓 (8个点) - 格式：confidence, x, y，按顺时针排列形成凸多边形
            0.9f, 80.0f, 90.0f,   // 0 - 左中
            0.9f, 85.0f, 85.0f,   // 1 - 左上
            0.9f, 95.0f, 80.0f,   // 2 - 上中
            0.9f, 105.0f, 85.0f,  // 3 - 右上
            0.9f, 110.0f, 90.0f,  // 4 - 右中
            0.9f, 105.0f, 95.0f,  // 5 - 右下
            0.9f, 95.0f, 100.0f,  // 6 - 下中
            0.9f, 85.0f, 95.0f,   // 7 - 左下

            // 虹膜轮廓 (8个点) - 格式：confidence, x, y
            0.9f, 90.0f, 87.0f,   // 8
            0.9f, 93.0f, 85.0f,   // 9
            0.9f, 97.0f, 87.0f,   // 10
            0.9f, 100.0f, 90.0f,  // 11
            0.9f, 97.0f, 93.0f,   // 12
            0.9f, 93.0f, 95.0f,   // 13
            0.9f, 90.0f, 93.0f,   // 14
            0.9f, 87.0f, 90.0f,   // 15

            // 瞳孔点 (1个点) - 格式：confidence, x, y
            0.9f, 95.0f, 90.0f    // 16
        };
        
        size_t total_size = keypoints_data.size() * sizeof(float);
        num_array->data = new unsigned char[total_size];
        std::memcpy(num_array->data, keypoints_data.data(), total_size);
        
        return num_array;
    }

    // 创建无效的关键点数据（瞳孔在虹膜外）
    std::shared_ptr<NumArray> CreateInvalidKeypointsNumArray() {
        auto num_array = CreateValidKeypointsNumArray();
        
        // 修改瞳孔位置到虹膜外
        float* data = reinterpret_cast<float*>(num_array->data);
        data[16 * 3 + 0] = 150.0f; // x坐标移到远处
        data[16 * 3 + 1] = 150.0f; // y坐标移到远处
        
        return num_array;
    }

    std::unique_ptr<CcKeypointsQualityEvaluator> evaluator_;
};

TEST_F(KeypointsQualityEvaluatorIntegrationTest, ValidEyeTopologyIntegration) {
    auto keypoints = CreateValidKeypointsNumArray();
    std::vector<std::shared_ptr<NumArray>> inputs = {keypoints};

    // 执行评估
    evaluator_->setInput(inputs);
    evaluator_->execute();
    
    // 检查眼睛拓扑验证结果
    auto topology_result = evaluator_->getEyeTopologyValidation();
    EXPECT_TRUE(topology_result.is_valid);
    EXPECT_TRUE(topology_result.pupil_in_iris);
    EXPECT_TRUE(topology_result.iris_in_eye);
    EXPECT_TRUE(topology_result.eye_contour_valid);
    EXPECT_TRUE(topology_result.iris_contour_valid);
    EXPECT_TRUE(topology_result.size_ratio_valid);
    EXPECT_TRUE(topology_result.distribution_valid);
    EXPECT_TRUE(topology_result.smoothness_valid);
    EXPECT_GT(topology_result.confidence_score, 0.5f);
    
    // 检查便捷方法
    EXPECT_TRUE(evaluator_->isEyeTopologyValid());
}

TEST_F(KeypointsQualityEvaluatorIntegrationTest, InvalidEyeTopologyIntegration) {
    auto keypoints = CreateInvalidKeypointsNumArray();
    std::vector<std::shared_ptr<NumArray>> inputs = {keypoints};

    // 执行评估
    evaluator_->setInput(inputs);
    evaluator_->execute();
    
    // 检查眼睛拓扑验证结果
    auto topology_result = evaluator_->getEyeTopologyValidation();
    EXPECT_FALSE(topology_result.is_valid);
    EXPECT_FALSE(topology_result.pupil_in_iris); // 瞳孔不在虹膜内
    
    // 检查便捷方法
    EXPECT_FALSE(evaluator_->isEyeTopologyValid());
}

TEST_F(KeypointsQualityEvaluatorIntegrationTest, DisabledEyeTopologyValidation) {
    // 重新配置为禁用眼睛拓扑验证
    Json::Value config;
    config["num_keypoints"] = 17;
    config["enable_eye_topology_validation"] = false;
    
    evaluator_->init(config);
    
    auto keypoints = CreateValidKeypointsNumArray();
    std::vector<std::shared_ptr<NumArray>> inputs = {keypoints};

    // 执行评估
    evaluator_->setInput(inputs);
    evaluator_->execute();
    
    // 当禁用时，应该返回默认的无效结果
    auto topology_result = evaluator_->getEyeTopologyValidation();
    EXPECT_FALSE(topology_result.is_valid);
    EXPECT_FALSE(evaluator_->isEyeTopologyValid());
}

TEST_F(KeypointsQualityEvaluatorIntegrationTest, NonEyeKeypointsCount) {
    // 重新配置为非17个关键点
    Json::Value config;
    config["num_keypoints"] = 68; // 面部关键点
    config["enable_eye_topology_validation"] = true;
    
    evaluator_->init(config);
    
    // 创建68个关键点的数据
    auto num_array = std::make_shared<NumArray>();
    num_array->shape = {68, 3};
    num_array->word_size = sizeof(float);
    
    std::vector<float> keypoints_data(68 * 3, 0.5f); // 填充默认值
    size_t total_size = keypoints_data.size() * sizeof(float);
    num_array->data = new unsigned char[total_size];
    std::memcpy(num_array->data, keypoints_data.data(), total_size);

    std::vector<std::shared_ptr<NumArray>> inputs = {num_array};

    // 执行评估
    evaluator_->setInput(inputs);
    evaluator_->execute();
    
    // 对于非17个关键点，不应该执行眼睛拓扑验证
    auto topology_result = evaluator_->getEyeTopologyValidation();
    EXPECT_FALSE(topology_result.is_valid);
    EXPECT_FALSE(evaluator_->isEyeTopologyValid());
}
