#include <stddef.h>
#include <sys/time.h>
#include <time.h>
#include <unistd.h>
#include <iostream>
#include "cc_media_server.h"
#include "opencv2/opencv.hpp"
#include "tx_dms_sdk.h"

long last_ts = 0;
TXDmsResult dms_result;

int main(int argc, char** argv) {
    // tongxing::CcMediaServer media_server;
    // media_server.init("./");
    last_ts = clock() / 1000;
    long img_index = 0;
    std::cout << "--------main------- start" << std::endl;
    int ret = 0;
#ifdef TX_STANDARD_NON_EU_TYPE
    ret = TXDmsSetStandardType(TX_STANDARD_NON_EU);
     if (ret != 0) {
        std::cout << "set Non EU standard type fail" << std::endl;
        return -1;
    } else {
        std::cout << "set Non EU standard type success" << std::endl;
    }
#endif
    TXStandardType type = TXDmsGetStandardType();
    std::string type_str = "";
    if (type == TX_STANDARD_EU)
        type_str = "EU";
    else if (type == TX_STANDARD_NON_EU)
        type_str = "NON_EU";

    long hDms = TXDmsCreate(NULL, "cache/");
    if (hDms == 0) {
        std::cout << "creat dms fail" << std::endl;
        return -1;
    } else {
        std::cout << "creat dms success" << std::endl;
    }

    TXPoint2i left_top_point;
    TXPoint2i right_bottom_point;
    left_top_point.x = 280;
    left_top_point.y = 0;
    right_bottom_point.x = 1000;
    right_bottom_point.y = 720;
    TXDmsSetDriverRoi(hDms, &left_top_point, &right_bottom_point);
    cv::Mat yuv422M;
    TXDmsSetLogLevel(LEVEL_INFO);

#ifdef WITH_PHONE_SMOKING_DET
    TXDmsEnablePhoneAndSmokingDetect(hDms, 1);
#endif
    for (int index = 1; index <= 10000; index++) {
        TXImageInfo image_;
        char path[1024] = {0};
        if (dms_result.calibrate_status != 0) {
            sprintf(path, "../image/1calibration_sc2ede.png");
        } else {
            sprintf(path, "../image/1dis_sc2ede.png");
        }
        // sprintf(path, "test.jpg", index);
        std::cout << "path:" << path << std::endl;
        cv::Mat image = cv::imread(path, 0);  //读取图片;
        cv::resize(image, image, cv::Size(1280, 800));
        image_.dataType = TXInputFormat::GRAY;
        image_.height = image.size().height;
        image_.width = image.size().width;
        image_.stride = image.size().width;
        image_.dataLen = image_.height * image_.width;
        image_.data = (char*)image.data;

        TXCarInfo carInfo = {0};
        std::cout << "start------------------------------------" << index
                  << "------------------------------" << std::endl;
        TXDmsSetInput(hDms, &image_, &dms_result);
        std::cout << "end------------------------------------" << index
                  << "------------------------------" << std::endl;
        carInfo.mask = TX_CAR_INFO_MASK_SPEED | TX_CAR_INFO_MASK_TURN_LIGHT |
            TX_CAR_INFO_MASK_GEARPOSITION | TX_CAR_DRIVER_SEAT | TX_CAR_DRIVER_DOOR;
        carInfo.speed = 60;
        carInfo.gear = TXGearPition::FORWARD;
        carInfo.turn_light = TXTurnSignal::TURN_OFF;
        carInfo.driver_door_status = TXDriverDoorStatus::DOOR_CLOSE;
        carInfo.driver_seat_status = TXDriverSeatStatus::SEAT_STATIC;

        TXDmsUpdataCarInfo(hDms, &carInfo);
        printf("result_frame_id:%ld,camera_status:%d,drowsiness_status:%d,distraction_status:%d\n",
               dms_result.result_frame_id, dms_result.camera_status, dms_result.drowsiness_status,
               dms_result.distraction_status);

        usleep(10000);
    }
    return 0;
}