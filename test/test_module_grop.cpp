#include "cc_module_grop.h"
#include <fstream>
#include <iostream>
#include "cc_npy_loader.h"
#include "image_utils.h"
#include "opencv2/opencv.hpp"
#include <time.h>
#include "cc_numarray_tool.h"
int main()
{
    Json::Reader json_reader;
    Json::Value config;
    std::ifstream infile("../resourc/mnn/dms_config.json", std::ios::binary);
    if (!infile.is_open())
    {
        std::cout << "Open config file failed!" << std::endl;
        return -1;
    }

    if (!json_reader.parse(infile, config))
    {
        std::cout << "Parse json config file failed!" << std::endl;
        return -1;
    }
    std::cout << "_______init_______" << std::endl;
    std::shared_ptr<calmcar::CcModule> grop = calmcar::get_cc_module(config);

    // cv::Mat cv_mat = cv::imread("normal_2022-03-25_18-13-14_30.jpg", 0);
    cv::VideoCapture videoCapture;
    videoCapture.open("004a608c23baea79.jpg");
    if (!videoCapture.isOpened())
    {
        std::cout << "open video fails!" << std::endl;
        return -1;
    }
    bool run = true;

    for (int i = 0; run; i++)
    {
        cv::Mat cv_mat;
        videoCapture >> cv_mat;
        if(cv_mat.empty()){
            break;
        }
 
        cv::Mat show = cv_mat.clone();
        cv::cvtColor(cv_mat, cv_mat, cv::COLOR_RGB2GRAY);
        std::cout << "image size:" << cv_mat.size().width * cv_mat.size().height << std::endl;
        std::shared_ptr<calmcar::BlobData> blob(new calmcar::BlobData);
        std::shared_ptr<calmcar::NumArray> ptr(new calmcar::NumArray);
        blob->init(cv_mat.size().width * cv_mat.size().height);
        calmcar::cv_mat_to_unsigned_char(cv_mat, cv_mat.size().width, cv_mat.size().height, 1, blob->pu8VirAddr);
        ptr->type = calmcar::NumArray::UINT8;
        ptr->word_size = 1;
        ptr->data = blob->pu8VirAddr;
        ptr->shape.push_back(1);
        ptr->shape.push_back(1);
        ptr->shape.push_back(cv_mat.size().height);
        ptr->shape.push_back(cv_mat.size().width);

        ptr->data_blob_ptr = blob;

        std::vector<std::shared_ptr<calmcar::NumArray>> input;
        input.push_back(ptr);
        std::cout << "_______set input_______" << std::endl;
        std::shared_ptr<calmcar::NumArray> face_roi = calmcar::creat_numarray({1, 4}, calmcar::NumArray::DataType::FLOAT32);
        float *face_roi_data_ptr = (float *)face_roi->data;
        face_roi_data_ptr[0] = 0;
        face_roi_data_ptr[1] = 0;
        face_roi_data_ptr[2] = 1920;
        face_roi_data_ptr[3] = 1080;
        input.push_back(face_roi);
        grop->setInput(input);
        std::cout << "_______get output0_______" << std::endl;

        double start, end, cost;
        start = clock();
        auto face_bbox = grop->getOutput(0);
        auto face_keypoint = grop->getOutput(1);
        auto right_eye_infer = grop->getOutput(2)->getTensor<float>();
        auto left_eye_infer = grop->getOutput(3)->getTensor<float>();
        auto angle = grop->getOutput(4)->getTensor<float>();
        auto faceid = grop->getOutput(7)->getTensor<float>();
        end = clock();
        cost = end - start;
        printf("%f\n", cost);
        faceid->printShape();
        std::cout << right_eye_infer->operator[](0)[0].get() << " " << right_eye_infer->operator[](0)[1].get() << std::endl;
        std::cout << left_eye_infer->operator[](0)[0].get() << " " << left_eye_infer->operator[](0)[1].get() << std::endl;
        std::cout << angle->operator[](0)[0].get() << " " << angle->operator[](0)[1].get() << " " << angle->operator[](0)[2].get() << std::endl;

        // auto faceId=grop->getOutput(6)->getTensor<float>();

        for (int i = 0; i < face_bbox->shape[1]; i++)
        {
            cv::Rect r(face_bbox->getTensor<float>()->operator[](0)[i][2].get(), face_bbox->getTensor<float>()->operator[](0)[i][3].get(), face_bbox->getTensor<float>()->operator[](0)[i][4].get(), face_bbox->getTensor<float>()->operator[](0)[i][5].get());
            cv::rectangle(show, r, cv::Scalar(0, 255, 0));
            // cv::putText(show, "le:" + std::to_string(int(left_eye_infer->operator[](0)[0].get() * 100)), cv::Point(face_bbox->getTensor<float>()->operator[](0)[i][2].get(), face_bbox->getTensor<float>()->operator[](0)[i][3].get()), 1, 2, cv::Scalar(0, 255, 0));
            // cv::putText(show, "re:" + std::to_string(int(right_eye_infer->operator[](0)[0].get() * 100)), cv::Point(face_bbox->getTensor<float>()->operator[](0)[i][2].get() + 120, face_bbox->getTensor<float>()->operator[](0)[i][3].get()), 1, 2, cv::Scalar(0, 255, 0));
            cv::putText(show, "pit:" + std::to_string(int(angle->operator[](0)[0].get())), cv::Point(face_bbox->getTensor<float>()->operator[](0)[i][2].get(), face_bbox->getTensor<float>()->operator[](0)[i][3].get() + 50), 1, 2, cv::Scalar(0, 255, 0));
            cv::putText(show, "yaw:" + std::to_string(int(angle->operator[](0)[1].get())), cv::Point(face_bbox->getTensor<float>()->operator[](0)[i][2].get() + 120, face_bbox->getTensor<float>()->operator[](0)[i][3].get() + 50), 1, 2, cv::Scalar(0, 255, 0));
        }

        for (int i = 0; i < face_keypoint->shape[1]; i++)
        {
            cv::Point p(face_keypoint->getTensor<float>()->operator[](0)[i][1].get(), face_keypoint->getTensor<float>()->operator[](0)[i][2].get());
            std::cout<<p<<std::endl;
            cv::circle(show, p,
                       1, cv::Scalar(0, 255, 0));
        }
        cv::imwrite("show.jpg", show);
    }
}
