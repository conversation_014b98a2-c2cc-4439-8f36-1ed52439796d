/**
本文件为内部单元测试用例，通过json文件读取指定图片、时间戳、车速等信息，模拟测试功能逻辑;json配置如下：
{
    "image":"/home/<USER>/case1/1.jpg",
    "car_speed":74,
    "time_points":"1694404284218",
    "reset_drowsiness_warn":false,
    "reset_distraction_warn":false
}

*/
#include <stdio.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include "calmcar_dms_process.h"
#include "json.h"
#include "opencv2/opencv.hpp"
#include "tx_dms_sdk.h"

// long hDms = 0;
TXDmsResult dms_result;
#include <memory>

std::unique_ptr<tongxing::CcDmsProcess> handle = std::make_unique<tongxing::CcDmsProcess>();
// 释放时会自动调用delete，不需要手动delete


static std::string extractDirectoryName(const std::string& filePath) {
    std::istringstream pathStream(filePath);
    std::string item;
    std::vector<std::string> pathParts;

    while (std::getline(pathStream, item, '/')) {
        if (!item.empty()) {
            pathParts.push_back(item);
        }
    }

    if (pathParts.size() >= 2) {
        return pathParts[pathParts.size() - 2];
    } else {
        return "";
    }
}

static int InitSdk() {
    std::cout << "dms verson: " << TXDmsGetVersion() << std::endl;
    std::cout << "dms really verson: " << TXDmsGetRealVersion() << std::endl;
    int iRet = 0;
#ifdef TX_STANDARD_NON_EU_TYPE
    iRet = TXDmsSetStandardType(TX_STANDARD_NON_EU);
    if (iRet != 0) {
        std::cout << "set Non EU standard type fail" << std::endl;
        return -1;
    } else {
        std::cout << "set Non EU standard type success" << std::endl;
    }
#endif
    TXStandardType type = TXDmsGetStandardType();
    std::cout << "dms standard type: " << (int)type << std::endl;
    iRet = handle->Init(NULL, "cache/");
    if (iRet != 0) {
        std::cout << "creat dms fail" << std::endl;
        return -1;
    } else {
        std::cout << "creat dms success" << std::endl;
    }

    // TXPoint2i left_top_point;
    // TXPoint2i right_bottom_point;
    // left_top_point.x = 280;
    // left_top_point.y = 0;
    // right_bottom_point.x = 1000;
    // right_bottom_point.y = 720;
    // TXDmsSetDriverRoi(hDms, &left_top_point, &right_bottom_point);
    return 0;
}

static void RunSdk(std::string& image_path, int car_speed, TXCarInfo& carInfo, long ts) {
    // cv::Mat image = cv::imread(root["image"].asString(), 0);  //读取图片;
    cv::Mat image = cv::imread(image_path, 0);

    TXImageInfo image_;
    cv::resize(image, image, cv::Size(1280, 800));
    image_.dataType = TXInputFormat::GRAY;
    image_.height = image.size().height;
    image_.width = image.size().width;
    image_.stride = image.size().width;
    image_.dataLen = image_.height * image_.width;
    image_.data = (char*)image.data;

    carInfo.speed = car_speed;
    carInfo.gear = TXGearPition::FORWARD;
    carInfo.steer_whl_snsr_rad = 0;
    carInfo.turn_light = TXTurnSignal::TURN_OFF;
    carInfo.driver_door_status = TXDriverDoorStatus::DOOR_CLOSE;
    carInfo.driver_seat_status = TXDriverSeatStatus::SEAT_STATIC;
    carInfo.mask =
        (TX_CAR_INFO_MASK_SPEED | TX_CAR_INFO_MASK_STEER_WHL_SNSR | TX_CAR_INFO_MASK_GEARPOSITION |
         TX_CAR_INFO_MASK_TURN_LIGHT | TX_CAR_DRIVER_DOOR | TX_CAR_DRIVER_SEAT);
    handle->updateCarInfo(&carInfo);
    handle->SetInput(&image_, &dms_result, ts);
}

static void SaveResultToJsonFile(std::string& str, std::string ts, TXCarInfo carInfo) {
    Json::Value root;
    Json::Value dms_result_json;
    Json::Value warnInfo_json;
    Json::Value tired_json;
    Json::Value distraction_json;
    dms_result_json["result_frame_id"] = dms_result.result_frame_id;
    dms_result_json["camera_status"] = dms_result.camera_status;
    dms_result_json["drowsiness_type"] = dms_result.drowsiness_status;
    dms_result_json["distraction_type"] = dms_result.distraction_status;
    dms_result_json["calibrate_status"] = dms_result.calibrate_status;
#if defined(DISTRACTION_INTERVENTION)
    dms_result_json["distrinterv_status"] = dms_result.distrinterv_status;
#endif
    dms_result_json["system_status"] = dms_result.system_status;
    dms_result_json["car_speed"] = carInfo.speed;
    dms_result_json["car_gear"] = carInfo.gear;
    dms_result_json["car_steer_whl_snsr_rad"] = carInfo.steer_whl_snsr_rad;
    dms_result_json["turn_light"] = carInfo.turn_light;
    dms_result_json["door_status"] = carInfo.driver_door_status;
    dms_result_json["seat_status"] = carInfo.driver_seat_status;
    dms_result_json["distraction_params"] = handle->GetDistractParamers();
    dms_result_json["distraction_reason"] = handle->GetDistractReason(dms_result.camera_status);
    float left_eye_thr, right_eye_thr;
    handle->GetRightLeftEyeThr(left_eye_thr, right_eye_thr);
    dms_result_json["left_up_down_proportion"] = left_eye_thr;
    dms_result_json["right_up_down_proportion"] = right_eye_thr;
    {
        Json::Value face_info;
        face_info["score"] = dms_result.face_info.score;
        face_info["xmin"] = dms_result.face_info.xmin / 2;
        face_info["ymin"] = dms_result.face_info.ymin / 2;
        face_info["xmax"] = dms_result.face_info.xmax / 2;
        face_info["ymax"] = dms_result.face_info.ymax / 2;
        face_info["yaw"] = dms_result.face_info.head_yaw;
        face_info["pitch"] = dms_result.face_info.head_pitch;
        face_info["roll"] = dms_result.face_info.head_roll;
        face_info["isMask"] = dms_result.face_info.isMask;
        face_info["isGlass"] = dms_result.face_info.isGlass;
        face_info["isIRBlock"] = dms_result.face_info.isIRBlock;
        face_info["right_close_eye_score"] = dms_result.face_info.right_close_eye_score;
        face_info["left_close_eye_score"] = dms_result.face_info.left_close_eye_score;
        face_info["mouth_opening"] = dms_result.face_info.mouth_opening;
        Json::Value landmarks(Json::arrayValue);
        for (int i = 0; i < TX_MAX_FLD_SIZE; i++) {
            Json::Value point;
            point["x"] = dms_result.face_info.landmarks[i].x / 2;
            point["y"] = dms_result.face_info.landmarks[i].y / 2;
            landmarks[i] = point;
        }

        {
            Json::Value right_eye_landmark;
            right_eye_landmark["eye_score"] = dms_result.face_info.right_eye_landmark.eye_score;
            right_eye_landmark["eye_angle"] = dms_result.face_info.right_eye_landmark.eye_angle;
            right_eye_landmark["eye_center"] = Json::Value();
            right_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.right_eye_landmark.eye_center.x / 2;
            right_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.right_eye_landmark.eye_center.y / 2;
            right_eye_landmark["eye_size"] = Json::Value();
            right_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.right_eye_landmark.eye_size.width / 2;
            right_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.right_eye_landmark.eye_size.height / 2;
            right_eye_landmark["iris_score"] = dms_result.face_info.right_eye_landmark.iris_score;
            right_eye_landmark["iris_center"] = Json::Value();
            right_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.right_eye_landmark.iris_center.x / 2;
            right_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.right_eye_landmark.iris_center.y / 2;
            right_eye_landmark["iris_radius"] =
                dms_result.face_info.right_eye_landmark.iris_radius / 2;
            right_eye_landmark["pupil_score"] = dms_result.face_info.right_eye_landmark.pupil_score;
            right_eye_landmark["pupil_center"] = Json::Value();
            right_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.right_eye_landmark.pupil_center.x / 2;
            right_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.right_eye_landmark.pupil_center.y / 2;
            right_eye_landmark["pupil_radius"] =
                dms_result.face_info.right_eye_landmark.pupil_radius / 2;
            right_eye_landmark["yaw"] = dms_result.face_info.right_eye_landmark.yaw;
            right_eye_landmark["pitch"] = dms_result.face_info.right_eye_landmark.pitch;
            face_info["right_eye_landmark"] = right_eye_landmark;
            Json::Value left_eye_landmark;
            left_eye_landmark["eye_score"] = dms_result.face_info.left_eye_landmark.eye_score;
            left_eye_landmark["eye_angle"] = dms_result.face_info.left_eye_landmark.eye_angle;
            left_eye_landmark["eye_center"] = Json::Value();
            left_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.left_eye_landmark.eye_center.x / 2;
            left_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.left_eye_landmark.eye_center.y / 2;
            left_eye_landmark["eye_size"] = Json::Value();
            left_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.left_eye_landmark.eye_size.width / 2;
            left_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.left_eye_landmark.eye_size.height / 2;
            left_eye_landmark["iris_score"] = dms_result.face_info.left_eye_landmark.iris_score;
            left_eye_landmark["iris_center"] = Json::Value();
            left_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.left_eye_landmark.iris_center.x / 2;
            left_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.left_eye_landmark.iris_center.y / 2;
            left_eye_landmark["iris_radius"] =
                dms_result.face_info.left_eye_landmark.iris_radius / 2;
            left_eye_landmark["pupil_score"] = dms_result.face_info.left_eye_landmark.pupil_score;
            left_eye_landmark["pupil_center"] = Json::Value();
            left_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.left_eye_landmark.pupil_center.x / 2;
            left_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.left_eye_landmark.pupil_center.y / 2;
            left_eye_landmark["pupil_radius"] =
                dms_result.face_info.left_eye_landmark.pupil_radius / 2;
            left_eye_landmark["yaw"] = dms_result.face_info.left_eye_landmark.yaw;
            left_eye_landmark["pitch"] = dms_result.face_info.left_eye_landmark.pitch;
            face_info["left_eye_landmark"] = left_eye_landmark;
        }
        face_info["landmarks"] = landmarks;
        dms_result_json["face_info"] = face_info;
    }
    {
        tx_tired temp = {0};
        handle->GetTiredInfo(temp);
        tired_json["total_eye_count"] = temp.total_eye_count;
        tired_json["fatigue_blink_count"] = int(temp.fatigue_blink_count);
        tired_json["fatigue_blink_duration_ms"] = temp.fatigue_blink_duration_ms;
        tired_json["fatigue_blink_start_end_index"] = (temp.fatigue_blink_start_end_index);
        tired_json["close_eye_count"] = int(temp.close_eye_count);
        tired_json["close_eye_duration_ms"] = temp.close_eye_duration_ms;
        tired_json["close_eye_start_end_index"] = (temp.close_eye_start_end_index);
        tired_json["total_mouth_count"] = int(temp.total_mouth_count);
        tired_json["yawn_count"] = temp.yawn_count;
        tired_json["open_mouth_duration_ms"] = (temp.open_mouth_duration_ms);
        tired_json["open_mouth_start_end_index"] = (temp.open_mouth_start_end_index);
    }
    {
        internal_analysis_distraction_info temp = {0};
        handle->GetDistractionInfo(temp);
        distraction_json["distraction_continue_percent"] = temp.distraction_continue_percent;
        distraction_json["distraction_continue_time"] = int(temp.distraction_continue_time);
        distraction_json["distraction_sum_time"] = int(temp.distraction_sum_time);
        distraction_json["distraction_front_continue_time"] =
            int(temp.distraction_front_continue_time);
        distraction_json["time_gap"] = int(temp.time_gap);
    }
    root["ts"] = ts;
    root["dms_result"] = dms_result_json;
    root["warnInfo"] = warnInfo_json;
    root["tiredInfo"] = tired_json;
    root["distraction_info"] = distraction_json;
    root["sdk_version"] = TXDmsGetVersion();
    root["sdk_really_version"] = TXDmsGetRealVersion();

    //写入json
    Json::StyledWriter writer;
    std::ofstream os;
    std::string json_path_root = "./output_json/" + extractDirectoryName(str) + "/";
    std::cout << json_path_root << std::endl;

    if ((access(json_path_root.c_str(), 0)) != -1) {
    } else {
        mkdir(json_path_root.c_str(), S_IRWXU);
    }

    std::string::size_type at_start = str.find_last_of("/");
    std::string::size_type at_end = str.find_last_of(".");
    std::string json_path = str.substr(at_start, at_end - at_start);
    std::string json_output_file = json_path_root + json_path + "_out" + ".json";

    os.open(json_output_file);
    os << writer.write(root);
    os.close();
}

int main(int argc, char* argv[]) {
    //判断参数信息是否满足
    if (argc != 2) {
        printf("Usage %s  case_list.txt \n", argv[0]);
        return -1;
    }

    if (InitSdk() != 0) {
        return -2;
    }
    printf("InitSdk success!\n");

#if 1
    auto txt_file = argv[1];

    std::ifstream readFile;
    readFile.open(txt_file, std::ios::in);

    if (!readFile.is_open()) {
        printf("Open File[%s] Failure!\n", txt_file);
        return -1;
    }

    std::string str;
    while (std::getline(readFile, str)) {
        std::cout << "json_file name: " << str << std::endl;
        Json::Reader json_reader;
        Json::Value root;

        std::ifstream infile(str, std::ios::in);
        if (!infile.is_open()) {
            std::cout << "Open config file failed!" << std::endl;
            continue;
        }

        if (!json_reader.parse(infile, root)) {
            std::cout << "Parse json config file failed!" << std::endl;
            return -1;
        }

        std::string image_path = root["image"].asString();
        int car_speed = root["car_speed"].asInt();
        long ts = std::stoll(root["time_points"].asString());
        // bool reset_drowsiness_warn = root["reset_drowsiness_warn"].asBool();
        // bool reset_distraction_warn = root["reset_distraction_warn"].asBool();

        // if (reset_drowsiness_warn)
        //     handle->RestAlarm();

        // if (reset_distraction_warn)
        //     handle->RestDistractAlarm();

        //运行算法
        TXCarInfo carInfo = {0};
        RunSdk(image_path, car_speed, carInfo, ts);

        //保存结果
        SaveResultToJsonFile(str, root["time_points"].asString(), carInfo);
    }

    readFile.close();
#endif
    printf("The dms logic test is completed..\n");
    return 0;
}